import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { IconCheck, IconX, IconLoader2, IconRefresh, IconAlertCircle } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { ordersApisCreateOrder } from '@/client'
import { type CreateOrderSchema, type OrderHandlingStatusSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { ColumnMapping } from './ColumnMapping'

export interface ProcessingResult {
  rowIndex: number
  success: boolean
  orderId?: string
  error?: string
  data: Record<string, any>
}

interface BulkImportProcessorProps {
  data: Record<string, any>[]
  mapping: ColumnMapping
  onComplete: (results: ProcessingResult[]) => void
  onBack: () => void
  className?: string
}

export function BulkImportProcessor({
  data,
  mapping,
  onComplete,
  onBack,
  className
}: BulkImportProcessorProps) {
  const { selectedOffice } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [results, setResults] = useState<ProcessingResult[]>([])
  const [hasStarted, setHasStarted] = useState(false)

  const totalRows = data.length
  const successCount = results.filter(r => r.success).length
  const errorCount = results.filter(r => !r.success).length
  const progress = hasStarted ? (results.length / totalRows) * 100 : 0

  const mapRowToOrder = (row: Record<string, any>): Partial<CreateOrderSchema> => {
    const order: Partial<CreateOrderSchema> = {
      office_id: selectedOffice?.id || '',
      handling_status: 'PENDING' as OrderHandlingStatusSchema,
      breakable: false,
      notes: '',
      customer_address: '',
    }

    Object.entries(mapping).forEach(([fileColumn, orderField]) => {
      if (orderField && row[fileColumn] !== undefined && row[fileColumn] !== null) {
        const value = row[fileColumn]
        
        switch (orderField) {
          case 'code':
          case 'customer_name':
          case 'customer_phone':
          case 'customer_address':
          case 'notes':
            order[orderField] = String(value).trim()
            break
          case 'total_price':
          case 'commission_fixed_rate':
            const numValue = parseFloat(String(value))
            order[orderField] = isNaN(numValue) ? null : numValue
            break
          case 'breakable':
            order[orderField] = Boolean(value) || String(value).toLowerCase() === 'true' || String(value) === '1'
            break
          case 'deadline_date':
            try {
              const date = new Date(value)
              order[orderField] = isNaN(date.getTime()) ? null : date.toISOString()
            } catch {
              order[orderField] = null
            }
            break
        }
      }
    })

    return order
  }

  const validateOrderData = (order: Partial<CreateOrderSchema>): string | null => {
    if (!order.code?.trim()) return 'رمز الطلب مطلوب'
    if (!order.customer_name?.trim()) return 'اسم العميل مطلوب'
    if (!order.customer_phone?.trim()) return 'رقم الهاتف مطلوب'
    if (!selectedOffice) return 'لم يتم اختيار مكتب'
    return null
  }

  const processRow = async (rowIndex: number): Promise<ProcessingResult> => {
    const row = data[rowIndex]
    const orderData = mapRowToOrder(row)
    
    const validationError = validateOrderData(orderData)
    if (validationError) {
      return {
        rowIndex,
        success: false,
        error: validationError,
        data: row
      }
    }

    try {
      const response = await ordersApisCreateOrder({
        body: orderData as CreateOrderSchema
      })

      return {
        rowIndex,
        success: true,
        orderId: response.data?.id,
        data: row
      }
    } catch (error: any) {
      let errorMessage = 'خطأ غير معروف'
      
      if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail
      } else if (error?.message) {
        errorMessage = error.message
      }

      return {
        rowIndex,
        success: false,
        error: errorMessage,
        data: row
      }
    }
  }

  const startProcessing = async () => {
    if (!selectedOffice) {
      toast.error('يرجى اختيار مكتب')
      return
    }

    setIsProcessing(true)
    setHasStarted(true)
    setResults([])
    setCurrentIndex(0)

    const newResults: ProcessingResult[] = []

    for (let i = 0; i < data.length; i++) {
      setCurrentIndex(i + 1)
      
      const result = await processRow(i)
      newResults.push(result)
      setResults([...newResults])

      // Small delay to prevent overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    setIsProcessing(false)
    onComplete(newResults)
    
    toast.success(`تم الانتهاء من المعالجة. نجح: ${newResults.filter(r => r.success).length}, فشل: ${newResults.filter(r => !r.success).length}`)
  }

  const retryFailedRow = async (rowIndex: number) => {
    const result = await processRow(rowIndex)
    setResults(prev => prev.map(r => r.rowIndex === rowIndex ? result : r))
    
    if (result.success) {
      toast.success(`تم إنشاء الطلب بنجاح للصف ${rowIndex + 1}`)
    } else {
      toast.error(`فشل في إنشاء الطلب للصف ${rowIndex + 1}: ${result.error}`)
    }
  }

  const getPhoneNumber = (row: Record<string, any>): string => {
    const phoneColumn = Object.entries(mapping).find(([_, field]) => field === 'customer_phone')?.[0]
    return phoneColumn ? String(row[phoneColumn] || '') : ''
  }

  if (!hasStarted) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>جاهز للاستيراد</CardTitle>
          <CardDescription>
            سيتم إنشاء {totalRows} طلب جديد. تأكد من صحة البيانات قبل البدء.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <IconAlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>سيتم إرسال طلب منفصل لكل صف في الملف. هذه العملية قد تستغرق بعض الوقت.</p>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    <li>عدد الطلبات: {totalRows}</li>
                    <li>المكتب المحدد: {selectedOffice?.name || 'غير محدد'}</li>
                    <li>يمكنك إعادة المحاولة للطلبات الفاشلة لاحقاً</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>

            <div className="flex justify-between">
              <Button variant="outline" onClick={onBack}>
                السابق
              </Button>
              <Button onClick={startProcessing} disabled={!selectedOffice}>
                بدء الاستيراد
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isProcessing ? (
              <IconLoader2 className="h-5 w-5 animate-spin" />
            ) : (
              <IconCheck className="h-5 w-5 text-green-600" />
            )}
            {isProcessing ? 'جاري المعالجة...' : 'تم الانتهاء من المعالجة'}
          </CardTitle>
          <CardDescription>
            {isProcessing 
              ? `معالجة الصف ${currentIndex} من ${totalRows}`
              : `تمت معالجة ${totalRows} صف`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={progress} className="w-full" />
            
            <div className="flex gap-4">
              <Badge variant="default" className="flex items-center gap-1">
                <IconCheck className="h-3 w-3" />
                نجح: {successCount}
              </Badge>
              <Badge variant="destructive" className="flex items-center gap-1">
                <IconX className="h-3 w-3" />
                فشل: {errorCount}
              </Badge>
              <Badge variant="secondary">
                المتبقي: {totalRows - results.length}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل النتائج</CardTitle>
            <CardDescription>
              عرض تفصيلي لحالة كل طلب
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الصف</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>رقم الهاتف</TableHead>
                    <TableHead>التفاصيل</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {results.map((result) => (
                    <TableRow key={result.rowIndex}>
                      <TableCell>{result.rowIndex + 1}</TableCell>
                      <TableCell>
                        {result.success ? (
                          <Badge variant="default" className="flex items-center gap-1 w-fit">
                            <IconCheck className="h-3 w-3" />
                            نجح
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                            <IconX className="h-3 w-3" />
                            فشل
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{getPhoneNumber(result.data)}</TableCell>
                      <TableCell className="max-w-xs">
                        {result.success ? (
                          <span className="text-sm text-green-600">
                            تم إنشاء الطلب بنجاح
                          </span>
                        ) : (
                          <span className="text-sm text-red-600">
                            {result.error}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {!result.success && !isProcessing && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => retryFailedRow(result.rowIndex)}
                            className="flex items-center gap-1"
                          >
                            <IconRefresh className="h-3 w-3" />
                            إعادة المحاولة
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
