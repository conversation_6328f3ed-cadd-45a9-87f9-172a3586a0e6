const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/login-tFLs2Mxw.js","assets/login-form-DnIsgA3c.js","assets/input-Bg4pLWIo.js","assets/label-DaXzHiyZ.js","assets/dialog-D2ymaVEk.js","assets/alert-61bUGbMG.js","assets/dashboard-BroB8hAU.js","assets/badge-NAf3816A.js","assets/card-DfIKKa7Z.js","assets/select-A78k4D-A.js","assets/sortable.esm-C3po2gnc.js","assets/index-TcjrkwFS.js","assets/checkbox-DKGEQKUJ.js","assets/table-BihrCVHm.js","assets/tabs-M_6D914u.js","assets/site-header-CIzPfLmd.js","assets/index-ebqzxrP_.js","assets/index-CIkfWiSn.js","assets/index-DPK73jEt.js","assets/DataTableComponent-CrSkCuVP.js","assets/IconUser-BnIbjf-4.js","assets/IconX-DXjRRO1_.js","assets/IconLoader2-Ml2xLZUB.js","assets/index-B7Uq39qR.js","assets/index--IE_NJJA.js","assets/form-XyB-BgSJ.js","assets/textarea-D29iqAZm.js","assets/bulk-import-BR-XyOjB.js","assets/IconCheck-DRw7ev0l.js","assets/assign-CilY3stJ.js","assets/form-C7KAM3Wc.js","assets/form-CckWcHWV.js","assets/index-BYvrSxmV.js","assets/index-D3TAvStq.js","assets/form-BL770Dfi.js"])))=>i.map(i=>d[i]);
var qx=Object.defineProperty;var Yx=(n,l,o)=>l in n?qx(n,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[l]=o;var bv=(n,l,o)=>Yx(n,typeof l!="symbol"?l+"":l,o);function $x(n,l){for(var o=0;o<l.length;o++){const i=l[o];if(typeof i!="string"&&!Array.isArray(i)){for(const c in i)if(c!=="default"&&!(c in n)){const f=Object.getOwnPropertyDescriptor(i,c);f&&Object.defineProperty(n,c,f.get?f:{enumerable:!0,get:()=>i[c]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))i(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&i(d)}).observe(document,{childList:!0,subtree:!0});function o(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function i(c){if(c.ep)return;c.ep=!0;const f=o(c);fetch(c.href,f)}})();var J2=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Kf(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Qu={exports:{}},rl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sv;function Ix(){if(Sv)return rl;Sv=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function o(i,c,f){var d=null;if(f!==void 0&&(d=""+f),c.key!==void 0&&(d=""+c.key),"key"in c){f={};for(var h in c)h!=="key"&&(f[h]=c[h])}else f=c;return c=f.ref,{$$typeof:n,type:i,key:d,ref:c!==void 0?c:null,props:f}}return rl.Fragment=l,rl.jsx=o,rl.jsxs=o,rl}var xv;function Xx(){return xv||(xv=1,Qu.exports=Ix()),Qu.exports}var _=Xx(),Zu={exports:{}},Se={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _v;function Kx(){if(_v)return Se;_v=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),g=Symbol.iterator;function b(A){return A===null||typeof A!="object"?null:(A=g&&A[g]||A["@@iterator"],typeof A=="function"?A:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,E={};function C(A,Y,J){this.props=A,this.context=Y,this.refs=E,this.updater=J||w}C.prototype.isReactComponent={},C.prototype.setState=function(A,Y){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,Y,"setState")},C.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function T(){}T.prototype=C.prototype;function z(A,Y,J){this.props=A,this.context=Y,this.refs=E,this.updater=J||w}var L=z.prototype=new T;L.constructor=z,R(L,C.prototype),L.isPureReactComponent=!0;var N=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function V(A,Y,J,F,ee,oe){return J=oe.ref,{$$typeof:n,type:A,key:Y,ref:J!==void 0?J:null,props:oe}}function q(A,Y){return V(A.type,Y,void 0,void 0,void 0,A.props)}function ae(A){return typeof A=="object"&&A!==null&&A.$$typeof===n}function Z(A){var Y={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(J){return Y[J]})}var re=/\/+/g;function te(A,Y){return typeof A=="object"&&A!==null&&A.key!=null?Z(""+A.key):Y.toString(36)}function le(){}function ue(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(le,le):(A.status="pending",A.then(function(Y){A.status==="pending"&&(A.status="fulfilled",A.value=Y)},function(Y){A.status==="pending"&&(A.status="rejected",A.reason=Y)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function de(A,Y,J,F,ee){var oe=typeof A;(oe==="undefined"||oe==="boolean")&&(A=null);var ne=!1;if(A===null)ne=!0;else switch(oe){case"bigint":case"string":case"number":ne=!0;break;case"object":switch(A.$$typeof){case n:case l:ne=!0;break;case v:return ne=A._init,de(ne(A._payload),Y,J,F,ee)}}if(ne)return ee=ee(A),ne=F===""?"."+te(A,0):F,N(ee)?(J="",ne!=null&&(J=ne.replace(re,"$&/")+"/"),de(ee,Y,J,"",function(ge){return ge})):ee!=null&&(ae(ee)&&(ee=q(ee,J+(ee.key==null||A&&A.key===ee.key?"":(""+ee.key).replace(re,"$&/")+"/")+ne)),Y.push(ee)),1;ne=0;var fe=F===""?".":F+":";if(N(A))for(var me=0;me<A.length;me++)F=A[me],oe=fe+te(F,me),ne+=de(F,Y,J,oe,ee);else if(me=b(A),typeof me=="function")for(A=me.call(A),me=0;!(F=A.next()).done;)F=F.value,oe=fe+te(F,me++),ne+=de(F,Y,J,oe,ee);else if(oe==="object"){if(typeof A.then=="function")return de(ue(A),Y,J,F,ee);throw Y=String(A),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return ne}function O(A,Y,J){if(A==null)return A;var F=[],ee=0;return de(A,F,"","",function(oe){return Y.call(J,oe,ee++)}),F}function $(A){if(A._status===-1){var Y=A._result;Y=Y(),Y.then(function(J){(A._status===0||A._status===-1)&&(A._status=1,A._result=J)},function(J){(A._status===0||A._status===-1)&&(A._status=2,A._result=J)}),A._status===-1&&(A._status=0,A._result=Y)}if(A._status===1)return A._result.default;throw A._result}var U=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function Q(){}return Se.Children={map:O,forEach:function(A,Y,J){O(A,function(){Y.apply(this,arguments)},J)},count:function(A){var Y=0;return O(A,function(){Y++}),Y},toArray:function(A){return O(A,function(Y){return Y})||[]},only:function(A){if(!ae(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},Se.Component=C,Se.Fragment=o,Se.Profiler=c,Se.PureComponent=z,Se.StrictMode=i,Se.Suspense=m,Se.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,Se.__COMPILER_RUNTIME={__proto__:null,c:function(A){return G.H.useMemoCache(A)}},Se.cache=function(A){return function(){return A.apply(null,arguments)}},Se.cloneElement=function(A,Y,J){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var F=R({},A.props),ee=A.key,oe=void 0;if(Y!=null)for(ne in Y.ref!==void 0&&(oe=void 0),Y.key!==void 0&&(ee=""+Y.key),Y)!W.call(Y,ne)||ne==="key"||ne==="__self"||ne==="__source"||ne==="ref"&&Y.ref===void 0||(F[ne]=Y[ne]);var ne=arguments.length-2;if(ne===1)F.children=J;else if(1<ne){for(var fe=Array(ne),me=0;me<ne;me++)fe[me]=arguments[me+2];F.children=fe}return V(A.type,ee,void 0,void 0,oe,F)},Se.createContext=function(A){return A={$$typeof:d,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:f,_context:A},A},Se.createElement=function(A,Y,J){var F,ee={},oe=null;if(Y!=null)for(F in Y.key!==void 0&&(oe=""+Y.key),Y)W.call(Y,F)&&F!=="key"&&F!=="__self"&&F!=="__source"&&(ee[F]=Y[F]);var ne=arguments.length-2;if(ne===1)ee.children=J;else if(1<ne){for(var fe=Array(ne),me=0;me<ne;me++)fe[me]=arguments[me+2];ee.children=fe}if(A&&A.defaultProps)for(F in ne=A.defaultProps,ne)ee[F]===void 0&&(ee[F]=ne[F]);return V(A,oe,void 0,void 0,null,ee)},Se.createRef=function(){return{current:null}},Se.forwardRef=function(A){return{$$typeof:h,render:A}},Se.isValidElement=ae,Se.lazy=function(A){return{$$typeof:v,_payload:{_status:-1,_result:A},_init:$}},Se.memo=function(A,Y){return{$$typeof:p,type:A,compare:Y===void 0?null:Y}},Se.startTransition=function(A){var Y=G.T,J={};G.T=J;try{var F=A(),ee=G.S;ee!==null&&ee(J,F),typeof F=="object"&&F!==null&&typeof F.then=="function"&&F.then(Q,U)}catch(oe){U(oe)}finally{G.T=Y}},Se.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},Se.use=function(A){return G.H.use(A)},Se.useActionState=function(A,Y,J){return G.H.useActionState(A,Y,J)},Se.useCallback=function(A,Y){return G.H.useCallback(A,Y)},Se.useContext=function(A){return G.H.useContext(A)},Se.useDebugValue=function(){},Se.useDeferredValue=function(A,Y){return G.H.useDeferredValue(A,Y)},Se.useEffect=function(A,Y,J){var F=G.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return F.useEffect(A,Y)},Se.useId=function(){return G.H.useId()},Se.useImperativeHandle=function(A,Y,J){return G.H.useImperativeHandle(A,Y,J)},Se.useInsertionEffect=function(A,Y){return G.H.useInsertionEffect(A,Y)},Se.useLayoutEffect=function(A,Y){return G.H.useLayoutEffect(A,Y)},Se.useMemo=function(A,Y){return G.H.useMemo(A,Y)},Se.useOptimistic=function(A,Y){return G.H.useOptimistic(A,Y)},Se.useReducer=function(A,Y,J){return G.H.useReducer(A,Y,J)},Se.useRef=function(A){return G.H.useRef(A)},Se.useState=function(A){return G.H.useState(A)},Se.useSyncExternalStore=function(A,Y,J){return G.H.useSyncExternalStore(A,Y,J)},Se.useTransition=function(){return G.H.useTransition()},Se.version="19.1.0",Se}var wv;function xl(){return wv||(wv=1,Zu.exports=Kx()),Zu.exports}var S=xl();const Ut=Kf(S),jg=$x({__proto__:null,default:Ut},[S]);var Fu={exports:{}},ol={},Wu={exports:{}},Ju={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ev;function Qx(){return Ev||(Ev=1,function(n){function l(O,$){var U=O.length;O.push($);e:for(;0<U;){var Q=U-1>>>1,A=O[Q];if(0<c(A,$))O[Q]=$,O[U]=A,U=Q;else break e}}function o(O){return O.length===0?null:O[0]}function i(O){if(O.length===0)return null;var $=O[0],U=O.pop();if(U!==$){O[0]=U;e:for(var Q=0,A=O.length,Y=A>>>1;Q<Y;){var J=2*(Q+1)-1,F=O[J],ee=J+1,oe=O[ee];if(0>c(F,U))ee<A&&0>c(oe,F)?(O[Q]=oe,O[ee]=U,Q=ee):(O[Q]=F,O[J]=U,Q=J);else if(ee<A&&0>c(oe,U))O[Q]=oe,O[ee]=U,Q=ee;else break e}}return $}function c(O,$){var U=O.sortIndex-$.sortIndex;return U!==0?U:O.id-$.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();n.unstable_now=function(){return d.now()-h}}var m=[],p=[],v=1,g=null,b=3,w=!1,R=!1,E=!1,C=!1,T=typeof setTimeout=="function"?setTimeout:null,z=typeof clearTimeout=="function"?clearTimeout:null,L=typeof setImmediate<"u"?setImmediate:null;function N(O){for(var $=o(p);$!==null;){if($.callback===null)i(p);else if($.startTime<=O)i(p),$.sortIndex=$.expirationTime,l(m,$);else break;$=o(p)}}function G(O){if(E=!1,N(O),!R)if(o(m)!==null)R=!0,W||(W=!0,te());else{var $=o(p);$!==null&&de(G,$.startTime-O)}}var W=!1,V=-1,q=5,ae=-1;function Z(){return C?!0:!(n.unstable_now()-ae<q)}function re(){if(C=!1,W){var O=n.unstable_now();ae=O;var $=!0;try{e:{R=!1,E&&(E=!1,z(V),V=-1),w=!0;var U=b;try{t:{for(N(O),g=o(m);g!==null&&!(g.expirationTime>O&&Z());){var Q=g.callback;if(typeof Q=="function"){g.callback=null,b=g.priorityLevel;var A=Q(g.expirationTime<=O);if(O=n.unstable_now(),typeof A=="function"){g.callback=A,N(O),$=!0;break t}g===o(m)&&i(m),N(O)}else i(m);g=o(m)}if(g!==null)$=!0;else{var Y=o(p);Y!==null&&de(G,Y.startTime-O),$=!1}}break e}finally{g=null,b=U,w=!1}$=void 0}}finally{$?te():W=!1}}}var te;if(typeof L=="function")te=function(){L(re)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,ue=le.port2;le.port1.onmessage=re,te=function(){ue.postMessage(null)}}else te=function(){T(re,0)};function de(O,$){V=T(function(){O(n.unstable_now())},$)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(O){O.callback=null},n.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<O?Math.floor(1e3/O):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(O){switch(b){case 1:case 2:case 3:var $=3;break;default:$=b}var U=b;b=$;try{return O()}finally{b=U}},n.unstable_requestPaint=function(){C=!0},n.unstable_runWithPriority=function(O,$){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var U=b;b=O;try{return $()}finally{b=U}},n.unstable_scheduleCallback=function(O,$,U){var Q=n.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?Q+U:Q):U=Q,O){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=U+A,O={id:v++,callback:$,priorityLevel:O,startTime:U,expirationTime:A,sortIndex:-1},U>Q?(O.sortIndex=U,l(p,O),o(m)===null&&O===o(p)&&(E?(z(V),V=-1):E=!0,de(G,U-Q))):(O.sortIndex=A,l(m,O),R||w||(R=!0,W||(W=!0,te()))),O},n.unstable_shouldYield=Z,n.unstable_wrapCallback=function(O){var $=b;return function(){var U=b;b=$;try{return O.apply(this,arguments)}finally{b=U}}}}(Ju)),Ju}var Rv;function Zx(){return Rv||(Rv=1,Wu.exports=Qx()),Wu.exports}var ef={exports:{}},ht={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cv;function Fx(){if(Cv)return ht;Cv=1;var n=xl();function l(m){var p="https://react.dev/errors/"+m;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)p+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+m+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(l(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},c=Symbol.for("react.portal");function f(m,p,v){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:g==null?null:""+g,children:m,containerInfo:p,implementation:v}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(m,p){if(m==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return ht.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,ht.createPortal=function(m,p){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(l(299));return f(m,p,null,v)},ht.flushSync=function(m){var p=d.T,v=i.p;try{if(d.T=null,i.p=2,m)return m()}finally{d.T=p,i.p=v,i.d.f()}},ht.preconnect=function(m,p){typeof m=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,i.d.C(m,p))},ht.prefetchDNS=function(m){typeof m=="string"&&i.d.D(m)},ht.preinit=function(m,p){if(typeof m=="string"&&p&&typeof p.as=="string"){var v=p.as,g=h(v,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,w=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;v==="style"?i.d.S(m,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:g,integrity:b,fetchPriority:w}):v==="script"&&i.d.X(m,{crossOrigin:g,integrity:b,fetchPriority:w,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},ht.preinitModule=function(m,p){if(typeof m=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var v=h(p.as,p.crossOrigin);i.d.M(m,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&i.d.M(m)},ht.preload=function(m,p){if(typeof m=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var v=p.as,g=h(v,p.crossOrigin);i.d.L(m,v,{crossOrigin:g,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},ht.preloadModule=function(m,p){if(typeof m=="string")if(p){var v=h(p.as,p.crossOrigin);i.d.m(m,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else i.d.m(m)},ht.requestFormReset=function(m){i.d.r(m)},ht.unstable_batchedUpdates=function(m,p){return m(p)},ht.useFormState=function(m,p,v){return d.H.useFormState(m,p,v)},ht.useFormStatus=function(){return d.H.useHostTransitionStatus()},ht.version="19.1.0",ht}var Av;function zg(){if(Av)return ef.exports;Av=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),ef.exports=Fx(),ef.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mv;function Wx(){if(Mv)return ol;Mv=1;var n=Zx(),l=xl(),o=zg();function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(i(188))}function m(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(i(188));return t!==e?null:e}for(var a=e,r=t;;){var s=a.return;if(s===null)break;var u=s.alternate;if(u===null){if(r=s.return,r!==null){a=r;continue}break}if(s.child===u.child){for(u=s.child;u;){if(u===a)return h(s),e;if(u===r)return h(s),t;u=u.sibling}throw Error(i(188))}if(a.return!==r.return)a=s,r=u;else{for(var y=!1,x=s.child;x;){if(x===a){y=!0,a=s,r=u;break}if(x===r){y=!0,r=s,a=u;break}x=x.sibling}if(!y){for(x=u.child;x;){if(x===a){y=!0,a=u,r=s;break}if(x===r){y=!0,r=u,a=s;break}x=x.sibling}if(!y)throw Error(i(189))}}if(a.alternate!==r)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,g=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),w=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),z=Symbol.for("react.consumer"),L=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),V=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),ae=Symbol.for("react.activity"),Z=Symbol.for("react.memo_cache_sentinel"),re=Symbol.iterator;function te(e){return e===null||typeof e!="object"?null:(e=re&&e[re]||e["@@iterator"],typeof e=="function"?e:null)}var le=Symbol.for("react.client.reference");function ue(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===le?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case C:return"Profiler";case E:return"StrictMode";case G:return"Suspense";case W:return"SuspenseList";case ae:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case w:return"Portal";case L:return(e.displayName||"Context")+".Provider";case z:return(e._context.displayName||"Context")+".Consumer";case N:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case V:return t=e.displayName||null,t!==null?t:ue(e.type)||"Memo";case q:t=e._payload,e=e._init;try{return ue(e(t))}catch{}}return null}var de=Array.isArray,O=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},Q=[],A=-1;function Y(e){return{current:e}}function J(e){0>A||(e.current=Q[A],Q[A]=null,A--)}function F(e,t){A++,Q[A]=e.current,e.current=t}var ee=Y(null),oe=Y(null),ne=Y(null),fe=Y(null);function me(e,t){switch(F(ne,t),F(oe,e),F(ee,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Xp(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Xp(t),e=Kp(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(ee),F(ee,e)}function ge(){J(ee),J(oe),J(ne)}function Ce(e){e.memoizedState!==null&&F(fe,e);var t=ee.current,a=Kp(t,e.type);t!==a&&(F(oe,e),F(ee,a))}function De(e){oe.current===e&&(J(ee),J(oe)),fe.current===e&&(J(fe),Jo._currentValue=U)}var He=Object.prototype.hasOwnProperty,ut=n.unstable_scheduleCallback,wn=n.unstable_cancelCallback,ao=n.unstable_shouldYield,ro=n.unstable_requestPaint,xt=n.unstable_now,$n=n.unstable_getCurrentPriorityLevel,Pt=n.unstable_ImmediatePriority,oo=n.unstable_UserBlockingPriority,_a=n.unstable_NormalPriority,Ge=n.unstable_LowPriority,ot=n.unstable_IdlePriority,cn=n.log,Td=n.unstable_setDisableYieldValue,lo=null,At=null;function In(e){if(typeof cn=="function"&&Td(e),At&&typeof At.setStrictMode=="function")try{At.setStrictMode(lo,e)}catch{}}var Mt=Math.clz32?Math.clz32:Tb,Ab=Math.log,Mb=Math.LN2;function Tb(e){return e>>>=0,e===0?32:31-(Ab(e)/Mb|0)|0}var Al=256,Ml=4194304;function wa(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Tl(e,t,a){var r=e.pendingLanes;if(r===0)return 0;var s=0,u=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var x=r&134217727;return x!==0?(r=x&~u,r!==0?s=wa(r):(y&=x,y!==0?s=wa(y):a||(a=x&~e,a!==0&&(s=wa(a))))):(x=r&~u,x!==0?s=wa(x):y!==0?s=wa(y):a||(a=r&~e,a!==0&&(s=wa(a)))),s===0?0:t!==0&&t!==s&&(t&u)===0&&(u=s&-s,a=t&-t,u>=a||u===32&&(a&4194048)!==0)?t:s}function io(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Ob(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Od(){var e=Al;return Al<<=1,(Al&4194048)===0&&(Al=256),e}function Dd(){var e=Ml;return Ml<<=1,(Ml&62914560)===0&&(Ml=4194304),e}function Bs(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function so(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Db(e,t,a,r,s,u){var y=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var x=e.entanglements,M=e.expirationTimes,B=e.hiddenUpdates;for(a=y&~a;0<a;){var I=31-Mt(a),K=1<<I;x[I]=0,M[I]=-1;var P=B[I];if(P!==null)for(B[I]=null,I=0;I<P.length;I++){var H=P[I];H!==null&&(H.lane&=-536870913)}a&=~K}r!==0&&Nd(e,r,0),u!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=u&~(y&~t))}function Nd(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-Mt(t);e.entangledLanes|=t,e.entanglements[r]=e.entanglements[r]|1073741824|a&4194090}function Ld(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var r=31-Mt(a),s=1<<r;s&t|e[r]&t&&(e[r]|=t),a&=~s}}function Ps(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Hs(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function jd(){var e=$.p;return e!==0?e:(e=window.event,e===void 0?32:hv(e.type))}function Nb(e,t){var a=$.p;try{return $.p=e,t()}finally{$.p=a}}var Xn=Math.random().toString(36).slice(2),ft="__reactFiber$"+Xn,_t="__reactProps$"+Xn,Fa="__reactContainer$"+Xn,Gs="__reactEvents$"+Xn,Lb="__reactListeners$"+Xn,jb="__reactHandles$"+Xn,zd="__reactResources$"+Xn,co="__reactMarker$"+Xn;function Vs(e){delete e[ft],delete e[_t],delete e[Gs],delete e[Lb],delete e[jb]}function Wa(e){var t=e[ft];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Fa]||a[ft]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Wp(e);e!==null;){if(a=e[ft])return a;e=Wp(e)}return t}e=a,a=e.parentNode}return null}function Ja(e){if(e=e[ft]||e[Fa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function uo(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function er(e){var t=e[zd];return t||(t=e[zd]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function tt(e){e[co]=!0}var Ud=new Set,kd={};function Ea(e,t){tr(e,t),tr(e+"Capture",t)}function tr(e,t){for(kd[e]=t,e=0;e<t.length;e++)Ud.add(t[e])}var zb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Bd={},Pd={};function Ub(e){return He.call(Pd,e)?!0:He.call(Bd,e)?!1:zb.test(e)?Pd[e]=!0:(Bd[e]=!0,!1)}function Ol(e,t,a){if(Ub(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var r=t.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Dl(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function En(e,t,a,r){if(r===null)e.removeAttribute(a);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+r)}}var qs,Hd;function nr(e){if(qs===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);qs=t&&t[1]||"",Hd=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+qs+e+Hd}var Ys=!1;function $s(e,t){if(!e||Ys)return"";Ys=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var K=function(){throw Error()};if(Object.defineProperty(K.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(K,[])}catch(H){var P=H}Reflect.construct(e,[],K)}else{try{K.call()}catch(H){P=H}e.call(K.prototype)}}else{try{throw Error()}catch(H){P=H}(K=e())&&typeof K.catch=="function"&&K.catch(function(){})}}catch(H){if(H&&P&&typeof H.stack=="string")return[H.stack,P.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=r.DetermineComponentFrameRoot(),y=u[0],x=u[1];if(y&&x){var M=y.split(`
`),B=x.split(`
`);for(s=r=0;r<M.length&&!M[r].includes("DetermineComponentFrameRoot");)r++;for(;s<B.length&&!B[s].includes("DetermineComponentFrameRoot");)s++;if(r===M.length||s===B.length)for(r=M.length-1,s=B.length-1;1<=r&&0<=s&&M[r]!==B[s];)s--;for(;1<=r&&0<=s;r--,s--)if(M[r]!==B[s]){if(r!==1||s!==1)do if(r--,s--,0>s||M[r]!==B[s]){var I=`
`+M[r].replace(" at new "," at ");return e.displayName&&I.includes("<anonymous>")&&(I=I.replace("<anonymous>",e.displayName)),I}while(1<=r&&0<=s);break}}}finally{Ys=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?nr(a):""}function kb(e){switch(e.tag){case 26:case 27:case 5:return nr(e.type);case 16:return nr("Lazy");case 13:return nr("Suspense");case 19:return nr("SuspenseList");case 0:case 15:return $s(e.type,!1);case 11:return $s(e.type.render,!1);case 1:return $s(e.type,!0);case 31:return nr("Activity");default:return""}}function Gd(e){try{var t="";do t+=kb(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Ht(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Vd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Bb(e){var t=Vd(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,u=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(y){r=""+y,u.call(this,y)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return r},setValue:function(y){r=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Nl(e){e._valueTracker||(e._valueTracker=Bb(e))}function qd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),r="";return e&&(r=Vd(e)?e.checked?"true":"false":e.value),e=r,e!==a?(t.setValue(e),!0):!1}function Ll(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Pb=/[\n"\\]/g;function Gt(e){return e.replace(Pb,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Is(e,t,a,r,s,u,y,x){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),t!=null?y==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Ht(t)):e.value!==""+Ht(t)&&(e.value=""+Ht(t)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),t!=null?Xs(e,y,Ht(t)):a!=null?Xs(e,y,Ht(a)):r!=null&&e.removeAttribute("value"),s==null&&u!=null&&(e.defaultChecked=!!u),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?e.name=""+Ht(x):e.removeAttribute("name")}function Yd(e,t,a,r,s,u,y,x){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;a=a!=null?""+Ht(a):"",t=t!=null?""+Ht(t):a,x||t===e.value||(e.value=t),e.defaultValue=t}r=r??s,r=typeof r!="function"&&typeof r!="symbol"&&!!r,e.checked=x?e.checked:!!r,e.defaultChecked=!!r,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function Xs(e,t,a){t==="number"&&Ll(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function ar(e,t,a,r){if(e=e.options,t){t={};for(var s=0;s<a.length;s++)t["$"+a[s]]=!0;for(a=0;a<e.length;a++)s=t.hasOwnProperty("$"+e[a].value),e[a].selected!==s&&(e[a].selected=s),s&&r&&(e[a].defaultSelected=!0)}else{for(a=""+Ht(a),t=null,s=0;s<e.length;s++){if(e[s].value===a){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function $d(e,t,a){if(t!=null&&(t=""+Ht(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Ht(a):""}function Id(e,t,a,r){if(t==null){if(r!=null){if(a!=null)throw Error(i(92));if(de(r)){if(1<r.length)throw Error(i(93));r=r[0]}a=r}a==null&&(a=""),t=a}a=Ht(t),e.defaultValue=a,r=e.textContent,r===a&&r!==""&&r!==null&&(e.value=r)}function rr(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Hb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Xd(e,t,a){var r=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?r?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":r?e.setProperty(t,a):typeof a!="number"||a===0||Hb.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Kd(e,t,a){if(t!=null&&typeof t!="object")throw Error(i(62));if(e=e.style,a!=null){for(var r in a)!a.hasOwnProperty(r)||t!=null&&t.hasOwnProperty(r)||(r.indexOf("--")===0?e.setProperty(r,""):r==="float"?e.cssFloat="":e[r]="");for(var s in t)r=t[s],t.hasOwnProperty(s)&&a[s]!==r&&Xd(e,s,r)}else for(var u in t)t.hasOwnProperty(u)&&Xd(e,u,t[u])}function Ks(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Gb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Vb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function jl(e){return Vb.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Qs=null;function Zs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var or=null,lr=null;function Qd(e){var t=Ja(e);if(t&&(e=t.stateNode)){var a=e[_t]||null;e:switch(e=t.stateNode,t.type){case"input":if(Is(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Gt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var r=a[t];if(r!==e&&r.form===e.form){var s=r[_t]||null;if(!s)throw Error(i(90));Is(r,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<a.length;t++)r=a[t],r.form===e.form&&qd(r)}break e;case"textarea":$d(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&ar(e,!!a.multiple,t,!1)}}}var Fs=!1;function Zd(e,t,a){if(Fs)return e(t,a);Fs=!0;try{var r=e(t);return r}finally{if(Fs=!1,(or!==null||lr!==null)&&(bi(),or&&(t=or,e=lr,lr=or=null,Qd(t),e)))for(t=0;t<e.length;t++)Qd(e[t])}}function fo(e,t){var a=e.stateNode;if(a===null)return null;var r=a[_t]||null;if(r===null)return null;a=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(i(231,t,typeof a));return a}var Rn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ws=!1;if(Rn)try{var ho={};Object.defineProperty(ho,"passive",{get:function(){Ws=!0}}),window.addEventListener("test",ho,ho),window.removeEventListener("test",ho,ho)}catch{Ws=!1}var Kn=null,Js=null,zl=null;function Fd(){if(zl)return zl;var e,t=Js,a=t.length,r,s="value"in Kn?Kn.value:Kn.textContent,u=s.length;for(e=0;e<a&&t[e]===s[e];e++);var y=a-e;for(r=1;r<=y&&t[a-r]===s[u-r];r++);return zl=s.slice(e,1<r?1-r:void 0)}function Ul(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function kl(){return!0}function Wd(){return!1}function wt(e){function t(a,r,s,u,y){this._reactName=a,this._targetInst=s,this.type=r,this.nativeEvent=u,this.target=y,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(a=e[x],this[x]=a?a(u):u[x]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?kl:Wd,this.isPropagationStopped=Wd,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=kl)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=kl)},persist:function(){},isPersistent:kl}),t}var Ra={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bl=wt(Ra),mo=v({},Ra,{view:0,detail:0}),qb=wt(mo),ec,tc,po,Pl=v({},mo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ac,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==po&&(po&&e.type==="mousemove"?(ec=e.screenX-po.screenX,tc=e.screenY-po.screenY):tc=ec=0,po=e),ec)},movementY:function(e){return"movementY"in e?e.movementY:tc}}),Jd=wt(Pl),Yb=v({},Pl,{dataTransfer:0}),$b=wt(Yb),Ib=v({},mo,{relatedTarget:0}),nc=wt(Ib),Xb=v({},Ra,{animationName:0,elapsedTime:0,pseudoElement:0}),Kb=wt(Xb),Qb=v({},Ra,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Zb=wt(Qb),Fb=v({},Ra,{data:0}),eh=wt(Fb),Wb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},eS={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function tS(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=eS[e])?!!t[e]:!1}function ac(){return tS}var nS=v({},mo,{key:function(e){if(e.key){var t=Wb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ul(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jb[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ac,charCode:function(e){return e.type==="keypress"?Ul(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ul(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),aS=wt(nS),rS=v({},Pl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),th=wt(rS),oS=v({},mo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ac}),lS=wt(oS),iS=v({},Ra,{propertyName:0,elapsedTime:0,pseudoElement:0}),sS=wt(iS),cS=v({},Pl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),uS=wt(cS),fS=v({},Ra,{newState:0,oldState:0}),dS=wt(fS),hS=[9,13,27,32],rc=Rn&&"CompositionEvent"in window,vo=null;Rn&&"documentMode"in document&&(vo=document.documentMode);var mS=Rn&&"TextEvent"in window&&!vo,nh=Rn&&(!rc||vo&&8<vo&&11>=vo),ah=" ",rh=!1;function oh(e,t){switch(e){case"keyup":return hS.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function lh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ir=!1;function pS(e,t){switch(e){case"compositionend":return lh(t);case"keypress":return t.which!==32?null:(rh=!0,ah);case"textInput":return e=t.data,e===ah&&rh?null:e;default:return null}}function vS(e,t){if(ir)return e==="compositionend"||!rc&&oh(e,t)?(e=Fd(),zl=Js=Kn=null,ir=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nh&&t.locale!=="ko"?null:t.data;default:return null}}var gS={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ih(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!gS[e.type]:t==="textarea"}function sh(e,t,a,r){or?lr?lr.push(r):lr=[r]:or=r,t=Ri(t,"onChange"),0<t.length&&(a=new Bl("onChange","change",null,a,r),e.push({event:a,listeners:t}))}var go=null,yo=null;function yS(e){Vp(e,0)}function Hl(e){var t=uo(e);if(qd(t))return e}function ch(e,t){if(e==="change")return t}var uh=!1;if(Rn){var oc;if(Rn){var lc="oninput"in document;if(!lc){var fh=document.createElement("div");fh.setAttribute("oninput","return;"),lc=typeof fh.oninput=="function"}oc=lc}else oc=!1;uh=oc&&(!document.documentMode||9<document.documentMode)}function dh(){go&&(go.detachEvent("onpropertychange",hh),yo=go=null)}function hh(e){if(e.propertyName==="value"&&Hl(yo)){var t=[];sh(t,yo,e,Zs(e)),Zd(yS,t)}}function bS(e,t,a){e==="focusin"?(dh(),go=t,yo=a,go.attachEvent("onpropertychange",hh)):e==="focusout"&&dh()}function SS(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hl(yo)}function xS(e,t){if(e==="click")return Hl(t)}function _S(e,t){if(e==="input"||e==="change")return Hl(t)}function wS(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:wS;function bo(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),r=Object.keys(t);if(a.length!==r.length)return!1;for(r=0;r<a.length;r++){var s=a[r];if(!He.call(t,s)||!Tt(e[s],t[s]))return!1}return!0}function mh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ph(e,t){var a=mh(e);e=0;for(var r;a;){if(a.nodeType===3){if(r=e+a.textContent.length,e<=t&&r>=t)return{node:a,offset:t-e};e=r}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=mh(a)}}function vh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gh(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ll(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ll(e.document)}return t}function ic(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ES=Rn&&"documentMode"in document&&11>=document.documentMode,sr=null,sc=null,So=null,cc=!1;function yh(e,t,a){var r=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;cc||sr==null||sr!==Ll(r)||(r=sr,"selectionStart"in r&&ic(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),So&&bo(So,r)||(So=r,r=Ri(sc,"onSelect"),0<r.length&&(t=new Bl("onSelect","select",null,t,a),e.push({event:t,listeners:r}),t.target=sr)))}function Ca(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var cr={animationend:Ca("Animation","AnimationEnd"),animationiteration:Ca("Animation","AnimationIteration"),animationstart:Ca("Animation","AnimationStart"),transitionrun:Ca("Transition","TransitionRun"),transitionstart:Ca("Transition","TransitionStart"),transitioncancel:Ca("Transition","TransitionCancel"),transitionend:Ca("Transition","TransitionEnd")},uc={},bh={};Rn&&(bh=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);function Aa(e){if(uc[e])return uc[e];if(!cr[e])return e;var t=cr[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in bh)return uc[e]=t[a];return e}var Sh=Aa("animationend"),xh=Aa("animationiteration"),_h=Aa("animationstart"),RS=Aa("transitionrun"),CS=Aa("transitionstart"),AS=Aa("transitioncancel"),wh=Aa("transitionend"),Eh=new Map,fc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");fc.push("scrollEnd");function en(e,t){Eh.set(e,t),Ea(t,[e])}var Rh=new WeakMap;function Vt(e,t){if(typeof e=="object"&&e!==null){var a=Rh.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Gd(t)},Rh.set(e,t),t)}return{value:e,source:t,stack:Gd(t)}}var qt=[],ur=0,dc=0;function Gl(){for(var e=ur,t=dc=ur=0;t<e;){var a=qt[t];qt[t++]=null;var r=qt[t];qt[t++]=null;var s=qt[t];qt[t++]=null;var u=qt[t];if(qt[t++]=null,r!==null&&s!==null){var y=r.pending;y===null?s.next=s:(s.next=y.next,y.next=s),r.pending=s}u!==0&&Ch(a,s,u)}}function Vl(e,t,a,r){qt[ur++]=e,qt[ur++]=t,qt[ur++]=a,qt[ur++]=r,dc|=r,e.lanes|=r,e=e.alternate,e!==null&&(e.lanes|=r)}function hc(e,t,a,r){return Vl(e,t,a,r),ql(e)}function fr(e,t){return Vl(e,null,null,t),ql(e)}function Ch(e,t,a){e.lanes|=a;var r=e.alternate;r!==null&&(r.lanes|=a);for(var s=!1,u=e.return;u!==null;)u.childLanes|=a,r=u.alternate,r!==null&&(r.childLanes|=a),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(s=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,s&&t!==null&&(s=31-Mt(a),e=u.hiddenUpdates,r=e[s],r===null?e[s]=[t]:r.push(t),t.lane=a|536870912),u):null}function ql(e){if(50<$o)throw $o=0,bu=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var dr={};function MS(e,t,a,r){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,a,r){return new MS(e,t,a,r)}function mc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cn(e,t){var a=e.alternate;return a===null?(a=Ot(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Ah(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Yl(e,t,a,r,s,u){var y=0;if(r=e,typeof e=="function")mc(e)&&(y=1);else if(typeof e=="string")y=Ox(e,a,ee.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ae:return e=Ot(31,a,t,s),e.elementType=ae,e.lanes=u,e;case R:return Ma(a.children,s,u,t);case E:y=8,s|=24;break;case C:return e=Ot(12,a,t,s|2),e.elementType=C,e.lanes=u,e;case G:return e=Ot(13,a,t,s),e.elementType=G,e.lanes=u,e;case W:return e=Ot(19,a,t,s),e.elementType=W,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case T:case L:y=10;break e;case z:y=9;break e;case N:y=11;break e;case V:y=14;break e;case q:y=16,r=null;break e}y=29,a=Error(i(130,e===null?"null":typeof e,"")),r=null}return t=Ot(y,a,t,s),t.elementType=e,t.type=r,t.lanes=u,t}function Ma(e,t,a,r){return e=Ot(7,e,r,t),e.lanes=a,e}function pc(e,t,a){return e=Ot(6,e,null,t),e.lanes=a,e}function vc(e,t,a){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var hr=[],mr=0,$l=null,Il=0,Yt=[],$t=0,Ta=null,An=1,Mn="";function Oa(e,t){hr[mr++]=Il,hr[mr++]=$l,$l=e,Il=t}function Mh(e,t,a){Yt[$t++]=An,Yt[$t++]=Mn,Yt[$t++]=Ta,Ta=e;var r=An;e=Mn;var s=32-Mt(r)-1;r&=~(1<<s),a+=1;var u=32-Mt(t)+s;if(30<u){var y=s-s%5;u=(r&(1<<y)-1).toString(32),r>>=y,s-=y,An=1<<32-Mt(t)+s|a<<s|r,Mn=u+e}else An=1<<u|a<<s|r,Mn=e}function gc(e){e.return!==null&&(Oa(e,1),Mh(e,1,0))}function yc(e){for(;e===$l;)$l=hr[--mr],hr[mr]=null,Il=hr[--mr],hr[mr]=null;for(;e===Ta;)Ta=Yt[--$t],Yt[$t]=null,Mn=Yt[--$t],Yt[$t]=null,An=Yt[--$t],Yt[$t]=null}var pt=null,$e=null,Oe=!1,Da=null,un=!1,bc=Error(i(519));function Na(e){var t=Error(i(418,""));throw wo(Vt(t,e)),bc}function Th(e){var t=e.stateNode,a=e.type,r=e.memoizedProps;switch(t[ft]=e,t[_t]=r,a){case"dialog":Re("cancel",t),Re("close",t);break;case"iframe":case"object":case"embed":Re("load",t);break;case"video":case"audio":for(a=0;a<Xo.length;a++)Re(Xo[a],t);break;case"source":Re("error",t);break;case"img":case"image":case"link":Re("error",t),Re("load",t);break;case"details":Re("toggle",t);break;case"input":Re("invalid",t),Yd(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Nl(t);break;case"select":Re("invalid",t);break;case"textarea":Re("invalid",t),Id(t,r.value,r.defaultValue,r.children),Nl(t)}a=r.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||r.suppressHydrationWarning===!0||Ip(t.textContent,a)?(r.popover!=null&&(Re("beforetoggle",t),Re("toggle",t)),r.onScroll!=null&&Re("scroll",t),r.onScrollEnd!=null&&Re("scrollend",t),r.onClick!=null&&(t.onclick=Ci),t=!0):t=!1,t||Na(e)}function Oh(e){for(pt=e.return;pt;)switch(pt.tag){case 5:case 13:un=!1;return;case 27:case 3:un=!0;return;default:pt=pt.return}}function xo(e){if(e!==pt)return!1;if(!Oe)return Oh(e),Oe=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||zu(e.type,e.memoizedProps)),a=!a),a&&$e&&Na(e),Oh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){$e=nn(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}$e=null}}else t===27?(t=$e,ua(e.type)?(e=Pu,Pu=null,$e=e):$e=t):$e=pt?nn(e.stateNode.nextSibling):null;return!0}function _o(){$e=pt=null,Oe=!1}function Dh(){var e=Da;return e!==null&&(Ct===null?Ct=e:Ct.push.apply(Ct,e),Da=null),e}function wo(e){Da===null?Da=[e]:Da.push(e)}var Sc=Y(null),La=null,Tn=null;function Qn(e,t,a){F(Sc,t._currentValue),t._currentValue=a}function On(e){e._currentValue=Sc.current,J(Sc)}function xc(e,t,a){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===a)break;e=e.return}}function _c(e,t,a,r){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var u=s.dependencies;if(u!==null){var y=s.child;u=u.firstContext;e:for(;u!==null;){var x=u;u=s;for(var M=0;M<t.length;M++)if(x.context===t[M]){u.lanes|=a,x=u.alternate,x!==null&&(x.lanes|=a),xc(u.return,a,e),r||(y=null);break e}u=x.next}}else if(s.tag===18){if(y=s.return,y===null)throw Error(i(341));y.lanes|=a,u=y.alternate,u!==null&&(u.lanes|=a),xc(y,a,e),y=null}else y=s.child;if(y!==null)y.return=s;else for(y=s;y!==null;){if(y===e){y=null;break}if(s=y.sibling,s!==null){s.return=y.return,y=s;break}y=y.return}s=y}}function Eo(e,t,a,r){e=null;for(var s=t,u=!1;s!==null;){if(!u){if((s.flags&524288)!==0)u=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var y=s.alternate;if(y===null)throw Error(i(387));if(y=y.memoizedProps,y!==null){var x=s.type;Tt(s.pendingProps.value,y.value)||(e!==null?e.push(x):e=[x])}}else if(s===fe.current){if(y=s.alternate,y===null)throw Error(i(387));y.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Jo):e=[Jo])}s=s.return}e!==null&&_c(t,e,a,r),t.flags|=262144}function Xl(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ja(e){La=e,Tn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dt(e){return Nh(La,e)}function Kl(e,t){return La===null&&ja(e),Nh(e,t)}function Nh(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Tn===null){if(e===null)throw Error(i(308));Tn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Tn=Tn.next=t;return a}var TS=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,r){e.push(r)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},OS=n.unstable_scheduleCallback,DS=n.unstable_NormalPriority,Fe={$$typeof:L,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function wc(){return{controller:new TS,data:new Map,refCount:0}}function Ro(e){e.refCount--,e.refCount===0&&OS(DS,function(){e.controller.abort()})}var Co=null,Ec=0,pr=0,vr=null;function NS(e,t){if(Co===null){var a=Co=[];Ec=0,pr=Cu(),vr={status:"pending",value:void 0,then:function(r){a.push(r)}}}return Ec++,t.then(Lh,Lh),t}function Lh(){if(--Ec===0&&Co!==null){vr!==null&&(vr.status="fulfilled");var e=Co;Co=null,pr=0,vr=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function LS(e,t){var a=[],r={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var s=0;s<a.length;s++)(0,a[s])(t)},function(s){for(r.status="rejected",r.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),r}var jh=O.S;O.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&NS(e,t),jh!==null&&jh(e,t)};var za=Y(null);function Rc(){var e=za.current;return e!==null?e:Pe.pooledCache}function Ql(e,t){t===null?F(za,za.current):F(za,t.pool)}function zh(){var e=Rc();return e===null?null:{parent:Fe._currentValue,pool:e}}var Ao=Error(i(460)),Uh=Error(i(474)),Zl=Error(i(542)),Cc={then:function(){}};function kh(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Fl(){}function Bh(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Fl,Fl),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hh(e),e;default:if(typeof t.status=="string")t.then(Fl,Fl);else{if(e=Pe,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status="pending",e.then(function(r){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=r}},function(r){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=r}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hh(e),e}throw Mo=t,Ao}}var Mo=null;function Ph(){if(Mo===null)throw Error(i(459));var e=Mo;return Mo=null,e}function Hh(e){if(e===Ao||e===Zl)throw Error(i(483))}var Zn=!1;function Ac(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Mc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Fn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Wn(e,t,a){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(Ne&2)!==0){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,t=ql(e),Ch(e,null,a),t}return Vl(e,r,t,a),ql(e)}function To(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var r=t.lanes;r&=e.pendingLanes,a|=r,t.lanes=a,Ld(e,a)}}function Tc(e,t){var a=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,a===r)){var s=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var y={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?s=u=y:u=u.next=y,a=a.next}while(a!==null);u===null?s=u=t:u=u.next=t}else s=u=t;a={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:u,shared:r.shared,callbacks:r.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Oc=!1;function Oo(){if(Oc){var e=vr;if(e!==null)throw e}}function Do(e,t,a,r){Oc=!1;var s=e.updateQueue;Zn=!1;var u=s.firstBaseUpdate,y=s.lastBaseUpdate,x=s.shared.pending;if(x!==null){s.shared.pending=null;var M=x,B=M.next;M.next=null,y===null?u=B:y.next=B,y=M;var I=e.alternate;I!==null&&(I=I.updateQueue,x=I.lastBaseUpdate,x!==y&&(x===null?I.firstBaseUpdate=B:x.next=B,I.lastBaseUpdate=M))}if(u!==null){var K=s.baseState;y=0,I=B=M=null,x=u;do{var P=x.lane&-536870913,H=P!==x.lane;if(H?(Ae&P)===P:(r&P)===P){P!==0&&P===pr&&(Oc=!0),I!==null&&(I=I.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});e:{var ye=e,pe=x;P=t;var ke=a;switch(pe.tag){case 1:if(ye=pe.payload,typeof ye=="function"){K=ye.call(ke,K,P);break e}K=ye;break e;case 3:ye.flags=ye.flags&-65537|128;case 0:if(ye=pe.payload,P=typeof ye=="function"?ye.call(ke,K,P):ye,P==null)break e;K=v({},K,P);break e;case 2:Zn=!0}}P=x.callback,P!==null&&(e.flags|=64,H&&(e.flags|=8192),H=s.callbacks,H===null?s.callbacks=[P]:H.push(P))}else H={lane:P,tag:x.tag,payload:x.payload,callback:x.callback,next:null},I===null?(B=I=H,M=K):I=I.next=H,y|=P;if(x=x.next,x===null){if(x=s.shared.pending,x===null)break;H=x,x=H.next,H.next=null,s.lastBaseUpdate=H,s.shared.pending=null}}while(!0);I===null&&(M=K),s.baseState=M,s.firstBaseUpdate=B,s.lastBaseUpdate=I,u===null&&(s.shared.lanes=0),la|=y,e.lanes=y,e.memoizedState=K}}function Gh(e,t){if(typeof e!="function")throw Error(i(191,e));e.call(t)}function Vh(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Gh(a[e],t)}var gr=Y(null),Wl=Y(0);function qh(e,t){e=kn,F(Wl,e),F(gr,t),kn=e|t.baseLanes}function Dc(){F(Wl,kn),F(gr,gr.current)}function Nc(){kn=Wl.current,J(gr),J(Wl)}var Jn=0,xe=null,ze=null,Qe=null,Jl=!1,yr=!1,Ua=!1,ei=0,No=0,br=null,jS=0;function Xe(){throw Error(i(321))}function Lc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Tt(e[a],t[a]))return!1;return!0}function jc(e,t,a,r,s,u){return Jn=u,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=e===null||e.memoizedState===null?Cm:Am,Ua=!1,u=a(r,s),Ua=!1,yr&&(u=$h(t,a,r,s)),Yh(e),u}function Yh(e){O.H=li;var t=ze!==null&&ze.next!==null;if(Jn=0,Qe=ze=xe=null,Jl=!1,No=0,br=null,t)throw Error(i(300));e===null||nt||(e=e.dependencies,e!==null&&Xl(e)&&(nt=!0))}function $h(e,t,a,r){xe=e;var s=0;do{if(yr&&(br=null),No=0,yr=!1,25<=s)throw Error(i(301));if(s+=1,Qe=ze=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}O.H=GS,u=t(a,r)}while(yr);return u}function zS(){var e=O.H,t=e.useState()[0];return t=typeof t.then=="function"?Lo(t):t,e=e.useState()[0],(ze!==null?ze.memoizedState:null)!==e&&(xe.flags|=1024),t}function zc(){var e=ei!==0;return ei=0,e}function Uc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function kc(e){if(Jl){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Jl=!1}Jn=0,Qe=ze=xe=null,yr=!1,No=ei=0,br=null}function Et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Qe===null?xe.memoizedState=Qe=e:Qe=Qe.next=e,Qe}function Ze(){if(ze===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=ze.next;var t=Qe===null?xe.memoizedState:Qe.next;if(t!==null)Qe=t,ze=e;else{if(e===null)throw xe.alternate===null?Error(i(467)):Error(i(310));ze=e,e={memoizedState:ze.memoizedState,baseState:ze.baseState,baseQueue:ze.baseQueue,queue:ze.queue,next:null},Qe===null?xe.memoizedState=Qe=e:Qe=Qe.next=e}return Qe}function Bc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Lo(e){var t=No;return No+=1,br===null&&(br=[]),e=Bh(br,e,t),t=xe,(Qe===null?t.memoizedState:Qe.next)===null&&(t=t.alternate,O.H=t===null||t.memoizedState===null?Cm:Am),e}function ti(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Lo(e);if(e.$$typeof===L)return dt(e)}throw Error(i(438,String(e)))}function Pc(e){var t=null,a=xe.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var r=xe.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(t={data:r.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Bc(),xe.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),r=0;r<e;r++)a[r]=Z;return t.index++,a}function Dn(e,t){return typeof t=="function"?t(e):t}function ni(e){var t=Ze();return Hc(t,ze,e)}function Hc(e,t,a){var r=e.queue;if(r===null)throw Error(i(311));r.lastRenderedReducer=a;var s=e.baseQueue,u=r.pending;if(u!==null){if(s!==null){var y=s.next;s.next=u.next,u.next=y}t.baseQueue=s=u,r.pending=null}if(u=e.baseState,s===null)e.memoizedState=u;else{t=s.next;var x=y=null,M=null,B=t,I=!1;do{var K=B.lane&-536870913;if(K!==B.lane?(Ae&K)===K:(Jn&K)===K){var P=B.revertLane;if(P===0)M!==null&&(M=M.next={lane:0,revertLane:0,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null}),K===pr&&(I=!0);else if((Jn&P)===P){B=B.next,P===pr&&(I=!0);continue}else K={lane:0,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},M===null?(x=M=K,y=u):M=M.next=K,xe.lanes|=P,la|=P;K=B.action,Ua&&a(u,K),u=B.hasEagerState?B.eagerState:a(u,K)}else P={lane:K,revertLane:B.revertLane,action:B.action,hasEagerState:B.hasEagerState,eagerState:B.eagerState,next:null},M===null?(x=M=P,y=u):M=M.next=P,xe.lanes|=K,la|=K;B=B.next}while(B!==null&&B!==t);if(M===null?y=u:M.next=x,!Tt(u,e.memoizedState)&&(nt=!0,I&&(a=vr,a!==null)))throw a;e.memoizedState=u,e.baseState=y,e.baseQueue=M,r.lastRenderedState=u}return s===null&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Gc(e){var t=Ze(),a=t.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var r=a.dispatch,s=a.pending,u=t.memoizedState;if(s!==null){a.pending=null;var y=s=s.next;do u=e(u,y.action),y=y.next;while(y!==s);Tt(u,t.memoizedState)||(nt=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),a.lastRenderedState=u}return[u,r]}function Ih(e,t,a){var r=xe,s=Ze(),u=Oe;if(u){if(a===void 0)throw Error(i(407));a=a()}else a=t();var y=!Tt((ze||s).memoizedState,a);y&&(s.memoizedState=a,nt=!0),s=s.queue;var x=Qh.bind(null,r,s,e);if(jo(2048,8,x,[e]),s.getSnapshot!==t||y||Qe!==null&&Qe.memoizedState.tag&1){if(r.flags|=2048,Sr(9,ai(),Kh.bind(null,r,s,a,t),null),Pe===null)throw Error(i(349));u||(Jn&124)!==0||Xh(r,t,a)}return a}function Xh(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=xe.updateQueue,t===null?(t=Bc(),xe.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Kh(e,t,a,r){t.value=a,t.getSnapshot=r,Zh(t)&&Fh(e)}function Qh(e,t,a){return a(function(){Zh(t)&&Fh(e)})}function Zh(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Tt(e,a)}catch{return!0}}function Fh(e){var t=fr(e,2);t!==null&&zt(t,e,2)}function Vc(e){var t=Et();if(typeof e=="function"){var a=e;if(e=a(),Ua){In(!0);try{a()}finally{In(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dn,lastRenderedState:e},t}function Wh(e,t,a,r){return e.baseState=a,Hc(e,ze,typeof r=="function"?r:Dn)}function US(e,t,a,r,s){if(oi(e))throw Error(i(485));if(e=t.action,e!==null){var u={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){u.listeners.push(y)}};O.T!==null?a(!0):u.isTransition=!1,r(u),a=t.pending,a===null?(u.next=t.pending=u,Jh(t,u)):(u.next=a.next,t.pending=a.next=u)}}function Jh(e,t){var a=t.action,r=t.payload,s=e.state;if(t.isTransition){var u=O.T,y={};O.T=y;try{var x=a(s,r),M=O.S;M!==null&&M(y,x),em(e,t,x)}catch(B){qc(e,t,B)}finally{O.T=u}}else try{u=a(s,r),em(e,t,u)}catch(B){qc(e,t,B)}}function em(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(r){tm(e,t,r)},function(r){return qc(e,t,r)}):tm(e,t,a)}function tm(e,t,a){t.status="fulfilled",t.value=a,nm(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Jh(e,a)))}function qc(e,t,a){var r=e.pending;if(e.pending=null,r!==null){r=r.next;do t.status="rejected",t.reason=a,nm(t),t=t.next;while(t!==r)}e.action=null}function nm(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function am(e,t){return t}function rm(e,t){if(Oe){var a=Pe.formState;if(a!==null){e:{var r=xe;if(Oe){if($e){t:{for(var s=$e,u=un;s.nodeType!==8;){if(!u){s=null;break t}if(s=nn(s.nextSibling),s===null){s=null;break t}}u=s.data,s=u==="F!"||u==="F"?s:null}if(s){$e=nn(s.nextSibling),r=s.data==="F!";break e}}Na(r)}r=!1}r&&(t=a[0])}}return a=Et(),a.memoizedState=a.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:am,lastRenderedState:t},a.queue=r,a=wm.bind(null,xe,r),r.dispatch=a,r=Vc(!1),u=Kc.bind(null,xe,!1,r.queue),r=Et(),s={state:t,dispatch:null,action:e,pending:null},r.queue=s,a=US.bind(null,xe,s,u,a),s.dispatch=a,r.memoizedState=e,[t,a,!1]}function om(e){var t=Ze();return lm(t,ze,e)}function lm(e,t,a){if(t=Hc(e,t,am)[0],e=ni(Dn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var r=Lo(t)}catch(y){throw y===Ao?Zl:y}else r=t;t=Ze();var s=t.queue,u=s.dispatch;return a!==t.memoizedState&&(xe.flags|=2048,Sr(9,ai(),kS.bind(null,s,a),null)),[r,u,e]}function kS(e,t){e.action=t}function im(e){var t=Ze(),a=ze;if(a!==null)return lm(t,a,e);Ze(),t=t.memoizedState,a=Ze();var r=a.queue.dispatch;return a.memoizedState=e,[t,r,!1]}function Sr(e,t,a,r){return e={tag:e,create:a,deps:r,inst:t,next:null},t=xe.updateQueue,t===null&&(t=Bc(),xe.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(r=a.next,a.next=e,e.next=r,t.lastEffect=e),e}function ai(){return{destroy:void 0,resource:void 0}}function sm(){return Ze().memoizedState}function ri(e,t,a,r){var s=Et();r=r===void 0?null:r,xe.flags|=e,s.memoizedState=Sr(1|t,ai(),a,r)}function jo(e,t,a,r){var s=Ze();r=r===void 0?null:r;var u=s.memoizedState.inst;ze!==null&&r!==null&&Lc(r,ze.memoizedState.deps)?s.memoizedState=Sr(t,u,a,r):(xe.flags|=e,s.memoizedState=Sr(1|t,u,a,r))}function cm(e,t){ri(8390656,8,e,t)}function um(e,t){jo(2048,8,e,t)}function fm(e,t){return jo(4,2,e,t)}function dm(e,t){return jo(4,4,e,t)}function hm(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mm(e,t,a){a=a!=null?a.concat([e]):null,jo(4,4,hm.bind(null,t,e),a)}function Yc(){}function pm(e,t){var a=Ze();t=t===void 0?null:t;var r=a.memoizedState;return t!==null&&Lc(t,r[1])?r[0]:(a.memoizedState=[e,t],e)}function vm(e,t){var a=Ze();t=t===void 0?null:t;var r=a.memoizedState;if(t!==null&&Lc(t,r[1]))return r[0];if(r=e(),Ua){In(!0);try{e()}finally{In(!1)}}return a.memoizedState=[r,t],r}function $c(e,t,a){return a===void 0||(Jn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=bp(),xe.lanes|=e,la|=e,a)}function gm(e,t,a,r){return Tt(a,t)?a:gr.current!==null?(e=$c(e,a,r),Tt(e,t)||(nt=!0),e):(Jn&42)===0?(nt=!0,e.memoizedState=a):(e=bp(),xe.lanes|=e,la|=e,t)}function ym(e,t,a,r,s){var u=$.p;$.p=u!==0&&8>u?u:8;var y=O.T,x={};O.T=x,Kc(e,!1,t,a);try{var M=s(),B=O.S;if(B!==null&&B(x,M),M!==null&&typeof M=="object"&&typeof M.then=="function"){var I=LS(M,r);zo(e,t,I,jt(e))}else zo(e,t,r,jt(e))}catch(K){zo(e,t,{then:function(){},status:"rejected",reason:K},jt())}finally{$.p=u,O.T=y}}function BS(){}function Ic(e,t,a,r){if(e.tag!==5)throw Error(i(476));var s=bm(e).queue;ym(e,s,t,U,a===null?BS:function(){return Sm(e),a(r)})}function bm(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dn,lastRenderedState:U},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dn,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Sm(e){var t=bm(e).next.queue;zo(e,t,{},jt())}function Xc(){return dt(Jo)}function xm(){return Ze().memoizedState}function _m(){return Ze().memoizedState}function PS(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=jt();e=Fn(a);var r=Wn(t,e,a);r!==null&&(zt(r,t,a),To(r,t,a)),t={cache:wc()},e.payload=t;return}t=t.return}}function HS(e,t,a){var r=jt();a={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},oi(e)?Em(t,a):(a=hc(e,t,a,r),a!==null&&(zt(a,e,r),Rm(a,t,r)))}function wm(e,t,a){var r=jt();zo(e,t,a,r)}function zo(e,t,a,r){var s={lane:r,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(oi(e))Em(t,s);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var y=t.lastRenderedState,x=u(y,a);if(s.hasEagerState=!0,s.eagerState=x,Tt(x,y))return Vl(e,t,s,0),Pe===null&&Gl(),!1}catch{}finally{}if(a=hc(e,t,s,r),a!==null)return zt(a,e,r),Rm(a,t,r),!0}return!1}function Kc(e,t,a,r){if(r={lane:2,revertLane:Cu(),action:r,hasEagerState:!1,eagerState:null,next:null},oi(e)){if(t)throw Error(i(479))}else t=hc(e,a,r,2),t!==null&&zt(t,e,2)}function oi(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function Em(e,t){yr=Jl=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Rm(e,t,a){if((a&4194048)!==0){var r=t.lanes;r&=e.pendingLanes,a|=r,t.lanes=a,Ld(e,a)}}var li={readContext:dt,use:ti,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useLayoutEffect:Xe,useInsertionEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useSyncExternalStore:Xe,useId:Xe,useHostTransitionStatus:Xe,useFormState:Xe,useActionState:Xe,useOptimistic:Xe,useMemoCache:Xe,useCacheRefresh:Xe},Cm={readContext:dt,use:ti,useCallback:function(e,t){return Et().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:cm,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,ri(4194308,4,hm.bind(null,t,e),a)},useLayoutEffect:function(e,t){return ri(4194308,4,e,t)},useInsertionEffect:function(e,t){ri(4,2,e,t)},useMemo:function(e,t){var a=Et();t=t===void 0?null:t;var r=e();if(Ua){In(!0);try{e()}finally{In(!1)}}return a.memoizedState=[r,t],r},useReducer:function(e,t,a){var r=Et();if(a!==void 0){var s=a(t);if(Ua){In(!0);try{a(t)}finally{In(!1)}}}else s=t;return r.memoizedState=r.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},r.queue=e,e=e.dispatch=HS.bind(null,xe,e),[r.memoizedState,e]},useRef:function(e){var t=Et();return e={current:e},t.memoizedState=e},useState:function(e){e=Vc(e);var t=e.queue,a=wm.bind(null,xe,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Yc,useDeferredValue:function(e,t){var a=Et();return $c(a,e,t)},useTransition:function(){var e=Vc(!1);return e=ym.bind(null,xe,e.queue,!0,!1),Et().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var r=xe,s=Et();if(Oe){if(a===void 0)throw Error(i(407));a=a()}else{if(a=t(),Pe===null)throw Error(i(349));(Ae&124)!==0||Xh(r,t,a)}s.memoizedState=a;var u={value:a,getSnapshot:t};return s.queue=u,cm(Qh.bind(null,r,u,e),[e]),r.flags|=2048,Sr(9,ai(),Kh.bind(null,r,u,a,t),null),a},useId:function(){var e=Et(),t=Pe.identifierPrefix;if(Oe){var a=Mn,r=An;a=(r&~(1<<32-Mt(r)-1)).toString(32)+a,t="«"+t+"R"+a,a=ei++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=jS++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Xc,useFormState:rm,useActionState:rm,useOptimistic:function(e){var t=Et();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Kc.bind(null,xe,!0,a),a.dispatch=t,[e,t]},useMemoCache:Pc,useCacheRefresh:function(){return Et().memoizedState=PS.bind(null,xe)}},Am={readContext:dt,use:ti,useCallback:pm,useContext:dt,useEffect:um,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:vm,useReducer:ni,useRef:sm,useState:function(){return ni(Dn)},useDebugValue:Yc,useDeferredValue:function(e,t){var a=Ze();return gm(a,ze.memoizedState,e,t)},useTransition:function(){var e=ni(Dn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Lo(e),t]},useSyncExternalStore:Ih,useId:xm,useHostTransitionStatus:Xc,useFormState:om,useActionState:om,useOptimistic:function(e,t){var a=Ze();return Wh(a,ze,e,t)},useMemoCache:Pc,useCacheRefresh:_m},GS={readContext:dt,use:ti,useCallback:pm,useContext:dt,useEffect:um,useImperativeHandle:mm,useInsertionEffect:fm,useLayoutEffect:dm,useMemo:vm,useReducer:Gc,useRef:sm,useState:function(){return Gc(Dn)},useDebugValue:Yc,useDeferredValue:function(e,t){var a=Ze();return ze===null?$c(a,e,t):gm(a,ze.memoizedState,e,t)},useTransition:function(){var e=Gc(Dn)[0],t=Ze().memoizedState;return[typeof e=="boolean"?e:Lo(e),t]},useSyncExternalStore:Ih,useId:xm,useHostTransitionStatus:Xc,useFormState:im,useActionState:im,useOptimistic:function(e,t){var a=Ze();return ze!==null?Wh(a,ze,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Pc,useCacheRefresh:_m},xr=null,Uo=0;function ii(e){var t=Uo;return Uo+=1,xr===null&&(xr=[]),Bh(xr,e,t)}function ko(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function si(e,t){throw t.$$typeof===g?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Mm(e){var t=e._init;return t(e._payload)}function Tm(e){function t(j,D){if(e){var k=j.deletions;k===null?(j.deletions=[D],j.flags|=16):k.push(D)}}function a(j,D){if(!e)return null;for(;D!==null;)t(j,D),D=D.sibling;return null}function r(j){for(var D=new Map;j!==null;)j.key!==null?D.set(j.key,j):D.set(j.index,j),j=j.sibling;return D}function s(j,D){return j=Cn(j,D),j.index=0,j.sibling=null,j}function u(j,D,k){return j.index=k,e?(k=j.alternate,k!==null?(k=k.index,k<D?(j.flags|=67108866,D):k):(j.flags|=67108866,D)):(j.flags|=1048576,D)}function y(j){return e&&j.alternate===null&&(j.flags|=67108866),j}function x(j,D,k,X){return D===null||D.tag!==6?(D=pc(k,j.mode,X),D.return=j,D):(D=s(D,k),D.return=j,D)}function M(j,D,k,X){var ie=k.type;return ie===R?I(j,D,k.props.children,X,k.key):D!==null&&(D.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===q&&Mm(ie)===D.type)?(D=s(D,k.props),ko(D,k),D.return=j,D):(D=Yl(k.type,k.key,k.props,null,j.mode,X),ko(D,k),D.return=j,D)}function B(j,D,k,X){return D===null||D.tag!==4||D.stateNode.containerInfo!==k.containerInfo||D.stateNode.implementation!==k.implementation?(D=vc(k,j.mode,X),D.return=j,D):(D=s(D,k.children||[]),D.return=j,D)}function I(j,D,k,X,ie){return D===null||D.tag!==7?(D=Ma(k,j.mode,X,ie),D.return=j,D):(D=s(D,k),D.return=j,D)}function K(j,D,k){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return D=pc(""+D,j.mode,k),D.return=j,D;if(typeof D=="object"&&D!==null){switch(D.$$typeof){case b:return k=Yl(D.type,D.key,D.props,null,j.mode,k),ko(k,D),k.return=j,k;case w:return D=vc(D,j.mode,k),D.return=j,D;case q:var X=D._init;return D=X(D._payload),K(j,D,k)}if(de(D)||te(D))return D=Ma(D,j.mode,k,null),D.return=j,D;if(typeof D.then=="function")return K(j,ii(D),k);if(D.$$typeof===L)return K(j,Kl(j,D),k);si(j,D)}return null}function P(j,D,k,X){var ie=D!==null?D.key:null;if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return ie!==null?null:x(j,D,""+k,X);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case b:return k.key===ie?M(j,D,k,X):null;case w:return k.key===ie?B(j,D,k,X):null;case q:return ie=k._init,k=ie(k._payload),P(j,D,k,X)}if(de(k)||te(k))return ie!==null?null:I(j,D,k,X,null);if(typeof k.then=="function")return P(j,D,ii(k),X);if(k.$$typeof===L)return P(j,D,Kl(j,k),X);si(j,k)}return null}function H(j,D,k,X,ie){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return j=j.get(k)||null,x(D,j,""+X,ie);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case b:return j=j.get(X.key===null?k:X.key)||null,M(D,j,X,ie);case w:return j=j.get(X.key===null?k:X.key)||null,B(D,j,X,ie);case q:var we=X._init;return X=we(X._payload),H(j,D,k,X,ie)}if(de(X)||te(X))return j=j.get(k)||null,I(D,j,X,ie,null);if(typeof X.then=="function")return H(j,D,k,ii(X),ie);if(X.$$typeof===L)return H(j,D,k,Kl(D,X),ie);si(D,X)}return null}function ye(j,D,k,X){for(var ie=null,we=null,he=D,ve=D=0,rt=null;he!==null&&ve<k.length;ve++){he.index>ve?(rt=he,he=null):rt=he.sibling;var Me=P(j,he,k[ve],X);if(Me===null){he===null&&(he=rt);break}e&&he&&Me.alternate===null&&t(j,he),D=u(Me,D,ve),we===null?ie=Me:we.sibling=Me,we=Me,he=rt}if(ve===k.length)return a(j,he),Oe&&Oa(j,ve),ie;if(he===null){for(;ve<k.length;ve++)he=K(j,k[ve],X),he!==null&&(D=u(he,D,ve),we===null?ie=he:we.sibling=he,we=he);return Oe&&Oa(j,ve),ie}for(he=r(he);ve<k.length;ve++)rt=H(he,j,ve,k[ve],X),rt!==null&&(e&&rt.alternate!==null&&he.delete(rt.key===null?ve:rt.key),D=u(rt,D,ve),we===null?ie=rt:we.sibling=rt,we=rt);return e&&he.forEach(function(pa){return t(j,pa)}),Oe&&Oa(j,ve),ie}function pe(j,D,k,X){if(k==null)throw Error(i(151));for(var ie=null,we=null,he=D,ve=D=0,rt=null,Me=k.next();he!==null&&!Me.done;ve++,Me=k.next()){he.index>ve?(rt=he,he=null):rt=he.sibling;var pa=P(j,he,Me.value,X);if(pa===null){he===null&&(he=rt);break}e&&he&&pa.alternate===null&&t(j,he),D=u(pa,D,ve),we===null?ie=pa:we.sibling=pa,we=pa,he=rt}if(Me.done)return a(j,he),Oe&&Oa(j,ve),ie;if(he===null){for(;!Me.done;ve++,Me=k.next())Me=K(j,Me.value,X),Me!==null&&(D=u(Me,D,ve),we===null?ie=Me:we.sibling=Me,we=Me);return Oe&&Oa(j,ve),ie}for(he=r(he);!Me.done;ve++,Me=k.next())Me=H(he,j,ve,Me.value,X),Me!==null&&(e&&Me.alternate!==null&&he.delete(Me.key===null?ve:Me.key),D=u(Me,D,ve),we===null?ie=Me:we.sibling=Me,we=Me);return e&&he.forEach(function(Vx){return t(j,Vx)}),Oe&&Oa(j,ve),ie}function ke(j,D,k,X){if(typeof k=="object"&&k!==null&&k.type===R&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case b:e:{for(var ie=k.key;D!==null;){if(D.key===ie){if(ie=k.type,ie===R){if(D.tag===7){a(j,D.sibling),X=s(D,k.props.children),X.return=j,j=X;break e}}else if(D.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===q&&Mm(ie)===D.type){a(j,D.sibling),X=s(D,k.props),ko(X,k),X.return=j,j=X;break e}a(j,D);break}else t(j,D);D=D.sibling}k.type===R?(X=Ma(k.props.children,j.mode,X,k.key),X.return=j,j=X):(X=Yl(k.type,k.key,k.props,null,j.mode,X),ko(X,k),X.return=j,j=X)}return y(j);case w:e:{for(ie=k.key;D!==null;){if(D.key===ie)if(D.tag===4&&D.stateNode.containerInfo===k.containerInfo&&D.stateNode.implementation===k.implementation){a(j,D.sibling),X=s(D,k.children||[]),X.return=j,j=X;break e}else{a(j,D);break}else t(j,D);D=D.sibling}X=vc(k,j.mode,X),X.return=j,j=X}return y(j);case q:return ie=k._init,k=ie(k._payload),ke(j,D,k,X)}if(de(k))return ye(j,D,k,X);if(te(k)){if(ie=te(k),typeof ie!="function")throw Error(i(150));return k=ie.call(k),pe(j,D,k,X)}if(typeof k.then=="function")return ke(j,D,ii(k),X);if(k.$$typeof===L)return ke(j,D,Kl(j,k),X);si(j,k)}return typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint"?(k=""+k,D!==null&&D.tag===6?(a(j,D.sibling),X=s(D,k),X.return=j,j=X):(a(j,D),X=pc(k,j.mode,X),X.return=j,j=X),y(j)):a(j,D)}return function(j,D,k,X){try{Uo=0;var ie=ke(j,D,k,X);return xr=null,ie}catch(he){if(he===Ao||he===Zl)throw he;var we=Ot(29,he,null,j.mode);return we.lanes=X,we.return=j,we}finally{}}}var _r=Tm(!0),Om=Tm(!1),It=Y(null),fn=null;function ea(e){var t=e.alternate;F(We,We.current&1),F(It,e),fn===null&&(t===null||gr.current!==null||t.memoizedState!==null)&&(fn=e)}function Dm(e){if(e.tag===22){if(F(We,We.current),F(It,e),fn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(fn=e)}}else ta()}function ta(){F(We,We.current),F(It,It.current)}function Nn(e){J(It),fn===e&&(fn=null),J(We)}var We=Y(0);function ci(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Bu(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Qc(e,t,a,r){t=e.memoizedState,a=a(r,t),a=a==null?t:v({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Zc={enqueueSetState:function(e,t,a){e=e._reactInternals;var r=jt(),s=Fn(r);s.payload=t,a!=null&&(s.callback=a),t=Wn(e,s,r),t!==null&&(zt(t,e,r),To(t,e,r))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var r=jt(),s=Fn(r);s.tag=1,s.payload=t,a!=null&&(s.callback=a),t=Wn(e,s,r),t!==null&&(zt(t,e,r),To(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=jt(),r=Fn(a);r.tag=2,t!=null&&(r.callback=t),t=Wn(e,r,a),t!==null&&(zt(t,e,a),To(t,e,a))}};function Nm(e,t,a,r,s,u,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,u,y):t.prototype&&t.prototype.isPureReactComponent?!bo(a,r)||!bo(s,u):!0}function Lm(e,t,a,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,r),t.state!==e&&Zc.enqueueReplaceState(t,t.state,null)}function ka(e,t){var a=t;if("ref"in t){a={};for(var r in t)r!=="ref"&&(a[r]=t[r])}if(e=e.defaultProps){a===t&&(a=v({},a));for(var s in e)a[s]===void 0&&(a[s]=e[s])}return a}var ui=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function jm(e){ui(e)}function zm(e){console.error(e)}function Um(e){ui(e)}function fi(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(r){setTimeout(function(){throw r})}}function km(e,t,a){try{var r=e.onCaughtError;r(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Fc(e,t,a){return a=Fn(a),a.tag=3,a.payload={element:null},a.callback=function(){fi(e,t)},a}function Bm(e){return e=Fn(e),e.tag=3,e}function Pm(e,t,a,r){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var u=r.value;e.payload=function(){return s(u)},e.callback=function(){km(t,a,r)}}var y=a.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){km(t,a,r),typeof s!="function"&&(ia===null?ia=new Set([this]):ia.add(this));var x=r.stack;this.componentDidCatch(r.value,{componentStack:x!==null?x:""})})}function VS(e,t,a,r,s){if(a.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(t=a.alternate,t!==null&&Eo(t,a,s,!0),a=It.current,a!==null){switch(a.tag){case 13:return fn===null?xu():a.alternate===null&&Ie===0&&(Ie=3),a.flags&=-257,a.flags|=65536,a.lanes=s,r===Cc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([r]):t.add(r),wu(e,r,s)),!1;case 22:return a.flags|=65536,r===Cc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([r]):a.add(r)),wu(e,r,s)),!1}throw Error(i(435,a.tag))}return wu(e,r,s),xu(),!1}if(Oe)return t=It.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,r!==bc&&(e=Error(i(422),{cause:r}),wo(Vt(e,a)))):(r!==bc&&(t=Error(i(423),{cause:r}),wo(Vt(t,a))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,r=Vt(r,a),s=Fc(e.stateNode,r,s),Tc(e,s),Ie!==4&&(Ie=2)),!1;var u=Error(i(520),{cause:r});if(u=Vt(u,a),Yo===null?Yo=[u]:Yo.push(u),Ie!==4&&(Ie=2),t===null)return!0;r=Vt(r,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=s&-s,a.lanes|=e,e=Fc(a.stateNode,r,e),Tc(a,e),!1;case 1:if(t=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(ia===null||!ia.has(u))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Bm(s),Pm(s,e,a,r),Tc(a,s),!1}a=a.return}while(a!==null);return!1}var Hm=Error(i(461)),nt=!1;function lt(e,t,a,r){t.child=e===null?Om(t,null,a,r):_r(t,e.child,a,r)}function Gm(e,t,a,r,s){a=a.render;var u=t.ref;if("ref"in r){var y={};for(var x in r)x!=="ref"&&(y[x]=r[x])}else y=r;return ja(t),r=jc(e,t,a,y,u,s),x=zc(),e!==null&&!nt?(Uc(e,t,s),Ln(e,t,s)):(Oe&&x&&gc(t),t.flags|=1,lt(e,t,r,s),t.child)}function Vm(e,t,a,r,s){if(e===null){var u=a.type;return typeof u=="function"&&!mc(u)&&u.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=u,qm(e,t,u,r,s)):(e=Yl(a.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!ou(e,s)){var y=u.memoizedProps;if(a=a.compare,a=a!==null?a:bo,a(y,r)&&e.ref===t.ref)return Ln(e,t,s)}return t.flags|=1,e=Cn(u,r),e.ref=t.ref,e.return=t,t.child=e}function qm(e,t,a,r,s){if(e!==null){var u=e.memoizedProps;if(bo(u,r)&&e.ref===t.ref)if(nt=!1,t.pendingProps=r=u,ou(e,s))(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,Ln(e,t,s)}return Wc(e,t,a,r,s)}function Ym(e,t,a){var r=t.pendingProps,s=r.children,u=e!==null?e.memoizedState:null;if(r.mode==="hidden"){if((t.flags&128)!==0){if(r=u!==null?u.baseLanes|a:a,e!==null){for(s=t.child=e.child,u=0;s!==null;)u=u|s.lanes|s.childLanes,s=s.sibling;t.childLanes=u&~r}else t.childLanes=0,t.child=null;return $m(e,t,r,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ql(t,u!==null?u.cachePool:null),u!==null?qh(t,u):Dc(),Dm(t);else return t.lanes=t.childLanes=536870912,$m(e,t,u!==null?u.baseLanes|a:a,a)}else u!==null?(Ql(t,u.cachePool),qh(t,u),ta(),t.memoizedState=null):(e!==null&&Ql(t,null),Dc(),ta());return lt(e,t,s,a),t.child}function $m(e,t,a,r){var s=Rc();return s=s===null?null:{parent:Fe._currentValue,pool:s},t.memoizedState={baseLanes:a,cachePool:s},e!==null&&Ql(t,null),Dc(),Dm(t),e!==null&&Eo(e,t,r,!0),null}function di(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(i(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Wc(e,t,a,r,s){return ja(t),a=jc(e,t,a,r,void 0,s),r=zc(),e!==null&&!nt?(Uc(e,t,s),Ln(e,t,s)):(Oe&&r&&gc(t),t.flags|=1,lt(e,t,a,s),t.child)}function Im(e,t,a,r,s,u){return ja(t),t.updateQueue=null,a=$h(t,r,a,s),Yh(e),r=zc(),e!==null&&!nt?(Uc(e,t,u),Ln(e,t,u)):(Oe&&r&&gc(t),t.flags|=1,lt(e,t,a,u),t.child)}function Xm(e,t,a,r,s){if(ja(t),t.stateNode===null){var u=dr,y=a.contextType;typeof y=="object"&&y!==null&&(u=dt(y)),u=new a(r,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Zc,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=r,u.state=t.memoizedState,u.refs={},Ac(t),y=a.contextType,u.context=typeof y=="object"&&y!==null?dt(y):dr,u.state=t.memoizedState,y=a.getDerivedStateFromProps,typeof y=="function"&&(Qc(t,a,y,r),u.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(y=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),y!==u.state&&Zc.enqueueReplaceState(u,u.state,null),Do(t,r,u,s),Oo(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),r=!0}else if(e===null){u=t.stateNode;var x=t.memoizedProps,M=ka(a,x);u.props=M;var B=u.context,I=a.contextType;y=dr,typeof I=="object"&&I!==null&&(y=dt(I));var K=a.getDerivedStateFromProps;I=typeof K=="function"||typeof u.getSnapshotBeforeUpdate=="function",x=t.pendingProps!==x,I||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(x||B!==y)&&Lm(t,u,r,y),Zn=!1;var P=t.memoizedState;u.state=P,Do(t,r,u,s),Oo(),B=t.memoizedState,x||P!==B||Zn?(typeof K=="function"&&(Qc(t,a,K,r),B=t.memoizedState),(M=Zn||Nm(t,a,M,r,P,B,y))?(I||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=B),u.props=r,u.state=B,u.context=y,r=M):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{u=t.stateNode,Mc(e,t),y=t.memoizedProps,I=ka(a,y),u.props=I,K=t.pendingProps,P=u.context,B=a.contextType,M=dr,typeof B=="object"&&B!==null&&(M=dt(B)),x=a.getDerivedStateFromProps,(B=typeof x=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(y!==K||P!==M)&&Lm(t,u,r,M),Zn=!1,P=t.memoizedState,u.state=P,Do(t,r,u,s),Oo();var H=t.memoizedState;y!==K||P!==H||Zn||e!==null&&e.dependencies!==null&&Xl(e.dependencies)?(typeof x=="function"&&(Qc(t,a,x,r),H=t.memoizedState),(I=Zn||Nm(t,a,I,r,P,H,M)||e!==null&&e.dependencies!==null&&Xl(e.dependencies))?(B||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(r,H,M),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(r,H,M)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||y===e.memoizedProps&&P===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&P===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=H),u.props=r,u.state=H,u.context=M,r=I):(typeof u.componentDidUpdate!="function"||y===e.memoizedProps&&P===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&P===e.memoizedState||(t.flags|=1024),r=!1)}return u=r,di(e,t),r=(t.flags&128)!==0,u||r?(u=t.stateNode,a=r&&typeof a.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&r?(t.child=_r(t,e.child,null,s),t.child=_r(t,null,a,s)):lt(e,t,a,s),t.memoizedState=u.state,e=t.child):e=Ln(e,t,s),e}function Km(e,t,a,r){return _o(),t.flags|=256,lt(e,t,a,r),t.child}var Jc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function eu(e){return{baseLanes:e,cachePool:zh()}}function tu(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Xt),e}function Qm(e,t,a){var r=t.pendingProps,s=!1,u=(t.flags&128)!==0,y;if((y=u)||(y=e!==null&&e.memoizedState===null?!1:(We.current&2)!==0),y&&(s=!0,t.flags&=-129),y=(t.flags&32)!==0,t.flags&=-33,e===null){if(Oe){if(s?ea(t):ta(),Oe){var x=$e,M;if(M=x){e:{for(M=x,x=un;M.nodeType!==8;){if(!x){x=null;break e}if(M=nn(M.nextSibling),M===null){x=null;break e}}x=M}x!==null?(t.memoizedState={dehydrated:x,treeContext:Ta!==null?{id:An,overflow:Mn}:null,retryLane:536870912,hydrationErrors:null},M=Ot(18,null,null,0),M.stateNode=x,M.return=t,t.child=M,pt=t,$e=null,M=!0):M=!1}M||Na(t)}if(x=t.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return Bu(x)?t.lanes=32:t.lanes=536870912,null;Nn(t)}return x=r.children,r=r.fallback,s?(ta(),s=t.mode,x=hi({mode:"hidden",children:x},s),r=Ma(r,s,a,null),x.return=t,r.return=t,x.sibling=r,t.child=x,s=t.child,s.memoizedState=eu(a),s.childLanes=tu(e,y,a),t.memoizedState=Jc,r):(ea(t),nu(t,x))}if(M=e.memoizedState,M!==null&&(x=M.dehydrated,x!==null)){if(u)t.flags&256?(ea(t),t.flags&=-257,t=au(e,t,a)):t.memoizedState!==null?(ta(),t.child=e.child,t.flags|=128,t=null):(ta(),s=r.fallback,x=t.mode,r=hi({mode:"visible",children:r.children},x),s=Ma(s,x,a,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,_r(t,e.child,null,a),r=t.child,r.memoizedState=eu(a),r.childLanes=tu(e,y,a),t.memoizedState=Jc,t=s);else if(ea(t),Bu(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var B=y.dgst;y=B,r=Error(i(419)),r.stack="",r.digest=y,wo({value:r,source:null,stack:null}),t=au(e,t,a)}else if(nt||Eo(e,t,a,!1),y=(a&e.childLanes)!==0,nt||y){if(y=Pe,y!==null&&(r=a&-a,r=(r&42)!==0?1:Ps(r),r=(r&(y.suspendedLanes|a))!==0?0:r,r!==0&&r!==M.retryLane))throw M.retryLane=r,fr(e,r),zt(y,e,r),Hm;x.data==="$?"||xu(),t=au(e,t,a)}else x.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=M.treeContext,$e=nn(x.nextSibling),pt=t,Oe=!0,Da=null,un=!1,e!==null&&(Yt[$t++]=An,Yt[$t++]=Mn,Yt[$t++]=Ta,An=e.id,Mn=e.overflow,Ta=t),t=nu(t,r.children),t.flags|=4096);return t}return s?(ta(),s=r.fallback,x=t.mode,M=e.child,B=M.sibling,r=Cn(M,{mode:"hidden",children:r.children}),r.subtreeFlags=M.subtreeFlags&65011712,B!==null?s=Cn(B,s):(s=Ma(s,x,a,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,x=e.child.memoizedState,x===null?x=eu(a):(M=x.cachePool,M!==null?(B=Fe._currentValue,M=M.parent!==B?{parent:B,pool:B}:M):M=zh(),x={baseLanes:x.baseLanes|a,cachePool:M}),s.memoizedState=x,s.childLanes=tu(e,y,a),t.memoizedState=Jc,r):(ea(t),a=e.child,e=a.sibling,a=Cn(a,{mode:"visible",children:r.children}),a.return=t,a.sibling=null,e!==null&&(y=t.deletions,y===null?(t.deletions=[e],t.flags|=16):y.push(e)),t.child=a,t.memoizedState=null,a)}function nu(e,t){return t=hi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function hi(e,t){return e=Ot(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function au(e,t,a){return _r(t,e.child,null,a),e=nu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zm(e,t,a){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),xc(e.return,t,a)}function ru(e,t,a,r,s){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:a,tailMode:s}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=r,u.tail=a,u.tailMode=s)}function Fm(e,t,a){var r=t.pendingProps,s=r.revealOrder,u=r.tail;if(lt(e,t,r.children,a),r=We.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zm(e,a,t);else if(e.tag===19)Zm(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(F(We,r),s){case"forwards":for(a=t.child,s=null;a!==null;)e=a.alternate,e!==null&&ci(e)===null&&(s=a),a=a.sibling;a=s,a===null?(s=t.child,t.child=null):(s=a.sibling,a.sibling=null),ru(t,!1,s,a,u);break;case"backwards":for(a=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ci(e)===null){t.child=s;break}e=s.sibling,s.sibling=a,a=s,s=e}ru(t,!0,a,null,u);break;case"together":ru(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ln(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),la|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Eo(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,a=Cn(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Cn(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function ou(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Xl(e)))}function qS(e,t,a){switch(t.tag){case 3:me(t,t.stateNode.containerInfo),Qn(t,Fe,e.memoizedState.cache),_o();break;case 27:case 5:Ce(t);break;case 4:me(t,t.stateNode.containerInfo);break;case 10:Qn(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(r!==null)return r.dehydrated!==null?(ea(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Qm(e,t,a):(ea(t),e=Ln(e,t,a),e!==null?e.sibling:null);ea(t);break;case 19:var s=(e.flags&128)!==0;if(r=(a&t.childLanes)!==0,r||(Eo(e,t,a,!1),r=(a&t.childLanes)!==0),s){if(r)return Fm(e,t,a);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),F(We,We.current),r)break;return null;case 22:case 23:return t.lanes=0,Ym(e,t,a);case 24:Qn(t,Fe,e.memoizedState.cache)}return Ln(e,t,a)}function Wm(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)nt=!0;else{if(!ou(e,a)&&(t.flags&128)===0)return nt=!1,qS(e,t,a);nt=(e.flags&131072)!==0}else nt=!1,Oe&&(t.flags&1048576)!==0&&Mh(t,Il,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,s=r._init;if(r=s(r._payload),t.type=r,typeof r=="function")mc(r)?(e=ka(r,e),t.tag=1,t=Xm(null,t,r,e,a)):(t.tag=0,t=Wc(null,t,r,e,a));else{if(r!=null){if(s=r.$$typeof,s===N){t.tag=11,t=Gm(null,t,r,e,a);break e}else if(s===V){t.tag=14,t=Vm(null,t,r,e,a);break e}}throw t=ue(r)||r,Error(i(306,t,""))}}return t;case 0:return Wc(e,t,t.type,t.pendingProps,a);case 1:return r=t.type,s=ka(r,t.pendingProps),Xm(e,t,r,s,a);case 3:e:{if(me(t,t.stateNode.containerInfo),e===null)throw Error(i(387));r=t.pendingProps;var u=t.memoizedState;s=u.element,Mc(e,t),Do(t,r,null,a);var y=t.memoizedState;if(r=y.cache,Qn(t,Fe,r),r!==u.cache&&_c(t,[Fe],a,!0),Oo(),r=y.element,u.isDehydrated)if(u={element:r,isDehydrated:!1,cache:y.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=Km(e,t,r,a);break e}else if(r!==s){s=Vt(Error(i(424)),t),wo(s),t=Km(e,t,r,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for($e=nn(e.firstChild),pt=t,Oe=!0,Da=null,un=!0,a=Om(t,null,r,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(_o(),r===s){t=Ln(e,t,a);break e}lt(e,t,r,a)}t=t.child}return t;case 26:return di(e,t),e===null?(a=nv(t.type,null,t.pendingProps,null))?t.memoizedState=a:Oe||(a=t.type,e=t.pendingProps,r=Ai(ne.current).createElement(a),r[ft]=t,r[_t]=e,st(r,a,e),tt(r),t.stateNode=r):t.memoizedState=nv(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ce(t),e===null&&Oe&&(r=t.stateNode=Jp(t.type,t.pendingProps,ne.current),pt=t,un=!0,s=$e,ua(t.type)?(Pu=s,$e=nn(r.firstChild)):$e=s),lt(e,t,t.pendingProps.children,a),di(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Oe&&((s=r=$e)&&(r=gx(r,t.type,t.pendingProps,un),r!==null?(t.stateNode=r,pt=t,$e=nn(r.firstChild),un=!1,s=!0):s=!1),s||Na(t)),Ce(t),s=t.type,u=t.pendingProps,y=e!==null?e.memoizedProps:null,r=u.children,zu(s,u)?r=null:y!==null&&zu(s,y)&&(t.flags|=32),t.memoizedState!==null&&(s=jc(e,t,zS,null,null,a),Jo._currentValue=s),di(e,t),lt(e,t,r,a),t.child;case 6:return e===null&&Oe&&((e=a=$e)&&(a=yx(a,t.pendingProps,un),a!==null?(t.stateNode=a,pt=t,$e=null,e=!0):e=!1),e||Na(t)),null;case 13:return Qm(e,t,a);case 4:return me(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_r(t,null,r,a):lt(e,t,r,a),t.child;case 11:return Gm(e,t,t.type,t.pendingProps,a);case 7:return lt(e,t,t.pendingProps,a),t.child;case 8:return lt(e,t,t.pendingProps.children,a),t.child;case 12:return lt(e,t,t.pendingProps.children,a),t.child;case 10:return r=t.pendingProps,Qn(t,t.type,r.value),lt(e,t,r.children,a),t.child;case 9:return s=t.type._context,r=t.pendingProps.children,ja(t),s=dt(s),r=r(s),t.flags|=1,lt(e,t,r,a),t.child;case 14:return Vm(e,t,t.type,t.pendingProps,a);case 15:return qm(e,t,t.type,t.pendingProps,a);case 19:return Fm(e,t,a);case 31:return r=t.pendingProps,a=t.mode,r={mode:r.mode,children:r.children},e===null?(a=hi(r,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Cn(e.child,r),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Ym(e,t,a);case 24:return ja(t),r=dt(Fe),e===null?(s=Rc(),s===null&&(s=Pe,u=wc(),s.pooledCache=u,u.refCount++,u!==null&&(s.pooledCacheLanes|=a),s=u),t.memoizedState={parent:r,cache:s},Ac(t),Qn(t,Fe,s)):((e.lanes&a)!==0&&(Mc(e,t),Do(t,null,null,a),Oo()),s=e.memoizedState,u=t.memoizedState,s.parent!==r?(s={parent:r,cache:r},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),Qn(t,Fe,r)):(r=u.cache,Qn(t,Fe,r),r!==s.cache&&_c(t,[Fe],a,!0))),lt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function jn(e){e.flags|=4}function Jm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!iv(t)){if(t=It.current,t!==null&&((Ae&4194048)===Ae?fn!==null:(Ae&62914560)!==Ae&&(Ae&536870912)===0||t!==fn))throw Mo=Cc,Uh;e.flags|=8192}}function mi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Dd():536870912,e.lanes|=t,Cr|=t)}function Bo(e,t){if(!Oe)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var r=null;a!==null;)a.alternate!==null&&(r=a),a=a.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,r=0;if(t)for(var s=e.child;s!==null;)a|=s.lanes|s.childLanes,r|=s.subtreeFlags&65011712,r|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)a|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=a,t}function YS(e,t,a){var r=t.pendingProps;switch(yc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return Ye(t),null;case 3:return a=t.stateNode,r=null,e!==null&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),On(Fe),ge(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(xo(t)?jn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Dh())),Ye(t),null;case 26:return a=t.memoizedState,e===null?(jn(t),a!==null?(Ye(t),Jm(t,a)):(Ye(t),t.flags&=-16777217)):a?a!==e.memoizedState?(jn(t),Ye(t),Jm(t,a)):(Ye(t),t.flags&=-16777217):(e.memoizedProps!==r&&jn(t),Ye(t),t.flags&=-16777217),null;case 27:De(t),a=ne.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==r&&jn(t);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Ye(t),null}e=ee.current,xo(t)?Th(t):(e=Jp(s,r,a),t.stateNode=e,jn(t))}return Ye(t),null;case 5:if(De(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==r&&jn(t);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Ye(t),null}if(e=ee.current,xo(t))Th(t);else{switch(s=Ai(ne.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof r.is=="string"?s.createElement("select",{is:r.is}):s.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e=typeof r.is=="string"?s.createElement(a,{is:r.is}):s.createElement(a)}}e[ft]=t,e[_t]=r;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(st(e,a,r),a){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&jn(t)}}return Ye(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==r&&jn(t);else{if(typeof r!="string"&&t.stateNode===null)throw Error(i(166));if(e=ne.current,xo(t)){if(e=t.stateNode,a=t.memoizedProps,r=null,s=pt,s!==null)switch(s.tag){case 27:case 5:r=s.memoizedProps}e[ft]=t,e=!!(e.nodeValue===a||r!==null&&r.suppressHydrationWarning===!0||Ip(e.nodeValue,a)),e||Na(t)}else e=Ai(e).createTextNode(r),e[ft]=t,t.stateNode=e}return Ye(t),null;case 13:if(r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=xo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(i(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(i(317));s[ft]=t}else _o(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),s=!1}else s=Dh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(Nn(t),t):(Nn(t),null)}if(Nn(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=r!==null,e=e!==null&&e.memoizedState!==null,a){r=t.child,s=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(s=r.alternate.memoizedState.cachePool.pool);var u=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(u=r.memoizedState.cachePool.pool),u!==s&&(r.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),mi(t,t.updateQueue),Ye(t),null;case 4:return ge(),e===null&&Ou(t.stateNode.containerInfo),Ye(t),null;case 10:return On(t.type),Ye(t),null;case 19:if(J(We),s=t.memoizedState,s===null)return Ye(t),null;if(r=(t.flags&128)!==0,u=s.rendering,u===null)if(r)Bo(s,!1);else{if(Ie!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=ci(e),u!==null){for(t.flags|=128,Bo(s,!1),e=u.updateQueue,t.updateQueue=e,mi(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Ah(a,e),a=a.sibling;return F(We,We.current&1|2),t.child}e=e.sibling}s.tail!==null&&xt()>gi&&(t.flags|=128,r=!0,Bo(s,!1),t.lanes=4194304)}else{if(!r)if(e=ci(u),e!==null){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,mi(t,e),Bo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!u.alternate&&!Oe)return Ye(t),null}else 2*xt()-s.renderingStartTime>gi&&a!==536870912&&(t.flags|=128,r=!0,Bo(s,!1),t.lanes=4194304);s.isBackwards?(u.sibling=t.child,t.child=u):(e=s.last,e!==null?e.sibling=u:t.child=u,s.last=u)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=xt(),t.sibling=null,e=We.current,F(We,r?e&1|2:e&1),t):(Ye(t),null);case 22:case 23:return Nn(t),Nc(),r=t.memoizedState!==null,e!==null?e.memoizedState!==null!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?(a&536870912)!==0&&(t.flags&128)===0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),a=t.updateQueue,a!==null&&mi(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),r=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(r=t.memoizedState.cachePool.pool),r!==a&&(t.flags|=2048),e!==null&&J(za),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),On(Fe),Ye(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function $S(e,t){switch(yc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return On(Fe),ge(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return De(t),null;case 13:if(Nn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));_o()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(We),null;case 4:return ge(),null;case 10:return On(t.type),null;case 22:case 23:return Nn(t),Nc(),e!==null&&J(za),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return On(Fe),null;case 25:return null;default:return null}}function ep(e,t){switch(yc(t),t.tag){case 3:On(Fe),ge();break;case 26:case 27:case 5:De(t);break;case 4:ge();break;case 13:Nn(t);break;case 19:J(We);break;case 10:On(t.type);break;case 22:case 23:Nn(t),Nc(),e!==null&&J(za);break;case 24:On(Fe)}}function Po(e,t){try{var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var s=r.next;a=s;do{if((a.tag&e)===e){r=void 0;var u=a.create,y=a.inst;r=u(),y.destroy=r}a=a.next}while(a!==s)}}catch(x){Be(t,t.return,x)}}function na(e,t,a){try{var r=t.updateQueue,s=r!==null?r.lastEffect:null;if(s!==null){var u=s.next;r=u;do{if((r.tag&e)===e){var y=r.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,s=t;var M=a,B=x;try{B()}catch(I){Be(s,M,I)}}}r=r.next}while(r!==u)}}catch(I){Be(t,t.return,I)}}function tp(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Vh(t,a)}catch(r){Be(e,e.return,r)}}}function np(e,t,a){a.props=ka(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(r){Be(e,t,r)}}function Ho(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;case 30:r=e.stateNode;break;default:r=e.stateNode}typeof a=="function"?e.refCleanup=a(r):a.current=r}}catch(s){Be(e,t,s)}}function dn(e,t){var a=e.ref,r=e.refCleanup;if(a!==null)if(typeof r=="function")try{r()}catch(s){Be(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){Be(e,t,s)}else a.current=null}function ap(e){var t=e.type,a=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break e;case"img":a.src?r.src=a.src:a.srcSet&&(r.srcset=a.srcSet)}}catch(s){Be(e,e.return,s)}}function lu(e,t,a){try{var r=e.stateNode;dx(r,e.type,a,t),r[_t]=t}catch(s){Be(e,e.return,s)}}function rp(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ua(e.type)||e.tag===4}function iu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ua(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function su(e,t,a){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Ci));else if(r!==4&&(r===27&&ua(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(su(e,t,a),e=e.sibling;e!==null;)su(e,t,a),e=e.sibling}function pi(e,t,a){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(r!==4&&(r===27&&ua(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(pi(e,t,a),e=e.sibling;e!==null;)pi(e,t,a),e=e.sibling}function op(e){var t=e.stateNode,a=e.memoizedProps;try{for(var r=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);st(t,r,a),t[ft]=e,t[_t]=a}catch(u){Be(e,e.return,u)}}var zn=!1,Ke=!1,cu=!1,lp=typeof WeakSet=="function"?WeakSet:Set,at=null;function IS(e,t){if(e=e.containerInfo,Lu=Li,e=gh(e),ic(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var r=a.getSelection&&a.getSelection();if(r&&r.rangeCount!==0){a=r.anchorNode;var s=r.anchorOffset,u=r.focusNode;r=r.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break e}var y=0,x=-1,M=-1,B=0,I=0,K=e,P=null;t:for(;;){for(var H;K!==a||s!==0&&K.nodeType!==3||(x=y+s),K!==u||r!==0&&K.nodeType!==3||(M=y+r),K.nodeType===3&&(y+=K.nodeValue.length),(H=K.firstChild)!==null;)P=K,K=H;for(;;){if(K===e)break t;if(P===a&&++B===s&&(x=y),P===u&&++I===r&&(M=y),(H=K.nextSibling)!==null)break;K=P,P=K.parentNode}K=H}a=x===-1||M===-1?null:{start:x,end:M}}else a=null}a=a||{start:0,end:0}}else a=null;for(ju={focusedElem:e,selectionRange:a},Li=!1,at=t;at!==null;)if(t=at,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,at=e;else for(;at!==null;){switch(t=at,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,a=t,s=u.memoizedProps,u=u.memoizedState,r=a.stateNode;try{var ye=ka(a.type,s,a.elementType===a.type);e=r.getSnapshotBeforeUpdate(ye,u),r.__reactInternalSnapshotBeforeUpdate=e}catch(pe){Be(a,a.return,pe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)ku(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ku(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,at=e;break}at=t.return}}function ip(e,t,a){var r=a.flags;switch(a.tag){case 0:case 11:case 15:aa(e,a),r&4&&Po(5,a);break;case 1:if(aa(e,a),r&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(y){Be(a,a.return,y)}else{var s=ka(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Be(a,a.return,y)}}r&64&&tp(a),r&512&&Ho(a,a.return);break;case 3:if(aa(e,a),r&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Vh(e,t)}catch(y){Be(a,a.return,y)}}break;case 27:t===null&&r&4&&op(a);case 26:case 5:aa(e,a),t===null&&r&4&&ap(a),r&512&&Ho(a,a.return);break;case 12:aa(e,a);break;case 13:aa(e,a),r&4&&up(e,a),r&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=tx.bind(null,a),bx(e,a))));break;case 22:if(r=a.memoizedState!==null||zn,!r){t=t!==null&&t.memoizedState!==null||Ke,s=zn;var u=Ke;zn=r,(Ke=t)&&!u?ra(e,a,(a.subtreeFlags&8772)!==0):aa(e,a),zn=s,Ke=u}break;case 30:break;default:aa(e,a)}}function sp(e){var t=e.alternate;t!==null&&(e.alternate=null,sp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Vs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var qe=null,Rt=!1;function Un(e,t,a){for(a=a.child;a!==null;)cp(e,t,a),a=a.sibling}function cp(e,t,a){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(lo,a)}catch{}switch(a.tag){case 26:Ke||dn(a,t),Un(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ke||dn(a,t);var r=qe,s=Rt;ua(a.type)&&(qe=a.stateNode,Rt=!1),Un(e,t,a),Qo(a.stateNode),qe=r,Rt=s;break;case 5:Ke||dn(a,t);case 6:if(r=qe,s=Rt,qe=null,Un(e,t,a),qe=r,Rt=s,qe!==null)if(Rt)try{(qe.nodeType===9?qe.body:qe.nodeName==="HTML"?qe.ownerDocument.body:qe).removeChild(a.stateNode)}catch(u){Be(a,t,u)}else try{qe.removeChild(a.stateNode)}catch(u){Be(a,t,u)}break;case 18:qe!==null&&(Rt?(e=qe,Fp(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),al(e)):Fp(qe,a.stateNode));break;case 4:r=qe,s=Rt,qe=a.stateNode.containerInfo,Rt=!0,Un(e,t,a),qe=r,Rt=s;break;case 0:case 11:case 14:case 15:Ke||na(2,a,t),Ke||na(4,a,t),Un(e,t,a);break;case 1:Ke||(dn(a,t),r=a.stateNode,typeof r.componentWillUnmount=="function"&&np(a,t,r)),Un(e,t,a);break;case 21:Un(e,t,a);break;case 22:Ke=(r=Ke)||a.memoizedState!==null,Un(e,t,a),Ke=r;break;default:Un(e,t,a)}}function up(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{al(e)}catch(a){Be(t,t.return,a)}}function XS(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new lp),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new lp),t;default:throw Error(i(435,e.tag))}}function uu(e,t){var a=XS(e);t.forEach(function(r){var s=nx.bind(null,e,r);a.has(r)||(a.add(r),r.then(s,s))})}function Dt(e,t){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var s=a[r],u=e,y=t,x=y;e:for(;x!==null;){switch(x.tag){case 27:if(ua(x.type)){qe=x.stateNode,Rt=!1;break e}break;case 5:qe=x.stateNode,Rt=!1;break e;case 3:case 4:qe=x.stateNode.containerInfo,Rt=!0;break e}x=x.return}if(qe===null)throw Error(i(160));cp(u,y,s),qe=null,Rt=!1,u=s.alternate,u!==null&&(u.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)fp(t,e),t=t.sibling}var tn=null;function fp(e,t){var a=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Dt(t,e),Nt(e),r&4&&(na(3,e,e.return),Po(3,e),na(5,e,e.return));break;case 1:Dt(t,e),Nt(e),r&512&&(Ke||a===null||dn(a,a.return)),r&64&&zn&&(e=e.updateQueue,e!==null&&(r=e.callbacks,r!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?r:a.concat(r))));break;case 26:var s=tn;if(Dt(t,e),Nt(e),r&512&&(Ke||a===null||dn(a,a.return)),r&4){var u=a!==null?a.memoizedState:null;if(r=e.memoizedState,a===null)if(r===null)if(e.stateNode===null){e:{r=e.type,a=e.memoizedProps,s=s.ownerDocument||s;t:switch(r){case"title":u=s.getElementsByTagName("title")[0],(!u||u[co]||u[ft]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=s.createElement(r),s.head.insertBefore(u,s.querySelector("head > title"))),st(u,r,a),u[ft]=e,tt(u),r=u;break e;case"link":var y=ov("link","href",s).get(r+(a.href||""));if(y){for(var x=0;x<y.length;x++)if(u=y[x],u.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){y.splice(x,1);break t}}u=s.createElement(r),st(u,r,a),s.head.appendChild(u);break;case"meta":if(y=ov("meta","content",s).get(r+(a.content||""))){for(x=0;x<y.length;x++)if(u=y[x],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){y.splice(x,1);break t}}u=s.createElement(r),st(u,r,a),s.head.appendChild(u);break;default:throw Error(i(468,r))}u[ft]=e,tt(u),r=u}e.stateNode=r}else lv(s,e.type,e.stateNode);else e.stateNode=rv(s,r,e.memoizedProps);else u!==r?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,r===null?lv(s,e.type,e.stateNode):rv(s,r,e.memoizedProps)):r===null&&e.stateNode!==null&&lu(e,e.memoizedProps,a.memoizedProps)}break;case 27:Dt(t,e),Nt(e),r&512&&(Ke||a===null||dn(a,a.return)),a!==null&&r&4&&lu(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Dt(t,e),Nt(e),r&512&&(Ke||a===null||dn(a,a.return)),e.flags&32){s=e.stateNode;try{rr(s,"")}catch(H){Be(e,e.return,H)}}r&4&&e.stateNode!=null&&(s=e.memoizedProps,lu(e,s,a!==null?a.memoizedProps:s)),r&1024&&(cu=!0);break;case 6:if(Dt(t,e),Nt(e),r&4){if(e.stateNode===null)throw Error(i(162));r=e.memoizedProps,a=e.stateNode;try{a.nodeValue=r}catch(H){Be(e,e.return,H)}}break;case 3:if(Oi=null,s=tn,tn=Mi(t.containerInfo),Dt(t,e),tn=s,Nt(e),r&4&&a!==null&&a.memoizedState.isDehydrated)try{al(t.containerInfo)}catch(H){Be(e,e.return,H)}cu&&(cu=!1,dp(e));break;case 4:r=tn,tn=Mi(e.stateNode.containerInfo),Dt(t,e),Nt(e),tn=r;break;case 12:Dt(t,e),Nt(e);break;case 13:Dt(t,e),Nt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(vu=xt()),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,uu(e,r)));break;case 22:s=e.memoizedState!==null;var M=a!==null&&a.memoizedState!==null,B=zn,I=Ke;if(zn=B||s,Ke=I||M,Dt(t,e),Ke=I,zn=B,Nt(e),r&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(a===null||M||zn||Ke||Ba(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){M=a=t;try{if(u=M.stateNode,s)y=u.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=M.stateNode;var K=M.memoizedProps.style,P=K!=null&&K.hasOwnProperty("display")?K.display:null;x.style.display=P==null||typeof P=="boolean"?"":(""+P).trim()}}catch(H){Be(M,M.return,H)}}}else if(t.tag===6){if(a===null){M=t;try{M.stateNode.nodeValue=s?"":M.memoizedProps}catch(H){Be(M,M.return,H)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}r&4&&(r=e.updateQueue,r!==null&&(a=r.retryQueue,a!==null&&(r.retryQueue=null,uu(e,a))));break;case 19:Dt(t,e),Nt(e),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,uu(e,r)));break;case 30:break;case 21:break;default:Dt(t,e),Nt(e)}}function Nt(e){var t=e.flags;if(t&2){try{for(var a,r=e.return;r!==null;){if(rp(r)){a=r;break}r=r.return}if(a==null)throw Error(i(160));switch(a.tag){case 27:var s=a.stateNode,u=iu(e);pi(e,u,s);break;case 5:var y=a.stateNode;a.flags&32&&(rr(y,""),a.flags&=-33);var x=iu(e);pi(e,x,y);break;case 3:case 4:var M=a.stateNode.containerInfo,B=iu(e);su(e,B,M);break;default:throw Error(i(161))}}catch(I){Be(e,e.return,I)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function dp(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;dp(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function aa(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)ip(e,t.alternate,t),t=t.sibling}function Ba(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:na(4,t,t.return),Ba(t);break;case 1:dn(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&np(t,t.return,a),Ba(t);break;case 27:Qo(t.stateNode);case 26:case 5:dn(t,t.return),Ba(t);break;case 22:t.memoizedState===null&&Ba(t);break;case 30:Ba(t);break;default:Ba(t)}e=e.sibling}}function ra(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var r=t.alternate,s=e,u=t,y=u.flags;switch(u.tag){case 0:case 11:case 15:ra(s,u,a),Po(4,u);break;case 1:if(ra(s,u,a),r=u,s=r.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(B){Be(r,r.return,B)}if(r=u,s=r.updateQueue,s!==null){var x=r.stateNode;try{var M=s.shared.hiddenCallbacks;if(M!==null)for(s.shared.hiddenCallbacks=null,s=0;s<M.length;s++)Gh(M[s],x)}catch(B){Be(r,r.return,B)}}a&&y&64&&tp(u),Ho(u,u.return);break;case 27:op(u);case 26:case 5:ra(s,u,a),a&&r===null&&y&4&&ap(u),Ho(u,u.return);break;case 12:ra(s,u,a);break;case 13:ra(s,u,a),a&&y&4&&up(s,u);break;case 22:u.memoizedState===null&&ra(s,u,a),Ho(u,u.return);break;case 30:break;default:ra(s,u,a)}t=t.sibling}}function fu(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Ro(a))}function du(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ro(e))}function hn(e,t,a,r){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)hp(e,t,a,r),t=t.sibling}function hp(e,t,a,r){var s=t.flags;switch(t.tag){case 0:case 11:case 15:hn(e,t,a,r),s&2048&&Po(9,t);break;case 1:hn(e,t,a,r);break;case 3:hn(e,t,a,r),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ro(e)));break;case 12:if(s&2048){hn(e,t,a,r),e=t.stateNode;try{var u=t.memoizedProps,y=u.id,x=u.onPostCommit;typeof x=="function"&&x(y,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(M){Be(t,t.return,M)}}else hn(e,t,a,r);break;case 13:hn(e,t,a,r);break;case 23:break;case 22:u=t.stateNode,y=t.alternate,t.memoizedState!==null?u._visibility&2?hn(e,t,a,r):Go(e,t):u._visibility&2?hn(e,t,a,r):(u._visibility|=2,wr(e,t,a,r,(t.subtreeFlags&10256)!==0)),s&2048&&fu(y,t);break;case 24:hn(e,t,a,r),s&2048&&du(t.alternate,t);break;default:hn(e,t,a,r)}}function wr(e,t,a,r,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,y=t,x=a,M=r,B=y.flags;switch(y.tag){case 0:case 11:case 15:wr(u,y,x,M,s),Po(8,y);break;case 23:break;case 22:var I=y.stateNode;y.memoizedState!==null?I._visibility&2?wr(u,y,x,M,s):Go(u,y):(I._visibility|=2,wr(u,y,x,M,s)),s&&B&2048&&fu(y.alternate,y);break;case 24:wr(u,y,x,M,s),s&&B&2048&&du(y.alternate,y);break;default:wr(u,y,x,M,s)}t=t.sibling}}function Go(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,r=t,s=r.flags;switch(r.tag){case 22:Go(a,r),s&2048&&fu(r.alternate,r);break;case 24:Go(a,r),s&2048&&du(r.alternate,r);break;default:Go(a,r)}t=t.sibling}}var Vo=8192;function Er(e){if(e.subtreeFlags&Vo)for(e=e.child;e!==null;)mp(e),e=e.sibling}function mp(e){switch(e.tag){case 26:Er(e),e.flags&Vo&&e.memoizedState!==null&&Nx(tn,e.memoizedState,e.memoizedProps);break;case 5:Er(e);break;case 3:case 4:var t=tn;tn=Mi(e.stateNode.containerInfo),Er(e),tn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Vo,Vo=16777216,Er(e),Vo=t):Er(e));break;default:Er(e)}}function pp(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function qo(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var r=t[a];at=r,gp(r,e)}pp(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)vp(e),e=e.sibling}function vp(e){switch(e.tag){case 0:case 11:case 15:qo(e),e.flags&2048&&na(9,e,e.return);break;case 3:qo(e);break;case 12:qo(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,vi(e)):qo(e);break;default:qo(e)}}function vi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var r=t[a];at=r,gp(r,e)}pp(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:na(8,t,t.return),vi(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,vi(t));break;default:vi(t)}e=e.sibling}}function gp(e,t){for(;at!==null;){var a=at;switch(a.tag){case 0:case 11:case 15:na(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var r=a.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Ro(a.memoizedState.cache)}if(r=a.child,r!==null)r.return=a,at=r;else e:for(a=e;at!==null;){r=at;var s=r.sibling,u=r.return;if(sp(r),r===a){at=null;break e}if(s!==null){s.return=u,at=s;break e}at=u}}}var KS={getCacheForType:function(e){var t=dt(Fe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},QS=typeof WeakMap=="function"?WeakMap:Map,Ne=0,Pe=null,Ee=null,Ae=0,Le=0,Lt=null,oa=!1,Rr=!1,hu=!1,kn=0,Ie=0,la=0,Pa=0,mu=0,Xt=0,Cr=0,Yo=null,Ct=null,pu=!1,vu=0,gi=1/0,yi=null,ia=null,it=0,sa=null,Ar=null,Mr=0,gu=0,yu=null,yp=null,$o=0,bu=null;function jt(){if((Ne&2)!==0&&Ae!==0)return Ae&-Ae;if(O.T!==null){var e=pr;return e!==0?e:Cu()}return jd()}function bp(){Xt===0&&(Xt=(Ae&536870912)===0||Oe?Od():536870912);var e=It.current;return e!==null&&(e.flags|=32),Xt}function zt(e,t,a){(e===Pe&&(Le===2||Le===9)||e.cancelPendingCommit!==null)&&(Tr(e,0),ca(e,Ae,Xt,!1)),so(e,a),((Ne&2)===0||e!==Pe)&&(e===Pe&&((Ne&2)===0&&(Pa|=a),Ie===4&&ca(e,Ae,Xt,!1)),mn(e))}function Sp(e,t,a){if((Ne&6)!==0)throw Error(i(327));var r=!a&&(t&124)===0&&(t&e.expiredLanes)===0||io(e,t),s=r?WS(e,t):_u(e,t,!0),u=r;do{if(s===0){Rr&&!r&&ca(e,t,0,!1);break}else{if(a=e.current.alternate,u&&!ZS(a)){s=_u(e,t,!1),u=!1;continue}if(s===2){if(u=t,e.errorRecoveryDisabledLanes&u)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){t=y;e:{var x=e;s=Yo;var M=x.current.memoizedState.isDehydrated;if(M&&(Tr(x,y).flags|=256),y=_u(x,y,!1),y!==2){if(hu&&!M){x.errorRecoveryDisabledLanes|=u,Pa|=u,s=4;break e}u=Ct,Ct=s,u!==null&&(Ct===null?Ct=u:Ct.push.apply(Ct,u))}s=y}if(u=!1,s!==2)continue}}if(s===1){Tr(e,0),ca(e,t,0,!0);break}e:{switch(r=e,u=s,u){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:ca(r,t,Xt,!oa);break e;case 2:Ct=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(s=vu+300-xt(),10<s)){if(ca(r,t,Xt,!oa),Tl(r,0,!0)!==0)break e;r.timeoutHandle=Qp(xp.bind(null,r,a,Ct,yi,pu,t,Xt,Pa,Cr,oa,u,2,-0,0),s);break e}xp(r,a,Ct,yi,pu,t,Xt,Pa,Cr,oa,u,0,-0,0)}}break}while(!0);mn(e)}function xp(e,t,a,r,s,u,y,x,M,B,I,K,P,H){if(e.timeoutHandle=-1,K=t.subtreeFlags,(K&8192||(K&16785408)===16785408)&&(Wo={stylesheets:null,count:0,unsuspend:Dx},mp(t),K=Lx(),K!==null)){e.cancelPendingCommit=K(Mp.bind(null,e,t,u,a,r,s,y,x,M,I,1,P,H)),ca(e,u,y,!B);return}Mp(e,t,u,a,r,s,y,x,M)}function ZS(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var r=0;r<a.length;r++){var s=a[r],u=s.getSnapshot;s=s.value;try{if(!Tt(u(),s))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ca(e,t,a,r){t&=~mu,t&=~Pa,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var s=t;0<s;){var u=31-Mt(s),y=1<<u;r[u]=-1,s&=~y}a!==0&&Nd(e,a,t)}function bi(){return(Ne&6)===0?(Io(0),!1):!0}function Su(){if(Ee!==null){if(Le===0)var e=Ee.return;else e=Ee,Tn=La=null,kc(e),xr=null,Uo=0,e=Ee;for(;e!==null;)ep(e.alternate,e),e=e.return;Ee=null}}function Tr(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,mx(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Su(),Pe=e,Ee=a=Cn(e.current,null),Ae=t,Le=0,Lt=null,oa=!1,Rr=io(e,t),hu=!1,Cr=Xt=mu=Pa=la=Ie=0,Ct=Yo=null,pu=!1,(t&8)!==0&&(t|=t&32);var r=e.entangledLanes;if(r!==0)for(e=e.entanglements,r&=t;0<r;){var s=31-Mt(r),u=1<<s;t|=e[s],r&=~u}return kn=t,Gl(),a}function _p(e,t){xe=null,O.H=li,t===Ao||t===Zl?(t=Ph(),Le=3):t===Uh?(t=Ph(),Le=4):Le=t===Hm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Lt=t,Ee===null&&(Ie=1,fi(e,Vt(t,e.current)))}function wp(){var e=O.H;return O.H=li,e===null?li:e}function Ep(){var e=O.A;return O.A=KS,e}function xu(){Ie=4,oa||(Ae&4194048)!==Ae&&It.current!==null||(Rr=!0),(la&134217727)===0&&(Pa&134217727)===0||Pe===null||ca(Pe,Ae,Xt,!1)}function _u(e,t,a){var r=Ne;Ne|=2;var s=wp(),u=Ep();(Pe!==e||Ae!==t)&&(yi=null,Tr(e,t)),t=!1;var y=Ie;e:do try{if(Le!==0&&Ee!==null){var x=Ee,M=Lt;switch(Le){case 8:Su(),y=6;break e;case 3:case 2:case 9:case 6:It.current===null&&(t=!0);var B=Le;if(Le=0,Lt=null,Or(e,x,M,B),a&&Rr){y=0;break e}break;default:B=Le,Le=0,Lt=null,Or(e,x,M,B)}}FS(),y=Ie;break}catch(I){_p(e,I)}while(!0);return t&&e.shellSuspendCounter++,Tn=La=null,Ne=r,O.H=s,O.A=u,Ee===null&&(Pe=null,Ae=0,Gl()),y}function FS(){for(;Ee!==null;)Rp(Ee)}function WS(e,t){var a=Ne;Ne|=2;var r=wp(),s=Ep();Pe!==e||Ae!==t?(yi=null,gi=xt()+500,Tr(e,t)):Rr=io(e,t);e:do try{if(Le!==0&&Ee!==null){t=Ee;var u=Lt;t:switch(Le){case 1:Le=0,Lt=null,Or(e,t,u,1);break;case 2:case 9:if(kh(u)){Le=0,Lt=null,Cp(t);break}t=function(){Le!==2&&Le!==9||Pe!==e||(Le=7),mn(e)},u.then(t,t);break e;case 3:Le=7;break e;case 4:Le=5;break e;case 7:kh(u)?(Le=0,Lt=null,Cp(t)):(Le=0,Lt=null,Or(e,t,u,7));break;case 5:var y=null;switch(Ee.tag){case 26:y=Ee.memoizedState;case 5:case 27:var x=Ee;if(!y||iv(y)){Le=0,Lt=null;var M=x.sibling;if(M!==null)Ee=M;else{var B=x.return;B!==null?(Ee=B,Si(B)):Ee=null}break t}}Le=0,Lt=null,Or(e,t,u,5);break;case 6:Le=0,Lt=null,Or(e,t,u,6);break;case 8:Su(),Ie=6;break e;default:throw Error(i(462))}}JS();break}catch(I){_p(e,I)}while(!0);return Tn=La=null,O.H=r,O.A=s,Ne=a,Ee!==null?0:(Pe=null,Ae=0,Gl(),Ie)}function JS(){for(;Ee!==null&&!ao();)Rp(Ee)}function Rp(e){var t=Wm(e.alternate,e,kn);e.memoizedProps=e.pendingProps,t===null?Si(e):Ee=t}function Cp(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Im(a,t,t.pendingProps,t.type,void 0,Ae);break;case 11:t=Im(a,t,t.pendingProps,t.type.render,t.ref,Ae);break;case 5:kc(t);default:ep(a,t),t=Ee=Ah(t,kn),t=Wm(a,t,kn)}e.memoizedProps=e.pendingProps,t===null?Si(e):Ee=t}function Or(e,t,a,r){Tn=La=null,kc(t),xr=null,Uo=0;var s=t.return;try{if(VS(e,s,t,a,Ae)){Ie=1,fi(e,Vt(a,e.current)),Ee=null;return}}catch(u){if(s!==null)throw Ee=s,u;Ie=1,fi(e,Vt(a,e.current)),Ee=null;return}t.flags&32768?(Oe||r===1?e=!0:Rr||(Ae&536870912)!==0?e=!1:(oa=e=!0,(r===2||r===9||r===3||r===6)&&(r=It.current,r!==null&&r.tag===13&&(r.flags|=16384))),Ap(t,e)):Si(t)}function Si(e){var t=e;do{if((t.flags&32768)!==0){Ap(t,oa);return}e=t.return;var a=YS(t.alternate,t,kn);if(a!==null){Ee=a;return}if(t=t.sibling,t!==null){Ee=t;return}Ee=t=e}while(t!==null);Ie===0&&(Ie=5)}function Ap(e,t){do{var a=$S(e.alternate,e);if(a!==null){a.flags&=32767,Ee=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){Ee=e;return}Ee=e=a}while(e!==null);Ie=6,Ee=null}function Mp(e,t,a,r,s,u,y,x,M){e.cancelPendingCommit=null;do xi();while(it!==0);if((Ne&6)!==0)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(u=t.lanes|t.childLanes,u|=dc,Db(e,a,u,y,x,M),e===Pe&&(Ee=Pe=null,Ae=0),Ar=t,sa=e,Mr=a,gu=u,yu=s,yp=r,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ax(_a,function(){return Lp(),null})):(e.callbackNode=null,e.callbackPriority=0),r=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||r){r=O.T,O.T=null,s=$.p,$.p=2,y=Ne,Ne|=4;try{IS(e,t,a)}finally{Ne=y,$.p=s,O.T=r}}it=1,Tp(),Op(),Dp()}}function Tp(){if(it===1){it=0;var e=sa,t=Ar,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=O.T,O.T=null;var r=$.p;$.p=2;var s=Ne;Ne|=4;try{fp(t,e);var u=ju,y=gh(e.containerInfo),x=u.focusedElem,M=u.selectionRange;if(y!==x&&x&&x.ownerDocument&&vh(x.ownerDocument.documentElement,x)){if(M!==null&&ic(x)){var B=M.start,I=M.end;if(I===void 0&&(I=B),"selectionStart"in x)x.selectionStart=B,x.selectionEnd=Math.min(I,x.value.length);else{var K=x.ownerDocument||document,P=K&&K.defaultView||window;if(P.getSelection){var H=P.getSelection(),ye=x.textContent.length,pe=Math.min(M.start,ye),ke=M.end===void 0?pe:Math.min(M.end,ye);!H.extend&&pe>ke&&(y=ke,ke=pe,pe=y);var j=ph(x,pe),D=ph(x,ke);if(j&&D&&(H.rangeCount!==1||H.anchorNode!==j.node||H.anchorOffset!==j.offset||H.focusNode!==D.node||H.focusOffset!==D.offset)){var k=K.createRange();k.setStart(j.node,j.offset),H.removeAllRanges(),pe>ke?(H.addRange(k),H.extend(D.node,D.offset)):(k.setEnd(D.node,D.offset),H.addRange(k))}}}}for(K=[],H=x;H=H.parentNode;)H.nodeType===1&&K.push({element:H,left:H.scrollLeft,top:H.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<K.length;x++){var X=K[x];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}Li=!!Lu,ju=Lu=null}finally{Ne=s,$.p=r,O.T=a}}e.current=t,it=2}}function Op(){if(it===2){it=0;var e=sa,t=Ar,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=O.T,O.T=null;var r=$.p;$.p=2;var s=Ne;Ne|=4;try{ip(e,t.alternate,t)}finally{Ne=s,$.p=r,O.T=a}}it=3}}function Dp(){if(it===4||it===3){it=0,ro();var e=sa,t=Ar,a=Mr,r=yp;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?it=5:(it=0,Ar=sa=null,Np(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(ia=null),Hs(a),t=t.stateNode,At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(lo,t,void 0,(t.current.flags&128)===128)}catch{}if(r!==null){t=O.T,s=$.p,$.p=2,O.T=null;try{for(var u=e.onRecoverableError,y=0;y<r.length;y++){var x=r[y];u(x.value,{componentStack:x.stack})}}finally{O.T=t,$.p=s}}(Mr&3)!==0&&xi(),mn(e),s=e.pendingLanes,(a&4194090)!==0&&(s&42)!==0?e===bu?$o++:($o=0,bu=e):$o=0,Io(0)}}function Np(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ro(t)))}function xi(e){return Tp(),Op(),Dp(),Lp()}function Lp(){if(it!==5)return!1;var e=sa,t=gu;gu=0;var a=Hs(Mr),r=O.T,s=$.p;try{$.p=32>a?32:a,O.T=null,a=yu,yu=null;var u=sa,y=Mr;if(it=0,Ar=sa=null,Mr=0,(Ne&6)!==0)throw Error(i(331));var x=Ne;if(Ne|=4,vp(u.current),hp(u,u.current,y,a),Ne=x,Io(0,!1),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(lo,u)}catch{}return!0}finally{$.p=s,O.T=r,Np(e,t)}}function jp(e,t,a){t=Vt(a,t),t=Fc(e.stateNode,t,2),e=Wn(e,t,2),e!==null&&(so(e,2),mn(e))}function Be(e,t,a){if(e.tag===3)jp(e,e,a);else for(;t!==null;){if(t.tag===3){jp(t,e,a);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ia===null||!ia.has(r))){e=Vt(a,e),a=Bm(2),r=Wn(t,a,2),r!==null&&(Pm(a,r,t,e),so(r,2),mn(r));break}}t=t.return}}function wu(e,t,a){var r=e.pingCache;if(r===null){r=e.pingCache=new QS;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(a)||(hu=!0,s.add(a),e=ex.bind(null,e,t,a),t.then(e,e))}function ex(e,t,a){var r=e.pingCache;r!==null&&r.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Pe===e&&(Ae&a)===a&&(Ie===4||Ie===3&&(Ae&62914560)===Ae&&300>xt()-vu?(Ne&2)===0&&Tr(e,0):mu|=a,Cr===Ae&&(Cr=0)),mn(e)}function zp(e,t){t===0&&(t=Dd()),e=fr(e,t),e!==null&&(so(e,t),mn(e))}function tx(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),zp(e,a)}function nx(e,t){var a=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(a=s.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}r!==null&&r.delete(t),zp(e,a)}function ax(e,t){return ut(e,t)}var _i=null,Dr=null,Eu=!1,wi=!1,Ru=!1,Ha=0;function mn(e){e!==Dr&&e.next===null&&(Dr===null?_i=Dr=e:Dr=Dr.next=e),wi=!0,Eu||(Eu=!0,ox())}function Io(e,t){if(!Ru&&wi){Ru=!0;do for(var a=!1,r=_i;r!==null;){if(e!==0){var s=r.pendingLanes;if(s===0)var u=0;else{var y=r.suspendedLanes,x=r.pingedLanes;u=(1<<31-Mt(42|e)+1)-1,u&=s&~(y&~x),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(a=!0,Pp(r,u))}else u=Ae,u=Tl(r,r===Pe?u:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(u&3)===0||io(r,u)||(a=!0,Pp(r,u));r=r.next}while(a);Ru=!1}}function rx(){Up()}function Up(){wi=Eu=!1;var e=0;Ha!==0&&(hx()&&(e=Ha),Ha=0);for(var t=xt(),a=null,r=_i;r!==null;){var s=r.next,u=kp(r,t);u===0?(r.next=null,a===null?_i=s:a.next=s,s===null&&(Dr=a)):(a=r,(e!==0||(u&3)!==0)&&(wi=!0)),r=s}Io(e)}function kp(e,t){for(var a=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var y=31-Mt(u),x=1<<y,M=s[y];M===-1?((x&a)===0||(x&r)!==0)&&(s[y]=Ob(x,t)):M<=t&&(e.expiredLanes|=x),u&=~x}if(t=Pe,a=Ae,a=Tl(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r=e.callbackNode,a===0||e===t&&(Le===2||Le===9)||e.cancelPendingCommit!==null)return r!==null&&r!==null&&wn(r),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||io(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(r!==null&&wn(r),Hs(a)){case 2:case 8:a=oo;break;case 32:a=_a;break;case 268435456:a=ot;break;default:a=_a}return r=Bp.bind(null,e),a=ut(a,r),e.callbackPriority=t,e.callbackNode=a,t}return r!==null&&r!==null&&wn(r),e.callbackPriority=2,e.callbackNode=null,2}function Bp(e,t){if(it!==0&&it!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(xi()&&e.callbackNode!==a)return null;var r=Ae;return r=Tl(e,e===Pe?r:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r===0?null:(Sp(e,r,t),kp(e,xt()),e.callbackNode!=null&&e.callbackNode===a?Bp.bind(null,e):null)}function Pp(e,t){if(xi())return null;Sp(e,t,!0)}function ox(){px(function(){(Ne&6)!==0?ut(Pt,rx):Up()})}function Cu(){return Ha===0&&(Ha=Od()),Ha}function Hp(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:jl(""+e)}function Gp(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function lx(e,t,a,r,s){if(t==="submit"&&a&&a.stateNode===s){var u=Hp((s[_t]||null).action),y=r.submitter;y&&(t=(t=y[_t]||null)?Hp(t.formAction):y.getAttribute("formAction"),t!==null&&(u=t,y=null));var x=new Bl("action","action",null,r,s);e.push({event:x,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(Ha!==0){var M=y?Gp(s,y):new FormData(s);Ic(a,{pending:!0,data:M,method:s.method,action:u},null,M)}}else typeof u=="function"&&(x.preventDefault(),M=y?Gp(s,y):new FormData(s),Ic(a,{pending:!0,data:M,method:s.method,action:u},u,M))},currentTarget:s}]})}}for(var Au=0;Au<fc.length;Au++){var Mu=fc[Au],ix=Mu.toLowerCase(),sx=Mu[0].toUpperCase()+Mu.slice(1);en(ix,"on"+sx)}en(Sh,"onAnimationEnd"),en(xh,"onAnimationIteration"),en(_h,"onAnimationStart"),en("dblclick","onDoubleClick"),en("focusin","onFocus"),en("focusout","onBlur"),en(RS,"onTransitionRun"),en(CS,"onTransitionStart"),en(AS,"onTransitionCancel"),en(wh,"onTransitionEnd"),tr("onMouseEnter",["mouseout","mouseover"]),tr("onMouseLeave",["mouseout","mouseover"]),tr("onPointerEnter",["pointerout","pointerover"]),tr("onPointerLeave",["pointerout","pointerover"]),Ea("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ea("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ea("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ea("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ea("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ea("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),cx=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Xo));function Vp(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var r=e[a],s=r.event;r=r.listeners;e:{var u=void 0;if(t)for(var y=r.length-1;0<=y;y--){var x=r[y],M=x.instance,B=x.currentTarget;if(x=x.listener,M!==u&&s.isPropagationStopped())break e;u=x,s.currentTarget=B;try{u(s)}catch(I){ui(I)}s.currentTarget=null,u=M}else for(y=0;y<r.length;y++){if(x=r[y],M=x.instance,B=x.currentTarget,x=x.listener,M!==u&&s.isPropagationStopped())break e;u=x,s.currentTarget=B;try{u(s)}catch(I){ui(I)}s.currentTarget=null,u=M}}}}function Re(e,t){var a=t[Gs];a===void 0&&(a=t[Gs]=new Set);var r=e+"__bubble";a.has(r)||(qp(t,e,2,!1),a.add(r))}function Tu(e,t,a){var r=0;t&&(r|=4),qp(a,e,r,t)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function Ou(e){if(!e[Ei]){e[Ei]=!0,Ud.forEach(function(a){a!=="selectionchange"&&(cx.has(a)||Tu(a,!1,e),Tu(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ei]||(t[Ei]=!0,Tu("selectionchange",!1,t))}}function qp(e,t,a,r){switch(hv(t)){case 2:var s=Ux;break;case 8:s=kx;break;default:s=Yu}a=s.bind(null,t,a,e),s=void 0,!Ws||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,a,{capture:!0,passive:s}):e.addEventListener(t,a,!0):s!==void 0?e.addEventListener(t,a,{passive:s}):e.addEventListener(t,a,!1)}function Du(e,t,a,r,s){var u=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var y=r.tag;if(y===3||y===4){var x=r.stateNode.containerInfo;if(x===s)break;if(y===4)for(y=r.return;y!==null;){var M=y.tag;if((M===3||M===4)&&y.stateNode.containerInfo===s)return;y=y.return}for(;x!==null;){if(y=Wa(x),y===null)return;if(M=y.tag,M===5||M===6||M===26||M===27){r=u=y;continue e}x=x.parentNode}}r=r.return}Zd(function(){var B=u,I=Zs(a),K=[];e:{var P=Eh.get(e);if(P!==void 0){var H=Bl,ye=e;switch(e){case"keypress":if(Ul(a)===0)break e;case"keydown":case"keyup":H=aS;break;case"focusin":ye="focus",H=nc;break;case"focusout":ye="blur",H=nc;break;case"beforeblur":case"afterblur":H=nc;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":H=Jd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":H=$b;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":H=lS;break;case Sh:case xh:case _h:H=Kb;break;case wh:H=sS;break;case"scroll":case"scrollend":H=qb;break;case"wheel":H=uS;break;case"copy":case"cut":case"paste":H=Zb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":H=th;break;case"toggle":case"beforetoggle":H=dS}var pe=(t&4)!==0,ke=!pe&&(e==="scroll"||e==="scrollend"),j=pe?P!==null?P+"Capture":null:P;pe=[];for(var D=B,k;D!==null;){var X=D;if(k=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||k===null||j===null||(X=fo(D,j),X!=null&&pe.push(Ko(D,X,k))),ke)break;D=D.return}0<pe.length&&(P=new H(P,ye,null,a,I),K.push({event:P,listeners:pe}))}}if((t&7)===0){e:{if(P=e==="mouseover"||e==="pointerover",H=e==="mouseout"||e==="pointerout",P&&a!==Qs&&(ye=a.relatedTarget||a.fromElement)&&(Wa(ye)||ye[Fa]))break e;if((H||P)&&(P=I.window===I?I:(P=I.ownerDocument)?P.defaultView||P.parentWindow:window,H?(ye=a.relatedTarget||a.toElement,H=B,ye=ye?Wa(ye):null,ye!==null&&(ke=f(ye),pe=ye.tag,ye!==ke||pe!==5&&pe!==27&&pe!==6)&&(ye=null)):(H=null,ye=B),H!==ye)){if(pe=Jd,X="onMouseLeave",j="onMouseEnter",D="mouse",(e==="pointerout"||e==="pointerover")&&(pe=th,X="onPointerLeave",j="onPointerEnter",D="pointer"),ke=H==null?P:uo(H),k=ye==null?P:uo(ye),P=new pe(X,D+"leave",H,a,I),P.target=ke,P.relatedTarget=k,X=null,Wa(I)===B&&(pe=new pe(j,D+"enter",ye,a,I),pe.target=k,pe.relatedTarget=ke,X=pe),ke=X,H&&ye)t:{for(pe=H,j=ye,D=0,k=pe;k;k=Nr(k))D++;for(k=0,X=j;X;X=Nr(X))k++;for(;0<D-k;)pe=Nr(pe),D--;for(;0<k-D;)j=Nr(j),k--;for(;D--;){if(pe===j||j!==null&&pe===j.alternate)break t;pe=Nr(pe),j=Nr(j)}pe=null}else pe=null;H!==null&&Yp(K,P,H,pe,!1),ye!==null&&ke!==null&&Yp(K,ke,ye,pe,!0)}}e:{if(P=B?uo(B):window,H=P.nodeName&&P.nodeName.toLowerCase(),H==="select"||H==="input"&&P.type==="file")var ie=ch;else if(ih(P))if(uh)ie=_S;else{ie=SS;var we=bS}else H=P.nodeName,!H||H.toLowerCase()!=="input"||P.type!=="checkbox"&&P.type!=="radio"?B&&Ks(B.elementType)&&(ie=ch):ie=xS;if(ie&&(ie=ie(e,B))){sh(K,ie,a,I);break e}we&&we(e,P,B),e==="focusout"&&B&&P.type==="number"&&B.memoizedProps.value!=null&&Xs(P,"number",P.value)}switch(we=B?uo(B):window,e){case"focusin":(ih(we)||we.contentEditable==="true")&&(sr=we,sc=B,So=null);break;case"focusout":So=sc=sr=null;break;case"mousedown":cc=!0;break;case"contextmenu":case"mouseup":case"dragend":cc=!1,yh(K,a,I);break;case"selectionchange":if(ES)break;case"keydown":case"keyup":yh(K,a,I)}var he;if(rc)e:{switch(e){case"compositionstart":var ve="onCompositionStart";break e;case"compositionend":ve="onCompositionEnd";break e;case"compositionupdate":ve="onCompositionUpdate";break e}ve=void 0}else ir?oh(e,a)&&(ve="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ve="onCompositionStart");ve&&(nh&&a.locale!=="ko"&&(ir||ve!=="onCompositionStart"?ve==="onCompositionEnd"&&ir&&(he=Fd()):(Kn=I,Js="value"in Kn?Kn.value:Kn.textContent,ir=!0)),we=Ri(B,ve),0<we.length&&(ve=new eh(ve,e,null,a,I),K.push({event:ve,listeners:we}),he?ve.data=he:(he=lh(a),he!==null&&(ve.data=he)))),(he=mS?pS(e,a):vS(e,a))&&(ve=Ri(B,"onBeforeInput"),0<ve.length&&(we=new eh("onBeforeInput","beforeinput",null,a,I),K.push({event:we,listeners:ve}),we.data=he)),lx(K,e,B,a,I)}Vp(K,t)})}function Ko(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Ri(e,t){for(var a=t+"Capture",r=[];e!==null;){var s=e,u=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||u===null||(s=fo(e,a),s!=null&&r.unshift(Ko(e,s,u)),s=fo(e,t),s!=null&&r.push(Ko(e,s,u))),e.tag===3)return r;e=e.return}return[]}function Nr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Yp(e,t,a,r,s){for(var u=t._reactName,y=[];a!==null&&a!==r;){var x=a,M=x.alternate,B=x.stateNode;if(x=x.tag,M!==null&&M===r)break;x!==5&&x!==26&&x!==27||B===null||(M=B,s?(B=fo(a,u),B!=null&&y.unshift(Ko(a,B,M))):s||(B=fo(a,u),B!=null&&y.push(Ko(a,B,M)))),a=a.return}y.length!==0&&e.push({event:t,listeners:y})}var ux=/\r\n?/g,fx=/\u0000|\uFFFD/g;function $p(e){return(typeof e=="string"?e:""+e).replace(ux,`
`).replace(fx,"")}function Ip(e,t){return t=$p(t),$p(e)===t}function Ci(){}function Ue(e,t,a,r,s,u){switch(a){case"children":typeof r=="string"?t==="body"||t==="textarea"&&r===""||rr(e,r):(typeof r=="number"||typeof r=="bigint")&&t!=="body"&&rr(e,""+r);break;case"className":Dl(e,"class",r);break;case"tabIndex":Dl(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Dl(e,a,r);break;case"style":Kd(e,r,u);break;case"data":if(t!=="object"){Dl(e,"data",r);break}case"src":case"href":if(r===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(a);break}r=jl(""+r),e.setAttribute(a,r);break;case"action":case"formAction":if(typeof r=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(t!=="input"&&Ue(e,t,"name",s.name,s,null),Ue(e,t,"formEncType",s.formEncType,s,null),Ue(e,t,"formMethod",s.formMethod,s,null),Ue(e,t,"formTarget",s.formTarget,s,null)):(Ue(e,t,"encType",s.encType,s,null),Ue(e,t,"method",s.method,s,null),Ue(e,t,"target",s.target,s,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(a);break}r=jl(""+r),e.setAttribute(a,r);break;case"onClick":r!=null&&(e.onclick=Ci);break;case"onScroll":r!=null&&Re("scroll",e);break;case"onScrollEnd":r!=null&&Re("scrollend",e);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(i(61));if(a=r.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"multiple":e.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":e.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){e.removeAttribute("xlink:href");break}a=jl(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,""+r):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":r===!0?e.setAttribute(a,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(a,r):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?e.setAttribute(a,r):e.removeAttribute(a);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?e.removeAttribute(a):e.setAttribute(a,r);break;case"popover":Re("beforetoggle",e),Re("toggle",e),Ol(e,"popover",r);break;case"xlinkActuate":En(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":En(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":En(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":En(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":En(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":En(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":En(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":En(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":En(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Ol(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Gb.get(a)||a,Ol(e,a,r))}}function Nu(e,t,a,r,s,u){switch(a){case"style":Kd(e,r,u);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(i(61));if(a=r.__html,a!=null){if(s.children!=null)throw Error(i(60));e.innerHTML=a}}break;case"children":typeof r=="string"?rr(e,r):(typeof r=="number"||typeof r=="bigint")&&rr(e,""+r);break;case"onScroll":r!=null&&Re("scroll",e);break;case"onScrollEnd":r!=null&&Re("scrollend",e);break;case"onClick":r!=null&&(e.onclick=Ci);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!kd.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),t=a.slice(2,s?a.length-7:void 0),u=e[_t]||null,u=u!=null?u[a]:null,typeof u=="function"&&e.removeEventListener(t,u,s),typeof r=="function")){typeof u!="function"&&u!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,r,s);break e}a in e?e[a]=r:r===!0?e.setAttribute(a,""):Ol(e,a,r)}}}function st(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Re("error",e),Re("load",e);var r=!1,s=!1,u;for(u in a)if(a.hasOwnProperty(u)){var y=a[u];if(y!=null)switch(u){case"src":r=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ue(e,t,u,y,a,null)}}s&&Ue(e,t,"srcSet",a.srcSet,a,null),r&&Ue(e,t,"src",a.src,a,null);return;case"input":Re("invalid",e);var x=u=y=s=null,M=null,B=null;for(r in a)if(a.hasOwnProperty(r)){var I=a[r];if(I!=null)switch(r){case"name":s=I;break;case"type":y=I;break;case"checked":M=I;break;case"defaultChecked":B=I;break;case"value":u=I;break;case"defaultValue":x=I;break;case"children":case"dangerouslySetInnerHTML":if(I!=null)throw Error(i(137,t));break;default:Ue(e,t,r,I,a,null)}}Yd(e,u,x,M,B,y,s,!1),Nl(e);return;case"select":Re("invalid",e),r=y=u=null;for(s in a)if(a.hasOwnProperty(s)&&(x=a[s],x!=null))switch(s){case"value":u=x;break;case"defaultValue":y=x;break;case"multiple":r=x;default:Ue(e,t,s,x,a,null)}t=u,a=y,e.multiple=!!r,t!=null?ar(e,!!r,t,!1):a!=null&&ar(e,!!r,a,!0);return;case"textarea":Re("invalid",e),u=s=r=null;for(y in a)if(a.hasOwnProperty(y)&&(x=a[y],x!=null))switch(y){case"value":r=x;break;case"defaultValue":s=x;break;case"children":u=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(i(91));break;default:Ue(e,t,y,x,a,null)}Id(e,r,s,u),Nl(e);return;case"option":for(M in a)if(a.hasOwnProperty(M)&&(r=a[M],r!=null))switch(M){case"selected":e.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:Ue(e,t,M,r,a,null)}return;case"dialog":Re("beforetoggle",e),Re("toggle",e),Re("cancel",e),Re("close",e);break;case"iframe":case"object":Re("load",e);break;case"video":case"audio":for(r=0;r<Xo.length;r++)Re(Xo[r],e);break;case"image":Re("error",e),Re("load",e);break;case"details":Re("toggle",e);break;case"embed":case"source":case"link":Re("error",e),Re("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(B in a)if(a.hasOwnProperty(B)&&(r=a[B],r!=null))switch(B){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ue(e,t,B,r,a,null)}return;default:if(Ks(t)){for(I in a)a.hasOwnProperty(I)&&(r=a[I],r!==void 0&&Nu(e,t,I,r,a,void 0));return}}for(x in a)a.hasOwnProperty(x)&&(r=a[x],r!=null&&Ue(e,t,x,r,a,null))}function dx(e,t,a,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,u=null,y=null,x=null,M=null,B=null,I=null;for(H in a){var K=a[H];if(a.hasOwnProperty(H)&&K!=null)switch(H){case"checked":break;case"value":break;case"defaultValue":M=K;default:r.hasOwnProperty(H)||Ue(e,t,H,null,r,K)}}for(var P in r){var H=r[P];if(K=a[P],r.hasOwnProperty(P)&&(H!=null||K!=null))switch(P){case"type":u=H;break;case"name":s=H;break;case"checked":B=H;break;case"defaultChecked":I=H;break;case"value":y=H;break;case"defaultValue":x=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(i(137,t));break;default:H!==K&&Ue(e,t,P,H,r,K)}}Is(e,y,x,M,B,I,u,s);return;case"select":H=y=x=P=null;for(u in a)if(M=a[u],a.hasOwnProperty(u)&&M!=null)switch(u){case"value":break;case"multiple":H=M;default:r.hasOwnProperty(u)||Ue(e,t,u,null,r,M)}for(s in r)if(u=r[s],M=a[s],r.hasOwnProperty(s)&&(u!=null||M!=null))switch(s){case"value":P=u;break;case"defaultValue":x=u;break;case"multiple":y=u;default:u!==M&&Ue(e,t,s,u,r,M)}t=x,a=y,r=H,P!=null?ar(e,!!a,P,!1):!!r!=!!a&&(t!=null?ar(e,!!a,t,!0):ar(e,!!a,a?[]:"",!1));return;case"textarea":H=P=null;for(x in a)if(s=a[x],a.hasOwnProperty(x)&&s!=null&&!r.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Ue(e,t,x,null,r,s)}for(y in r)if(s=r[y],u=a[y],r.hasOwnProperty(y)&&(s!=null||u!=null))switch(y){case"value":P=s;break;case"defaultValue":H=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(i(91));break;default:s!==u&&Ue(e,t,y,s,r,u)}$d(e,P,H);return;case"option":for(var ye in a)if(P=a[ye],a.hasOwnProperty(ye)&&P!=null&&!r.hasOwnProperty(ye))switch(ye){case"selected":e.selected=!1;break;default:Ue(e,t,ye,null,r,P)}for(M in r)if(P=r[M],H=a[M],r.hasOwnProperty(M)&&P!==H&&(P!=null||H!=null))switch(M){case"selected":e.selected=P&&typeof P!="function"&&typeof P!="symbol";break;default:Ue(e,t,M,P,r,H)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var pe in a)P=a[pe],a.hasOwnProperty(pe)&&P!=null&&!r.hasOwnProperty(pe)&&Ue(e,t,pe,null,r,P);for(B in r)if(P=r[B],H=a[B],r.hasOwnProperty(B)&&P!==H&&(P!=null||H!=null))switch(B){case"children":case"dangerouslySetInnerHTML":if(P!=null)throw Error(i(137,t));break;default:Ue(e,t,B,P,r,H)}return;default:if(Ks(t)){for(var ke in a)P=a[ke],a.hasOwnProperty(ke)&&P!==void 0&&!r.hasOwnProperty(ke)&&Nu(e,t,ke,void 0,r,P);for(I in r)P=r[I],H=a[I],!r.hasOwnProperty(I)||P===H||P===void 0&&H===void 0||Nu(e,t,I,P,r,H);return}}for(var j in a)P=a[j],a.hasOwnProperty(j)&&P!=null&&!r.hasOwnProperty(j)&&Ue(e,t,j,null,r,P);for(K in r)P=r[K],H=a[K],!r.hasOwnProperty(K)||P===H||P==null&&H==null||Ue(e,t,K,P,r,H)}var Lu=null,ju=null;function Ai(e){return e.nodeType===9?e:e.ownerDocument}function Xp(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Kp(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function zu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Uu=null;function hx(){var e=window.event;return e&&e.type==="popstate"?e===Uu?!1:(Uu=e,!0):(Uu=null,!1)}var Qp=typeof setTimeout=="function"?setTimeout:void 0,mx=typeof clearTimeout=="function"?clearTimeout:void 0,Zp=typeof Promise=="function"?Promise:void 0,px=typeof queueMicrotask=="function"?queueMicrotask:typeof Zp<"u"?function(e){return Zp.resolve(null).then(e).catch(vx)}:Qp;function vx(e){setTimeout(function(){throw e})}function ua(e){return e==="head"}function Fp(e,t){var a=t,r=0,s=0;do{var u=a.nextSibling;if(e.removeChild(a),u&&u.nodeType===8)if(a=u.data,a==="/$"){if(0<r&&8>r){a=r;var y=e.ownerDocument;if(a&1&&Qo(y.documentElement),a&2&&Qo(y.body),a&4)for(a=y.head,Qo(a),y=a.firstChild;y;){var x=y.nextSibling,M=y.nodeName;y[co]||M==="SCRIPT"||M==="STYLE"||M==="LINK"&&y.rel.toLowerCase()==="stylesheet"||a.removeChild(y),y=x}}if(s===0){e.removeChild(u),al(t);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:r=a.charCodeAt(0)-48;else r=0;a=u}while(a);al(t)}function ku(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":ku(a),Vs(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function gx(e,t,a,r){for(;e.nodeType===1;){var s=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(r){if(!e[co])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=nn(e.nextSibling),e===null)break}return null}function yx(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=nn(e.nextSibling),e===null))return null;return e}function Bu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function bx(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var r=function(){t(),a.removeEventListener("DOMContentLoaded",r)};a.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}function nn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Pu=null;function Wp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Jp(e,t,a){switch(t=Ai(a),e){case"html":if(e=t.documentElement,!e)throw Error(i(452));return e;case"head":if(e=t.head,!e)throw Error(i(453));return e;case"body":if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function Qo(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Vs(e)}var Kt=new Map,ev=new Set;function Mi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Bn=$.d;$.d={f:Sx,r:xx,D:_x,C:wx,L:Ex,m:Rx,X:Ax,S:Cx,M:Mx};function Sx(){var e=Bn.f(),t=bi();return e||t}function xx(e){var t=Ja(e);t!==null&&t.tag===5&&t.type==="form"?Sm(t):Bn.r(e)}var Lr=typeof document>"u"?null:document;function tv(e,t,a){var r=Lr;if(r&&typeof t=="string"&&t){var s=Gt(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),ev.has(s)||(ev.add(s),e={rel:e,crossOrigin:a,href:t},r.querySelector(s)===null&&(t=r.createElement("link"),st(t,"link",e),tt(t),r.head.appendChild(t)))}}function _x(e){Bn.D(e),tv("dns-prefetch",e,null)}function wx(e,t){Bn.C(e,t),tv("preconnect",e,t)}function Ex(e,t,a){Bn.L(e,t,a);var r=Lr;if(r&&e&&t){var s='link[rel="preload"][as="'+Gt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+Gt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+Gt(a.imageSizes)+'"]')):s+='[href="'+Gt(e)+'"]';var u=s;switch(t){case"style":u=jr(e);break;case"script":u=zr(e)}Kt.has(u)||(e=v({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Kt.set(u,e),r.querySelector(s)!==null||t==="style"&&r.querySelector(Zo(u))||t==="script"&&r.querySelector(Fo(u))||(t=r.createElement("link"),st(t,"link",e),tt(t),r.head.appendChild(t)))}}function Rx(e,t){Bn.m(e,t);var a=Lr;if(a&&e){var r=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+Gt(r)+'"][href="'+Gt(e)+'"]',u=s;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=zr(e)}if(!Kt.has(u)&&(e=v({rel:"modulepreload",href:e},t),Kt.set(u,e),a.querySelector(s)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Fo(u)))return}r=a.createElement("link"),st(r,"link",e),tt(r),a.head.appendChild(r)}}}function Cx(e,t,a){Bn.S(e,t,a);var r=Lr;if(r&&e){var s=er(r).hoistableStyles,u=jr(e);t=t||"default";var y=s.get(u);if(!y){var x={loading:0,preload:null};if(y=r.querySelector(Zo(u)))x.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Kt.get(u))&&Hu(e,a);var M=y=r.createElement("link");tt(M),st(M,"link",e),M._p=new Promise(function(B,I){M.onload=B,M.onerror=I}),M.addEventListener("load",function(){x.loading|=1}),M.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Ti(y,t,r)}y={type:"stylesheet",instance:y,count:1,state:x},s.set(u,y)}}}function Ax(e,t){Bn.X(e,t);var a=Lr;if(a&&e){var r=er(a).hoistableScripts,s=zr(e),u=r.get(s);u||(u=a.querySelector(Fo(s)),u||(e=v({src:e,async:!0},t),(t=Kt.get(s))&&Gu(e,t),u=a.createElement("script"),tt(u),st(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},r.set(s,u))}}function Mx(e,t){Bn.M(e,t);var a=Lr;if(a&&e){var r=er(a).hoistableScripts,s=zr(e),u=r.get(s);u||(u=a.querySelector(Fo(s)),u||(e=v({src:e,async:!0,type:"module"},t),(t=Kt.get(s))&&Gu(e,t),u=a.createElement("script"),tt(u),st(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},r.set(s,u))}}function nv(e,t,a,r){var s=(s=ne.current)?Mi(s):null;if(!s)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=jr(a.href),a=er(s).hoistableStyles,r=a.get(t),r||(r={type:"style",instance:null,count:0,state:null},a.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=jr(a.href);var u=er(s).hoistableStyles,y=u.get(e);if(y||(s=s.ownerDocument||s,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,y),(u=s.querySelector(Zo(e)))&&!u._p&&(y.instance=u,y.state.loading=5),Kt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Kt.set(e,a),u||Tx(s,e,a,y.state))),t&&r===null)throw Error(i(528,""));return y}if(t&&r!==null)throw Error(i(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=zr(a),a=er(s).hoistableScripts,r=a.get(t),r||(r={type:"script",instance:null,count:0,state:null},a.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function jr(e){return'href="'+Gt(e)+'"'}function Zo(e){return'link[rel="stylesheet"]['+e+"]"}function av(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function Tx(e,t,a,r){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?r.loading=1:(t=e.createElement("link"),r.preload=t,t.addEventListener("load",function(){return r.loading|=1}),t.addEventListener("error",function(){return r.loading|=2}),st(t,"link",a),tt(t),e.head.appendChild(t))}function zr(e){return'[src="'+Gt(e)+'"]'}function Fo(e){return"script[async]"+e}function rv(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+Gt(a.href)+'"]');if(r)return t.instance=r,tt(r),r;var s=v({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return r=(e.ownerDocument||e).createElement("style"),tt(r),st(r,"style",s),Ti(r,a.precedence,e),t.instance=r;case"stylesheet":s=jr(a.href);var u=e.querySelector(Zo(s));if(u)return t.state.loading|=4,t.instance=u,tt(u),u;r=av(a),(s=Kt.get(s))&&Hu(r,s),u=(e.ownerDocument||e).createElement("link"),tt(u);var y=u;return y._p=new Promise(function(x,M){y.onload=x,y.onerror=M}),st(u,"link",r),t.state.loading|=4,Ti(u,a.precedence,e),t.instance=u;case"script":return u=zr(a.src),(s=e.querySelector(Fo(u)))?(t.instance=s,tt(s),s):(r=a,(s=Kt.get(u))&&(r=v({},a),Gu(r,s)),e=e.ownerDocument||e,s=e.createElement("script"),tt(s),st(s,"link",r),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(i(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(r=t.instance,t.state.loading|=4,Ti(r,a.precedence,e));return t.instance}function Ti(e,t,a){for(var r=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=r.length?r[r.length-1]:null,u=s,y=0;y<r.length;y++){var x=r[y];if(x.dataset.precedence===t)u=x;else if(u!==s)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Hu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Gu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Oi=null;function ov(e,t,a){if(Oi===null){var r=new Map,s=Oi=new Map;s.set(a,r)}else s=Oi,r=s.get(a),r||(r=new Map,s.set(a,r));if(r.has(e))return r;for(r.set(e,null),a=a.getElementsByTagName(e),s=0;s<a.length;s++){var u=a[s];if(!(u[co]||u[ft]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var y=u.getAttribute(t)||"";y=e+y;var x=r.get(y);x?x.push(u):r.set(y,[u])}}return r}function lv(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Ox(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function iv(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Wo=null;function Dx(){}function Nx(e,t,a){if(Wo===null)throw Error(i(475));var r=Wo;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=jr(a.href),u=e.querySelector(Zo(s));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(r.count++,r=Di.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=u,tt(u);return}u=e.ownerDocument||e,a=av(a),(s=Kt.get(s))&&Hu(a,s),u=u.createElement("link"),tt(u);var y=u;y._p=new Promise(function(x,M){y.onload=x,y.onerror=M}),st(u,"link",a),t.instance=u}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(r.count++,t=Di.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}function Lx(){if(Wo===null)throw Error(i(475));var e=Wo;return e.stylesheets&&e.count===0&&Vu(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Vu(e,e.stylesheets),e.unsuspend){var r=e.unsuspend;e.unsuspend=null,r()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Di(){if(this.count--,this.count===0){if(this.stylesheets)Vu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ni=null;function Vu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ni=new Map,t.forEach(jx,e),Ni=null,Di.call(e))}function jx(e,t){if(!(t.state.loading&4)){var a=Ni.get(e);if(a)var r=a.get(null);else{a=new Map,Ni.set(e,a);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<s.length;u++){var y=s[u];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(a.set(y.dataset.precedence,y),r=y)}r&&a.set(null,r)}s=t.instance,y=s.getAttribute("data-precedence"),u=a.get(y)||r,u===r&&a.set(null,s),a.set(y,s),this.count++,r=Di.bind(this),s.addEventListener("load",r),s.addEventListener("error",r),u?u.parentNode.insertBefore(s,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var Jo={$$typeof:L,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function zx(e,t,a,r,s,u,y,x){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Bs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bs(0),this.hiddenUpdates=Bs(null),this.identifierPrefix=r,this.onUncaughtError=s,this.onCaughtError=u,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function sv(e,t,a,r,s,u,y,x,M,B,I,K){return e=new zx(e,t,a,y,x,M,B,K),t=1,u===!0&&(t|=24),u=Ot(3,null,null,t),e.current=u,u.stateNode=e,t=wc(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:r,isDehydrated:a,cache:t},Ac(u),e}function cv(e){return e?(e=dr,e):dr}function uv(e,t,a,r,s,u){s=cv(s),r.context===null?r.context=s:r.pendingContext=s,r=Fn(t),r.payload={element:a},u=u===void 0?null:u,u!==null&&(r.callback=u),a=Wn(e,r,t),a!==null&&(zt(a,e,t),To(a,e,t))}function fv(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function qu(e,t){fv(e,t),(e=e.alternate)&&fv(e,t)}function dv(e){if(e.tag===13){var t=fr(e,67108864);t!==null&&zt(t,e,67108864),qu(e,67108864)}}var Li=!0;function Ux(e,t,a,r){var s=O.T;O.T=null;var u=$.p;try{$.p=2,Yu(e,t,a,r)}finally{$.p=u,O.T=s}}function kx(e,t,a,r){var s=O.T;O.T=null;var u=$.p;try{$.p=8,Yu(e,t,a,r)}finally{$.p=u,O.T=s}}function Yu(e,t,a,r){if(Li){var s=$u(r);if(s===null)Du(e,t,r,ji,a),mv(e,r);else if(Px(s,e,t,a,r))r.stopPropagation();else if(mv(e,r),t&4&&-1<Bx.indexOf(e)){for(;s!==null;){var u=Ja(s);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var y=wa(u.pendingLanes);if(y!==0){var x=u;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var M=1<<31-Mt(y);x.entanglements[1]|=M,y&=~M}mn(u),(Ne&6)===0&&(gi=xt()+500,Io(0))}}break;case 13:x=fr(u,2),x!==null&&zt(x,u,2),bi(),qu(u,2)}if(u=$u(r),u===null&&Du(e,t,r,ji,a),u===s)break;s=u}s!==null&&r.stopPropagation()}else Du(e,t,r,null,a)}}function $u(e){return e=Zs(e),Iu(e)}var ji=null;function Iu(e){if(ji=null,e=Wa(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ji=e,null}function hv(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch($n()){case Pt:return 2;case oo:return 8;case _a:case Ge:return 32;case ot:return 268435456;default:return 32}default:return 32}}var Xu=!1,fa=null,da=null,ha=null,el=new Map,tl=new Map,ma=[],Bx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function mv(e,t){switch(e){case"focusin":case"focusout":fa=null;break;case"dragenter":case"dragleave":da=null;break;case"mouseover":case"mouseout":ha=null;break;case"pointerover":case"pointerout":el.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":tl.delete(t.pointerId)}}function nl(e,t,a,r,s,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:a,eventSystemFlags:r,nativeEvent:u,targetContainers:[s]},t!==null&&(t=Ja(t),t!==null&&dv(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Px(e,t,a,r,s){switch(t){case"focusin":return fa=nl(fa,e,t,a,r,s),!0;case"dragenter":return da=nl(da,e,t,a,r,s),!0;case"mouseover":return ha=nl(ha,e,t,a,r,s),!0;case"pointerover":var u=s.pointerId;return el.set(u,nl(el.get(u)||null,e,t,a,r,s)),!0;case"gotpointercapture":return u=s.pointerId,tl.set(u,nl(tl.get(u)||null,e,t,a,r,s)),!0}return!1}function pv(e){var t=Wa(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,Nb(e.priority,function(){if(a.tag===13){var r=jt();r=Ps(r);var s=fr(a,r);s!==null&&zt(s,a,r),qu(a,r)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function zi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=$u(e.nativeEvent);if(a===null){a=e.nativeEvent;var r=new a.constructor(a.type,a);Qs=r,a.target.dispatchEvent(r),Qs=null}else return t=Ja(a),t!==null&&dv(t),e.blockedOn=a,!1;t.shift()}return!0}function vv(e,t,a){zi(e)&&a.delete(t)}function Hx(){Xu=!1,fa!==null&&zi(fa)&&(fa=null),da!==null&&zi(da)&&(da=null),ha!==null&&zi(ha)&&(ha=null),el.forEach(vv),tl.forEach(vv)}function Ui(e,t){e.blockedOn===t&&(e.blockedOn=null,Xu||(Xu=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Hx)))}var ki=null;function gv(e){ki!==e&&(ki=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){ki===e&&(ki=null);for(var t=0;t<e.length;t+=3){var a=e[t],r=e[t+1],s=e[t+2];if(typeof r!="function"){if(Iu(r||a)===null)continue;break}var u=Ja(a);u!==null&&(e.splice(t,3),t-=3,Ic(u,{pending:!0,data:s,method:a.method,action:r},r,s))}}))}function al(e){function t(M){return Ui(M,e)}fa!==null&&Ui(fa,e),da!==null&&Ui(da,e),ha!==null&&Ui(ha,e),el.forEach(t),tl.forEach(t);for(var a=0;a<ma.length;a++){var r=ma[a];r.blockedOn===e&&(r.blockedOn=null)}for(;0<ma.length&&(a=ma[0],a.blockedOn===null);)pv(a),a.blockedOn===null&&ma.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(r=0;r<a.length;r+=3){var s=a[r],u=a[r+1],y=s[_t]||null;if(typeof u=="function")y||gv(a);else if(y){var x=null;if(u&&u.hasAttribute("formAction")){if(s=u,y=u[_t]||null)x=y.formAction;else if(Iu(s)!==null)continue}else x=y.action;typeof x=="function"?a[r+1]=x:(a.splice(r,3),r-=3),gv(a)}}}function Ku(e){this._internalRoot=e}Bi.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var a=t.current,r=jt();uv(a,r,e,t,null,null)},Bi.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;uv(e.current,2,null,e,null,null),bi(),t[Fa]=null}};function Bi(e){this._internalRoot=e}Bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=jd();e={blockedOn:null,target:e,priority:t};for(var a=0;a<ma.length&&t!==0&&t<ma[a].priority;a++);ma.splice(a,0,e),a===0&&pv(e)}};var yv=l.version;if(yv!=="19.1.0")throw Error(i(527,yv,"19.1.0"));$.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=m(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var Gx={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Pi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Pi.isDisabled&&Pi.supportsFiber)try{lo=Pi.inject(Gx),At=Pi}catch{}}return ol.createRoot=function(e,t){if(!c(e))throw Error(i(299));var a=!1,r="",s=jm,u=zm,y=Um,x=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(y=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(x=t.unstable_transitionCallbacks)),t=sv(e,1,!1,null,null,a,r,s,u,y,x,null),e[Fa]=t.current,Ou(e),new Ku(t)},ol.hydrateRoot=function(e,t,a){if(!c(e))throw Error(i(299));var r=!1,s="",u=jm,y=zm,x=Um,M=null,B=null;return a!=null&&(a.unstable_strictMode===!0&&(r=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(y=a.onCaughtError),a.onRecoverableError!==void 0&&(x=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(M=a.unstable_transitionCallbacks),a.formState!==void 0&&(B=a.formState)),t=sv(e,1,!0,t,a??null,r,s,u,y,x,M,B),t.context=cv(null),a=t.current,r=jt(),r=Ps(r),s=Fn(r),s.callback=null,Wn(a,s,r),a=r,t.current.lanes=a,so(t,a),mn(t),e[Fa]=t.current,Ou(e),new Bi(t)},ol.version="19.1.0",ol}var Tv;function Jx(){if(Tv)return Fu.exports;Tv=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),Fu.exports=Wx(),Fu.exports}var e1=Jx();const t1=Kf(e1);var n1="Invariant failed";function Vn(n,l){if(!n)throw new Error(n1)}const Vr=new WeakMap,Wi=new WeakMap,ns={current:[]};let tf=!1,fl=0;const cl=new Set,Hi=new Map;function Ug(n){const l=Array.from(n).sort((o,i)=>o instanceof qr&&o.options.deps.includes(i)?1:i instanceof qr&&i.options.deps.includes(o)?-1:0);for(const o of l){if(ns.current.includes(o))continue;ns.current.push(o),o.recompute();const i=Wi.get(o);if(i)for(const c of i){const f=Vr.get(c);f&&Ug(f)}}}function a1(n){n.listeners.forEach(l=>l({prevVal:n.prevState,currentVal:n.state}))}function r1(n){n.listeners.forEach(l=>l({prevVal:n.prevState,currentVal:n.state}))}function kg(n){if(fl>0&&!Hi.has(n)&&Hi.set(n,n.prevState),cl.add(n),!(fl>0)&&!tf)try{for(tf=!0;cl.size>0;){const l=Array.from(cl);cl.clear();for(const o of l){const i=Hi.get(o)??o.prevState;o.prevState=i,a1(o)}for(const o of l){const i=Vr.get(o);i&&(ns.current.push(o),Ug(i))}for(const o of l){const i=Vr.get(o);if(i)for(const c of i)r1(c)}}}finally{tf=!1,ns.current=[],Hi.clear()}}function Ov(n){fl++;try{n()}finally{if(fl--,fl===0){const l=Array.from(cl)[0];l&&kg(l)}}}function o1(n){return typeof n=="function"}class Mf{constructor(l,o){this.listeners=new Set,this.subscribe=i=>{var c,f;this.listeners.add(i);const d=(f=(c=this.options)==null?void 0:c.onSubscribe)==null?void 0:f.call(c,i,this);return()=>{this.listeners.delete(i),d==null||d()}},this.prevState=l,this.state=l,this.options=o}setState(l){var o,i,c;this.prevState=this.state,(o=this.options)!=null&&o.updateFn?this.state=this.options.updateFn(this.prevState)(l):o1(l)?this.state=l(this.prevState):this.state=l,(c=(i=this.options)==null?void 0:i.onUpdate)==null||c.call(i),kg(this)}}class qr{constructor(l){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const o=[],i=[];for(const c of this.options.deps)o.push(c.prevState),i.push(c.state);return this.lastSeenDepValues=i,{prevDepVals:o,currDepVals:i,prevVal:this.prevState??void 0}},this.recompute=()=>{var o,i;this.prevState=this.state;const{prevDepVals:c,currDepVals:f,prevVal:d}=this.getDepVals();this.state=this.options.fn({prevDepVals:c,currDepVals:f,prevVal:d}),(i=(o=this.options).onUpdate)==null||i.call(o)},this.checkIfRecalculationNeededDeeply=()=>{for(const f of this.options.deps)f instanceof qr&&f.checkIfRecalculationNeededDeeply();let o=!1;const i=this.lastSeenDepValues,{currDepVals:c}=this.getDepVals();for(let f=0;f<c.length;f++)if(c[f]!==i[f]){o=!0;break}o&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const o of this._subscriptions)o()}),this.subscribe=o=>{var i,c;this.listeners.add(o);const f=(c=(i=this.options).onSubscribe)==null?void 0:c.call(i,o,this);return()=>{this.listeners.delete(o),f==null||f()}},this.options=l,this.state=l.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(l=this.options.deps){for(const o of l)if(o instanceof qr)o.registerOnGraph(),this.registerOnGraph(o.options.deps);else if(o instanceof Mf){let i=Vr.get(o);i||(i=new Set,Vr.set(o,i)),i.add(this);let c=Wi.get(this);c||(c=new Set,Wi.set(this,c)),c.add(o)}}unregisterFromGraph(l=this.options.deps){for(const o of l)if(o instanceof qr)this.unregisterFromGraph(o.options.deps);else if(o instanceof Mf){const i=Vr.get(o);i&&i.delete(this);const c=Wi.get(this);c&&c.delete(o)}}}const ya="__TSR_index",Dv="popstate",Nv="beforeunload";function Bg(n){let l=n.getLocation();const o=new Set,i=d=>{l=n.getLocation(),o.forEach(h=>h({location:l,action:d}))},c=d=>{n.notifyOnIndexChange??!0?i(d):l=n.getLocation()},f=async({task:d,navigateOpts:h,...m})=>{var p,v;if((h==null?void 0:h.ignoreBlocker)??!1){d();return}const b=((p=n.getBlockers)==null?void 0:p.call(n))??[],w=m.type==="PUSH"||m.type==="REPLACE";if(typeof document<"u"&&b.length&&w)for(const R of b){const E=dl(m.path,m.state);if(await R.blockerFn({currentLocation:l,nextLocation:E,action:m.type})){(v=n.onBlocked)==null||v.call(n);return}}d()};return{get location(){return l},get length(){return n.getLength()},subscribers:o,subscribe:d=>(o.add(d),()=>{o.delete(d)}),push:(d,h,m)=>{const p=l.state[ya];h=Tf(p+1,h),f({task:()=>{n.pushState(d,h),i({type:"PUSH"})},navigateOpts:m,type:"PUSH",path:d,state:h})},replace:(d,h,m)=>{const p=l.state[ya];h=Tf(p,h),f({task:()=>{n.replaceState(d,h),i({type:"REPLACE"})},navigateOpts:m,type:"REPLACE",path:d,state:h})},go:(d,h)=>{f({task:()=>{n.go(d),c({type:"GO",index:d})},navigateOpts:h,type:"GO"})},back:d=>{f({task:()=>{n.back((d==null?void 0:d.ignoreBlocker)??!1),c({type:"BACK"})},navigateOpts:d,type:"BACK"})},forward:d=>{f({task:()=>{n.forward((d==null?void 0:d.ignoreBlocker)??!1),c({type:"FORWARD"})},navigateOpts:d,type:"FORWARD"})},canGoBack:()=>l.state[ya]!==0,createHref:d=>n.createHref(d),block:d=>{var h;if(!n.setBlockers)return()=>{};const m=((h=n.getBlockers)==null?void 0:h.call(n))??[];return n.setBlockers([...m,d]),()=>{var p,v;const g=((p=n.getBlockers)==null?void 0:p.call(n))??[];(v=n.setBlockers)==null||v.call(n,g.filter(b=>b!==d))}},flush:()=>{var d;return(d=n.flush)==null?void 0:d.call(n)},destroy:()=>{var d;return(d=n.destroy)==null?void 0:d.call(n)},notify:i}}function Tf(n,l){l||(l={});const o=Qf();return{...l,key:o,__TSR_key:o,[ya]:n}}function l1(n){var l,o;const i=typeof document<"u"?window:void 0,c=i.history.pushState,f=i.history.replaceState;let d=[];const h=()=>d,m=Z=>d=Z,p=Z=>Z,v=()=>dl(`${i.location.pathname}${i.location.search}${i.location.hash}`,i.history.state);if(!((l=i.history.state)!=null&&l.__TSR_key)&&!((o=i.history.state)!=null&&o.key)){const Z=Qf();i.history.replaceState({[ya]:0,key:Z,__TSR_key:Z},"")}let g=v(),b,w=!1,R=!1,E=!1,C=!1;const T=()=>g;let z,L;const N=()=>{z&&(ae._ignoreSubscribers=!0,(z.isPush?i.history.pushState:i.history.replaceState)(z.state,"",z.href),ae._ignoreSubscribers=!1,z=void 0,L=void 0,b=void 0)},G=(Z,re,te)=>{const le=p(re);L||(b=g),g=dl(re,te),z={href:le,state:te,isPush:(z==null?void 0:z.isPush)||Z==="push"},L||(L=Promise.resolve().then(()=>N()))},W=Z=>{g=v(),ae.notify({type:Z})},V=async()=>{if(R){R=!1;return}const Z=v(),re=Z.state[ya]-g.state[ya],te=re===1,le=re===-1,ue=!te&&!le||w;w=!1;const de=ue?"GO":le?"BACK":"FORWARD",O=ue?{type:"GO",index:re}:{type:le?"BACK":"FORWARD"};if(E)E=!1;else{const $=h();if(typeof document<"u"&&$.length){for(const U of $)if(await U.blockerFn({currentLocation:g,nextLocation:Z,action:de})){R=!0,i.history.go(1),ae.notify(O);return}}}g=v(),ae.notify(O)},q=Z=>{if(C){C=!1;return}let re=!1;const te=h();if(typeof document<"u"&&te.length)for(const le of te){const ue=le.enableBeforeUnload??!0;if(ue===!0){re=!0;break}if(typeof ue=="function"&&ue()===!0){re=!0;break}}if(re)return Z.preventDefault(),Z.returnValue=""},ae=Bg({getLocation:T,getLength:()=>i.history.length,pushState:(Z,re)=>G("push",Z,re),replaceState:(Z,re)=>G("replace",Z,re),back:Z=>(Z&&(E=!0),C=!0,i.history.back()),forward:Z=>{Z&&(E=!0),C=!0,i.history.forward()},go:Z=>{w=!0,i.history.go(Z)},createHref:Z=>p(Z),flush:N,destroy:()=>{i.history.pushState=c,i.history.replaceState=f,i.removeEventListener(Nv,q,{capture:!0}),i.removeEventListener(Dv,V)},onBlocked:()=>{b&&g!==b&&(g=b)},getBlockers:h,setBlockers:m,notifyOnIndexChange:!1});return i.addEventListener(Nv,q,{capture:!0}),i.addEventListener(Dv,V),i.history.pushState=function(...Z){const re=c.apply(i.history,Z);return ae._ignoreSubscribers||W("PUSH"),re},i.history.replaceState=function(...Z){const re=f.apply(i.history,Z);return ae._ignoreSubscribers||W("REPLACE"),re},ae}function i1(n={initialEntries:["/"]}){const l=n.initialEntries;let o=n.initialIndex?Math.min(Math.max(n.initialIndex,0),l.length-1):l.length-1;const i=l.map((f,d)=>Tf(d,void 0));return Bg({getLocation:()=>dl(l[o],i[o]),getLength:()=>l.length,pushState:(f,d)=>{o<l.length-1&&(l.splice(o+1),i.splice(o+1)),i.push(d),l.push(f),o=Math.max(l.length-1,0)},replaceState:(f,d)=>{i[o]=d,l[o]=f},back:()=>{o=Math.max(o-1,0)},forward:()=>{o=Math.min(o+1,l.length-1)},go:f=>{o=Math.min(Math.max(o+f,0),l.length-1)},createHref:f=>f})}function dl(n,l){const o=n.indexOf("#"),i=n.indexOf("?"),c=Qf();return{href:n,pathname:n.substring(0,o>0?i>0?Math.min(o,i):o:i>0?i:n.length),hash:o>-1?n.substring(o):"",search:i>-1?n.slice(i,o===-1?void 0:o):"",state:l||{[ya]:0,key:c,__TSR_key:c}}}function Qf(){return(Math.random()+1).toString(36).substring(7)}function Of(n){return n[n.length-1]}function s1(n){return typeof n=="function"}function Va(n,l){return s1(n)?n(l):n}function as(n,l){return l.reduce((o,i)=>(o[i]=n[i],o),{})}function Qt(n,l){if(n===l)return n;const o=l,i=zv(n)&&zv(o);if(i||Lv(n)&&Lv(o)){const c=i?n:Object.keys(n).concat(Object.getOwnPropertySymbols(n)),f=c.length,d=i?o:Object.keys(o).concat(Object.getOwnPropertySymbols(o)),h=d.length,m=i?[]:{};let p=0;for(let v=0;v<h;v++){const g=i?v:d[v];(!i&&c.includes(g)||i)&&n[g]===void 0&&o[g]===void 0?(m[g]=void 0,p++):(m[g]=Qt(n[g],o[g]),m[g]===n[g]&&n[g]!==void 0&&p++)}return f===h&&p===f?n:m}return o}function Lv(n){return Df(n)&&Object.getOwnPropertyNames(n).length===Object.keys(n).length}function Df(n){if(!jv(n))return!1;const l=n.constructor;if(typeof l>"u")return!0;const o=l.prototype;return!(!jv(o)||!o.hasOwnProperty("isPrototypeOf"))}function jv(n){return Object.prototype.toString.call(n)==="[object Object]"}function zv(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function Uv(n,l){let o=Object.keys(n);return l&&(o=o.filter(i=>n[i]!==void 0)),o}function Yr(n,l,o){if(n===l)return!0;if(typeof n!=typeof l)return!1;if(Df(n)&&Df(l)){const i=(o==null?void 0:o.ignoreUndefined)??!0,c=Uv(n,i),f=Uv(l,i);return!(o!=null&&o.partial)&&c.length!==f.length?!1:f.every(d=>Yr(n[d],l[d],o))}return Array.isArray(n)&&Array.isArray(l)?n.length!==l.length?!1:!n.some((i,c)=>!Yr(i,l[c],o)):!1}function Hr(n){let l,o;const i=new Promise((c,f)=>{l=c,o=f});return i.status="pending",i.resolve=c=>{i.status="resolved",i.value=c,l(c),n==null||n(c)},i.reject=c=>{i.status="rejected",o(c)},i}function c1(n){return typeof(n==null?void 0:n.message)!="string"?!1:n.message.startsWith("Failed to fetch dynamically imported module")||n.message.startsWith("error loading dynamically imported module")||n.message.startsWith("Importing a module script failed")}const Hn=0,$a=1,Ia=2,Xr=3;function Gn(n){return Zf(n.filter(l=>l!==void 0).join("/"))}function Zf(n){return n.replace(/\/{2,}/g,"/")}function Ff(n){return n==="/"?n:n.replace(/^\/{1,}/,"")}function Kr(n){return n==="/"?n:n.replace(/\/{1,}$/,"")}function nf(n){return Kr(Ff(n))}function rs(n,l){return n!=null&&n.endsWith("/")&&n!=="/"&&n!==`${l}/`?n.slice(0,-1):n}function u1(n,l,o){return rs(n,o)===rs(l,o)}function f1(n){const{type:l,value:o}=n;if(l===Hn)return o;const{prefixSegment:i,suffixSegment:c}=n;if(l===$a){const f=o.substring(1);if(i&&c)return`${i}{$${f}}${c}`;if(i)return`${i}{$${f}}`;if(c)return`{$${f}}${c}`}if(l===Xr){const f=o.substring(1);return i&&c?`${i}{-$${f}}${c}`:i?`${i}{-$${f}}`:c?`{-$${f}}${c}`:`{-$${f}}`}if(l===Ia){if(i&&c)return`${i}{$}${c}`;if(i)return`${i}{$}`;if(c)return`{$}${c}`}return o}function d1({basepath:n,base:l,to:o,trailingSlash:i="never",caseSensitive:c}){var f;l=os(n,l,c),o=os(n,o,c);let d=Qr(l).slice();const h=Qr(o);d.length>1&&((f=Of(d))==null?void 0:f.value)==="/"&&d.pop();for(let v=0,g=h.length;v<g;v++){const b=h[v],w=b.value;w==="/"?v?v===g-1&&d.push(b):d=[b]:w===".."?d.pop():w==="."||d.push(b)}d.length>1&&(Of(d).value==="/"?i==="never"&&d.pop():i==="always"&&d.push({type:Hn,value:"/"}));const m=d.map(f1);return Gn([n,...m])}const h1=/^\$.{1,}$/,m1=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,p1=/^(.*?)\{-(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,v1=/^\$$/,g1=/^(.*?)\{\$\}(.*)$/;function Qr(n){if(!n)return[];n=Zf(n);const l=[];if(n.slice(0,1)==="/"&&(n=n.substring(1),l.push({type:Hn,value:"/"})),!n)return l;const o=n.split("/").filter(Boolean);return l.push(...o.map(i=>{const c=i.match(g1);if(c){const h=c[1],m=c[2];return{type:Ia,value:"$",prefixSegment:h||void 0,suffixSegment:m||void 0}}const f=i.match(p1);if(f){const h=f[1],m=f[2],p=f[3];return{type:Xr,value:m,prefixSegment:h||void 0,suffixSegment:p||void 0}}const d=i.match(m1);if(d){const h=d[1],m=d[2],p=d[3];return{type:$a,value:""+m,prefixSegment:h||void 0,suffixSegment:p||void 0}}if(h1.test(i)){const h=i.substring(1);return{type:$a,value:"$"+h,prefixSegment:void 0,suffixSegment:void 0}}return v1.test(i)?{type:Ia,value:"$",prefixSegment:void 0,suffixSegment:void 0}:{type:Hn,value:i.includes("%25")?i.split("%25").map(h=>decodeURI(h)).join("%25"):decodeURI(i)}})),n.slice(-1)==="/"&&(n=n.substring(1),l.push({type:Hn,value:"/"})),l}function Gi({path:n,params:l,leaveWildcards:o,leaveParams:i,decodeCharMap:c}){const f=Qr(n);function d(v){const g=l[v],b=typeof g=="string";return v==="*"||v==="_splat"?b?encodeURI(g):g:b?y1(g,c):g}let h=!1;const m={},p=Gn(f.map(v=>{if(v.type===Hn)return v.value;if(v.type===Ia){m._splat=l._splat;const g=v.prefixSegment||"",b=v.suffixSegment||"";if(!("_splat"in l))return h=!0,o?`${g}${v.value}${b}`:g||b?`${g}${b}`:void 0;const w=d("_splat");return o?`${g}${v.value}${w??""}${b}`:`${g}${w}${b}`}if(v.type===$a){const g=v.value.substring(1);!h&&!(g in l)&&(h=!0),m[g]=l[g];const b=v.prefixSegment||"",w=v.suffixSegment||"";if(i){const R=d(v.value);return`${b}${v.value}${R??""}${w}`}return`${b}${d(g)??"undefined"}${w}`}if(v.type===Xr){const g=v.value.substring(1),b=v.prefixSegment||"",w=v.suffixSegment||"";if(!(g in l)||l[g]==null)return o?`${b}${g}${w}`:b||w?`${b}${w}`:void 0;if(m[g]=l[g],i){const R=d(v.value);return`${b}${v.value}${R??""}${w}`}return`${b}${d(g)??""}${w}`}return v.value}));return{usedParams:m,interpolatedPath:p,isMissingParams:h}}function y1(n,l){let o=encodeURIComponent(n);if(l)for(const[i,c]of l)o=o.replaceAll(i,c);return o}function Nf(n,l,o){const i=b1(n,l,o);if(!(o.to&&!i))return i??{}}function os(n,l,o=!1){const i=o?n:n.toLowerCase(),c=o?l:l.toLowerCase();switch(!0){case i==="/":return l;case c===i:return"";case l.length<n.length:return l;case c[i.length]!=="/":return l;case c.startsWith(i):return l.slice(n.length);default:return l}}function b1(n,l,{to:o,fuzzy:i,caseSensitive:c}){if(n!=="/"&&!l.startsWith(n))return;l=os(n,l,c),o=os(n,`${o??"$"}`,c);const f=Qr(l.startsWith("/")?l:`/${l}`),d=Qr(o.startsWith("/")?o:`/${o}`),h={};return S1(f,d,h,i,c)?h:void 0}function S1(n,l,o,i,c){var f,d,h;let m=0,p=0;for(;m<n.length||p<l.length;){const v=n[m],g=l[p];if(g){if(g.type===Ia){const b=n.slice(m);let w;if(g.prefixSegment||g.suffixSegment){if(!v)return!1;const R=g.prefixSegment||"",E=g.suffixSegment||"",C=v.value;if("prefixSegment"in g&&!C.startsWith(R)||"suffixSegment"in g&&!((f=n[n.length-1])!=null&&f.value.endsWith(E)))return!1;let T=decodeURI(Gn(b.map(z=>z.value)));R&&T.startsWith(R)&&(T=T.slice(R.length)),E&&T.endsWith(E)&&(T=T.slice(0,T.length-E.length)),w=T}else w=decodeURI(Gn(b.map(R=>R.value)));return o["*"]=w,o._splat=w,!0}if(g.type===Hn){if(g.value==="/"&&!(v!=null&&v.value)){p++;continue}if(v){if(c){if(g.value!==v.value)return!1}else if(g.value.toLowerCase()!==v.value.toLowerCase())return!1;m++,p++;continue}else return!1}if(g.type===$a){if(!v||v.value==="/")return!1;let b="",w=!1;if(g.prefixSegment||g.suffixSegment){const R=g.prefixSegment||"",E=g.suffixSegment||"",C=v.value;if(R&&!C.startsWith(R)||E&&!C.endsWith(E))return!1;let T=C;R&&T.startsWith(R)&&(T=T.slice(R.length)),E&&T.endsWith(E)&&(T=T.slice(0,T.length-E.length)),b=decodeURIComponent(T),w=!0}else b=decodeURIComponent(v.value),w=!0;w&&(o[g.value.substring(1)]=b,m++),p++;continue}if(g.type===Xr){if(!v){p++;continue}if(v.value==="/"){p++;continue}let b="",w=!1;if(g.prefixSegment||g.suffixSegment){const R=g.prefixSegment||"",E=g.suffixSegment||"",C=v.value;if((!R||C.startsWith(R))&&(!E||C.endsWith(E))){let T=C;R&&T.startsWith(R)&&(T=T.slice(R.length)),E&&T.endsWith(E)&&(T=T.slice(0,T.length-E.length)),b=decodeURIComponent(T),w=!0}}else{let R=!0;for(let E=p+1;E<l.length;E++){const C=l[E];if((C==null?void 0:C.type)===Hn&&C.value===v.value){R=!1;break}if((C==null?void 0:C.type)===$a||(C==null?void 0:C.type)===Ia)break}R&&(b=decodeURIComponent(v.value),w=!0)}w&&(o[g.value.substring(1)]=b,m++),p++;continue}}if(m<n.length&&p>=l.length)return o["**"]=Gn(n.slice(m).map(b=>b.value)),!!i&&((d=l[l.length-1])==null?void 0:d.value)!=="/";if(p<l.length&&m>=n.length){for(let b=p;b<l.length;b++)if(((h=l[b])==null?void 0:h.type)!==Xr)return!1;break}break}return!0}function Zt(n){return!!(n!=null&&n.isNotFound)}function x1(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{return}}const ls="tsr-scroll-restoration-v1_3",_1=(n,l)=>{let o;return(...i)=>{o||(o=setTimeout(()=>{n(...i),o=null},l))}};function w1(){const n=x1();if(!n)return;const l=n.getItem(ls);let o=l?JSON.parse(l):{};return{state:o,set:i=>(o=Va(i,o)||o,n.setItem(ls,JSON.stringify(o)))}}const af=w1(),Lf=n=>n.state.__TSR_key||n.href;function E1(n){const l=[];let o;for(;o=n.parentNode;)l.unshift(`${n.tagName}:nth-child(${[].indexOf.call(o.children,n)+1})`),n=o;return`${l.join(" > ")}`.toLowerCase()}let is=!1;function Pg(n,l,o,i,c){var f;let d;try{d=JSON.parse(sessionStorage.getItem(n)||"{}")}catch(p){console.error(p);return}const h=l||((f=window.history.state)==null?void 0:f.key),m=d[h];is=!0,(()=>{if(i&&m){for(const v in m){const g=m[v];if(v==="window")window.scrollTo({top:g.scrollY,left:g.scrollX,behavior:o});else if(v){const b=document.querySelector(v);b&&(b.scrollLeft=g.scrollX,b.scrollTop=g.scrollY)}}return}const p=window.location.hash.split("#")[1];if(p){const v=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(v){const g=document.getElementById(p);g&&g.scrollIntoView(v)}return}["window",...(c==null?void 0:c.filter(v=>v!=="window"))??[]].forEach(v=>{const g=v==="window"?window:typeof v=="function"?v():document.querySelector(v);g&&g.scrollTo({top:0,left:0,behavior:o})})})(),is=!1}function R1(n,l){if(af===void 0||((n.options.scrollRestoration??!1)&&(n.isScrollRestoring=!0),typeof document>"u"||n.isScrollRestorationSetup))return;n.isScrollRestorationSetup=!0,is=!1;const i=n.options.getScrollRestorationKey||Lf;window.history.scrollRestoration="manual";const c=f=>{if(is||!n.isScrollRestoring)return;let d="";if(f.target===document||f.target===window)d="window";else{const m=f.target.getAttribute("data-scroll-restoration-id");m?d=`[data-scroll-restoration-id="${m}"]`:d=E1(f.target)}const h=i(n.state.location);af.set(m=>{const p=m[h]=m[h]||{},v=p[d]=p[d]||{};if(d==="window")v.scrollX=window.scrollX||0,v.scrollY=window.scrollY||0;else if(d){const g=document.querySelector(d);g&&(v.scrollX=g.scrollLeft||0,v.scrollY=g.scrollTop||0)}return m})};typeof document<"u"&&document.addEventListener("scroll",_1(c,100),!0),n.subscribe("onRendered",f=>{const d=i(f.toLocation);if(!n.resetNextScroll){n.resetNextScroll=!0;return}Pg(ls,d,n.options.scrollRestorationBehavior||void 0,n.isScrollRestoring||void 0,n.options.scrollToTopSelectors||void 0),n.isScrollRestoring&&af.set(h=>(h[d]=h[d]||{},h))})}function C1(n){if(typeof document<"u"&&document.querySelector){const l=n.state.location.state.__hashScrollIntoViewOptions??!0;if(l&&n.state.location.hash!==""){const o=document.getElementById(n.state.location.hash);o&&o.scrollIntoView(l)}}}function A1(n,l){const o=Object.entries(n).flatMap(([c,f])=>Array.isArray(f)?f.map(d=>[c,String(d)]):[[c,String(f)]]);return""+new URLSearchParams(o).toString()}function rf(n){return n?n==="false"?!1:n==="true"?!0:+n*0===0&&+n+""===n?+n:n:""}function M1(n,l){const o=n;return[...new URLSearchParams(o).entries()].reduce((f,[d,h])=>{const m=f[d];return m==null?f[d]=rf(h):f[d]=Array.isArray(m)?[...m,rf(h)]:[m,rf(h)],f},{})}const T1=D1(JSON.parse),O1=N1(JSON.stringify,JSON.parse);function D1(n){return l=>{l.substring(0,1)==="?"&&(l=l.substring(1));const o=M1(l);for(const i in o){const c=o[i];if(typeof c=="string")try{o[i]=n(c)}catch{}}return o}}function N1(n,l){function o(i){if(typeof i=="object"&&i!==null)try{return n(i)}catch{}else if(typeof i=="string"&&typeof l=="function")try{return l(i),n(i)}catch{}return i}return i=>{i={...i},Object.keys(i).forEach(f=>{const d=i[f];typeof d>"u"||d===void 0?delete i[f]:i[f]=o(d)});const c=A1(i).toString();return c?`?${c}`:""}}const Ft="__root__";function L1(n){if(n.statusCode=n.statusCode||n.code||307,!n.reloadDocument)try{new URL(`${n.href}`),n.reloadDocument=!0}catch{}const l=new Headers(n.headers||{});n.href&&l.get("Location")===null&&l.set("Location",n.href);const o=new Response(null,{status:n.statusCode,headers:l});if(o.options=n,n.throw)throw o;return o}function pn(n){return n instanceof Response&&!!n.options}function qa(n){const l=n.resolvedLocation,o=n.location,i=(l==null?void 0:l.pathname)!==o.pathname,c=(l==null?void 0:l.href)!==o.href,f=(l==null?void 0:l.hash)!==o.hash;return{fromLocation:l,toLocation:o,pathChanged:i,hrefChanged:c,hashChanged:f}}class j1{constructor(l){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=o=>o(),this.update=o=>{var i;o.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const c=this.options;this.options={...this.options,...o},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(f=>[encodeURIComponent(f),f])):void 0,(!this.basepath||o.basepath&&o.basepath!==c.basepath)&&(o.basepath===void 0||o.basepath===""||o.basepath==="/"?this.basepath="/":this.basepath=`/${nf(o.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?i1({initialEntries:[this.basepath||"/"]}):l1()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new Mf(U1(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(f=>!["redirected"].includes(f.status))}}}),R1(this)),typeof window<"u"&&"CSS"in window&&typeof((i=window.CSS)==null?void 0:i.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)"))},this.buildRouteTree=()=>{const{routesById:o,routesByPath:i,flatRoutes:c}=H1({routeTree:this.routeTree,initRoute:(d,h)=>{d.init({originalIndex:h})}});this.routesById=o,this.routesByPath=i,this.flatRoutes=c;const f=this.options.notFoundRoute;f&&(f.init({originalIndex:99999999999}),this.routesById[f.id]=f)},this.subscribe=(o,i)=>{const c={eventType:o,fn:i};return this.subscribers.add(c),()=>{this.subscribers.delete(c)}},this.emit=o=>{this.subscribers.forEach(i=>{i.eventType===o.type&&i.fn(o)})},this.parseLocation=(o,i)=>{const c=({pathname:m,search:p,hash:v,state:g})=>{const b=this.options.parseSearch(p),w=this.options.stringifySearch(b);return{pathname:m,searchStr:w,search:Qt(o==null?void 0:o.search,b),hash:v.split("#").reverse()[0]??"",href:`${m}${w}${v}`,state:Qt(o==null?void 0:o.state,g)}},f=c(i??this.history.location),{__tempLocation:d,__tempKey:h}=f.state;if(d&&(!h||h===this.tempLocationKey)){const m=c(d);return m.state.key=f.state.key,m.state.__TSR_key=f.state.__TSR_key,delete m.state.__tempLocation,{...m,maskedLocation:f}}return f},this.resolvePathWithBase=(o,i)=>d1({basepath:this.basepath,base:o,to:Zf(i),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive}),this.matchRoutes=(o,i,c)=>typeof o=="string"?this.matchRoutesInternal({pathname:o,search:i},c):this.matchRoutesInternal(o,i),this.getMatchedRoutes=(o,i)=>G1({pathname:o,routePathname:i,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes}),this.cancelMatch=o=>{const i=this.getMatch(o);i&&(i.abortController.abort(),this.updateMatch(o,c=>(clearTimeout(c.pendingTimeout),{...c,pendingTimeout:void 0})))},this.cancelMatches=()=>{var o;(o=this.state.pendingMatches)==null||o.forEach(i=>{this.cancelMatch(i.id)})},this.buildLocation=o=>{const i=(f={})=>{var d;const h=f._fromLocation||this.latestLocation,m=this.matchRoutes(h,{_buildLocation:!0}),p=Of(m);let v=p.fullPath;const g=f.to?this.resolvePathWithBase(v,`${f.to}`):this.resolvePathWithBase(v,"."),b=!!f.to&&!this.comparePaths(f.to.toString(),v)&&!this.comparePaths(g,v);f.unsafeRelative==="path"?v=h.pathname:b&&f.from&&(v=f.from);const w=p.search,R={...p.params},E=f.to?this.resolvePathWithBase(v,`${f.to}`):this.resolvePathWithBase(v,".");let C=f.params===!1||f.params===null?{}:(f.params??!0)===!0?R:{...R,...Va(f.params,R)};const T=Gi({path:E,params:C??{}}).interpolatedPath,z=this.matchRoutes(T,{},{_buildLocation:!0}).map(ae=>this.looseRoutesById[ae.routeId]);Object.keys(C).length>0&&z.map(ae=>{var Z;return((Z=ae.options.params)==null?void 0:Z.stringify)??ae.options.stringifyParams}).filter(Boolean).forEach(ae=>{C={...C,...ae(C)}});const L=Gi({path:E,params:C??{},leaveWildcards:!1,leaveParams:o.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath;let N=w;if(o._includeValidateSearch&&((d=this.options.search)!=null&&d.strict)){let ae={};z.forEach(Z=>{try{Z.options.validateSearch&&(ae={...ae,...jf(Z.options.validateSearch,{...ae,...N})??{}})}catch{}}),N=ae}N=V1({search:N,dest:f,destRoutes:z,_includeValidateSearch:o._includeValidateSearch}),N=Qt(w,N);const G=this.options.stringifySearch(N),W=f.hash===!0?h.hash:f.hash?Va(f.hash,h.hash):void 0,V=W?`#${W}`:"";let q=f.state===!0?h.state:f.state?Va(f.state,h.state):{};return q=Qt(h.state,q),{pathname:L,search:N,searchStr:G,state:q,hash:W??"",href:`${L}${G}${V}`,unmaskOnReload:f.unmaskOnReload}},c=(f={},d)=>{var h;const m=i(f);let p=d?i(d):void 0;if(!p){let v={};const g=(h=this.options.routeMasks)==null?void 0:h.find(b=>{const w=Nf(this.basepath,m.pathname,{to:b.from,caseSensitive:!1,fuzzy:!1});return w?(v=w,!0):!1});if(g){const{from:b,...w}=g;d={...as(o,["from"]),...w,params:v},p=i(d)}}if(p){const v=i(d);m.maskedLocation=v}return m};return o.mask?c(o,{...as(o,["from"]),...o.mask}):c(o)},this.commitLocation=({viewTransition:o,ignoreBlocker:i,...c})=>{const f=()=>{const m=["key","__TSR_key","__TSR_index","__hashScrollIntoViewOptions"];m.forEach(v=>{c.state[v]=this.latestLocation.state[v]});const p=Yr(c.state,this.latestLocation.state);return m.forEach(v=>{delete c.state[v]}),p},d=this.latestLocation.href===c.href,h=this.commitLocationPromise;if(this.commitLocationPromise=Hr(()=>{h==null||h.resolve()}),d&&f())this.load();else{let{maskedLocation:m,hashScrollIntoView:p,...v}=c;m&&(v={...m,state:{...m.state,__tempKey:void 0,__tempLocation:{...v,search:v.searchStr,state:{...v.state,__tempKey:void 0,__tempLocation:void 0,__TSR_key:void 0,key:void 0}}}},(v.unmaskOnReload??this.options.unmaskOnReload??!1)&&(v.state.__tempKey=this.tempLocationKey)),v.state.__hashScrollIntoViewOptions=p??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=o,this.history[c.replace?"replace":"push"](v.href,v.state,{ignoreBlocker:i})}return this.resetNextScroll=c.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:o,resetScroll:i,hashScrollIntoView:c,viewTransition:f,ignoreBlocker:d,href:h,...m}={})=>{if(h){const v=this.history.location.state.__TSR_index,g=dl(h,{__TSR_index:o?v:v+1});m.to=g.pathname,m.search=this.options.parseSearch(g.search),m.hash=g.hash.slice(1)}const p=this.buildLocation({...m,_includeValidateSearch:!0});return this.commitLocation({...p,viewTransition:f,replace:o,resetScroll:i,hashScrollIntoView:c,ignoreBlocker:d})},this.navigate=({to:o,reloadDocument:i,href:c,...f})=>{if(!i&&c)try{new URL(`${c}`),i=!0}catch{}if(i){if(!c){const d=this.buildLocation({to:o,...f});c=this.history.createHref(d.href)}f.replace?window.location.replace(c):window.location.href=c;return}return this.buildAndCommitLocation({...f,href:c,to:o,_isNavigate:!0})},this.beforeLoad=()=>{if(this.cancelMatches(),this.latestLocation=this.parseLocation(this.latestLocation),this.isServer){const i=this.buildLocation({to:this.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0}),c=f=>{try{return encodeURI(decodeURI(f))}catch{return f}};if(nf(c(this.latestLocation.href))!==nf(c(i.href)))throw L1({href:i.href})}const o=this.matchRoutes(this.latestLocation);this.__store.setState(i=>({...i,status:"pending",statusCode:200,isLoading:!0,location:this.latestLocation,pendingMatches:o,cachedMatches:i.cachedMatches.filter(c=>!o.find(f=>f.id===c.id))}))},this.load=async o=>{let i,c,f;for(f=new Promise(d=>{this.startTransition(async()=>{var h;try{this.beforeLoad();const m=this.latestLocation,p=this.state.resolvedLocation;this.state.redirect||this.emit({type:"onBeforeNavigate",...qa({resolvedLocation:p,location:m})}),this.emit({type:"onBeforeLoad",...qa({resolvedLocation:p,location:m})}),await this.loadMatches({sync:o==null?void 0:o.sync,matches:this.state.pendingMatches,location:m,onReady:async()=>{this.startViewTransition(async()=>{let v,g,b;Ov(()=>{this.__store.setState(w=>{const R=w.matches,E=w.pendingMatches||w.matches;return v=R.filter(C=>!E.find(T=>T.id===C.id)),g=E.filter(C=>!R.find(T=>T.id===C.id)),b=R.filter(C=>E.find(T=>T.id===C.id)),{...w,isLoading:!1,loadedAt:Date.now(),matches:E,pendingMatches:void 0,cachedMatches:[...w.cachedMatches,...v.filter(C=>C.status!=="error")]}}),this.clearExpiredCache()}),[[v,"onLeave"],[g,"onEnter"],[b,"onStay"]].forEach(([w,R])=>{w.forEach(E=>{var C,T;(T=(C=this.looseRoutesById[E.routeId].options)[R])==null||T.call(C,E)})})})}})}catch(m){pn(m)?(i=m,this.isServer||this.navigate({...i.options,replace:!0,ignoreBlocker:!0})):Zt(m)&&(c=m),this.__store.setState(p=>({...p,statusCode:i?i.status:c?404:p.matches.some(v=>v.status==="error")?500:200,redirect:i}))}this.latestLoadPromise===f&&((h=this.commitLocationPromise)==null||h.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),d()})}),this.latestLoadPromise=f,await f;this.latestLoadPromise&&f!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(d=>({...d,statusCode:404}))},this.startViewTransition=o=>{const i=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,i&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let c;if(typeof i=="object"&&this.isViewTransitionTypesSupported){const f=this.latestLocation,d=this.state.resolvedLocation,h=typeof i.types=="function"?i.types(qa({resolvedLocation:d,location:f})):i.types;c={update:o,types:h}}else c=o;document.startViewTransition(c)}else o()},this.updateMatch=(o,i)=>{var c;let f;const d=(c=this.state.pendingMatches)==null?void 0:c.find(v=>v.id===o),h=this.state.matches.find(v=>v.id===o),m=this.state.cachedMatches.find(v=>v.id===o),p=d?"pendingMatches":h?"matches":m?"cachedMatches":"";return p&&this.__store.setState(v=>{var g;return{...v,[p]:(g=v[p])==null?void 0:g.map(b=>b.id===o?f=i(b):b)}}),f},this.getMatch=o=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(i=>i.id===o),this.loadMatches=async({location:o,matches:i,preload:c,onReady:f,updateMatch:d=this.updateMatch,sync:h})=>{let m,p=!1;const v=async()=>{p||(p=!0,await(f==null?void 0:f()))},g=R=>!!(c&&!this.state.matches.find(E=>E.id===R));!this.isServer&&this.state.matches.find(R=>R._forcePending)&&v();const b=(R,E)=>{var C,T,z;if(pn(E)||Zt(E)){if(pn(E)&&E.redirectHandled&&!E.options.reloadDocument)throw E;if((C=R.beforeLoadPromise)==null||C.resolve(),(T=R.loaderPromise)==null||T.resolve(),d(R.id,L=>({...L,status:pn(E)?"redirected":Zt(E)?"notFound":"error",isFetching:!1,error:E,beforeLoadPromise:void 0,loaderPromise:void 0})),E.routeId||(E.routeId=R.routeId),(z=R.loadPromise)==null||z.resolve(),pn(E))throw p=!0,E.options._fromLocation=o,E.redirectHandled=!0,E=this.resolveRedirect(E),E;if(Zt(E))throw this._handleNotFound(i,E,{updateMatch:d}),E}},w=R=>{const E=this.getMatch(R);return!!(!this.isServer&&E._dehydrated||this.isServer&&E.ssr===!1)};try{await new Promise((R,E)=>{(async()=>{var C,T,z,L;try{const N=(V,q,ae)=>{var Z,re;const{id:te,routeId:le}=i[V],ue=this.looseRoutesById[le];if(q instanceof Promise)throw q;q.routerCode=ae,m=m??V,b(this.getMatch(te),q);try{(re=(Z=ue.options).onError)==null||re.call(Z,q)}catch(de){q=de,b(this.getMatch(te),q)}d(te,de=>{var O,$;return(O=de.beforeLoadPromise)==null||O.resolve(),($=de.loadPromise)==null||$.resolve(),{...de,error:q,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(const[V,{id:q,routeId:ae}]of i.entries()){const Z=this.getMatch(q),re=(C=i[V-1])==null?void 0:C.id,te=re?this.getMatch(re):void 0,le=this.looseRoutesById[ae],ue=le.options.pendingMs??this.options.defaultPendingMs;if(this.isServer){let U;if(this.isShell())U=q===Ft;else{const Q=this.options.defaultSsr??!0;if((te==null?void 0:te.ssr)===!1)U=!1;else{let A;if(le.options.ssr===void 0)A=Q;else if(typeof le.options.ssr=="function"){let Y=function(oe,ne){return ne?{status:"error",error:ne}:{status:"success",value:oe}};const{search:J,params:F}=this.getMatch(q),ee={search:Y(J,Z.searchError),params:Y(F,Z.paramsError),location:o,matches:i.map(oe=>({index:oe.index,pathname:oe.pathname,fullPath:oe.fullPath,staticData:oe.staticData,id:oe.id,routeId:oe.routeId,search:Y(oe.search,oe.searchError),params:Y(oe.params,oe.paramsError),ssr:oe.ssr}))};A=await le.options.ssr(ee)??Q}else A=le.options.ssr;A===!0&&(te==null?void 0:te.ssr)==="data-only"?U="data-only":U=A}}d(q,Q=>({...Q,ssr:U}))}if(w(q))continue;const de=!!(f&&!this.isServer&&!g(q)&&(le.options.loader||le.options.beforeLoad||kv(le))&&typeof ue=="number"&&ue!==1/0&&(le.options.pendingComponent??((T=this.options)==null?void 0:T.defaultPendingComponent)));let O=!0;const $=()=>{if(de&&this.getMatch(q).pendingTimeout===void 0){const U=setTimeout(()=>{try{v()}catch{}},ue);d(q,Q=>({...Q,pendingTimeout:U}))}};if(Z.beforeLoadPromise||Z.loaderPromise){$(),await Z.beforeLoadPromise;const U=this.getMatch(q);U.status==="error"?O=!0:U.preload&&(U.status==="redirected"||U.status==="notFound")&&b(U,U.error)}if(O){try{d(q,ge=>{const Ce=ge.loadPromise;return{...ge,loadPromise:Hr(()=>{Ce==null||Ce.resolve()}),beforeLoadPromise:Hr()}});const{paramsError:U,searchError:Q}=this.getMatch(q);U&&N(V,U,"PARSE_PARAMS"),Q&&N(V,Q,"VALIDATE_SEARCH"),$();const A=new AbortController,Y=(te==null?void 0:te.context)??this.options.context??{};d(q,ge=>({...ge,isFetching:"beforeLoad",fetchCount:ge.fetchCount+1,abortController:A,context:{...Y,...ge.__routeContext}}));const{search:J,params:F,context:ee,cause:oe}=this.getMatch(q),ne=g(q),fe={search:J,abortController:A,params:F,preload:ne,context:ee,location:o,navigate:ge=>this.navigate({...ge,_fromLocation:o}),buildLocation:this.buildLocation,cause:ne?"preload":oe,matches:i},me=await((L=(z=le.options).beforeLoad)==null?void 0:L.call(z,fe));(pn(me)||Zt(me))&&N(V,me,"BEFORE_LOAD"),d(q,ge=>({...ge,__beforeLoadContext:me,context:{...Y,...ge.__routeContext,...me},abortController:A}))}catch(U){N(V,U,"BEFORE_LOAD")}d(q,U=>{var Q;return(Q=U.beforeLoadPromise)==null||Q.resolve(),{...U,beforeLoadPromise:void 0,isFetching:!1}})}}const G=i.slice(0,m),W=[];G.forEach(({id:V,routeId:q},ae)=>{W.push((async()=>{let Z=!1,re=!1;const te=this.looseRoutesById[q],le=async()=>{var O,$,U,Q,A,Y;const J=this.getMatch(V);if(!J)return;const F={matches:i,match:J,params:J.params,loaderData:J.loaderData},ee=await(($=(O=te.options).head)==null?void 0:$.call(O,F)),oe=ee==null?void 0:ee.meta,ne=ee==null?void 0:ee.links,fe=ee==null?void 0:ee.scripts,me=ee==null?void 0:ee.styles,ge=await((Q=(U=te.options).scripts)==null?void 0:Q.call(U,F)),Ce=await((Y=(A=te.options).headers)==null?void 0:Y.call(A,F));return{meta:oe,links:ne,headScripts:fe,headers:Ce,scripts:ge,styles:me}},ue=async()=>{const O=this.getMatch(V);O.minPendingPromise&&await O.minPendingPromise},de=this.getMatch(V);if(w(V)){if(this.isServer){const O=await le();return d(V,$=>({...$,...O})),this.getMatch(V)}}else if(de.loaderPromise){if(de.status==="success"&&!h&&!de.preload)return this.getMatch(V);await de.loaderPromise;const O=this.getMatch(V);O.error&&b(O,O.error)}else{const O=W[ae-1],$=()=>{const{params:ne,loaderDeps:fe,abortController:me,context:ge,cause:Ce}=this.getMatch(V),De=g(V);return{params:ne,deps:fe,preload:!!De,parentMatchPromise:O,abortController:me,context:ge,location:o,navigate:He=>this.navigate({...He,_fromLocation:o}),cause:De?"preload":Ce,route:te}},U=Date.now()-this.getMatch(V).updatedAt,Q=g(V),A=Q?te.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:te.options.staleTime??this.options.defaultStaleTime??0,Y=te.options.shouldReload,J=typeof Y=="function"?Y($()):Y;d(V,ne=>({...ne,loaderPromise:Hr(),preload:!!Q&&!this.state.matches.find(fe=>fe.id===V)}));const F=async()=>{var ne,fe,me,ge;try{try{(!this.isServer||this.isServer&&this.getMatch(V).ssr===!0)&&this.loadRouteChunk(te),d(V,He=>({...He,isFetching:"loader"}));const Ce=await((fe=(ne=te.options).loader)==null?void 0:fe.call(ne,$()));b(this.getMatch(V),Ce),d(V,He=>({...He,loaderData:Ce})),await te._lazyPromise;const De=await le();await ue(),await te._componentsPromise,d(V,He=>({...He,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),...De}))}catch(Ce){let De=Ce;await ue(),b(this.getMatch(V),Ce);try{(ge=(me=te.options).onError)==null||ge.call(me,Ce)}catch(ut){De=ut,b(this.getMatch(V),ut)}const He=await le();d(V,ut=>({...ut,error:De,status:"error",isFetching:!1,...He}))}}catch(Ce){const De=await le();d(V,He=>({...He,loaderPromise:void 0,...De})),b(this.getMatch(V),Ce)}},{status:ee,invalid:oe}=this.getMatch(V);if(Z=ee==="success"&&(oe||(J??U>A)),!(Q&&te.options.preload===!1))if(Z&&!h)re=!0,(async()=>{try{await F();const{loaderPromise:ne,loadPromise:fe}=this.getMatch(V);ne==null||ne.resolve(),fe==null||fe.resolve(),d(V,me=>({...me,loaderPromise:void 0}))}catch(ne){pn(ne)&&await this.navigate(ne.options)}})();else if(ee!=="success"||Z&&h)await F();else{const ne=await le();d(V,fe=>({...fe,...ne}))}}if(!re){const{loaderPromise:O,loadPromise:$}=this.getMatch(V);O==null||O.resolve(),$==null||$.resolve()}return d(V,O=>(clearTimeout(O.pendingTimeout),{...O,isFetching:re?O.isFetching:!1,loaderPromise:re?O.loaderPromise:void 0,invalid:!1,pendingTimeout:void 0,_dehydrated:void 0})),this.getMatch(V)})())}),await Promise.all(W),R()}catch(N){E(N)}})()}),await v()}catch(R){if(pn(R)||Zt(R))throw Zt(R)&&!c&&await v(),R}return i},this.invalidate=o=>{const i=c=>{var f;return((f=o==null?void 0:o.filter)==null?void 0:f.call(o,c))??!0?{...c,invalid:!0,...o!=null&&o.forcePending||c.status==="error"?{status:"pending",error:void 0}:{}}:c};return this.__store.setState(c=>{var f;return{...c,matches:c.matches.map(i),cachedMatches:c.cachedMatches.map(i),pendingMatches:(f=c.pendingMatches)==null?void 0:f.map(i)}}),this.shouldViewTransition=!1,this.load({sync:o==null?void 0:o.sync})},this.resolveRedirect=o=>(o.options.href||(o.options.href=this.buildLocation(o.options).href,o.headers.set("Location",o.options.href)),o.headers.get("Location")||o.headers.set("Location",o.options.href),o),this.clearCache=o=>{const i=o==null?void 0:o.filter;i!==void 0?this.__store.setState(c=>({...c,cachedMatches:c.cachedMatches.filter(f=>!i(f))})):this.__store.setState(c=>({...c,cachedMatches:[]}))},this.clearExpiredCache=()=>{const o=i=>{const c=this.looseRoutesById[i.routeId];if(!c.options.loader)return!0;const f=(i.preload?c.options.preloadGcTime??this.options.defaultPreloadGcTime:c.options.gcTime??this.options.defaultGcTime)??300*1e3;return!(i.status!=="error"&&Date.now()-i.updatedAt<f)};this.clearCache({filter:o})},this.loadRouteChunk=o=>(o._lazyPromise===void 0&&(o.lazyFn?o._lazyPromise=o.lazyFn().then(i=>{const{id:c,...f}=i.options;Object.assign(o.options,f)}):o._lazyPromise=Promise.resolve()),o._componentsPromise===void 0&&(o._componentsPromise=o._lazyPromise.then(()=>Promise.all(Hg.map(async i=>{const c=o.options[i];c!=null&&c.preload&&await c.preload()})))),o._componentsPromise),this.preloadRoute=async o=>{const i=this.buildLocation(o);let c=this.matchRoutes(i,{throwOnError:!0,preload:!0,dest:o});const f=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(h=>h.id)),d=new Set([...f,...this.state.cachedMatches.map(h=>h.id)]);Ov(()=>{c.forEach(h=>{d.has(h.id)||this.__store.setState(m=>({...m,cachedMatches:[...m.cachedMatches,h]}))})});try{return c=await this.loadMatches({matches:c,location:i,preload:!0,updateMatch:(h,m)=>{f.has(h)?c=c.map(p=>p.id===h?m(p):p):this.updateMatch(h,m)}}),c}catch(h){if(pn(h))return h.options.reloadDocument?void 0:await this.preloadRoute({...h.options,_fromLocation:i});Zt(h)||console.error(h);return}},this.matchRoute=(o,i)=>{const c={...o,to:o.to?this.resolvePathWithBase(o.from||"",o.to):void 0,params:o.params||{},leaveParams:!0},f=this.buildLocation(c);if(i!=null&&i.pending&&this.state.status!=="pending")return!1;const h=((i==null?void 0:i.pending)===void 0?!this.state.isLoading:i.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,m=Nf(this.basepath,h.pathname,{...i,to:f.pathname});return!m||o.params&&!Yr(m,o.params,{partial:!0})?!1:m&&((i==null?void 0:i.includeSearch)??!0)?Yr(h.search,f.search,{partial:!0})?m:!1:m},this._handleNotFound=(o,i,{updateMatch:c=this.updateMatch}={})=>{var f;const d=this.routesById[i.routeId??""]??this.routeTree,h={};for(const p of o)h[p.routeId]=p;!d.options.notFoundComponent&&((f=this.options)!=null&&f.defaultNotFoundComponent)&&(d.options.notFoundComponent=this.options.defaultNotFoundComponent),Vn(d.options.notFoundComponent);const m=h[d.id];Vn(m,"Could not find match for route: "+d.id),c(m.id,p=>({...p,status:"notFound",error:i,isFetching:!1})),i.routerCode==="BEFORE_LOAD"&&d.parentRoute&&(i.routeId=d.parentRoute.id,this._handleNotFound(o,i,{updateMatch:c}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(o=>o.status==="notFound"||o.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...l,caseSensitive:l.caseSensitive??!1,notFoundMode:l.notFoundMode??"fuzzy",stringifySearch:l.stringifySearch??O1,parseSearch:l.parseSearch??T1}),typeof document<"u"&&(self.__TSR_ROUTER__=this)}isShell(){return this.options.isShell}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(l,o){var i;const{foundRoute:c,matchedRoutes:f,routeParams:d}=this.getMatchedRoutes(l.pathname,(i=o==null?void 0:o.dest)==null?void 0:i.to);let h=!1;(c?c.path!=="/"&&d["**"]:Kr(l.pathname))&&(this.options.notFoundRoute?f.push(this.options.notFoundRoute):h=!0);const m=(()=>{if(h){if(this.options.notFoundMode!=="root")for(let b=f.length-1;b>=0;b--){const w=f[b];if(w.children)return w.id}return Ft}})(),p=f.map(b=>{var w;let R;const E=((w=b.options.params)==null?void 0:w.parse)??b.options.parseParams;if(E)try{const C=E(d);Object.assign(d,C)}catch(C){if(R=new z1(C.message,{cause:C}),o!=null&&o.throwOnError)throw R;return R}}),v=[],g=b=>(b==null?void 0:b.id)?b.context??this.options.context??{}:this.options.context??{};return f.forEach((b,w)=>{var R,E;const C=v[w-1],[T,z,L]=(()=>{const ue=(C==null?void 0:C.search)??l.search,de=(C==null?void 0:C._strictSearch)??{};try{const O=jf(b.options.validateSearch,{...ue})??{};return[{...ue,...O},{...de,...O},void 0]}catch(O){let $=O;if(O instanceof ss||($=new ss(O.message,{cause:O})),o!=null&&o.throwOnError)throw $;return[ue,{},$]}})(),N=((E=(R=b.options).loaderDeps)==null?void 0:E.call(R,{search:T}))??"",G=N?JSON.stringify(N):"",{usedParams:W,interpolatedPath:V}=Gi({path:b.fullPath,params:d,decodeCharMap:this.pathParamsDecodeCharMap}),q=Gi({path:b.id,params:d,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath+G,ae=this.getMatch(q),Z=this.state.matches.find(ue=>ue.routeId===b.id),re=Z?"stay":"enter";let te;if(ae)te={...ae,cause:re,params:Z?Qt(Z.params,d):d,_strictParams:W,search:Qt(Z?Z.search:ae.search,T),_strictSearch:z};else{const ue=b.options.loader||b.options.beforeLoad||b.lazyFn||kv(b)?"pending":"success";te={id:q,index:w,routeId:b.id,params:Z?Qt(Z.params,d):d,_strictParams:W,pathname:Gn([this.basepath,V]),updatedAt:Date.now(),search:Z?Qt(Z.search,T):T,_strictSearch:z,searchError:void 0,status:ue,isFetching:!1,error:void 0,paramsError:p[w],__routeContext:{},__beforeLoadContext:void 0,context:{},abortController:new AbortController,fetchCount:0,cause:re,loaderDeps:Z?Qt(Z.loaderDeps,N):N,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:b.options.staticData||{},loadPromise:Hr(),fullPath:b.fullPath}}o!=null&&o.preload||(te.globalNotFound=m===b.id),te.searchError=L;const le=g(C);te.context={...le,...te.__routeContext,...te.__beforeLoadContext},v.push(te)}),v.forEach((b,w)=>{var R,E;const C=this.looseRoutesById[b.routeId];if(!this.getMatch(b.id)&&(o==null?void 0:o._buildLocation)!==!0){const z=v[w-1],L=g(z),N={deps:b.loaderDeps,params:b.params,context:L,location:l,navigate:G=>this.navigate({...G,_fromLocation:l}),buildLocation:this.buildLocation,cause:b.cause,abortController:b.abortController,preload:!!b.preload,matches:v};b.__routeContext=((E=(R=C.options).context)==null?void 0:E.call(R,N))??{},b.context={...L,...b.__routeContext,...b.__beforeLoadContext}}}),v}comparePaths(l,o){return l.replace(/(.+)\/$/,"$1")===o.replace(/(.+)\/$/,"$1")}}class ss extends Error{}class z1 extends Error{}function U1(n){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:n,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function jf(n,l){if(n==null)return{};if("~standard"in n){const o=n["~standard"].validate(l);if(o instanceof Promise)throw new ss("Async validation not supported");if(o.issues)throw new ss(JSON.stringify(o.issues,void 0,2),{cause:o});return o.value}return"parse"in n?n.parse(l):typeof n=="function"?n(l):{}}const Hg=["component","errorComponent","pendingComponent","notFoundComponent"];function kv(n){var l;for(const o of Hg)if((l=n.options[o])!=null&&l.preload)return!0;return!1}const k1=.5,B1=.4,P1=.25;function Bv(n,l){return n.prefixSegment&&n.suffixSegment?l+.05:n.prefixSegment?l+.02:n.suffixSegment?l+.01:l}function H1({routeTree:n,initRoute:l}){const o={},i={},c=m=>{m.forEach((p,v)=>{l==null||l(p,v);const g=o[p.id];if(Vn(!g,`Duplicate routes found with id: ${String(p.id)}`),o[p.id]=p,!p.isRoot&&p.path){const w=Kr(p.fullPath);(!i[w]||p.fullPath.endsWith("/"))&&(i[w]=p)}const b=p.children;b!=null&&b.length&&c(b)})};c([n]);const f=[];Object.values(o).forEach((m,p)=>{var v;if(m.isRoot||!m.path)return;const g=Ff(m.fullPath);let b=Qr(g),w=0;for(;b.length>w+1&&((v=b[w])==null?void 0:v.value)==="/";)w++;w>0&&(b=b.slice(w));let R=0,E=!1;const C=b.map((T,z)=>{if(T.value==="/")return .75;let L;if(T.type===$a?L=k1:T.type===Xr?(L=B1,R++):T.type===Ia&&(L=P1),L){for(let N=z+1;N<b.length;N++){const G=b[N];if(G.type===Hn&&G.value!=="/")return E=!0,Bv(T,L+.2)}return Bv(T,L)}return 1});f.push({child:m,trimmed:g,parsed:b,index:p,scores:C,optionalParamCount:R,hasStaticAfter:E})});const h=f.sort((m,p)=>{const v=Math.min(m.scores.length,p.scores.length);for(let g=0;g<v;g++)if(m.scores[g]!==p.scores[g])return p.scores[g]-m.scores[g];if(m.scores.length!==p.scores.length){if(m.optionalParamCount!==p.optionalParamCount){if(m.hasStaticAfter===p.hasStaticAfter)return m.optionalParamCount-p.optionalParamCount;if(m.hasStaticAfter&&!p.hasStaticAfter)return-1;if(!m.hasStaticAfter&&p.hasStaticAfter)return 1}return p.scores.length-m.scores.length}for(let g=0;g<v;g++)if(m.parsed[g].value!==p.parsed[g].value)return m.parsed[g].value>p.parsed[g].value?1:-1;return m.index-p.index}).map((m,p)=>(m.child.rank=p,m.child));return{routesById:o,routesByPath:i,flatRoutes:h}}function G1({pathname:n,routePathname:l,basepath:o,caseSensitive:i,routesByPath:c,routesById:f,flatRoutes:d}){let h={};const m=Kr(n),p=w=>{var R;return Nf(o,m,{to:w.fullPath,caseSensitive:((R=w.options)==null?void 0:R.caseSensitive)??i,fuzzy:!0})};let v=l!==void 0?c[l]:void 0;if(v)h=p(v);else{let w;for(const R of d){const E=p(R);if(E)if(R.path!=="/"&&E["**"])w||(w={foundRoute:R,routeParams:E});else{v=R,h=E;break}}!v&&w&&(v=w.foundRoute,h=w.routeParams)}let g=v||f[Ft];const b=[g];for(;g.parentRoute;)g=g.parentRoute,b.push(g);return b.reverse(),{matchedRoutes:b,routeParams:h,foundRoute:v}}function V1({search:n,dest:l,destRoutes:o,_includeValidateSearch:i}){const c=o.reduce((h,m)=>{var p;const v=[];if("search"in m.options)(p=m.options.search)!=null&&p.middlewares&&v.push(...m.options.search.middlewares);else if(m.options.preSearchFilters||m.options.postSearchFilters){const g=({search:b,next:w})=>{let R=b;"preSearchFilters"in m.options&&m.options.preSearchFilters&&(R=m.options.preSearchFilters.reduce((C,T)=>T(C),b));const E=w(R);return"postSearchFilters"in m.options&&m.options.postSearchFilters?m.options.postSearchFilters.reduce((C,T)=>T(C),E):E};v.push(g)}if(i&&m.options.validateSearch){const g=({search:b,next:w})=>{const R=w(b);try{return{...R,...jf(m.options.validateSearch,R)??{}}}catch{return R}};v.push(g)}return h.concat(v)},[])??[],f=({search:h})=>l.search?l.search===!0?h:Va(l.search,h):{};c.push(f);const d=(h,m)=>{if(h>=c.length)return m;const p=c[h];return p({search:m,next:g=>d(h+1,g)})};return d(0,n)}const q1="Error preloading route! ☝️";class Gg{constructor(l){if(this.init=o=>{var i,c;this.originalIndex=o.originalIndex;const f=this.options,d=!(f!=null&&f.path)&&!(f!=null&&f.id);this.parentRoute=(c=(i=this.options).getParentRoute)==null?void 0:c.call(i),d?this._path=Ft:this.parentRoute||Vn(!1);let h=d?Ft:f==null?void 0:f.path;h&&h!=="/"&&(h=Ff(h));const m=(f==null?void 0:f.id)||h;let p=d?Ft:Gn([this.parentRoute.id===Ft?"":this.parentRoute.id,m]);h===Ft&&(h="/"),p!==Ft&&(p=Gn(["/",p]));const v=p===Ft?"/":Gn([this.parentRoute.fullPath,h]);this._path=h,this._id=p,this._fullPath=v,this._to=v},this.clone=o=>{this._path=o._path,this._id=o._id,this._fullPath=o._fullPath,this._to=o._to,this.options.getParentRoute=o.options.getParentRoute,this.children=o.children},this.addChildren=o=>this._addFileChildren(o),this._addFileChildren=o=>(Array.isArray(o)&&(this.children=o),typeof o=="object"&&o!==null&&(this.children=Object.values(o)),this),this._addFileTypes=()=>this,this.updateLoader=o=>(Object.assign(this.options,o),this),this.update=o=>(Object.assign(this.options,o),this),this.lazy=o=>(this.lazyFn=o,this),this.options=l||{},this.isRoot=!(l!=null&&l.getParentRoute),l!=null&&l.id&&(l!=null&&l.path))throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}}class Y1 extends Gg{constructor(l){super(l)}}function Wf(n){const l=n.errorComponent??vs;return _.jsx($1,{getResetKey:n.getResetKey,onCatch:n.onCatch,children:({error:o,reset:i})=>o?S.createElement(l,{error:o,reset:i}):n.children})}class $1 extends S.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(l){return{resetKey:l.getResetKey()}}static getDerivedStateFromError(l){return{error:l}}reset(){this.setState({error:null})}componentDidUpdate(l,o){o.error&&o.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(l,o){this.props.onCatch&&this.props.onCatch(l,o)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function vs({error:n}){const[l,o]=S.useState(!1);return _.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[_.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[_.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),_.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>o(i=>!i),children:l?"Hide Error":"Show Error"})]}),_.jsx("div",{style:{height:".25rem"}}),l?_.jsx("div",{children:_.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:n.message?_.jsx("code",{children:n.message}):null})}):null]})}function I1({children:n,fallback:l=null}){return X1()?_.jsx(Ut.Fragment,{children:n}):_.jsx(Ut.Fragment,{children:l})}function X1(){return Ut.useSyncExternalStore(K1,()=>!0,()=>!1)}function K1(){return()=>{}}var of={exports:{}},lf={},sf={exports:{}},cf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pv;function Q1(){if(Pv)return cf;Pv=1;var n=xl();function l(g,b){return g===b&&(g!==0||1/g===1/b)||g!==g&&b!==b}var o=typeof Object.is=="function"?Object.is:l,i=n.useState,c=n.useEffect,f=n.useLayoutEffect,d=n.useDebugValue;function h(g,b){var w=b(),R=i({inst:{value:w,getSnapshot:b}}),E=R[0].inst,C=R[1];return f(function(){E.value=w,E.getSnapshot=b,m(E)&&C({inst:E})},[g,w,b]),c(function(){return m(E)&&C({inst:E}),g(function(){m(E)&&C({inst:E})})},[g]),d(w),w}function m(g){var b=g.getSnapshot;g=g.value;try{var w=b();return!o(g,w)}catch{return!0}}function p(g,b){return b()}var v=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:h;return cf.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:v,cf}var Hv;function Vg(){return Hv||(Hv=1,sf.exports=Q1()),sf.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gv;function Z1(){if(Gv)return lf;Gv=1;var n=xl(),l=Vg();function o(p,v){return p===v&&(p!==0||1/p===1/v)||p!==p&&v!==v}var i=typeof Object.is=="function"?Object.is:o,c=l.useSyncExternalStore,f=n.useRef,d=n.useEffect,h=n.useMemo,m=n.useDebugValue;return lf.useSyncExternalStoreWithSelector=function(p,v,g,b,w){var R=f(null);if(R.current===null){var E={hasValue:!1,value:null};R.current=E}else E=R.current;R=h(function(){function T(W){if(!z){if(z=!0,L=W,W=b(W),w!==void 0&&E.hasValue){var V=E.value;if(w(V,W))return N=V}return N=W}if(V=N,i(L,W))return V;var q=b(W);return w!==void 0&&w(V,q)?(L=W,V):(L=W,N=q)}var z=!1,L,N,G=g===void 0?null:g;return[function(){return T(v())},G===null?void 0:function(){return T(G())}]},[v,g,b,w]);var C=c(p,R[0],R[1]);return d(function(){E.hasValue=!0,E.value=C},[C]),m(C),C},lf}var Vv;function F1(){return Vv||(Vv=1,of.exports=Z1()),of.exports}var W1=F1();function J1(n,l=o=>o){return W1.useSyncExternalStoreWithSelector(n.subscribe,()=>n.state,()=>n.state,l,e_)}function e_(n,l){if(Object.is(n,l))return!0;if(typeof n!="object"||n===null||typeof l!="object"||l===null)return!1;if(n instanceof Map&&l instanceof Map){if(n.size!==l.size)return!1;for(const[i,c]of n)if(!l.has(i)||!Object.is(c,l.get(i)))return!1;return!0}if(n instanceof Set&&l instanceof Set){if(n.size!==l.size)return!1;for(const i of n)if(!l.has(i))return!1;return!0}if(n instanceof Date&&l instanceof Date)return n.getTime()===l.getTime();const o=Object.keys(n);if(o.length!==Object.keys(l).length)return!1;for(let i=0;i<o.length;i++)if(!Object.prototype.hasOwnProperty.call(l,o[i])||!Object.is(n[o[i]],l[o[i]]))return!1;return!0}const uf=S.createContext(null);function qg(){return typeof document>"u"?uf:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=uf,uf)}function ln(n){const l=S.useContext(qg());return n==null||n.warn,l}function vt(n){const l=ln({warn:(n==null?void 0:n.router)===void 0}),o=(n==null?void 0:n.router)||l,i=S.useRef(void 0);return J1(o.__store,c=>{if(n!=null&&n.select){if(n.structuralSharing??o.options.defaultStructuralSharing){const f=Qt(i.current,n.select(c));return i.current=f,f}return n.select(c)}return c})}const gs=S.createContext(void 0),t_=S.createContext(void 0);function Jt(n){const l=S.useContext(n.from?t_:gs);return vt({select:i=>{const c=i.matches.find(f=>n.from?n.from===f.routeId:f.id===l);if(Vn(!((n.shouldThrow??!0)&&!c),`Could not find ${n.from?`an active match from "${n.from}"`:"a nearest match!"}`),c!==void 0)return n.select?n.select(c):c},structuralSharing:n.structuralSharing})}function Jf(n){return Jt({from:n.from,strict:n.strict,structuralSharing:n.structuralSharing,select:l=>n.select?n.select(l.loaderData):l.loaderData})}function ed(n){const{select:l,...o}=n;return Jt({...o,select:i=>l?l(i.loaderDeps):i.loaderDeps})}function td(n){return Jt({from:n.from,strict:n.strict,shouldThrow:n.shouldThrow,structuralSharing:n.structuralSharing,select:l=>n.select?n.select(l.params):l.params})}function nd(n){return Jt({from:n.from,strict:n.strict,shouldThrow:n.shouldThrow,structuralSharing:n.structuralSharing,select:l=>n.select?n.select(l.search):l.search})}function ys(n){const{navigate:l,state:o}=ln(),i=Jt({strict:!1,select:c=>c.index});return S.useCallback(c=>{const f=c.from??(n==null?void 0:n.from)??o.matches[i].fullPath;return l({...c,from:f})},[n==null?void 0:n.from,l])}var bs=zg();const n_=Kf(bs),Vi=typeof window<"u"?S.useLayoutEffect:S.useEffect;function ff(n){const l=S.useRef({value:n,prev:null}),o=l.current.value;return n!==o&&(l.current={value:n,prev:o}),l.current.prev}function a_(n,l,o={},i={}){S.useEffect(()=>{if(!n.current||i.disabled||typeof IntersectionObserver!="function")return;const c=new IntersectionObserver(([f])=>{l(f)},o);return c.observe(n.current),()=>{c.disconnect()}},[l,o,i.disabled,n])}function r_(n){const l=S.useRef(null);return S.useImperativeHandle(n,()=>l.current,[]),l}function o_(n,l){const o=ln(),[i,c]=S.useState(!1),f=S.useRef(!1),d=r_(l),{activeProps:h,inactiveProps:m,activeOptions:p,to:v,preload:g,preloadDelay:b,hashScrollIntoView:w,replace:R,startTransition:E,resetScroll:C,viewTransition:T,children:z,target:L,disabled:N,style:G,className:W,onClick:V,onFocus:q,onMouseEnter:ae,onMouseLeave:Z,onTouchStart:re,ignoreBlocker:te,params:le,search:ue,hash:de,state:O,mask:$,reloadDocument:U,unsafeRelative:Q,from:A,_fromLocation:Y,...J}=n,F=S.useMemo(()=>{try{return new URL(v),"external"}catch{}return"internal"},[v]),ee=vt({select:Ge=>Ge.location.search,structuralSharing:!0}),oe=Jt({strict:!1,select:Ge=>n.from??Ge.fullPath}),ne=S.useMemo(()=>o.buildLocation({...n,from:oe}),[o,ee,n._fromLocation,oe,n.hash,n.to,n.search,n.params,n.state,n.mask,n.unsafeRelative]),fe=F==="external",me=n.reloadDocument||fe?!1:g??o.options.defaultPreload,ge=b??o.options.defaultPreloadDelay??0,Ce=vt({select:Ge=>{if(fe)return!1;if(p!=null&&p.exact){if(!u1(Ge.location.pathname,ne.pathname,o.basepath))return!1}else{const ot=rs(Ge.location.pathname,o.basepath),cn=rs(ne.pathname,o.basepath);if(!(ot.startsWith(cn)&&(ot.length===cn.length||ot[cn.length]==="/")))return!1}return((p==null?void 0:p.includeSearch)??!0)&&!Yr(Ge.location.search,ne.search,{partial:!(p!=null&&p.exact),ignoreUndefined:!(p!=null&&p.explicitUndefined)})?!1:p!=null&&p.includeHash?Ge.location.hash===ne.hash:!0}}),De=S.useCallback(()=>{o.preloadRoute({...n,from:oe}).catch(Ge=>{console.warn(Ge),console.warn(q1)})},[o,n.to,n._fromLocation,oe,n.search,n.hash,n.params,n.state,n.mask,n.unsafeRelative,n.hashScrollIntoView,n.href,n.ignoreBlocker,n.reloadDocument,n.replace,n.resetScroll,n.viewTransition]),He=S.useCallback(Ge=>{Ge!=null&&Ge.isIntersecting&&De()},[De]);if(a_(d,He,u_,{disabled:!!N||me!=="viewport"}),S.useEffect(()=>{f.current||!N&&me==="render"&&(De(),f.current=!0)},[N,De,me]),fe)return{...J,ref:d,type:F,href:v,...z&&{children:z},...L&&{target:L},...N&&{disabled:N},...G&&{style:G},...W&&{className:W},...V&&{onClick:V},...q&&{onFocus:q},...ae&&{onMouseEnter:ae},...Z&&{onMouseLeave:Z},...re&&{onTouchStart:re}};const ut=Ge=>{if(!N&&!f_(Ge)&&!Ge.defaultPrevented&&(!L||L==="_self")&&Ge.button===0){Ge.preventDefault(),bs.flushSync(()=>{c(!0)});const ot=o.subscribe("onResolved",()=>{ot(),c(!1)});return o.navigate({...n,from:oe,replace:R,resetScroll:C,hashScrollIntoView:w,startTransition:E,viewTransition:T,ignoreBlocker:te})}},wn=Ge=>{N||me&&De()},ao=wn,ro=Ge=>{if(!(N||!me))if(!ge)De();else{const ot=Ge.target;if(ll.has(ot))return;const cn=setTimeout(()=>{ll.delete(ot),De()},ge);ll.set(ot,cn)}},xt=Ge=>{if(N||!me||!ge)return;const ot=Ge.target,cn=ll.get(ot);cn&&(clearTimeout(cn),ll.delete(ot))},$n=Ce?Va(h,{})??l_:df,Pt=Ce?df:Va(m,{})??df,oo=[W,$n.className,Pt.className].filter(Boolean).join(" "),_a=(G||$n.style||Pt.style)&&{...G,...$n.style,...Pt.style};return{...J,...$n,...Pt,href:N?void 0:ne.maskedLocation?o.history.createHref(ne.maskedLocation.href):o.history.createHref(ne.href),ref:d,onClick:il([V,ut]),onFocus:il([q,wn]),onMouseEnter:il([ae,ro]),onMouseLeave:il([Z,xt]),onTouchStart:il([re,ao]),disabled:!!N,target:L,..._a&&{style:_a},...oo&&{className:oo},...N&&i_,...Ce&&s_,...i&&c_}}const df={},l_={className:"active"},i_={role:"link","aria-disabled":!0},s_={"data-status":"active","aria-current":"page"},c_={"data-transitioning":"transitioning"},ll=new WeakMap,u_={rootMargin:"100px"},il=n=>l=>{n.filter(Boolean).forEach(o=>{l.defaultPrevented||o(l)})},Ss=S.forwardRef((n,l)=>{const{_asChild:o,...i}=n,{type:c,ref:f,...d}=o_(i,l),h=typeof i.children=="function"?i.children({isActive:d["data-status"]==="active"}):i.children;return o===void 0&&delete d.disabled,S.createElement(o||"a",{...d,ref:f},h)});function f_(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}let d_=class extends Gg{constructor(l){super(l),this.useMatch=o=>Jt({select:o==null?void 0:o.select,from:this.id,structuralSharing:o==null?void 0:o.structuralSharing}),this.useRouteContext=o=>Jt({...o,from:this.id,select:i=>o!=null&&o.select?o.select(i.context):i.context}),this.useSearch=o=>nd({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.id}),this.useParams=o=>td({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.id}),this.useLoaderDeps=o=>ed({...o,from:this.id}),this.useLoaderData=o=>Jf({...o,from:this.id}),this.useNavigate=()=>ys({from:this.fullPath}),this.Link=Ut.forwardRef((o,i)=>_.jsx(Ss,{ref:i,from:this.fullPath,...o})),this.$$typeof=Symbol.for("react.memo")}};function h_(n){return new d_(n)}class m_ extends Y1{constructor(l){super(l),this.useMatch=o=>Jt({select:o==null?void 0:o.select,from:this.id,structuralSharing:o==null?void 0:o.structuralSharing}),this.useRouteContext=o=>Jt({...o,from:this.id,select:i=>o!=null&&o.select?o.select(i.context):i.context}),this.useSearch=o=>nd({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.id}),this.useParams=o=>td({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.id}),this.useLoaderDeps=o=>ed({...o,from:this.id}),this.useLoaderData=o=>Jf({...o,from:this.id}),this.useNavigate=()=>ys({from:this.fullPath}),this.Link=Ut.forwardRef((o,i)=>_.jsx(Ss,{ref:i,from:this.fullPath,...o})),this.$$typeof=Symbol.for("react.memo")}}function p_(n){return new m_(n)}function ct(n){return typeof n=="object"?new qv(n,{silent:!0}).createRoute(n):new qv(n,{silent:!0}).createRoute}class qv{constructor(l,o){this.path=l,this.createRoute=i=>{this.silent;const c=h_(i);return c.isRoot=!1,c},this.silent=o==null?void 0:o.silent}}class Yv{constructor(l){this.useMatch=o=>Jt({select:o==null?void 0:o.select,from:this.options.id,structuralSharing:o==null?void 0:o.structuralSharing}),this.useRouteContext=o=>Jt({from:this.options.id,select:i=>o!=null&&o.select?o.select(i.context):i.context}),this.useSearch=o=>nd({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.options.id}),this.useParams=o=>td({select:o==null?void 0:o.select,structuralSharing:o==null?void 0:o.structuralSharing,from:this.options.id}),this.useLoaderDeps=o=>ed({...o,from:this.options.id}),this.useLoaderData=o=>Jf({...o,from:this.options.id}),this.useNavigate=()=>{const o=ln();return ys({from:o.routesById[this.options.id].fullPath})},this.options=l,this.$$typeof=Symbol.for("react.memo")}}function $v(n){return typeof n=="object"?new Yv(n):l=>new Yv({id:n,...l})}function gt(n,l){let o,i,c,f;const d=()=>(o||(o=n().then(m=>{o=void 0,i=m[l]}).catch(m=>{if(c=m,c1(c)&&c instanceof Error&&typeof window<"u"&&typeof sessionStorage<"u"){const p=`tanstack_router_reload:${c.message}`;sessionStorage.getItem(p)||(sessionStorage.setItem(p,"1"),f=!0)}})),o),h=function(p){if(f)throw window.location.reload(),new Promise(()=>{});if(c)throw c;if(!i)throw d();return S.createElement(i,p)};return h.preload=d,h}function v_(){const n=ln(),l=S.useRef({router:n,mounted:!1}),[o,i]=S.useState(!1),{hasPendingMatches:c,isLoading:f}=vt({select:g=>({isLoading:g.isLoading,hasPendingMatches:g.matches.some(b=>b.status==="pending")}),structuralSharing:!0}),d=ff(f),h=f||o||c,m=ff(h),p=f||c,v=ff(p);return n.startTransition=g=>{i(!0),S.startTransition(()=>{g(),i(!1)})},S.useEffect(()=>{const g=n.history.subscribe(n.load),b=n.buildLocation({to:n.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return Kr(n.latestLocation.href)!==Kr(b.href)&&n.commitLocation({...b,replace:!0}),()=>{g()}},[n,n.history]),Vi(()=>{if(typeof window<"u"&&n.ssr||l.current.router===n&&l.current.mounted)return;l.current={router:n,mounted:!0},(async()=>{try{await n.load()}catch(b){console.error(b)}})()},[n]),Vi(()=>{d&&!f&&n.emit({type:"onLoad",...qa(n.state)})},[d,n,f]),Vi(()=>{v&&!p&&n.emit({type:"onBeforeRouteMount",...qa(n.state)})},[p,v,n]),Vi(()=>{m&&!h&&(n.emit({type:"onResolved",...qa(n.state)}),n.__store.setState(g=>({...g,status:"idle",resolvedLocation:g.location})),C1(n))},[h,m,n]),null}function g_(n){const l=vt({select:o=>`not-found-${o.location.pathname}-${o.status}`});return _.jsx(Wf,{getResetKey:()=>l,onCatch:(o,i)=>{var c;if(Zt(o))(c=n.onCatch)==null||c.call(n,o,i);else throw o},errorComponent:({error:o})=>{var i;if(Zt(o))return(i=n.fallback)==null?void 0:i.call(n,o);throw o},children:n.children})}function y_(){return _.jsx("p",{children:"Not Found"})}function Gr(n){return _.jsx(_.Fragment,{children:n.children})}function Yg(n,l,o){return l.options.notFoundComponent?_.jsx(l.options.notFoundComponent,{data:o}):n.options.defaultNotFoundComponent?_.jsx(n.options.defaultNotFoundComponent,{data:o}):_.jsx(y_,{})}function b_({children:n}){return typeof document<"u"?null:_.jsx("script",{className:"$tsr",dangerouslySetInnerHTML:{__html:[n].filter(Boolean).join(`
`)}})}function S_(){const n=ln(),o=(n.options.getScrollRestorationKey||Lf)(n.latestLocation),i=o!==Lf(n.latestLocation)?o:null;return!n.isScrollRestoring||!n.isServer?null:_.jsx(b_,{children:`(${Pg.toString()})(${JSON.stringify(ls)},${JSON.stringify(i)}, undefined, true)`})}const $g=S.memo(function({matchId:l}){var o,i;const c=ln(),f=vt({select:L=>{const N=L.matches.find(G=>G.id===l);return Vn(N),as(N,["routeId","ssr","_displayPending"])},structuralSharing:!0}),d=c.routesById[f.routeId],h=d.options.pendingComponent??c.options.defaultPendingComponent,m=h?_.jsx(h,{}):null,p=d.options.errorComponent??c.options.defaultErrorComponent,v=d.options.onCatch??c.options.defaultOnCatch,g=d.isRoot?d.options.notFoundComponent??((o=c.options.notFoundRoute)==null?void 0:o.options.component):d.options.notFoundComponent,b=f.ssr===!1||f.ssr==="data-only",w=(!d.isRoot||d.options.wrapInSuspense||b)&&(d.options.wrapInSuspense??h??(((i=d.options.errorComponent)==null?void 0:i.preload)||b))?S.Suspense:Gr,R=p?Wf:Gr,E=g?g_:Gr,C=vt({select:L=>L.loadedAt}),T=vt({select:L=>{var N;const G=L.matches.findIndex(W=>W.id===l);return(N=L.matches[G-1])==null?void 0:N.routeId}}),z=d.isRoot?d.options.shellComponent??Gr:Gr;return _.jsxs(z,{children:[_.jsx(gs.Provider,{value:l,children:_.jsx(w,{fallback:m,children:_.jsx(R,{getResetKey:()=>C,errorComponent:p||vs,onCatch:(L,N)=>{if(Zt(L))throw L;v==null||v(L,N)},children:_.jsx(E,{fallback:L=>{if(!g||L.routeId&&L.routeId!==f.routeId||!L.routeId&&!d.isRoot)throw L;return S.createElement(g,L)},children:b||f._displayPending?_.jsx(I1,{fallback:m,children:_.jsx(Iv,{matchId:l})}):_.jsx(Iv,{matchId:l})})})})}),T===Ft&&c.options.scrollRestoration?_.jsxs(_.Fragment,{children:[_.jsx(x_,{}),_.jsx(S_,{})]}):null]})});function x_(){const n=ln(),l=S.useRef(void 0);return _.jsx("script",{suppressHydrationWarning:!0,ref:o=>{o&&(l.current===void 0||l.current.href!==n.latestLocation.href)&&(n.emit({type:"onRendered",...qa(n.state)}),l.current=n.latestLocation)}},n.latestLocation.state.__TSR_key)}const Iv=S.memo(function({matchId:l}){var o,i,c,f,d;const h=ln(),{match:m,key:p,routeId:v}=vt({select:w=>{const R=w.matches.findIndex(N=>N.id===l),E=w.matches[R],C=E.routeId,T=h.routesById[C].options.remountDeps??h.options.defaultRemountDeps,z=T==null?void 0:T({routeId:C,loaderDeps:E.loaderDeps,params:E._strictParams,search:E._strictSearch});return{key:z?JSON.stringify(z):void 0,routeId:C,match:as(E,["id","status","error","_forcePending","_displayPending"])}},structuralSharing:!0}),g=h.routesById[v],b=S.useMemo(()=>{const w=g.options.component??h.options.defaultComponent;return w?_.jsx(w,{},p):_.jsx(zf,{})},[p,g.options.component,h.options.defaultComponent]);if(m._displayPending)throw(o=h.getMatch(m.id))==null?void 0:o.displayPendingPromise;if(m._forcePending)throw(i=h.getMatch(m.id))==null?void 0:i.minPendingPromise;if(m.status==="pending"){const w=g.options.pendingMinMs??h.options.defaultPendingMinMs;if(w&&!((c=h.getMatch(m.id))!=null&&c.minPendingPromise)&&!h.isServer){const R=Hr();Promise.resolve().then(()=>{h.updateMatch(m.id,E=>({...E,minPendingPromise:R}))}),setTimeout(()=>{R.resolve(),h.updateMatch(m.id,E=>({...E,minPendingPromise:void 0}))},w)}throw(f=h.getMatch(m.id))==null?void 0:f.loadPromise}if(m.status==="notFound")return Vn(Zt(m.error)),Yg(h,g,m.error);if(m.status==="redirected")throw Vn(pn(m.error)),(d=h.getMatch(m.id))==null?void 0:d.loadPromise;if(m.status==="error"){if(h.isServer){const w=(g.options.errorComponent??h.options.defaultErrorComponent)||vs;return _.jsx(w,{error:m.error,reset:void 0,info:{componentStack:""}})}throw m.error}return b}),zf=S.memo(function(){const l=ln(),o=S.useContext(gs),i=vt({select:p=>{var v;return(v=p.matches.find(g=>g.id===o))==null?void 0:v.routeId}}),c=l.routesById[i],f=vt({select:p=>{const g=p.matches.find(b=>b.id===o);return Vn(g),g.globalNotFound}}),d=vt({select:p=>{var v;const g=p.matches,b=g.findIndex(w=>w.id===o);return(v=g[b+1])==null?void 0:v.id}}),h=l.options.defaultPendingComponent?_.jsx(l.options.defaultPendingComponent,{}):null;if(f)return Yg(l,c,void 0);if(!d)return null;const m=_.jsx($g,{matchId:d});return o===Ft?_.jsx(S.Suspense,{fallback:h,children:m}):m});function __(){const n=ln(),l=n.options.defaultPendingComponent?_.jsx(n.options.defaultPendingComponent,{}):null,o=n.isServer||typeof document<"u"&&n.ssr?Gr:S.Suspense,i=_.jsxs(o,{fallback:l,children:[!n.isServer&&_.jsx(v_,{}),_.jsx(w_,{})]});return n.options.InnerWrap?_.jsx(n.options.InnerWrap,{children:i}):i}function w_(){const n=vt({select:o=>{var i;return(i=o.matches[0])==null?void 0:i.id}}),l=vt({select:o=>o.loadedAt});return _.jsx(gs.Provider,{value:n,children:_.jsx(Wf,{getResetKey:()=>l,errorComponent:vs,onCatch:o=>{o.message||o.toString()},children:n?_.jsx($g,{matchId:n}):null})})}const E_=n=>new R_(n);class R_ extends j1{constructor(l){super(l)}}typeof globalThis<"u"?(globalThis.createFileRoute=ct,globalThis.createLazyFileRoute=$v):typeof window<"u"&&(window.createFileRoute=ct,window.createFileRoute=$v);function C_({router:n,children:l,...o}){Object.keys(o).length>0&&n.update({...n.options,...o,context:{...n.options.context,...o.context}});const i=qg(),c=_.jsx(i.Provider,{value:n,children:l});return n.options.Wrap?_.jsx(n.options.Wrap,{children:c}):c}function A_({router:n,...l}){return _.jsx(C_,{router:n,...l,children:_.jsx(__,{})})}function M_(n){return vt({select:l=>l.location})}const T_="modulepreload",O_=function(n){return"/"+n},Xv={},yt=function(l,o,i){let c=Promise.resolve();if(o&&o.length>0){let d=function(p){return Promise.all(p.map(v=>Promise.resolve(v).then(g=>({status:"fulfilled",value:g}),g=>({status:"rejected",reason:g}))))};document.getElementsByTagName("link");const h=document.querySelector("meta[property=csp-nonce]"),m=(h==null?void 0:h.nonce)||(h==null?void 0:h.getAttribute("nonce"));c=d(o.map(p=>{if(p=O_(p),p in Xv)return;Xv[p]=!0;const v=p.endsWith(".css"),g=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${g}`))return;const b=document.createElement("link");if(b.rel=v?"stylesheet":T_,v||(b.as="script"),b.crossOrigin="",b.href=p,m&&b.setAttribute("nonce",m),document.head.appendChild(b),v)return new Promise((w,R)=>{b.addEventListener("load",w),b.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${p}`)))})}))}function f(d){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=d,window.dispatchEvent(h),!h.defaultPrevented)throw d}return c.then(d=>{for(const h of d||[])h.status==="rejected"&&f(h.reason);return l().catch(f)})},Kv=function(){return null},D_={bodySerializer:n=>JSON.stringify(n,(l,o)=>typeof o=="bigint"?o.toString():o)},N_=async(n,l)=>{const o=typeof l=="function"?await l(n):l;if(o)return n.scheme==="bearer"?`Bearer ${o}`:n.scheme==="basic"?`Basic ${btoa(o)}`:o},L_=n=>{switch(n){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},j_=n=>{switch(n){case"form":return",";case"pipeDelimited":return"|";case"spaceDelimited":return"%20";default:return","}},z_=n=>{switch(n){case"label":return".";case"matrix":return";";case"simple":return",";default:return"&"}},Ig=({allowReserved:n,explode:l,name:o,style:i,value:c})=>{if(!l){const h=(n?c:c.map(m=>encodeURIComponent(m))).join(j_(i));switch(i){case"label":return`.${h}`;case"matrix":return`;${o}=${h}`;case"simple":return h;default:return`${o}=${h}`}}const f=L_(i),d=c.map(h=>i==="label"||i==="simple"?n?h:encodeURIComponent(h):xs({allowReserved:n,name:o,value:h})).join(f);return i==="label"||i==="matrix"?f+d:d},xs=({allowReserved:n,name:l,value:o})=>{if(o==null)return"";if(typeof o=="object")throw new Error("Deeply-nested arrays/objects aren’t supported. Provide your own `querySerializer()` to handle these.");return`${l}=${n?o:encodeURIComponent(o)}`},Xg=({allowReserved:n,explode:l,name:o,style:i,value:c,valueOnly:f})=>{if(c instanceof Date)return f?c.toISOString():`${o}=${c.toISOString()}`;if(i!=="deepObject"&&!l){let m=[];Object.entries(c).forEach(([v,g])=>{m=[...m,v,n?g:encodeURIComponent(g)]});const p=m.join(",");switch(i){case"form":return`${o}=${p}`;case"label":return`.${p}`;case"matrix":return`;${o}=${p}`;default:return p}}const d=z_(i),h=Object.entries(c).map(([m,p])=>xs({allowReserved:n,name:i==="deepObject"?`${o}[${m}]`:m,value:p})).join(d);return i==="label"||i==="matrix"?d+h:h},U_=/\{[^{}]+\}/g,k_=({path:n,url:l})=>{let o=l;const i=l.match(U_);if(i)for(const c of i){let f=!1,d=c.substring(1,c.length-1),h="simple";d.endsWith("*")&&(f=!0,d=d.substring(0,d.length-1)),d.startsWith(".")?(d=d.substring(1),h="label"):d.startsWith(";")&&(d=d.substring(1),h="matrix");const m=n[d];if(m==null)continue;if(Array.isArray(m)){o=o.replace(c,Ig({explode:f,name:d,style:h,value:m}));continue}if(typeof m=="object"){o=o.replace(c,Xg({explode:f,name:d,style:h,value:m,valueOnly:!0}));continue}if(h==="matrix"){o=o.replace(c,`;${xs({name:d,value:m})}`);continue}const p=encodeURIComponent(h==="label"?`.${m}`:m);o=o.replace(c,p)}return o},Kg=({allowReserved:n,array:l,object:o}={})=>c=>{const f=[];if(c&&typeof c=="object")for(const d in c){const h=c[d];if(h!=null)if(Array.isArray(h)){const m=Ig({allowReserved:n,explode:!0,name:d,style:"form",value:h,...l});m&&f.push(m)}else if(typeof h=="object"){const m=Xg({allowReserved:n,explode:!0,name:d,style:"deepObject",value:h,...o});m&&f.push(m)}else{const m=xs({allowReserved:n,name:d,value:h});m&&f.push(m)}}return f.join("&")},B_=n=>{var o;if(!n)return"stream";const l=(o=n.split(";")[0])==null?void 0:o.trim();if(l){if(l.startsWith("application/json")||l.endsWith("+json"))return"json";if(l==="multipart/form-data")return"formData";if(["application/","audio/","image/","video/"].some(i=>l.startsWith(i)))return"blob";if(l.startsWith("text/"))return"text"}},P_=async({security:n,...l})=>{for(const o of n){const i=await N_(o,l.auth);if(!i)continue;const c=o.name??"Authorization";switch(o.in){case"query":l.query||(l.query={}),l.query[c]=i;break;case"cookie":l.headers.append("Cookie",`${c}=${i}`);break;case"header":default:l.headers.set(c,i);break}return}},Qv=n=>H_({baseUrl:n.baseUrl,path:n.path,query:n.query,querySerializer:typeof n.querySerializer=="function"?n.querySerializer:Kg(n.querySerializer),url:n.url}),H_=({baseUrl:n,path:l,query:o,querySerializer:i,url:c})=>{const f=c.startsWith("/")?c:`/${c}`;let d=(n??"")+f;l&&(d=k_({path:l,url:d}));let h=o?i(o):"";return h.startsWith("?")&&(h=h.substring(1)),h&&(d+=`?${h}`),d},Zv=(n,l)=>{var i;const o={...n,...l};return(i=o.baseUrl)!=null&&i.endsWith("/")&&(o.baseUrl=o.baseUrl.substring(0,o.baseUrl.length-1)),o.headers=Qg(n.headers,l.headers),o},Qg=(...n)=>{const l=new Headers;for(const o of n){if(!o||typeof o!="object")continue;const i=o instanceof Headers?o.entries():Object.entries(o);for(const[c,f]of i)if(f===null)l.delete(c);else if(Array.isArray(f))for(const d of f)l.append(c,d);else f!==void 0&&l.set(c,typeof f=="object"?JSON.stringify(f):f)}return l};class hf{constructor(){bv(this,"_fns");this._fns=[]}clear(){this._fns=[]}getInterceptorIndex(l){return typeof l=="number"?this._fns[l]?l:-1:this._fns.indexOf(l)}exists(l){const o=this.getInterceptorIndex(l);return!!this._fns[o]}eject(l){const o=this.getInterceptorIndex(l);this._fns[o]&&(this._fns[o]=null)}update(l,o){const i=this.getInterceptorIndex(l);return this._fns[i]?(this._fns[i]=o,l):!1}use(l){return this._fns=[...this._fns,l],this._fns.length-1}}const G_=()=>({error:new hf,request:new hf,response:new hf}),V_=Kg({allowReserved:!1,array:{explode:!0,style:"form"},object:{explode:!0,style:"deepObject"}}),q_={"Content-Type":"application/json"},Zg=(n={})=>({...D_,headers:q_,parseAs:"auto",querySerializer:V_,...n}),Y_=(n={})=>{let l=Zv(Zg(),n);const o=()=>({...l}),i=d=>(l=Zv(l,d),o()),c=G_(),f=async d=>{const h={...l,...d,fetch:d.fetch??l.fetch??globalThis.fetch,headers:Qg(l.headers,d.headers)};h.security&&await P_({...h,security:h.security}),h.requestValidator&&await h.requestValidator(h),h.body&&h.bodySerializer&&(h.body=h.bodySerializer(h.body)),(h.body===void 0||h.body==="")&&h.headers.delete("Content-Type");const m=Qv(h),p={redirect:"follow",...h};let v=new Request(m,p);for(const z of c.request._fns)z&&(v=await z(v,h));const g=h.fetch;let b=await g(v);for(const z of c.response._fns)z&&(b=await z(b,v,h));const w={request:v,response:b};if(b.ok){if(b.status===204||b.headers.get("Content-Length")==="0")return h.responseStyle==="data"?{}:{data:{},...w};const z=(h.parseAs==="auto"?B_(b.headers.get("Content-Type")):h.parseAs)??"json";let L;switch(z){case"arrayBuffer":case"blob":case"formData":case"json":case"text":L=await b[z]();break;case"stream":return h.responseStyle==="data"?b.body:{data:b.body,...w}}return z==="json"&&(h.responseValidator&&await h.responseValidator(L),h.responseTransformer&&(L=await h.responseTransformer(L))),h.responseStyle==="data"?L:{data:L,...w}}const R=await b.text();let E;try{E=JSON.parse(R)}catch{}const C=E??R;let T=C;for(const z of c.error._fns)z&&(T=await z(C,b,v,h));if(T=T||{},h.throwOnError)throw T;return h.responseStyle==="data"?void 0:{error:T,...w}};return{buildUrl:Qv,connect:d=>f({...d,method:"CONNECT"}),delete:d=>f({...d,method:"DELETE"}),get:d=>f({...d,method:"GET"}),getConfig:o,head:d=>f({...d,method:"HEAD"}),interceptors:c,options:d=>f({...d,method:"OPTIONS"}),patch:d=>f({...d,method:"PATCH"}),post:d=>f({...d,method:"POST"}),put:d=>f({...d,method:"PUT"}),request:f,setConfig:i,trace:d=>f({...d,method:"TRACE"})}},Te=Y_(Zg()),$_=n=>(n.client??Te).post({url:"/api/accounts/login",...n,headers:{"Content-Type":"application/json",...n.headers}}),tO=n=>((n==null?void 0:n.client)??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/accounts/",...n}),nO=n=>(n.client??Te).post({security:[{scheme:"bearer",type:"http"}],url:"/api/accounts/",...n,headers:{"Content-Type":"application/json",...n.headers}}),aO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/accounts/{user_id}",...n}),rO=n=>(n.client??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/accounts/{user_id}",...n}),oO=n=>(n.client??Te).put({security:[{scheme:"bearer",type:"http"}],url:"/api/accounts/{user_id}",...n,headers:{"Content-Type":"application/json",...n.headers}}),lO=n=>(n.client??Te).get({url:"/api/merchants/offices/get-by-phone-number/",...n}),iO=n=>(n.client??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/merchants/offices/{office_id}/employees/",...n}),sO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/merchants/offices/{office_id}/employees/{employee_id}",...n}),cO=n=>((n==null?void 0:n.client)??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/companies/",...n}),uO=n=>(n.client??Te).post({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/companies/",...n,headers:{"Content-Type":"application/json",...n.headers}}),fO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/companies/{company_id}",...n}),dO=n=>(n.client??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/companies/{company_id}",...n}),hO=n=>(n.client??Te).put({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/companies/{company_id}",...n,headers:{"Content-Type":"application/json",...n.headers}}),mO=n=>((n==null?void 0:n.client)??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/company-channels/",...n}),pO=n=>(n.client??Te).post({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/company-channels/",...n,headers:{"Content-Type":"application/json",...n.headers}}),vO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/company-channels/{channel_id}",...n}),gO=n=>(n.client??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/company-channels/{channel_id}",...n}),yO=n=>(n.client??Te).put({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/company-channels/{channel_id}",...n,headers:{"Content-Type":"application/json",...n.headers}}),bO=n=>((n==null?void 0:n.client)??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/cancellation-templates/",...n}),SO=n=>(n.client??Te).post({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/cancellation-templates/",...n,headers:{"Content-Type":"application/json",...n.headers}}),xO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/cancellation-templates/{template_id}",...n}),_O=n=>(n.client??Te).put({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/cancellation-templates/{template_id}",...n,headers:{"Content-Type":"application/json",...n.headers}}),wO=n=>((n==null?void 0:n.client)??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/",...n}),EO=n=>(n.client??Te).post({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/",...n,headers:{"Content-Type":"application/json",...n.headers}}),RO=n=>(n.client??Te).delete({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/{order_id}",...n}),CO=n=>(n.client??Te).get({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/{order_id}",...n}),AO=n=>(n.client??Te).put({security:[{scheme:"bearer",type:"http"}],url:"/api/orders/{order_id}",...n,headers:{"Content-Type":"application/json",...n.headers}}),Fg=S.createContext(void 0),I_=({children:n})=>{const[l,o]=S.useState(null),[i,c]=S.useState(null),[f,d]=S.useState(null),[h,m]=S.useState(!0);S.useEffect(()=>{const R=localStorage.getItem("auth_token"),E=localStorage.getItem("auth_user"),C=localStorage.getItem("selected_office");if(R&&E)try{const T=JSON.parse(E),z=C?JSON.parse(C):null;c(R),o(T),d(z),Te.setConfig({baseUrl:"http://localhost:8000",auth:R,headers:{Authorization:`Bearer ${R}`}})}catch(T){console.error("Error parsing stored auth data:",T),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("selected_office")}m(!1)},[]);const w={user:l,token:i,selectedOffice:f,isAuthenticated:!!l&&!!i,isLoading:h,login:async(R,E,C)=>{var T,z,L;try{m(!0);const N=await $_({body:{phone_number:R,password:E,office_id:C}});if(N.data){const{token:G,user:W}=N.data;c(G),o(W),d(N.data.office),localStorage.setItem("auth_token",G),localStorage.setItem("auth_user",JSON.stringify(W)),localStorage.setItem("selected_office",JSON.stringify(N.data.office)),Te.setConfig({baseUrl:"http://localhost:8000",auth:G,headers:{Authorization:`Bearer ${G}`}})}else throw new Error("Invalid response from server")}catch(N){throw console.error("Login error:",N),c(null),o(null),d(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("selected_office"),((T=N==null?void 0:N.response)==null?void 0:T.status)===401?new Error("رقم الهاتف أو كلمة المرور غير صحيحة"):((z=N==null?void 0:N.response)==null?void 0:z.status)===403?new Error("ليس لديك صلاحية للوصول إلى هذا المكتب"):((L=N==null?void 0:N.response)==null?void 0:L.status)>=500?new Error("خطأ في الخادم، يرجى المحاولة لاحقاً"):navigator.onLine?new Error("حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى"):new Error("لا يوجد اتصال بالإنترنت")}finally{m(!1)}},logout:()=>{o(null),c(null),d(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("selected_office"),Te.setConfig({baseUrl:"http://localhost:8000",auth:void 0})},setSelectedOffice:R=>{d(R),R?localStorage.setItem("selected_office",JSON.stringify(R)):localStorage.removeItem("selected_office")}};return _.jsx(Fg.Provider,{value:w,children:n})},Wg=()=>{const n=S.useContext(Fg);if(n===void 0)throw new Error("useAuth must be used within an AuthProvider");return n};function Fv(n,l){if(typeof n=="function")return n(l);n!=null&&(n.current=l)}function _s(...n){return l=>{let o=!1;const i=n.map(c=>{const f=Fv(c,l);return!o&&typeof f=="function"&&(o=!0),f});if(o)return()=>{for(let c=0;c<i.length;c++){const f=i[c];typeof f=="function"?f():Fv(n[c],null)}}}}function et(...n){return S.useCallback(_s(...n),n)}function Zr(n){const l=X_(n),o=S.forwardRef((i,c)=>{const{children:f,...d}=i,h=S.Children.toArray(f),m=h.find(Q_);if(m){const p=m.props.children,v=h.map(g=>g===m?S.Children.count(p)>1?S.Children.only(null):S.isValidElement(p)?p.props.children:null:g);return _.jsx(l,{...d,ref:c,children:S.isValidElement(p)?S.cloneElement(p,void 0,v):null})}return _.jsx(l,{...d,ref:c,children:f})});return o.displayName=`${n}.Slot`,o}var Jg=Zr("Slot");function X_(n){const l=S.forwardRef((o,i)=>{const{children:c,...f}=o;if(S.isValidElement(c)){const d=F_(c),h=Z_(f,c.props);return c.type!==S.Fragment&&(h.ref=i?_s(i,d):d),S.cloneElement(c,h)}return S.Children.count(c)>1?S.Children.only(null):null});return l.displayName=`${n}.SlotClone`,l}var ey=Symbol("radix.slottable");function K_(n){const l=({children:o})=>_.jsx(_.Fragment,{children:o});return l.displayName=`${n}.Slottable`,l.__radixId=ey,l}function Q_(n){return S.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===ey}function Z_(n,l){const o={...l};for(const i in l){const c=n[i],f=l[i];/^on[A-Z]/.test(i)?c&&f?o[i]=(...h)=>{const m=f(...h);return c(...h),m}:c&&(o[i]=c):i==="style"?o[i]={...c,...f}:i==="className"&&(o[i]=[c,f].filter(Boolean).join(" "))}return{...n,...o}}function F_(n){var i,c;let l=(i=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:i.get,o=l&&"isReactWarning"in l&&l.isReactWarning;return o?n.ref:(l=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,o=l&&"isReactWarning"in l&&l.isReactWarning,o?n.props.ref:n.props.ref||n.ref)}function ty(n){var l,o,i="";if(typeof n=="string"||typeof n=="number")i+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(l=0;l<c;l++)n[l]&&(o=ty(n[l]))&&(i&&(i+=" "),i+=o)}else for(o in n)n[o]&&(i&&(i+=" "),i+=o);return i}function ny(){for(var n,l,o=0,i="",c=arguments.length;o<c;o++)(n=arguments[o])&&(l=ty(n))&&(i&&(i+=" "),i+=l);return i}const Wv=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Jv=ny,ay=(n,l)=>o=>{var i;if((l==null?void 0:l.variants)==null)return Jv(n,o==null?void 0:o.class,o==null?void 0:o.className);const{variants:c,defaultVariants:f}=l,d=Object.keys(c).map(p=>{const v=o==null?void 0:o[p],g=f==null?void 0:f[p];if(v===null)return null;const b=Wv(v)||Wv(g);return c[p][b]}),h=o&&Object.entries(o).reduce((p,v)=>{let[g,b]=v;return b===void 0||(p[g]=b),p},{}),m=l==null||(i=l.compoundVariants)===null||i===void 0?void 0:i.reduce((p,v)=>{let{class:g,className:b,...w}=v;return Object.entries(w).every(R=>{let[E,C]=R;return Array.isArray(C)?C.includes({...f,...h}[E]):{...f,...h}[E]===C})?[...p,g,b]:p},[]);return Jv(n,d,m,o==null?void 0:o.class,o==null?void 0:o.className)};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W_=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),J_=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,o,i)=>i?i.toUpperCase():o.toLowerCase()),eg=n=>{const l=J_(n);return l.charAt(0).toUpperCase()+l.slice(1)},ry=(...n)=>n.filter((l,o,i)=>!!l&&l.trim()!==""&&i.indexOf(l)===o).join(" ").trim(),ew=n=>{for(const l in n)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var tw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nw=S.forwardRef(({color:n="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:c="",children:f,iconNode:d,...h},m)=>S.createElement("svg",{ref:m,...tw,width:l,height:l,stroke:n,strokeWidth:i?Number(o)*24/Number(l):o,className:ry("lucide",c),...!f&&!ew(h)&&{"aria-hidden":"true"},...h},[...d.map(([p,v])=>S.createElement(p,v)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ad=(n,l)=>{const o=S.forwardRef(({className:i,...c},f)=>S.createElement(nw,{ref:f,iconNode:l,className:ry(`lucide-${W_(eg(n))}`,`lucide-${n}`,i),...c}));return o.displayName=eg(n),o};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aw=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],rw=ad("check",aw);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ow=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],lw=ad("panel-left",ow);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iw=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],sw=ad("x",iw),mf=768;function cw(){const[n,l]=S.useState(void 0);return S.useEffect(()=>{const o=window.matchMedia(`(max-width: ${mf-1}px)`),i=()=>{l(window.innerWidth<mf)};return o.addEventListener("change",i),l(window.innerWidth<mf),()=>o.removeEventListener("change",i)},[]),!!n}const rd="-",uw=n=>{const l=dw(n),{conflictingClassGroups:o,conflictingClassGroupModifiers:i}=n;return{getClassGroupId:d=>{const h=d.split(rd);return h[0]===""&&h.length!==1&&h.shift(),oy(h,l)||fw(d)},getConflictingClassGroupIds:(d,h)=>{const m=o[d]||[];return h&&i[d]?[...m,...i[d]]:m}}},oy=(n,l)=>{var d;if(n.length===0)return l.classGroupId;const o=n[0],i=l.nextPart.get(o),c=i?oy(n.slice(1),i):void 0;if(c)return c;if(l.validators.length===0)return;const f=n.join(rd);return(d=l.validators.find(({validator:h})=>h(f)))==null?void 0:d.classGroupId},tg=/^\[(.+)\]$/,fw=n=>{if(tg.test(n)){const l=tg.exec(n)[1],o=l==null?void 0:l.substring(0,l.indexOf(":"));if(o)return"arbitrary.."+o}},dw=n=>{const{theme:l,classGroups:o}=n,i={nextPart:new Map,validators:[]};for(const c in o)Uf(o[c],i,c,l);return i},Uf=(n,l,o,i)=>{n.forEach(c=>{if(typeof c=="string"){const f=c===""?l:ng(l,c);f.classGroupId=o;return}if(typeof c=="function"){if(hw(c)){Uf(c(i),l,o,i);return}l.validators.push({validator:c,classGroupId:o});return}Object.entries(c).forEach(([f,d])=>{Uf(d,ng(l,f),o,i)})})},ng=(n,l)=>{let o=n;return l.split(rd).forEach(i=>{o.nextPart.has(i)||o.nextPart.set(i,{nextPart:new Map,validators:[]}),o=o.nextPart.get(i)}),o},hw=n=>n.isThemeGetter,mw=n=>{if(n<1)return{get:()=>{},set:()=>{}};let l=0,o=new Map,i=new Map;const c=(f,d)=>{o.set(f,d),l++,l>n&&(l=0,i=o,o=new Map)};return{get(f){let d=o.get(f);if(d!==void 0)return d;if((d=i.get(f))!==void 0)return c(f,d),d},set(f,d){o.has(f)?o.set(f,d):c(f,d)}}},kf="!",Bf=":",pw=Bf.length,vw=n=>{const{prefix:l,experimentalParseClassName:o}=n;let i=c=>{const f=[];let d=0,h=0,m=0,p;for(let R=0;R<c.length;R++){let E=c[R];if(d===0&&h===0){if(E===Bf){f.push(c.slice(m,R)),m=R+pw;continue}if(E==="/"){p=R;continue}}E==="["?d++:E==="]"?d--:E==="("?h++:E===")"&&h--}const v=f.length===0?c:c.substring(m),g=gw(v),b=g!==v,w=p&&p>m?p-m:void 0;return{modifiers:f,hasImportantModifier:b,baseClassName:g,maybePostfixModifierPosition:w}};if(l){const c=l+Bf,f=i;i=d=>d.startsWith(c)?f(d.substring(c.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(o){const c=i;i=f=>o({className:f,parseClassName:c})}return i},gw=n=>n.endsWith(kf)?n.substring(0,n.length-1):n.startsWith(kf)?n.substring(1):n,yw=n=>{const l=Object.fromEntries(n.orderSensitiveModifiers.map(i=>[i,!0]));return i=>{if(i.length<=1)return i;const c=[];let f=[];return i.forEach(d=>{d[0]==="["||l[d]?(c.push(...f.sort(),d),f=[]):f.push(d)}),c.push(...f.sort()),c}},bw=n=>({cache:mw(n.cacheSize),parseClassName:vw(n),sortModifiers:yw(n),...uw(n)}),Sw=/\s+/,xw=(n,l)=>{const{parseClassName:o,getClassGroupId:i,getConflictingClassGroupIds:c,sortModifiers:f}=l,d=[],h=n.trim().split(Sw);let m="";for(let p=h.length-1;p>=0;p-=1){const v=h[p],{isExternal:g,modifiers:b,hasImportantModifier:w,baseClassName:R,maybePostfixModifierPosition:E}=o(v);if(g){m=v+(m.length>0?" "+m:m);continue}let C=!!E,T=i(C?R.substring(0,E):R);if(!T){if(!C){m=v+(m.length>0?" "+m:m);continue}if(T=i(R),!T){m=v+(m.length>0?" "+m:m);continue}C=!1}const z=f(b).join(":"),L=w?z+kf:z,N=L+T;if(d.includes(N))continue;d.push(N);const G=c(T,C);for(let W=0;W<G.length;++W){const V=G[W];d.push(L+V)}m=v+(m.length>0?" "+m:m)}return m};function _w(){let n=0,l,o,i="";for(;n<arguments.length;)(l=arguments[n++])&&(o=ly(l))&&(i&&(i+=" "),i+=o);return i}const ly=n=>{if(typeof n=="string")return n;let l,o="";for(let i=0;i<n.length;i++)n[i]&&(l=ly(n[i]))&&(o&&(o+=" "),o+=l);return o};function ww(n,...l){let o,i,c,f=d;function d(m){const p=l.reduce((v,g)=>g(v),n());return o=bw(p),i=o.cache.get,c=o.cache.set,f=h,h(m)}function h(m){const p=i(m);if(p)return p;const v=xw(m,o);return c(m,v),v}return function(){return f(_w.apply(null,arguments))}}const Je=n=>{const l=o=>o[n]||[];return l.isThemeGetter=!0,l},iy=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,sy=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ew=/^\d+\/\d+$/,Rw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Cw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Aw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Mw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Tw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ur=n=>Ew.test(n),_e=n=>!!n&&!Number.isNaN(Number(n)),va=n=>!!n&&Number.isInteger(Number(n)),pf=n=>n.endsWith("%")&&_e(n.slice(0,-1)),Pn=n=>Rw.test(n),Ow=()=>!0,Dw=n=>Cw.test(n)&&!Aw.test(n),cy=()=>!1,Nw=n=>Mw.test(n),Lw=n=>Tw.test(n),jw=n=>!se(n)&&!ce(n),zw=n=>Jr(n,dy,cy),se=n=>iy.test(n),Ga=n=>Jr(n,hy,Dw),vf=n=>Jr(n,Hw,_e),ag=n=>Jr(n,uy,cy),Uw=n=>Jr(n,fy,Lw),qi=n=>Jr(n,my,Nw),ce=n=>sy.test(n),sl=n=>eo(n,hy),kw=n=>eo(n,Gw),rg=n=>eo(n,uy),Bw=n=>eo(n,dy),Pw=n=>eo(n,fy),Yi=n=>eo(n,my,!0),Jr=(n,l,o)=>{const i=iy.exec(n);return i?i[1]?l(i[1]):o(i[2]):!1},eo=(n,l,o=!1)=>{const i=sy.exec(n);return i?i[1]?l(i[1]):o:!1},uy=n=>n==="position"||n==="percentage",fy=n=>n==="image"||n==="url",dy=n=>n==="length"||n==="size"||n==="bg-size",hy=n=>n==="length",Hw=n=>n==="number",Gw=n=>n==="family-name",my=n=>n==="shadow",Vw=()=>{const n=Je("color"),l=Je("font"),o=Je("text"),i=Je("font-weight"),c=Je("tracking"),f=Je("leading"),d=Je("breakpoint"),h=Je("container"),m=Je("spacing"),p=Je("radius"),v=Je("shadow"),g=Je("inset-shadow"),b=Je("text-shadow"),w=Je("drop-shadow"),R=Je("blur"),E=Je("perspective"),C=Je("aspect"),T=Je("ease"),z=Je("animate"),L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],N=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],G=()=>[...N(),ce,se],W=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto","contain","none"],q=()=>[ce,se,m],ae=()=>[Ur,"full","auto",...q()],Z=()=>[va,"none","subgrid",ce,se],re=()=>["auto",{span:["full",va,ce,se]},va,ce,se],te=()=>[va,"auto",ce,se],le=()=>["auto","min","max","fr",ce,se],ue=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],de=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...q()],$=()=>[Ur,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],U=()=>[n,ce,se],Q=()=>[...N(),rg,ag,{position:[ce,se]}],A=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Y=()=>["auto","cover","contain",Bw,zw,{size:[ce,se]}],J=()=>[pf,sl,Ga],F=()=>["","none","full",p,ce,se],ee=()=>["",_e,sl,Ga],oe=()=>["solid","dashed","dotted","double"],ne=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],fe=()=>[_e,pf,rg,ag],me=()=>["","none",R,ce,se],ge=()=>["none",_e,ce,se],Ce=()=>["none",_e,ce,se],De=()=>[_e,ce,se],He=()=>[Ur,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Pn],breakpoint:[Pn],color:[Ow],container:[Pn],"drop-shadow":[Pn],ease:["in","out","in-out"],font:[jw],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Pn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Pn],shadow:[Pn],spacing:["px",_e],text:[Pn],"text-shadow":[Pn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ur,se,ce,C]}],container:["container"],columns:[{columns:[_e,se,ce,h]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:G()}],overflow:[{overflow:W()}],"overflow-x":[{"overflow-x":W()}],"overflow-y":[{"overflow-y":W()}],overscroll:[{overscroll:V()}],"overscroll-x":[{"overscroll-x":V()}],"overscroll-y":[{"overscroll-y":V()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ae()}],"inset-x":[{"inset-x":ae()}],"inset-y":[{"inset-y":ae()}],start:[{start:ae()}],end:[{end:ae()}],top:[{top:ae()}],right:[{right:ae()}],bottom:[{bottom:ae()}],left:[{left:ae()}],visibility:["visible","invisible","collapse"],z:[{z:[va,"auto",ce,se]}],basis:[{basis:[Ur,"full","auto",h,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[_e,Ur,"auto","initial","none",se]}],grow:[{grow:["",_e,ce,se]}],shrink:[{shrink:["",_e,ce,se]}],order:[{order:[va,"first","last","none",ce,se]}],"grid-cols":[{"grid-cols":Z()}],"col-start-end":[{col:re()}],"col-start":[{"col-start":te()}],"col-end":[{"col-end":te()}],"grid-rows":[{"grid-rows":Z()}],"row-start-end":[{row:re()}],"row-start":[{"row-start":te()}],"row-end":[{"row-end":te()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":le()}],"auto-rows":[{"auto-rows":le()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...ue(),"normal"]}],"justify-items":[{"justify-items":[...de(),"normal"]}],"justify-self":[{"justify-self":["auto",...de()]}],"align-content":[{content:["normal",...ue()]}],"align-items":[{items:[...de(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...de(),{baseline:["","last"]}]}],"place-content":[{"place-content":ue()}],"place-items":[{"place-items":[...de(),"baseline"]}],"place-self":[{"place-self":["auto",...de()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:$()}],w:[{w:[h,"screen",...$()]}],"min-w":[{"min-w":[h,"screen","none",...$()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[d]},...$()]}],h:[{h:["screen","lh",...$()]}],"min-h":[{"min-h":["screen","lh","none",...$()]}],"max-h":[{"max-h":["screen","lh",...$()]}],"font-size":[{text:["base",o,sl,Ga]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,ce,vf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",pf,se]}],"font-family":[{font:[kw,se,l]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[c,ce,se]}],"line-clamp":[{"line-clamp":[_e,"none",ce,vf]}],leading:[{leading:[f,...q()]}],"list-image":[{"list-image":["none",ce,se]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ce,se]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...oe(),"wavy"]}],"text-decoration-thickness":[{decoration:[_e,"from-font","auto",ce,Ga]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[_e,"auto",ce,se]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ce,se]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ce,se]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Q()}],"bg-repeat":[{bg:A()}],"bg-size":[{bg:Y()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},va,ce,se],radial:["",ce,se],conic:[va,ce,se]},Pw,Uw]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:J()}],"gradient-via-pos":[{via:J()}],"gradient-to-pos":[{to:J()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:ee()}],"border-w-x":[{"border-x":ee()}],"border-w-y":[{"border-y":ee()}],"border-w-s":[{"border-s":ee()}],"border-w-e":[{"border-e":ee()}],"border-w-t":[{"border-t":ee()}],"border-w-r":[{"border-r":ee()}],"border-w-b":[{"border-b":ee()}],"border-w-l":[{"border-l":ee()}],"divide-x":[{"divide-x":ee()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ee()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...oe(),"hidden","none"]}],"divide-style":[{divide:[...oe(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...oe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[_e,ce,se]}],"outline-w":[{outline:["",_e,sl,Ga]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",v,Yi,qi]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",g,Yi,qi]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:ee()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[_e,Ga]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":ee()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",b,Yi,qi]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[_e,ce,se]}],"mix-blend":[{"mix-blend":[...ne(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ne()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[_e]}],"mask-image-linear-from-pos":[{"mask-linear-from":fe()}],"mask-image-linear-to-pos":[{"mask-linear-to":fe()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":fe()}],"mask-image-t-to-pos":[{"mask-t-to":fe()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":fe()}],"mask-image-r-to-pos":[{"mask-r-to":fe()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":fe()}],"mask-image-b-to-pos":[{"mask-b-to":fe()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":fe()}],"mask-image-l-to-pos":[{"mask-l-to":fe()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":fe()}],"mask-image-x-to-pos":[{"mask-x-to":fe()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":fe()}],"mask-image-y-to-pos":[{"mask-y-to":fe()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[ce,se]}],"mask-image-radial-from-pos":[{"mask-radial-from":fe()}],"mask-image-radial-to-pos":[{"mask-radial-to":fe()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":N()}],"mask-image-conic-pos":[{"mask-conic":[_e]}],"mask-image-conic-from-pos":[{"mask-conic-from":fe()}],"mask-image-conic-to-pos":[{"mask-conic-to":fe()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Q()}],"mask-repeat":[{mask:A()}],"mask-size":[{mask:Y()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ce,se]}],filter:[{filter:["","none",ce,se]}],blur:[{blur:me()}],brightness:[{brightness:[_e,ce,se]}],contrast:[{contrast:[_e,ce,se]}],"drop-shadow":[{"drop-shadow":["","none",w,Yi,qi]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",_e,ce,se]}],"hue-rotate":[{"hue-rotate":[_e,ce,se]}],invert:[{invert:["",_e,ce,se]}],saturate:[{saturate:[_e,ce,se]}],sepia:[{sepia:["",_e,ce,se]}],"backdrop-filter":[{"backdrop-filter":["","none",ce,se]}],"backdrop-blur":[{"backdrop-blur":me()}],"backdrop-brightness":[{"backdrop-brightness":[_e,ce,se]}],"backdrop-contrast":[{"backdrop-contrast":[_e,ce,se]}],"backdrop-grayscale":[{"backdrop-grayscale":["",_e,ce,se]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[_e,ce,se]}],"backdrop-invert":[{"backdrop-invert":["",_e,ce,se]}],"backdrop-opacity":[{"backdrop-opacity":[_e,ce,se]}],"backdrop-saturate":[{"backdrop-saturate":[_e,ce,se]}],"backdrop-sepia":[{"backdrop-sepia":["",_e,ce,se]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ce,se]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[_e,"initial",ce,se]}],ease:[{ease:["linear","initial",T,ce,se]}],delay:[{delay:[_e,ce,se]}],animate:[{animate:["none",z,ce,se]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[E,ce,se]}],"perspective-origin":[{"perspective-origin":G()}],rotate:[{rotate:ge()}],"rotate-x":[{"rotate-x":ge()}],"rotate-y":[{"rotate-y":ge()}],"rotate-z":[{"rotate-z":ge()}],scale:[{scale:Ce()}],"scale-x":[{"scale-x":Ce()}],"scale-y":[{"scale-y":Ce()}],"scale-z":[{"scale-z":Ce()}],"scale-3d":["scale-3d"],skew:[{skew:De()}],"skew-x":[{"skew-x":De()}],"skew-y":[{"skew-y":De()}],transform:[{transform:[ce,se,"","none","gpu","cpu"]}],"transform-origin":[{origin:G()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:He()}],"translate-x":[{"translate-x":He()}],"translate-y":[{"translate-y":He()}],"translate-z":[{"translate-z":He()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ce,se]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ce,se]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[_e,sl,Ga,vf]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},qw=ww(Vw);function je(...n){return qw(ny(n))}const Yw=ay("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function py({className:n,variant:l,size:o,asChild:i=!1,...c}){const f=i?Jg:"button";return _.jsx(f,{"data-slot":"button",className:je(Yw({variant:l,size:o,className:n})),...c})}var $w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ve=$w.reduce((n,l)=>{const o=Zr(`Primitive.${l}`),i=S.forwardRef((c,f)=>{const{asChild:d,...h}=c,m=d?o:l;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),_.jsx(m,{...h,ref:f})});return i.displayName=`Primitive.${l}`,{...n,[l]:i}},{});function vy(n,l){n&&bs.flushSync(()=>n.dispatchEvent(l))}function be(n,l,{checkForDefaultPrevented:o=!0}={}){return function(c){if(n==null||n(c),o===!1||!c.defaultPrevented)return l==null?void 0:l(c)}}function Iw(n,l){const o=S.createContext(l),i=f=>{const{children:d,...h}=f,m=S.useMemo(()=>h,Object.values(h));return _.jsx(o.Provider,{value:m,children:d})};i.displayName=n+"Provider";function c(f){const d=S.useContext(o);if(d)return d;if(l!==void 0)return l;throw new Error(`\`${f}\` must be used within \`${n}\``)}return[i,c]}function xa(n,l=[]){let o=[];function i(f,d){const h=S.createContext(d),m=o.length;o=[...o,d];const p=g=>{var T;const{scope:b,children:w,...R}=g,E=((T=b==null?void 0:b[n])==null?void 0:T[m])||h,C=S.useMemo(()=>R,Object.values(R));return _.jsx(E.Provider,{value:C,children:w})};p.displayName=f+"Provider";function v(g,b){var E;const w=((E=b==null?void 0:b[n])==null?void 0:E[m])||h,R=S.useContext(w);if(R)return R;if(d!==void 0)return d;throw new Error(`\`${g}\` must be used within \`${f}\``)}return[p,v]}const c=()=>{const f=o.map(d=>S.createContext(d));return function(h){const m=(h==null?void 0:h[n])||f;return S.useMemo(()=>({[`__scope${n}`]:{...h,[n]:m}}),[h,m])}};return c.scopeName=n,[i,Xw(c,...l)]}function Xw(...n){const l=n[0];if(n.length===1)return l;const o=()=>{const i=n.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(f){const d=i.reduce((h,{useScope:m,scopeName:p})=>{const g=m(f)[`__scope${p}`];return{...h,...g}},{});return S.useMemo(()=>({[`__scope${l.scopeName}`]:d}),[d])}};return o.scopeName=l.scopeName,o}var an=globalThis!=null&&globalThis.document?S.useLayoutEffect:()=>{},Kw=jg[" useId ".trim().toString()]||(()=>{}),Qw=0;function Ya(n){const[l,o]=S.useState(Kw());return an(()=>{o(i=>i??String(Qw++))},[n]),n||(l?`radix-${l}`:"")}var Zw=jg[" useInsertionEffect ".trim().toString()]||an;function ws({prop:n,defaultProp:l,onChange:o=()=>{},caller:i}){const[c,f,d]=Fw({defaultProp:l,onChange:o}),h=n!==void 0,m=h?n:c;{const v=S.useRef(n!==void 0);S.useEffect(()=>{const g=v.current;g!==h&&console.warn(`${i} is changing from ${g?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),v.current=h},[h,i])}const p=S.useCallback(v=>{var g;if(h){const b=Ww(v)?v(n):v;b!==n&&((g=d.current)==null||g.call(d,b))}else f(v)},[h,n,f,d]);return[m,p]}function Fw({defaultProp:n,onChange:l}){const[o,i]=S.useState(n),c=S.useRef(o),f=S.useRef(l);return Zw(()=>{f.current=l},[l]),S.useEffect(()=>{var d;c.current!==o&&((d=f.current)==null||d.call(f,o),c.current=o)},[o,c]),[o,i,f]}function Ww(n){return typeof n=="function"}function bn(n){const l=S.useRef(n);return S.useEffect(()=>{l.current=n}),S.useMemo(()=>(...o)=>{var i;return(i=l.current)==null?void 0:i.call(l,...o)},[])}function Jw(n,l=globalThis==null?void 0:globalThis.document){const o=bn(n);S.useEffect(()=>{const i=c=>{c.key==="Escape"&&o(c)};return l.addEventListener("keydown",i,{capture:!0}),()=>l.removeEventListener("keydown",i,{capture:!0})},[o,l])}var eE="DismissableLayer",Pf="dismissableLayer.update",tE="dismissableLayer.pointerDownOutside",nE="dismissableLayer.focusOutside",og,gy=S.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Es=S.forwardRef((n,l)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:d,onDismiss:h,...m}=n,p=S.useContext(gy),[v,g]=S.useState(null),b=(v==null?void 0:v.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,w]=S.useState({}),R=et(l,V=>g(V)),E=Array.from(p.layers),[C]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),T=E.indexOf(C),z=v?E.indexOf(v):-1,L=p.layersWithOutsidePointerEventsDisabled.size>0,N=z>=T,G=oE(V=>{const q=V.target,ae=[...p.branches].some(Z=>Z.contains(q));!N||ae||(c==null||c(V),d==null||d(V),V.defaultPrevented||h==null||h())},b),W=lE(V=>{const q=V.target;[...p.branches].some(Z=>Z.contains(q))||(f==null||f(V),d==null||d(V),V.defaultPrevented||h==null||h())},b);return Jw(V=>{z===p.layers.size-1&&(i==null||i(V),!V.defaultPrevented&&h&&(V.preventDefault(),h()))},b),S.useEffect(()=>{if(v)return o&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(og=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(v)),p.layers.add(v),lg(),()=>{o&&p.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=og)}},[v,b,o,p]),S.useEffect(()=>()=>{v&&(p.layers.delete(v),p.layersWithOutsidePointerEventsDisabled.delete(v),lg())},[v,p]),S.useEffect(()=>{const V=()=>w({});return document.addEventListener(Pf,V),()=>document.removeEventListener(Pf,V)},[]),_.jsx(Ve.div,{...m,ref:R,style:{pointerEvents:L?N?"auto":"none":void 0,...n.style},onFocusCapture:be(n.onFocusCapture,W.onFocusCapture),onBlurCapture:be(n.onBlurCapture,W.onBlurCapture),onPointerDownCapture:be(n.onPointerDownCapture,G.onPointerDownCapture)})});Es.displayName=eE;var aE="DismissableLayerBranch",rE=S.forwardRef((n,l)=>{const o=S.useContext(gy),i=S.useRef(null),c=et(l,i);return S.useEffect(()=>{const f=i.current;if(f)return o.branches.add(f),()=>{o.branches.delete(f)}},[o.branches]),_.jsx(Ve.div,{...n,ref:c})});rE.displayName=aE;function oE(n,l=globalThis==null?void 0:globalThis.document){const o=bn(n),i=S.useRef(!1),c=S.useRef(()=>{});return S.useEffect(()=>{const f=h=>{if(h.target&&!i.current){let m=function(){yy(tE,o,p,{discrete:!0})};const p={originalEvent:h};h.pointerType==="touch"?(l.removeEventListener("click",c.current),c.current=m,l.addEventListener("click",c.current,{once:!0})):m()}else l.removeEventListener("click",c.current);i.current=!1},d=window.setTimeout(()=>{l.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(d),l.removeEventListener("pointerdown",f),l.removeEventListener("click",c.current)}},[l,o]),{onPointerDownCapture:()=>i.current=!0}}function lE(n,l=globalThis==null?void 0:globalThis.document){const o=bn(n),i=S.useRef(!1);return S.useEffect(()=>{const c=f=>{f.target&&!i.current&&yy(nE,o,{originalEvent:f},{discrete:!1})};return l.addEventListener("focusin",c),()=>l.removeEventListener("focusin",c)},[l,o]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}function lg(){const n=new CustomEvent(Pf);document.dispatchEvent(n)}function yy(n,l,o,{discrete:i}){const c=o.originalEvent.target,f=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:o});l&&c.addEventListener(n,l,{once:!0}),i?vy(c,f):c.dispatchEvent(f)}var gf="focusScope.autoFocusOnMount",yf="focusScope.autoFocusOnUnmount",ig={bubbles:!1,cancelable:!0},iE="FocusScope",od=S.forwardRef((n,l)=>{const{loop:o=!1,trapped:i=!1,onMountAutoFocus:c,onUnmountAutoFocus:f,...d}=n,[h,m]=S.useState(null),p=bn(c),v=bn(f),g=S.useRef(null),b=et(l,E=>m(E)),w=S.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;S.useEffect(()=>{if(i){let E=function(L){if(w.paused||!h)return;const N=L.target;h.contains(N)?g.current=N:ga(g.current,{select:!0})},C=function(L){if(w.paused||!h)return;const N=L.relatedTarget;N!==null&&(h.contains(N)||ga(g.current,{select:!0}))},T=function(L){if(document.activeElement===document.body)for(const G of L)G.removedNodes.length>0&&ga(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",C);const z=new MutationObserver(T);return h&&z.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",C),z.disconnect()}}},[i,h,w.paused]),S.useEffect(()=>{if(h){cg.add(w);const E=document.activeElement;if(!h.contains(E)){const T=new CustomEvent(gf,ig);h.addEventListener(gf,p),h.dispatchEvent(T),T.defaultPrevented||(sE(hE(by(h)),{select:!0}),document.activeElement===E&&ga(h))}return()=>{h.removeEventListener(gf,p),setTimeout(()=>{const T=new CustomEvent(yf,ig);h.addEventListener(yf,v),h.dispatchEvent(T),T.defaultPrevented||ga(E??document.body,{select:!0}),h.removeEventListener(yf,v),cg.remove(w)},0)}}},[h,p,v,w]);const R=S.useCallback(E=>{if(!o&&!i||w.paused)return;const C=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,T=document.activeElement;if(C&&T){const z=E.currentTarget,[L,N]=cE(z);L&&N?!E.shiftKey&&T===N?(E.preventDefault(),o&&ga(L,{select:!0})):E.shiftKey&&T===L&&(E.preventDefault(),o&&ga(N,{select:!0})):T===z&&E.preventDefault()}},[o,i,w.paused]);return _.jsx(Ve.div,{tabIndex:-1,...d,ref:b,onKeyDown:R})});od.displayName=iE;function sE(n,{select:l=!1}={}){const o=document.activeElement;for(const i of n)if(ga(i,{select:l}),document.activeElement!==o)return}function cE(n){const l=by(n),o=sg(l,n),i=sg(l.reverse(),n);return[o,i]}function by(n){const l=[],o=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const c=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||c?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)l.push(o.currentNode);return l}function sg(n,l){for(const o of n)if(!uE(o,{upTo:l}))return o}function uE(n,{upTo:l}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(l!==void 0&&n===l)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function fE(n){return n instanceof HTMLInputElement&&"select"in n}function ga(n,{select:l=!1}={}){if(n&&n.focus){const o=document.activeElement;n.focus({preventScroll:!0}),n!==o&&fE(n)&&l&&n.select()}}var cg=dE();function dE(){let n=[];return{add(l){const o=n[0];l!==o&&(o==null||o.pause()),n=ug(n,l),n.unshift(l)},remove(l){var o;n=ug(n,l),(o=n[0])==null||o.resume()}}}function ug(n,l){const o=[...n],i=o.indexOf(l);return i!==-1&&o.splice(i,1),o}function hE(n){return n.filter(l=>l.tagName!=="A")}var mE="Portal",Rs=S.forwardRef((n,l)=>{var h;const{container:o,...i}=n,[c,f]=S.useState(!1);an(()=>f(!0),[]);const d=o||c&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return d?n_.createPortal(_.jsx(Ve.div,{...i,ref:l}),d):null});Rs.displayName=mE;function pE(n,l){return S.useReducer((o,i)=>l[o][i]??o,n)}var xn=n=>{const{present:l,children:o}=n,i=vE(l),c=typeof o=="function"?o({present:i.isPresent}):S.Children.only(o),f=et(i.ref,gE(c));return typeof o=="function"||i.isPresent?S.cloneElement(c,{ref:f}):null};xn.displayName="Presence";function vE(n){const[l,o]=S.useState(),i=S.useRef(null),c=S.useRef(n),f=S.useRef("none"),d=n?"mounted":"unmounted",[h,m]=pE(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return S.useEffect(()=>{const p=$i(i.current);f.current=h==="mounted"?p:"none"},[h]),an(()=>{const p=i.current,v=c.current;if(v!==n){const b=f.current,w=$i(p);n?m("MOUNT"):w==="none"||(p==null?void 0:p.display)==="none"?m("UNMOUNT"):m(v&&b!==w?"ANIMATION_OUT":"UNMOUNT"),c.current=n}},[n,m]),an(()=>{if(l){let p;const v=l.ownerDocument.defaultView??window,g=w=>{const E=$i(i.current).includes(w.animationName);if(w.target===l&&E&&(m("ANIMATION_END"),!c.current)){const C=l.style.animationFillMode;l.style.animationFillMode="forwards",p=v.setTimeout(()=>{l.style.animationFillMode==="forwards"&&(l.style.animationFillMode=C)})}},b=w=>{w.target===l&&(f.current=$i(i.current))};return l.addEventListener("animationstart",b),l.addEventListener("animationcancel",g),l.addEventListener("animationend",g),()=>{v.clearTimeout(p),l.removeEventListener("animationstart",b),l.removeEventListener("animationcancel",g),l.removeEventListener("animationend",g)}}else m("ANIMATION_END")},[l,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:S.useCallback(p=>{i.current=p?getComputedStyle(p):null,o(p)},[])}}function $i(n){return(n==null?void 0:n.animationName)||"none"}function gE(n){var i,c;let l=(i=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:i.get,o=l&&"isReactWarning"in l&&l.isReactWarning;return o?n.ref:(l=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,o=l&&"isReactWarning"in l&&l.isReactWarning,o?n.props.ref:n.props.ref||n.ref)}var bf=0;function Sy(){S.useEffect(()=>{const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",n[0]??fg()),document.body.insertAdjacentElement("beforeend",n[1]??fg()),bf++,()=>{bf===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),bf--}},[])}function fg(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var vn=function(){return vn=Object.assign||function(l){for(var o,i=1,c=arguments.length;i<c;i++){o=arguments[i];for(var f in o)Object.prototype.hasOwnProperty.call(o,f)&&(l[f]=o[f])}return l},vn.apply(this,arguments)};function xy(n,l){var o={};for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&l.indexOf(i)<0&&(o[i]=n[i]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,i=Object.getOwnPropertySymbols(n);c<i.length;c++)l.indexOf(i[c])<0&&Object.prototype.propertyIsEnumerable.call(n,i[c])&&(o[i[c]]=n[i[c]]);return o}function yE(n,l,o){if(o||arguments.length===2)for(var i=0,c=l.length,f;i<c;i++)(f||!(i in l))&&(f||(f=Array.prototype.slice.call(l,0,i)),f[i]=l[i]);return n.concat(f||Array.prototype.slice.call(l))}var Ji="right-scroll-bar-position",es="width-before-scroll-bar",bE="with-scroll-bars-hidden",SE="--removed-body-scroll-bar-size";function Sf(n,l){return typeof n=="function"?n(l):n&&(n.current=l),n}function xE(n,l){var o=S.useState(function(){return{value:n,callback:l,facade:{get current(){return o.value},set current(i){var c=o.value;c!==i&&(o.value=i,o.callback(i,c))}}}})[0];return o.callback=l,o.facade}var _E=typeof window<"u"?S.useLayoutEffect:S.useEffect,dg=new WeakMap;function wE(n,l){var o=xE(null,function(i){return n.forEach(function(c){return Sf(c,i)})});return _E(function(){var i=dg.get(o);if(i){var c=new Set(i),f=new Set(n),d=o.current;c.forEach(function(h){f.has(h)||Sf(h,null)}),f.forEach(function(h){c.has(h)||Sf(h,d)})}dg.set(o,n)},[n]),o}function EE(n){return n}function RE(n,l){l===void 0&&(l=EE);var o=[],i=!1,c={read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:n},useMedium:function(f){var d=l(f,i);return o.push(d),function(){o=o.filter(function(h){return h!==d})}},assignSyncMedium:function(f){for(i=!0;o.length;){var d=o;o=[],d.forEach(f)}o={push:function(h){return f(h)},filter:function(){return o}}},assignMedium:function(f){i=!0;var d=[];if(o.length){var h=o;o=[],h.forEach(f),d=o}var m=function(){var v=d;d=[],v.forEach(f)},p=function(){return Promise.resolve().then(m)};p(),o={push:function(v){d.push(v),p()},filter:function(v){return d=d.filter(v),o}}}};return c}function CE(n){n===void 0&&(n={});var l=RE(null);return l.options=vn({async:!0,ssr:!1},n),l}var _y=function(n){var l=n.sideCar,o=xy(n,["sideCar"]);if(!l)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=l.read();if(!i)throw new Error("Sidecar medium not found");return S.createElement(i,vn({},o))};_y.isSideCarExport=!0;function AE(n,l){return n.useMedium(l),_y}var wy=CE(),xf=function(){},Cs=S.forwardRef(function(n,l){var o=S.useRef(null),i=S.useState({onScrollCapture:xf,onWheelCapture:xf,onTouchMoveCapture:xf}),c=i[0],f=i[1],d=n.forwardProps,h=n.children,m=n.className,p=n.removeScrollBar,v=n.enabled,g=n.shards,b=n.sideCar,w=n.noRelative,R=n.noIsolation,E=n.inert,C=n.allowPinchZoom,T=n.as,z=T===void 0?"div":T,L=n.gapMode,N=xy(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=b,W=wE([o,l]),V=vn(vn({},N),c);return S.createElement(S.Fragment,null,v&&S.createElement(G,{sideCar:wy,removeScrollBar:p,shards:g,noRelative:w,noIsolation:R,inert:E,setCallbacks:f,allowPinchZoom:!!C,lockRef:o,gapMode:L}),d?S.cloneElement(S.Children.only(h),vn(vn({},V),{ref:W})):S.createElement(z,vn({},V,{className:m,ref:W}),h))});Cs.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Cs.classNames={fullWidth:es,zeroRight:Ji};var ME=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function TE(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var l=ME();return l&&n.setAttribute("nonce",l),n}function OE(n,l){n.styleSheet?n.styleSheet.cssText=l:n.appendChild(document.createTextNode(l))}function DE(n){var l=document.head||document.getElementsByTagName("head")[0];l.appendChild(n)}var NE=function(){var n=0,l=null;return{add:function(o){n==0&&(l=TE())&&(OE(l,o),DE(l)),n++},remove:function(){n--,!n&&l&&(l.parentNode&&l.parentNode.removeChild(l),l=null)}}},LE=function(){var n=NE();return function(l,o){S.useEffect(function(){return n.add(l),function(){n.remove()}},[l&&o])}},Ey=function(){var n=LE(),l=function(o){var i=o.styles,c=o.dynamic;return n(i,c),null};return l},jE={left:0,top:0,right:0,gap:0},_f=function(n){return parseInt(n||"",10)||0},zE=function(n){var l=window.getComputedStyle(document.body),o=l[n==="padding"?"paddingLeft":"marginLeft"],i=l[n==="padding"?"paddingTop":"marginTop"],c=l[n==="padding"?"paddingRight":"marginRight"];return[_f(o),_f(i),_f(c)]},UE=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return jE;var l=zE(n),o=document.documentElement.clientWidth,i=window.innerWidth;return{left:l[0],top:l[1],right:l[2],gap:Math.max(0,i-o+l[2]-l[0])}},kE=Ey(),$r="data-scroll-locked",BE=function(n,l,o,i){var c=n.left,f=n.top,d=n.right,h=n.gap;return o===void 0&&(o="margin"),`
  .`.concat(bE,` {
   overflow: hidden `).concat(i,`;
   padding-right: `).concat(h,"px ").concat(i,`;
  }
  body[`).concat($r,`] {
    overflow: hidden `).concat(i,`;
    overscroll-behavior: contain;
    `).concat([l&&"position: relative ".concat(i,";"),o==="margin"&&`
    padding-left: `.concat(c,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(i,`;
    `),o==="padding"&&"padding-right: ".concat(h,"px ").concat(i,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ji,` {
    right: `).concat(h,"px ").concat(i,`;
  }
  
  .`).concat(es,` {
    margin-right: `).concat(h,"px ").concat(i,`;
  }
  
  .`).concat(Ji," .").concat(Ji,` {
    right: 0 `).concat(i,`;
  }
  
  .`).concat(es," .").concat(es,` {
    margin-right: 0 `).concat(i,`;
  }
  
  body[`).concat($r,`] {
    `).concat(SE,": ").concat(h,`px;
  }
`)},hg=function(){var n=parseInt(document.body.getAttribute($r)||"0",10);return isFinite(n)?n:0},PE=function(){S.useEffect(function(){return document.body.setAttribute($r,(hg()+1).toString()),function(){var n=hg()-1;n<=0?document.body.removeAttribute($r):document.body.setAttribute($r,n.toString())}},[])},HE=function(n){var l=n.noRelative,o=n.noImportant,i=n.gapMode,c=i===void 0?"margin":i;PE();var f=S.useMemo(function(){return UE(c)},[c]);return S.createElement(kE,{styles:BE(f,!l,c,o?"":"!important")})},Hf=!1;if(typeof window<"u")try{var Ii=Object.defineProperty({},"passive",{get:function(){return Hf=!0,!0}});window.addEventListener("test",Ii,Ii),window.removeEventListener("test",Ii,Ii)}catch{Hf=!1}var kr=Hf?{passive:!1}:!1,GE=function(n){return n.tagName==="TEXTAREA"},Ry=function(n,l){if(!(n instanceof Element))return!1;var o=window.getComputedStyle(n);return o[l]!=="hidden"&&!(o.overflowY===o.overflowX&&!GE(n)&&o[l]==="visible")},VE=function(n){return Ry(n,"overflowY")},qE=function(n){return Ry(n,"overflowX")},mg=function(n,l){var o=l.ownerDocument,i=l;do{typeof ShadowRoot<"u"&&i instanceof ShadowRoot&&(i=i.host);var c=Cy(n,i);if(c){var f=Ay(n,i),d=f[1],h=f[2];if(d>h)return!0}i=i.parentNode}while(i&&i!==o.body);return!1},YE=function(n){var l=n.scrollTop,o=n.scrollHeight,i=n.clientHeight;return[l,o,i]},$E=function(n){var l=n.scrollLeft,o=n.scrollWidth,i=n.clientWidth;return[l,o,i]},Cy=function(n,l){return n==="v"?VE(l):qE(l)},Ay=function(n,l){return n==="v"?YE(l):$E(l)},IE=function(n,l){return n==="h"&&l==="rtl"?-1:1},XE=function(n,l,o,i,c){var f=IE(n,window.getComputedStyle(l).direction),d=f*i,h=o.target,m=l.contains(h),p=!1,v=d>0,g=0,b=0;do{if(!h)break;var w=Ay(n,h),R=w[0],E=w[1],C=w[2],T=E-C-f*R;(R||T)&&Cy(n,h)&&(g+=T,b+=R);var z=h.parentNode;h=z&&z.nodeType===Node.DOCUMENT_FRAGMENT_NODE?z.host:z}while(!m&&h!==document.body||m&&(l.contains(h)||l===h));return(v&&Math.abs(g)<1||!v&&Math.abs(b)<1)&&(p=!0),p},Xi=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},pg=function(n){return[n.deltaX,n.deltaY]},vg=function(n){return n&&"current"in n?n.current:n},KE=function(n,l){return n[0]===l[0]&&n[1]===l[1]},QE=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},ZE=0,Br=[];function FE(n){var l=S.useRef([]),o=S.useRef([0,0]),i=S.useRef(),c=S.useState(ZE++)[0],f=S.useState(Ey)[0],d=S.useRef(n);S.useEffect(function(){d.current=n},[n]),S.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(c));var E=yE([n.lockRef.current],(n.shards||[]).map(vg),!0).filter(Boolean);return E.forEach(function(C){return C.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),E.forEach(function(C){return C.classList.remove("allow-interactivity-".concat(c))})}}},[n.inert,n.lockRef.current,n.shards]);var h=S.useCallback(function(E,C){if("touches"in E&&E.touches.length===2||E.type==="wheel"&&E.ctrlKey)return!d.current.allowPinchZoom;var T=Xi(E),z=o.current,L="deltaX"in E?E.deltaX:z[0]-T[0],N="deltaY"in E?E.deltaY:z[1]-T[1],G,W=E.target,V=Math.abs(L)>Math.abs(N)?"h":"v";if("touches"in E&&V==="h"&&W.type==="range")return!1;var q=mg(V,W);if(!q)return!0;if(q?G=V:(G=V==="v"?"h":"v",q=mg(V,W)),!q)return!1;if(!i.current&&"changedTouches"in E&&(L||N)&&(i.current=G),!G)return!0;var ae=i.current||G;return XE(ae,C,E,ae==="h"?L:N)},[]),m=S.useCallback(function(E){var C=E;if(!(!Br.length||Br[Br.length-1]!==f)){var T="deltaY"in C?pg(C):Xi(C),z=l.current.filter(function(G){return G.name===C.type&&(G.target===C.target||C.target===G.shadowParent)&&KE(G.delta,T)})[0];if(z&&z.should){C.cancelable&&C.preventDefault();return}if(!z){var L=(d.current.shards||[]).map(vg).filter(Boolean).filter(function(G){return G.contains(C.target)}),N=L.length>0?h(C,L[0]):!d.current.noIsolation;N&&C.cancelable&&C.preventDefault()}}},[]),p=S.useCallback(function(E,C,T,z){var L={name:E,delta:C,target:T,should:z,shadowParent:WE(T)};l.current.push(L),setTimeout(function(){l.current=l.current.filter(function(N){return N!==L})},1)},[]),v=S.useCallback(function(E){o.current=Xi(E),i.current=void 0},[]),g=S.useCallback(function(E){p(E.type,pg(E),E.target,h(E,n.lockRef.current))},[]),b=S.useCallback(function(E){p(E.type,Xi(E),E.target,h(E,n.lockRef.current))},[]);S.useEffect(function(){return Br.push(f),n.setCallbacks({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:b}),document.addEventListener("wheel",m,kr),document.addEventListener("touchmove",m,kr),document.addEventListener("touchstart",v,kr),function(){Br=Br.filter(function(E){return E!==f}),document.removeEventListener("wheel",m,kr),document.removeEventListener("touchmove",m,kr),document.removeEventListener("touchstart",v,kr)}},[]);var w=n.removeScrollBar,R=n.inert;return S.createElement(S.Fragment,null,R?S.createElement(f,{styles:QE(c)}):null,w?S.createElement(HE,{noRelative:n.noRelative,gapMode:n.gapMode}):null)}function WE(n){for(var l=null;n!==null;)n instanceof ShadowRoot&&(l=n.host,n=n.host),n=n.parentNode;return l}const JE=AE(wy,FE);var ld=S.forwardRef(function(n,l){return S.createElement(Cs,vn({},n,{ref:l,sideCar:JE}))});ld.classNames=Cs.classNames;var eR=function(n){if(typeof document>"u")return null;var l=Array.isArray(n)?n[0]:n;return l.ownerDocument.body},Pr=new WeakMap,Ki=new WeakMap,Qi={},wf=0,My=function(n){return n&&(n.host||My(n.parentNode))},tR=function(n,l){return l.map(function(o){if(n.contains(o))return o;var i=My(o);return i&&n.contains(i)?i:(console.error("aria-hidden",o,"in not contained inside",n,". Doing nothing"),null)}).filter(function(o){return!!o})},nR=function(n,l,o,i){var c=tR(l,Array.isArray(n)?n:[n]);Qi[o]||(Qi[o]=new WeakMap);var f=Qi[o],d=[],h=new Set,m=new Set(c),p=function(g){!g||h.has(g)||(h.add(g),p(g.parentNode))};c.forEach(p);var v=function(g){!g||m.has(g)||Array.prototype.forEach.call(g.children,function(b){if(h.has(b))v(b);else try{var w=b.getAttribute(i),R=w!==null&&w!=="false",E=(Pr.get(b)||0)+1,C=(f.get(b)||0)+1;Pr.set(b,E),f.set(b,C),d.push(b),E===1&&R&&Ki.set(b,!0),C===1&&b.setAttribute(o,"true"),R||b.setAttribute(i,"true")}catch(T){console.error("aria-hidden: cannot operate on ",b,T)}})};return v(l),h.clear(),wf++,function(){d.forEach(function(g){var b=Pr.get(g)-1,w=f.get(g)-1;Pr.set(g,b),f.set(g,w),b||(Ki.has(g)||g.removeAttribute(i),Ki.delete(g)),w||g.removeAttribute(o)}),wf--,wf||(Pr=new WeakMap,Pr=new WeakMap,Ki=new WeakMap,Qi={})}},Ty=function(n,l,o){o===void 0&&(o="data-aria-hidden");var i=Array.from(Array.isArray(n)?n:[n]),c=eR(n);return c?(i.push.apply(i,Array.from(c.querySelectorAll("[aria-live], script"))),nR(i,c,o,"aria-hidden")):function(){return null}},As="Dialog",[Oy,MO]=xa(As),[aR,sn]=Oy(As),Dy=n=>{const{__scopeDialog:l,children:o,open:i,defaultOpen:c,onOpenChange:f,modal:d=!0}=n,h=S.useRef(null),m=S.useRef(null),[p,v]=ws({prop:i,defaultProp:c??!1,onChange:f,caller:As});return _.jsx(aR,{scope:l,triggerRef:h,contentRef:m,contentId:Ya(),titleId:Ya(),descriptionId:Ya(),open:p,onOpenChange:v,onOpenToggle:S.useCallback(()=>v(g=>!g),[v]),modal:d,children:o})};Dy.displayName=As;var Ny="DialogTrigger",Ly=S.forwardRef((n,l)=>{const{__scopeDialog:o,...i}=n,c=sn(Ny,o),f=et(l,c.triggerRef);return _.jsx(Ve.button,{type:"button","aria-haspopup":"dialog","aria-expanded":c.open,"aria-controls":c.contentId,"data-state":cd(c.open),...i,ref:f,onClick:be(n.onClick,c.onOpenToggle)})});Ly.displayName=Ny;var id="DialogPortal",[rR,jy]=Oy(id,{forceMount:void 0}),zy=n=>{const{__scopeDialog:l,forceMount:o,children:i,container:c}=n,f=sn(id,l);return _.jsx(rR,{scope:l,forceMount:o,children:S.Children.map(i,d=>_.jsx(xn,{present:o||f.open,children:_.jsx(Rs,{asChild:!0,container:c,children:d})}))})};zy.displayName=id;var cs="DialogOverlay",Uy=S.forwardRef((n,l)=>{const o=jy(cs,n.__scopeDialog),{forceMount:i=o.forceMount,...c}=n,f=sn(cs,n.__scopeDialog);return f.modal?_.jsx(xn,{present:i||f.open,children:_.jsx(lR,{...c,ref:l})}):null});Uy.displayName=cs;var oR=Zr("DialogOverlay.RemoveScroll"),lR=S.forwardRef((n,l)=>{const{__scopeDialog:o,...i}=n,c=sn(cs,o);return _.jsx(ld,{as:oR,allowPinchZoom:!0,shards:[c.contentRef],children:_.jsx(Ve.div,{"data-state":cd(c.open),...i,ref:l,style:{pointerEvents:"auto",...i.style}})})}),Xa="DialogContent",ky=S.forwardRef((n,l)=>{const o=jy(Xa,n.__scopeDialog),{forceMount:i=o.forceMount,...c}=n,f=sn(Xa,n.__scopeDialog);return _.jsx(xn,{present:i||f.open,children:f.modal?_.jsx(iR,{...c,ref:l}):_.jsx(sR,{...c,ref:l})})});ky.displayName=Xa;var iR=S.forwardRef((n,l)=>{const o=sn(Xa,n.__scopeDialog),i=S.useRef(null),c=et(l,o.contentRef,i);return S.useEffect(()=>{const f=i.current;if(f)return Ty(f)},[]),_.jsx(By,{...n,ref:c,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:be(n.onCloseAutoFocus,f=>{var d;f.preventDefault(),(d=o.triggerRef.current)==null||d.focus()}),onPointerDownOutside:be(n.onPointerDownOutside,f=>{const d=f.detail.originalEvent,h=d.button===0&&d.ctrlKey===!0;(d.button===2||h)&&f.preventDefault()}),onFocusOutside:be(n.onFocusOutside,f=>f.preventDefault())})}),sR=S.forwardRef((n,l)=>{const o=sn(Xa,n.__scopeDialog),i=S.useRef(!1),c=S.useRef(!1);return _.jsx(By,{...n,ref:l,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:f=>{var d,h;(d=n.onCloseAutoFocus)==null||d.call(n,f),f.defaultPrevented||(i.current||(h=o.triggerRef.current)==null||h.focus(),f.preventDefault()),i.current=!1,c.current=!1},onInteractOutside:f=>{var m,p;(m=n.onInteractOutside)==null||m.call(n,f),f.defaultPrevented||(i.current=!0,f.detail.originalEvent.type==="pointerdown"&&(c.current=!0));const d=f.target;((p=o.triggerRef.current)==null?void 0:p.contains(d))&&f.preventDefault(),f.detail.originalEvent.type==="focusin"&&c.current&&f.preventDefault()}})}),By=S.forwardRef((n,l)=>{const{__scopeDialog:o,trapFocus:i,onOpenAutoFocus:c,onCloseAutoFocus:f,...d}=n,h=sn(Xa,o),m=S.useRef(null),p=et(l,m);return Sy(),_.jsxs(_.Fragment,{children:[_.jsx(od,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:c,onUnmountAutoFocus:f,children:_.jsx(Es,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":cd(h.open),...d,ref:p,onDismiss:()=>h.onOpenChange(!1)})}),_.jsxs(_.Fragment,{children:[_.jsx(cR,{titleId:h.titleId}),_.jsx(fR,{contentRef:m,descriptionId:h.descriptionId})]})]})}),sd="DialogTitle",Py=S.forwardRef((n,l)=>{const{__scopeDialog:o,...i}=n,c=sn(sd,o);return _.jsx(Ve.h2,{id:c.titleId,...i,ref:l})});Py.displayName=sd;var Hy="DialogDescription",Gy=S.forwardRef((n,l)=>{const{__scopeDialog:o,...i}=n,c=sn(Hy,o);return _.jsx(Ve.p,{id:c.descriptionId,...i,ref:l})});Gy.displayName=Hy;var Vy="DialogClose",qy=S.forwardRef((n,l)=>{const{__scopeDialog:o,...i}=n,c=sn(Vy,o);return _.jsx(Ve.button,{type:"button",...i,ref:l,onClick:be(n.onClick,()=>c.onOpenChange(!1))})});qy.displayName=Vy;function cd(n){return n?"open":"closed"}var Yy="DialogTitleWarning",[TO,$y]=Iw(Yy,{contentName:Xa,titleName:sd,docsSlug:"dialog"}),cR=({titleId:n})=>{const l=$y(Yy),o=`\`${l.contentName}\` requires a \`${l.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${l.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${l.docsSlug}`;return S.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},uR="DialogDescriptionWarning",fR=({contentRef:n,descriptionId:l})=>{const i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${$y(uR).contentName}}.`;return S.useEffect(()=>{var f;const c=(f=n.current)==null?void 0:f.getAttribute("aria-describedby");l&&c&&(document.getElementById(l)||console.warn(i))},[i,n,l]),null},dR=Dy,OO=Ly,hR=zy,mR=Uy,pR=ky,vR=Py,gR=Gy,yR=qy;function bR({...n}){return _.jsx(dR,{"data-slot":"sheet",...n})}function SR({...n}){return _.jsx(hR,{"data-slot":"sheet-portal",...n})}function xR({className:n,...l}){return _.jsx(mR,{"data-slot":"sheet-overlay",className:je("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",n),...l})}function _R({className:n,children:l,side:o="right",...i}){return _.jsxs(SR,{children:[_.jsx(xR,{}),_.jsxs(pR,{"data-slot":"sheet-content",className:je("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",o==="right"&&"data-[state=closed]:slide-out-to-end data-[state=open]:slide-in-from-end inset-y-0 end-0 h-full w-3/4 border-s sm:max-w-sm",o==="left"&&"data-[state=closed]:slide-out-to-start data-[state=open]:slide-in-from-start inset-y-0 start-0 h-full w-3/4 border-e sm:max-w-sm",o==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",o==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",n),...i,children:[l,_.jsxs(yR,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 end-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[_.jsx(sw,{className:"size-4"}),_.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function wR({className:n,...l}){return _.jsx("div",{"data-slot":"sheet-header",className:je("flex flex-col gap-1.5 p-4",n),...l})}function ER({className:n,...l}){return _.jsx(vR,{"data-slot":"sheet-title",className:je("text-foreground font-semibold",n),...l})}function RR({className:n,...l}){return _.jsx(gR,{"data-slot":"sheet-description",className:je("text-muted-foreground text-sm",n),...l})}const CR=["top","right","bottom","left"],ba=Math.min,kt=Math.max,us=Math.round,Zi=Math.floor,yn=n=>({x:n,y:n}),AR={left:"right",right:"left",bottom:"top",top:"bottom"},MR={start:"end",end:"start"};function Gf(n,l,o){return kt(n,ba(l,o))}function qn(n,l){return typeof n=="function"?n(l):n}function Yn(n){return n.split("-")[0]}function to(n){return n.split("-")[1]}function ud(n){return n==="x"?"y":"x"}function fd(n){return n==="y"?"height":"width"}const TR=new Set(["top","bottom"]);function gn(n){return TR.has(Yn(n))?"y":"x"}function dd(n){return ud(gn(n))}function OR(n,l,o){o===void 0&&(o=!1);const i=to(n),c=dd(n),f=fd(c);let d=c==="x"?i===(o?"end":"start")?"right":"left":i==="start"?"bottom":"top";return l.reference[f]>l.floating[f]&&(d=fs(d)),[d,fs(d)]}function DR(n){const l=fs(n);return[Vf(n),l,Vf(l)]}function Vf(n){return n.replace(/start|end/g,l=>MR[l])}const gg=["left","right"],yg=["right","left"],NR=["top","bottom"],LR=["bottom","top"];function jR(n,l,o){switch(n){case"top":case"bottom":return o?l?yg:gg:l?gg:yg;case"left":case"right":return l?NR:LR;default:return[]}}function zR(n,l,o,i){const c=to(n);let f=jR(Yn(n),o==="start",i);return c&&(f=f.map(d=>d+"-"+c),l&&(f=f.concat(f.map(Vf)))),f}function fs(n){return n.replace(/left|right|bottom|top/g,l=>AR[l])}function UR(n){return{top:0,right:0,bottom:0,left:0,...n}}function Iy(n){return typeof n!="number"?UR(n):{top:n,right:n,bottom:n,left:n}}function ds(n){const{x:l,y:o,width:i,height:c}=n;return{width:i,height:c,top:o,left:l,right:l+i,bottom:o+c,x:l,y:o}}function bg(n,l,o){let{reference:i,floating:c}=n;const f=gn(l),d=dd(l),h=fd(d),m=Yn(l),p=f==="y",v=i.x+i.width/2-c.width/2,g=i.y+i.height/2-c.height/2,b=i[h]/2-c[h]/2;let w;switch(m){case"top":w={x:v,y:i.y-c.height};break;case"bottom":w={x:v,y:i.y+i.height};break;case"right":w={x:i.x+i.width,y:g};break;case"left":w={x:i.x-c.width,y:g};break;default:w={x:i.x,y:i.y}}switch(to(l)){case"start":w[d]-=b*(o&&p?-1:1);break;case"end":w[d]+=b*(o&&p?-1:1);break}return w}const kR=async(n,l,o)=>{const{placement:i="bottom",strategy:c="absolute",middleware:f=[],platform:d}=o,h=f.filter(Boolean),m=await(d.isRTL==null?void 0:d.isRTL(l));let p=await d.getElementRects({reference:n,floating:l,strategy:c}),{x:v,y:g}=bg(p,i,m),b=i,w={},R=0;for(let E=0;E<h.length;E++){const{name:C,fn:T}=h[E],{x:z,y:L,data:N,reset:G}=await T({x:v,y:g,initialPlacement:i,placement:b,strategy:c,middlewareData:w,rects:p,platform:d,elements:{reference:n,floating:l}});v=z??v,g=L??g,w={...w,[C]:{...w[C],...N}},G&&R<=50&&(R++,typeof G=="object"&&(G.placement&&(b=G.placement),G.rects&&(p=G.rects===!0?await d.getElementRects({reference:n,floating:l,strategy:c}):G.rects),{x:v,y:g}=bg(p,b,m)),E=-1)}return{x:v,y:g,placement:b,strategy:c,middlewareData:w}};async function hl(n,l){var o;l===void 0&&(l={});const{x:i,y:c,platform:f,rects:d,elements:h,strategy:m}=n,{boundary:p="clippingAncestors",rootBoundary:v="viewport",elementContext:g="floating",altBoundary:b=!1,padding:w=0}=qn(l,n),R=Iy(w),C=h[b?g==="floating"?"reference":"floating":g],T=ds(await f.getClippingRect({element:(o=await(f.isElement==null?void 0:f.isElement(C)))==null||o?C:C.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:p,rootBoundary:v,strategy:m})),z=g==="floating"?{x:i,y:c,width:d.floating.width,height:d.floating.height}:d.reference,L=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),N=await(f.isElement==null?void 0:f.isElement(L))?await(f.getScale==null?void 0:f.getScale(L))||{x:1,y:1}:{x:1,y:1},G=ds(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:z,offsetParent:L,strategy:m}):z);return{top:(T.top-G.top+R.top)/N.y,bottom:(G.bottom-T.bottom+R.bottom)/N.y,left:(T.left-G.left+R.left)/N.x,right:(G.right-T.right+R.right)/N.x}}const BR=n=>({name:"arrow",options:n,async fn(l){const{x:o,y:i,placement:c,rects:f,platform:d,elements:h,middlewareData:m}=l,{element:p,padding:v=0}=qn(n,l)||{};if(p==null)return{};const g=Iy(v),b={x:o,y:i},w=dd(c),R=fd(w),E=await d.getDimensions(p),C=w==="y",T=C?"top":"left",z=C?"bottom":"right",L=C?"clientHeight":"clientWidth",N=f.reference[R]+f.reference[w]-b[w]-f.floating[R],G=b[w]-f.reference[w],W=await(d.getOffsetParent==null?void 0:d.getOffsetParent(p));let V=W?W[L]:0;(!V||!await(d.isElement==null?void 0:d.isElement(W)))&&(V=h.floating[L]||f.floating[R]);const q=N/2-G/2,ae=V/2-E[R]/2-1,Z=ba(g[T],ae),re=ba(g[z],ae),te=Z,le=V-E[R]-re,ue=V/2-E[R]/2+q,de=Gf(te,ue,le),O=!m.arrow&&to(c)!=null&&ue!==de&&f.reference[R]/2-(ue<te?Z:re)-E[R]/2<0,$=O?ue<te?ue-te:ue-le:0;return{[w]:b[w]+$,data:{[w]:de,centerOffset:ue-de-$,...O&&{alignmentOffset:$}},reset:O}}}),PR=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(l){var o,i;const{placement:c,middlewareData:f,rects:d,initialPlacement:h,platform:m,elements:p}=l,{mainAxis:v=!0,crossAxis:g=!0,fallbackPlacements:b,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:E=!0,...C}=qn(n,l);if((o=f.arrow)!=null&&o.alignmentOffset)return{};const T=Yn(c),z=gn(h),L=Yn(h)===h,N=await(m.isRTL==null?void 0:m.isRTL(p.floating)),G=b||(L||!E?[fs(h)]:DR(h)),W=R!=="none";!b&&W&&G.push(...zR(h,E,R,N));const V=[h,...G],q=await hl(l,C),ae=[];let Z=((i=f.flip)==null?void 0:i.overflows)||[];if(v&&ae.push(q[T]),g){const ue=OR(c,d,N);ae.push(q[ue[0]],q[ue[1]])}if(Z=[...Z,{placement:c,overflows:ae}],!ae.every(ue=>ue<=0)){var re,te;const ue=(((re=f.flip)==null?void 0:re.index)||0)+1,de=V[ue];if(de&&(!(g==="alignment"?z!==gn(de):!1)||Z.every(U=>U.overflows[0]>0&&gn(U.placement)===z)))return{data:{index:ue,overflows:Z},reset:{placement:de}};let O=(te=Z.filter($=>$.overflows[0]<=0).sort(($,U)=>$.overflows[1]-U.overflows[1])[0])==null?void 0:te.placement;if(!O)switch(w){case"bestFit":{var le;const $=(le=Z.filter(U=>{if(W){const Q=gn(U.placement);return Q===z||Q==="y"}return!0}).map(U=>[U.placement,U.overflows.filter(Q=>Q>0).reduce((Q,A)=>Q+A,0)]).sort((U,Q)=>U[1]-Q[1])[0])==null?void 0:le[0];$&&(O=$);break}case"initialPlacement":O=h;break}if(c!==O)return{reset:{placement:O}}}return{}}}};function Sg(n,l){return{top:n.top-l.height,right:n.right-l.width,bottom:n.bottom-l.height,left:n.left-l.width}}function xg(n){return CR.some(l=>n[l]>=0)}const HR=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(l){const{rects:o}=l,{strategy:i="referenceHidden",...c}=qn(n,l);switch(i){case"referenceHidden":{const f=await hl(l,{...c,elementContext:"reference"}),d=Sg(f,o.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:xg(d)}}}case"escaped":{const f=await hl(l,{...c,altBoundary:!0}),d=Sg(f,o.floating);return{data:{escapedOffsets:d,escaped:xg(d)}}}default:return{}}}}},Xy=new Set(["left","top"]);async function GR(n,l){const{placement:o,platform:i,elements:c}=n,f=await(i.isRTL==null?void 0:i.isRTL(c.floating)),d=Yn(o),h=to(o),m=gn(o)==="y",p=Xy.has(d)?-1:1,v=f&&m?-1:1,g=qn(l,n);let{mainAxis:b,crossAxis:w,alignmentAxis:R}=typeof g=="number"?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return h&&typeof R=="number"&&(w=h==="end"?R*-1:R),m?{x:w*v,y:b*p}:{x:b*p,y:w*v}}const VR=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(l){var o,i;const{x:c,y:f,placement:d,middlewareData:h}=l,m=await GR(l,n);return d===((o=h.offset)==null?void 0:o.placement)&&(i=h.arrow)!=null&&i.alignmentOffset?{}:{x:c+m.x,y:f+m.y,data:{...m,placement:d}}}}},qR=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(l){const{x:o,y:i,placement:c}=l,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:C=>{let{x:T,y:z}=C;return{x:T,y:z}}},...m}=qn(n,l),p={x:o,y:i},v=await hl(l,m),g=gn(Yn(c)),b=ud(g);let w=p[b],R=p[g];if(f){const C=b==="y"?"top":"left",T=b==="y"?"bottom":"right",z=w+v[C],L=w-v[T];w=Gf(z,w,L)}if(d){const C=g==="y"?"top":"left",T=g==="y"?"bottom":"right",z=R+v[C],L=R-v[T];R=Gf(z,R,L)}const E=h.fn({...l,[b]:w,[g]:R});return{...E,data:{x:E.x-o,y:E.y-i,enabled:{[b]:f,[g]:d}}}}}},YR=function(n){return n===void 0&&(n={}),{options:n,fn(l){const{x:o,y:i,placement:c,rects:f,middlewareData:d}=l,{offset:h=0,mainAxis:m=!0,crossAxis:p=!0}=qn(n,l),v={x:o,y:i},g=gn(c),b=ud(g);let w=v[b],R=v[g];const E=qn(h,l),C=typeof E=="number"?{mainAxis:E,crossAxis:0}:{mainAxis:0,crossAxis:0,...E};if(m){const L=b==="y"?"height":"width",N=f.reference[b]-f.floating[L]+C.mainAxis,G=f.reference[b]+f.reference[L]-C.mainAxis;w<N?w=N:w>G&&(w=G)}if(p){var T,z;const L=b==="y"?"width":"height",N=Xy.has(Yn(c)),G=f.reference[g]-f.floating[L]+(N&&((T=d.offset)==null?void 0:T[g])||0)+(N?0:C.crossAxis),W=f.reference[g]+f.reference[L]+(N?0:((z=d.offset)==null?void 0:z[g])||0)-(N?C.crossAxis:0);R<G?R=G:R>W&&(R=W)}return{[b]:w,[g]:R}}}},$R=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(l){var o,i;const{placement:c,rects:f,platform:d,elements:h}=l,{apply:m=()=>{},...p}=qn(n,l),v=await hl(l,p),g=Yn(c),b=to(c),w=gn(c)==="y",{width:R,height:E}=f.floating;let C,T;g==="top"||g==="bottom"?(C=g,T=b===(await(d.isRTL==null?void 0:d.isRTL(h.floating))?"start":"end")?"left":"right"):(T=g,C=b==="end"?"top":"bottom");const z=E-v.top-v.bottom,L=R-v.left-v.right,N=ba(E-v[C],z),G=ba(R-v[T],L),W=!l.middlewareData.shift;let V=N,q=G;if((o=l.middlewareData.shift)!=null&&o.enabled.x&&(q=L),(i=l.middlewareData.shift)!=null&&i.enabled.y&&(V=z),W&&!b){const Z=kt(v.left,0),re=kt(v.right,0),te=kt(v.top,0),le=kt(v.bottom,0);w?q=R-2*(Z!==0||re!==0?Z+re:kt(v.left,v.right)):V=E-2*(te!==0||le!==0?te+le:kt(v.top,v.bottom))}await m({...l,availableWidth:q,availableHeight:V});const ae=await d.getDimensions(h.floating);return R!==ae.width||E!==ae.height?{reset:{rects:!0}}:{}}}};function Ms(){return typeof window<"u"}function no(n){return Ky(n)?(n.nodeName||"").toLowerCase():"#document"}function Bt(n){var l;return(n==null||(l=n.ownerDocument)==null?void 0:l.defaultView)||window}function _n(n){var l;return(l=(Ky(n)?n.ownerDocument:n.document)||window.document)==null?void 0:l.documentElement}function Ky(n){return Ms()?n instanceof Node||n instanceof Bt(n).Node:!1}function rn(n){return Ms()?n instanceof Element||n instanceof Bt(n).Element:!1}function Sn(n){return Ms()?n instanceof HTMLElement||n instanceof Bt(n).HTMLElement:!1}function _g(n){return!Ms()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof Bt(n).ShadowRoot}const IR=new Set(["inline","contents"]);function _l(n){const{overflow:l,overflowX:o,overflowY:i,display:c}=on(n);return/auto|scroll|overlay|hidden|clip/.test(l+i+o)&&!IR.has(c)}const XR=new Set(["table","td","th"]);function KR(n){return XR.has(no(n))}const QR=[":popover-open",":modal"];function Ts(n){return QR.some(l=>{try{return n.matches(l)}catch{return!1}})}const ZR=["transform","translate","scale","rotate","perspective"],FR=["transform","translate","scale","rotate","perspective","filter"],WR=["paint","layout","strict","content"];function hd(n){const l=md(),o=rn(n)?on(n):n;return ZR.some(i=>o[i]?o[i]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!l&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!l&&(o.filter?o.filter!=="none":!1)||FR.some(i=>(o.willChange||"").includes(i))||WR.some(i=>(o.contain||"").includes(i))}function JR(n){let l=Sa(n);for(;Sn(l)&&!Fr(l);){if(hd(l))return l;if(Ts(l))return null;l=Sa(l)}return null}function md(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const eC=new Set(["html","body","#document"]);function Fr(n){return eC.has(no(n))}function on(n){return Bt(n).getComputedStyle(n)}function Os(n){return rn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Sa(n){if(no(n)==="html")return n;const l=n.assignedSlot||n.parentNode||_g(n)&&n.host||_n(n);return _g(l)?l.host:l}function Qy(n){const l=Sa(n);return Fr(l)?n.ownerDocument?n.ownerDocument.body:n.body:Sn(l)&&_l(l)?l:Qy(l)}function ml(n,l,o){var i;l===void 0&&(l=[]),o===void 0&&(o=!0);const c=Qy(n),f=c===((i=n.ownerDocument)==null?void 0:i.body),d=Bt(c);if(f){const h=qf(d);return l.concat(d,d.visualViewport||[],_l(c)?c:[],h&&o?ml(h):[])}return l.concat(c,ml(c,[],o))}function qf(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function Zy(n){const l=on(n);let o=parseFloat(l.width)||0,i=parseFloat(l.height)||0;const c=Sn(n),f=c?n.offsetWidth:o,d=c?n.offsetHeight:i,h=us(o)!==f||us(i)!==d;return h&&(o=f,i=d),{width:o,height:i,$:h}}function pd(n){return rn(n)?n:n.contextElement}function Ir(n){const l=pd(n);if(!Sn(l))return yn(1);const o=l.getBoundingClientRect(),{width:i,height:c,$:f}=Zy(l);let d=(f?us(o.width):o.width)/i,h=(f?us(o.height):o.height)/c;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const tC=yn(0);function Fy(n){const l=Bt(n);return!md()||!l.visualViewport?tC:{x:l.visualViewport.offsetLeft,y:l.visualViewport.offsetTop}}function nC(n,l,o){return l===void 0&&(l=!1),!o||l&&o!==Bt(n)?!1:l}function Ka(n,l,o,i){l===void 0&&(l=!1),o===void 0&&(o=!1);const c=n.getBoundingClientRect(),f=pd(n);let d=yn(1);l&&(i?rn(i)&&(d=Ir(i)):d=Ir(n));const h=nC(f,o,i)?Fy(f):yn(0);let m=(c.left+h.x)/d.x,p=(c.top+h.y)/d.y,v=c.width/d.x,g=c.height/d.y;if(f){const b=Bt(f),w=i&&rn(i)?Bt(i):i;let R=b,E=qf(R);for(;E&&i&&w!==R;){const C=Ir(E),T=E.getBoundingClientRect(),z=on(E),L=T.left+(E.clientLeft+parseFloat(z.paddingLeft))*C.x,N=T.top+(E.clientTop+parseFloat(z.paddingTop))*C.y;m*=C.x,p*=C.y,v*=C.x,g*=C.y,m+=L,p+=N,R=Bt(E),E=qf(R)}}return ds({width:v,height:g,x:m,y:p})}function vd(n,l){const o=Os(n).scrollLeft;return l?l.left+o:Ka(_n(n)).left+o}function Wy(n,l,o){o===void 0&&(o=!1);const i=n.getBoundingClientRect(),c=i.left+l.scrollLeft-(o?0:vd(n,i)),f=i.top+l.scrollTop;return{x:c,y:f}}function aC(n){let{elements:l,rect:o,offsetParent:i,strategy:c}=n;const f=c==="fixed",d=_n(i),h=l?Ts(l.floating):!1;if(i===d||h&&f)return o;let m={scrollLeft:0,scrollTop:0},p=yn(1);const v=yn(0),g=Sn(i);if((g||!g&&!f)&&((no(i)!=="body"||_l(d))&&(m=Os(i)),Sn(i))){const w=Ka(i);p=Ir(i),v.x=w.x+i.clientLeft,v.y=w.y+i.clientTop}const b=d&&!g&&!f?Wy(d,m,!0):yn(0);return{width:o.width*p.x,height:o.height*p.y,x:o.x*p.x-m.scrollLeft*p.x+v.x+b.x,y:o.y*p.y-m.scrollTop*p.y+v.y+b.y}}function rC(n){return Array.from(n.getClientRects())}function oC(n){const l=_n(n),o=Os(n),i=n.ownerDocument.body,c=kt(l.scrollWidth,l.clientWidth,i.scrollWidth,i.clientWidth),f=kt(l.scrollHeight,l.clientHeight,i.scrollHeight,i.clientHeight);let d=-o.scrollLeft+vd(n);const h=-o.scrollTop;return on(i).direction==="rtl"&&(d+=kt(l.clientWidth,i.clientWidth)-c),{width:c,height:f,x:d,y:h}}function lC(n,l){const o=Bt(n),i=_n(n),c=o.visualViewport;let f=i.clientWidth,d=i.clientHeight,h=0,m=0;if(c){f=c.width,d=c.height;const p=md();(!p||p&&l==="fixed")&&(h=c.offsetLeft,m=c.offsetTop)}return{width:f,height:d,x:h,y:m}}const iC=new Set(["absolute","fixed"]);function sC(n,l){const o=Ka(n,!0,l==="fixed"),i=o.top+n.clientTop,c=o.left+n.clientLeft,f=Sn(n)?Ir(n):yn(1),d=n.clientWidth*f.x,h=n.clientHeight*f.y,m=c*f.x,p=i*f.y;return{width:d,height:h,x:m,y:p}}function wg(n,l,o){let i;if(l==="viewport")i=lC(n,o);else if(l==="document")i=oC(_n(n));else if(rn(l))i=sC(l,o);else{const c=Fy(n);i={x:l.x-c.x,y:l.y-c.y,width:l.width,height:l.height}}return ds(i)}function Jy(n,l){const o=Sa(n);return o===l||!rn(o)||Fr(o)?!1:on(o).position==="fixed"||Jy(o,l)}function cC(n,l){const o=l.get(n);if(o)return o;let i=ml(n,[],!1).filter(h=>rn(h)&&no(h)!=="body"),c=null;const f=on(n).position==="fixed";let d=f?Sa(n):n;for(;rn(d)&&!Fr(d);){const h=on(d),m=hd(d);!m&&h.position==="fixed"&&(c=null),(f?!m&&!c:!m&&h.position==="static"&&!!c&&iC.has(c.position)||_l(d)&&!m&&Jy(n,d))?i=i.filter(v=>v!==d):c=h,d=Sa(d)}return l.set(n,i),i}function uC(n){let{element:l,boundary:o,rootBoundary:i,strategy:c}=n;const d=[...o==="clippingAncestors"?Ts(l)?[]:cC(l,this._c):[].concat(o),i],h=d[0],m=d.reduce((p,v)=>{const g=wg(l,v,c);return p.top=kt(g.top,p.top),p.right=ba(g.right,p.right),p.bottom=ba(g.bottom,p.bottom),p.left=kt(g.left,p.left),p},wg(l,h,c));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function fC(n){const{width:l,height:o}=Zy(n);return{width:l,height:o}}function dC(n,l,o){const i=Sn(l),c=_n(l),f=o==="fixed",d=Ka(n,!0,f,l);let h={scrollLeft:0,scrollTop:0};const m=yn(0);function p(){m.x=vd(c)}if(i||!i&&!f)if((no(l)!=="body"||_l(c))&&(h=Os(l)),i){const w=Ka(l,!0,f,l);m.x=w.x+l.clientLeft,m.y=w.y+l.clientTop}else c&&p();f&&!i&&c&&p();const v=c&&!i&&!f?Wy(c,h):yn(0),g=d.left+h.scrollLeft-m.x-v.x,b=d.top+h.scrollTop-m.y-v.y;return{x:g,y:b,width:d.width,height:d.height}}function Ef(n){return on(n).position==="static"}function Eg(n,l){if(!Sn(n)||on(n).position==="fixed")return null;if(l)return l(n);let o=n.offsetParent;return _n(n)===o&&(o=o.ownerDocument.body),o}function e0(n,l){const o=Bt(n);if(Ts(n))return o;if(!Sn(n)){let c=Sa(n);for(;c&&!Fr(c);){if(rn(c)&&!Ef(c))return c;c=Sa(c)}return o}let i=Eg(n,l);for(;i&&KR(i)&&Ef(i);)i=Eg(i,l);return i&&Fr(i)&&Ef(i)&&!hd(i)?o:i||JR(n)||o}const hC=async function(n){const l=this.getOffsetParent||e0,o=this.getDimensions,i=await o(n.floating);return{reference:dC(n.reference,await l(n.floating),n.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function mC(n){return on(n).direction==="rtl"}const pC={convertOffsetParentRelativeRectToViewportRelativeRect:aC,getDocumentElement:_n,getClippingRect:uC,getOffsetParent:e0,getElementRects:hC,getClientRects:rC,getDimensions:fC,getScale:Ir,isElement:rn,isRTL:mC};function t0(n,l){return n.x===l.x&&n.y===l.y&&n.width===l.width&&n.height===l.height}function vC(n,l){let o=null,i;const c=_n(n);function f(){var h;clearTimeout(i),(h=o)==null||h.disconnect(),o=null}function d(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),f();const p=n.getBoundingClientRect(),{left:v,top:g,width:b,height:w}=p;if(h||l(),!b||!w)return;const R=Zi(g),E=Zi(c.clientWidth-(v+b)),C=Zi(c.clientHeight-(g+w)),T=Zi(v),L={rootMargin:-R+"px "+-E+"px "+-C+"px "+-T+"px",threshold:kt(0,ba(1,m))||1};let N=!0;function G(W){const V=W[0].intersectionRatio;if(V!==m){if(!N)return d();V?d(!1,V):i=setTimeout(()=>{d(!1,1e-7)},1e3)}V===1&&!t0(p,n.getBoundingClientRect())&&d(),N=!1}try{o=new IntersectionObserver(G,{...L,root:c.ownerDocument})}catch{o=new IntersectionObserver(G,L)}o.observe(n)}return d(!0),f}function gC(n,l,o,i){i===void 0&&(i={});const{ancestorScroll:c=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=i,p=pd(n),v=c||f?[...p?ml(p):[],...ml(l)]:[];v.forEach(T=>{c&&T.addEventListener("scroll",o,{passive:!0}),f&&T.addEventListener("resize",o)});const g=p&&h?vC(p,o):null;let b=-1,w=null;d&&(w=new ResizeObserver(T=>{let[z]=T;z&&z.target===p&&w&&(w.unobserve(l),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var L;(L=w)==null||L.observe(l)})),o()}),p&&!m&&w.observe(p),w.observe(l));let R,E=m?Ka(n):null;m&&C();function C(){const T=Ka(n);E&&!t0(E,T)&&o(),E=T,R=requestAnimationFrame(C)}return o(),()=>{var T;v.forEach(z=>{c&&z.removeEventListener("scroll",o),f&&z.removeEventListener("resize",o)}),g==null||g(),(T=w)==null||T.disconnect(),w=null,m&&cancelAnimationFrame(R)}}const yC=VR,bC=qR,SC=PR,xC=$R,_C=HR,Rg=BR,wC=YR,EC=(n,l,o)=>{const i=new Map,c={platform:pC,...o},f={...c.platform,_c:i};return kR(n,l,{...c,platform:f})};var RC=typeof document<"u",CC=function(){},ts=RC?S.useLayoutEffect:CC;function hs(n,l){if(n===l)return!0;if(typeof n!=typeof l)return!1;if(typeof n=="function"&&n.toString()===l.toString())return!0;let o,i,c;if(n&&l&&typeof n=="object"){if(Array.isArray(n)){if(o=n.length,o!==l.length)return!1;for(i=o;i--!==0;)if(!hs(n[i],l[i]))return!1;return!0}if(c=Object.keys(n),o=c.length,o!==Object.keys(l).length)return!1;for(i=o;i--!==0;)if(!{}.hasOwnProperty.call(l,c[i]))return!1;for(i=o;i--!==0;){const f=c[i];if(!(f==="_owner"&&n.$$typeof)&&!hs(n[f],l[f]))return!1}return!0}return n!==n&&l!==l}function n0(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Cg(n,l){const o=n0(n);return Math.round(l*o)/o}function Rf(n){const l=S.useRef(n);return ts(()=>{l.current=n}),l}function AC(n){n===void 0&&(n={});const{placement:l="bottom",strategy:o="absolute",middleware:i=[],platform:c,elements:{reference:f,floating:d}={},transform:h=!0,whileElementsMounted:m,open:p}=n,[v,g]=S.useState({x:0,y:0,strategy:o,placement:l,middlewareData:{},isPositioned:!1}),[b,w]=S.useState(i);hs(b,i)||w(i);const[R,E]=S.useState(null),[C,T]=S.useState(null),z=S.useCallback(U=>{U!==W.current&&(W.current=U,E(U))},[]),L=S.useCallback(U=>{U!==V.current&&(V.current=U,T(U))},[]),N=f||R,G=d||C,W=S.useRef(null),V=S.useRef(null),q=S.useRef(v),ae=m!=null,Z=Rf(m),re=Rf(c),te=Rf(p),le=S.useCallback(()=>{if(!W.current||!V.current)return;const U={placement:l,strategy:o,middleware:b};re.current&&(U.platform=re.current),EC(W.current,V.current,U).then(Q=>{const A={...Q,isPositioned:te.current!==!1};ue.current&&!hs(q.current,A)&&(q.current=A,bs.flushSync(()=>{g(A)}))})},[b,l,o,re,te]);ts(()=>{p===!1&&q.current.isPositioned&&(q.current.isPositioned=!1,g(U=>({...U,isPositioned:!1})))},[p]);const ue=S.useRef(!1);ts(()=>(ue.current=!0,()=>{ue.current=!1}),[]),ts(()=>{if(N&&(W.current=N),G&&(V.current=G),N&&G){if(Z.current)return Z.current(N,G,le);le()}},[N,G,le,Z,ae]);const de=S.useMemo(()=>({reference:W,floating:V,setReference:z,setFloating:L}),[z,L]),O=S.useMemo(()=>({reference:N,floating:G}),[N,G]),$=S.useMemo(()=>{const U={position:o,left:0,top:0};if(!O.floating)return U;const Q=Cg(O.floating,v.x),A=Cg(O.floating,v.y);return h?{...U,transform:"translate("+Q+"px, "+A+"px)",...n0(O.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:Q,top:A}},[o,h,O.floating,v.x,v.y]);return S.useMemo(()=>({...v,update:le,refs:de,elements:O,floatingStyles:$}),[v,le,de,O,$])}const MC=n=>{function l(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:n,fn(o){const{element:i,padding:c}=typeof n=="function"?n(o):n;return i&&l(i)?i.current!=null?Rg({element:i.current,padding:c}).fn(o):{}:i?Rg({element:i,padding:c}).fn(o):{}}}},TC=(n,l)=>({...yC(n),options:[n,l]}),OC=(n,l)=>({...bC(n),options:[n,l]}),DC=(n,l)=>({...wC(n),options:[n,l]}),NC=(n,l)=>({...SC(n),options:[n,l]}),LC=(n,l)=>({...xC(n),options:[n,l]}),jC=(n,l)=>({..._C(n),options:[n,l]}),zC=(n,l)=>({...MC(n),options:[n,l]});var UC="Arrow",a0=S.forwardRef((n,l)=>{const{children:o,width:i=10,height:c=5,...f}=n;return _.jsx(Ve.svg,{...f,ref:l,width:i,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?o:_.jsx("polygon",{points:"0,0 30,0 15,10"})})});a0.displayName=UC;var kC=a0;function BC(n){const[l,o]=S.useState(void 0);return an(()=>{if(n){o({width:n.offsetWidth,height:n.offsetHeight});const i=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const f=c[0];let d,h;if("borderBoxSize"in f){const m=f.borderBoxSize,p=Array.isArray(m)?m[0]:m;d=p.inlineSize,h=p.blockSize}else d=n.offsetWidth,h=n.offsetHeight;o({width:d,height:h})});return i.observe(n,{box:"border-box"}),()=>i.unobserve(n)}else o(void 0)},[n]),l}var gd="Popper",[r0,Ds]=xa(gd),[PC,o0]=r0(gd),l0=n=>{const{__scopePopper:l,children:o}=n,[i,c]=S.useState(null);return _.jsx(PC,{scope:l,anchor:i,onAnchorChange:c,children:o})};l0.displayName=gd;var i0="PopperAnchor",s0=S.forwardRef((n,l)=>{const{__scopePopper:o,virtualRef:i,...c}=n,f=o0(i0,o),d=S.useRef(null),h=et(l,d);return S.useEffect(()=>{f.onAnchorChange((i==null?void 0:i.current)||d.current)}),i?null:_.jsx(Ve.div,{...c,ref:h})});s0.displayName=i0;var yd="PopperContent",[HC,GC]=r0(yd),c0=S.forwardRef((n,l)=>{var fe,me,ge,Ce,De,He;const{__scopePopper:o,side:i="bottom",sideOffset:c=0,align:f="center",alignOffset:d=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:p=[],collisionPadding:v=0,sticky:g="partial",hideWhenDetached:b=!1,updatePositionStrategy:w="optimized",onPlaced:R,...E}=n,C=o0(yd,o),[T,z]=S.useState(null),L=et(l,ut=>z(ut)),[N,G]=S.useState(null),W=BC(N),V=(W==null?void 0:W.width)??0,q=(W==null?void 0:W.height)??0,ae=i+(f!=="center"?"-"+f:""),Z=typeof v=="number"?v:{top:0,right:0,bottom:0,left:0,...v},re=Array.isArray(p)?p:[p],te=re.length>0,le={padding:Z,boundary:re.filter(qC),altBoundary:te},{refs:ue,floatingStyles:de,placement:O,isPositioned:$,middlewareData:U}=AC({strategy:"fixed",placement:ae,whileElementsMounted:(...ut)=>gC(...ut,{animationFrame:w==="always"}),elements:{reference:C.anchor},middleware:[TC({mainAxis:c+q,alignmentAxis:d}),m&&OC({mainAxis:!0,crossAxis:!1,limiter:g==="partial"?DC():void 0,...le}),m&&NC({...le}),LC({...le,apply:({elements:ut,rects:wn,availableWidth:ao,availableHeight:ro})=>{const{width:xt,height:$n}=wn.reference,Pt=ut.floating.style;Pt.setProperty("--radix-popper-available-width",`${ao}px`),Pt.setProperty("--radix-popper-available-height",`${ro}px`),Pt.setProperty("--radix-popper-anchor-width",`${xt}px`),Pt.setProperty("--radix-popper-anchor-height",`${$n}px`)}}),N&&zC({element:N,padding:h}),YC({arrowWidth:V,arrowHeight:q}),b&&jC({strategy:"referenceHidden",...le})]}),[Q,A]=d0(O),Y=bn(R);an(()=>{$&&(Y==null||Y())},[$,Y]);const J=(fe=U.arrow)==null?void 0:fe.x,F=(me=U.arrow)==null?void 0:me.y,ee=((ge=U.arrow)==null?void 0:ge.centerOffset)!==0,[oe,ne]=S.useState();return an(()=>{T&&ne(window.getComputedStyle(T).zIndex)},[T]),_.jsx("div",{ref:ue.setFloating,"data-radix-popper-content-wrapper":"",style:{...de,transform:$?de.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:oe,"--radix-popper-transform-origin":[(Ce=U.transformOrigin)==null?void 0:Ce.x,(De=U.transformOrigin)==null?void 0:De.y].join(" "),...((He=U.hide)==null?void 0:He.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:_.jsx(HC,{scope:o,placedSide:Q,onArrowChange:G,arrowX:J,arrowY:F,shouldHideArrow:ee,children:_.jsx(Ve.div,{"data-side":Q,"data-align":A,...E,ref:L,style:{...E.style,animation:$?void 0:"none"}})})})});c0.displayName=yd;var u0="PopperArrow",VC={top:"bottom",right:"left",bottom:"top",left:"right"},f0=S.forwardRef(function(l,o){const{__scopePopper:i,...c}=l,f=GC(u0,i),d=VC[f.placedSide];return _.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:_.jsx(kC,{...c,ref:o,style:{...c.style,display:"block"}})})});f0.displayName=u0;function qC(n){return n!==null}var YC=n=>({name:"transformOrigin",options:n,fn(l){var C,T,z;const{placement:o,rects:i,middlewareData:c}=l,d=((C=c.arrow)==null?void 0:C.centerOffset)!==0,h=d?0:n.arrowWidth,m=d?0:n.arrowHeight,[p,v]=d0(o),g={start:"0%",center:"50%",end:"100%"}[v],b=(((T=c.arrow)==null?void 0:T.x)??0)+h/2,w=(((z=c.arrow)==null?void 0:z.y)??0)+m/2;let R="",E="";return p==="bottom"?(R=d?g:`${b}px`,E=`${-m}px`):p==="top"?(R=d?g:`${b}px`,E=`${i.floating.height+m}px`):p==="right"?(R=`${-m}px`,E=d?g:`${w}px`):p==="left"&&(R=`${i.floating.width+m}px`,E=d?g:`${w}px`),{data:{x:R,y:E}}}});function d0(n){const[l,o="center"]=n.split("-");return[l,o]}var h0=l0,m0=s0,p0=c0,v0=f0,$C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),IC="VisuallyHidden",g0=S.forwardRef((n,l)=>_.jsx(Ve.span,{...n,ref:l,style:{...$C,...n.style}}));g0.displayName=IC;var XC=g0,[Ns,DO]=xa("Tooltip",[Ds]),Ls=Ds(),y0="TooltipProvider",KC=700,Yf="tooltip.open",[QC,bd]=Ns(y0),b0=n=>{const{__scopeTooltip:l,delayDuration:o=KC,skipDelayDuration:i=300,disableHoverableContent:c=!1,children:f}=n,d=S.useRef(!0),h=S.useRef(!1),m=S.useRef(0);return S.useEffect(()=>{const p=m.current;return()=>window.clearTimeout(p)},[]),_.jsx(QC,{scope:l,isOpenDelayedRef:d,delayDuration:o,onOpen:S.useCallback(()=>{window.clearTimeout(m.current),d.current=!1},[]),onClose:S.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>d.current=!0,i)},[i]),isPointerInTransitRef:h,onPointerInTransitChange:S.useCallback(p=>{h.current=p},[]),disableHoverableContent:c,children:f})};b0.displayName=y0;var pl="Tooltip",[ZC,wl]=Ns(pl),S0=n=>{const{__scopeTooltip:l,children:o,open:i,defaultOpen:c,onOpenChange:f,disableHoverableContent:d,delayDuration:h}=n,m=bd(pl,n.__scopeTooltip),p=Ls(l),[v,g]=S.useState(null),b=Ya(),w=S.useRef(0),R=d??m.disableHoverableContent,E=h??m.delayDuration,C=S.useRef(!1),[T,z]=ws({prop:i,defaultProp:c??!1,onChange:V=>{V?(m.onOpen(),document.dispatchEvent(new CustomEvent(Yf))):m.onClose(),f==null||f(V)},caller:pl}),L=S.useMemo(()=>T?C.current?"delayed-open":"instant-open":"closed",[T]),N=S.useCallback(()=>{window.clearTimeout(w.current),w.current=0,C.current=!1,z(!0)},[z]),G=S.useCallback(()=>{window.clearTimeout(w.current),w.current=0,z(!1)},[z]),W=S.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>{C.current=!0,z(!0),w.current=0},E)},[E,z]);return S.useEffect(()=>()=>{w.current&&(window.clearTimeout(w.current),w.current=0)},[]),_.jsx(h0,{...p,children:_.jsx(ZC,{scope:l,contentId:b,open:T,stateAttribute:L,trigger:v,onTriggerChange:g,onTriggerEnter:S.useCallback(()=>{m.isOpenDelayedRef.current?W():N()},[m.isOpenDelayedRef,W,N]),onTriggerLeave:S.useCallback(()=>{R?G():(window.clearTimeout(w.current),w.current=0)},[G,R]),onOpen:N,onClose:G,disableHoverableContent:R,children:o})})};S0.displayName=pl;var $f="TooltipTrigger",x0=S.forwardRef((n,l)=>{const{__scopeTooltip:o,...i}=n,c=wl($f,o),f=bd($f,o),d=Ls(o),h=S.useRef(null),m=et(l,h,c.onTriggerChange),p=S.useRef(!1),v=S.useRef(!1),g=S.useCallback(()=>p.current=!1,[]);return S.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),_.jsx(m0,{asChild:!0,...d,children:_.jsx(Ve.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...i,ref:m,onPointerMove:be(n.onPointerMove,b=>{b.pointerType!=="touch"&&!v.current&&!f.isPointerInTransitRef.current&&(c.onTriggerEnter(),v.current=!0)}),onPointerLeave:be(n.onPointerLeave,()=>{c.onTriggerLeave(),v.current=!1}),onPointerDown:be(n.onPointerDown,()=>{c.open&&c.onClose(),p.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:be(n.onFocus,()=>{p.current||c.onOpen()}),onBlur:be(n.onBlur,c.onClose),onClick:be(n.onClick,c.onClose)})})});x0.displayName=$f;var Sd="TooltipPortal",[FC,WC]=Ns(Sd,{forceMount:void 0}),_0=n=>{const{__scopeTooltip:l,forceMount:o,children:i,container:c}=n,f=wl(Sd,l);return _.jsx(FC,{scope:l,forceMount:o,children:_.jsx(xn,{present:o||f.open,children:_.jsx(Rs,{asChild:!0,container:c,children:i})})})};_0.displayName=Sd;var Wr="TooltipContent",w0=S.forwardRef((n,l)=>{const o=WC(Wr,n.__scopeTooltip),{forceMount:i=o.forceMount,side:c="top",...f}=n,d=wl(Wr,n.__scopeTooltip);return _.jsx(xn,{present:i||d.open,children:d.disableHoverableContent?_.jsx(E0,{side:c,...f,ref:l}):_.jsx(JC,{side:c,...f,ref:l})})}),JC=S.forwardRef((n,l)=>{const o=wl(Wr,n.__scopeTooltip),i=bd(Wr,n.__scopeTooltip),c=S.useRef(null),f=et(l,c),[d,h]=S.useState(null),{trigger:m,onClose:p}=o,v=c.current,{onPointerInTransitChange:g}=i,b=S.useCallback(()=>{h(null),g(!1)},[g]),w=S.useCallback((R,E)=>{const C=R.currentTarget,T={x:R.clientX,y:R.clientY},z=aA(T,C.getBoundingClientRect()),L=rA(T,z),N=oA(E.getBoundingClientRect()),G=iA([...L,...N]);h(G),g(!0)},[g]);return S.useEffect(()=>()=>b(),[b]),S.useEffect(()=>{if(m&&v){const R=C=>w(C,v),E=C=>w(C,m);return m.addEventListener("pointerleave",R),v.addEventListener("pointerleave",E),()=>{m.removeEventListener("pointerleave",R),v.removeEventListener("pointerleave",E)}}},[m,v,w,b]),S.useEffect(()=>{if(d){const R=E=>{const C=E.target,T={x:E.clientX,y:E.clientY},z=(m==null?void 0:m.contains(C))||(v==null?void 0:v.contains(C)),L=!lA(T,d);z?b():L&&(b(),p())};return document.addEventListener("pointermove",R),()=>document.removeEventListener("pointermove",R)}},[m,v,d,p,b]),_.jsx(E0,{...n,ref:f})}),[eA,tA]=Ns(pl,{isInside:!1}),nA=K_("TooltipContent"),E0=S.forwardRef((n,l)=>{const{__scopeTooltip:o,children:i,"aria-label":c,onEscapeKeyDown:f,onPointerDownOutside:d,...h}=n,m=wl(Wr,o),p=Ls(o),{onClose:v}=m;return S.useEffect(()=>(document.addEventListener(Yf,v),()=>document.removeEventListener(Yf,v)),[v]),S.useEffect(()=>{if(m.trigger){const g=b=>{const w=b.target;w!=null&&w.contains(m.trigger)&&v()};return window.addEventListener("scroll",g,{capture:!0}),()=>window.removeEventListener("scroll",g,{capture:!0})}},[m.trigger,v]),_.jsx(Es,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:g=>g.preventDefault(),onDismiss:v,children:_.jsxs(p0,{"data-state":m.stateAttribute,...p,...h,ref:l,style:{...h.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[_.jsx(nA,{children:i}),_.jsx(eA,{scope:o,isInside:!0,children:_.jsx(XC,{id:m.contentId,role:"tooltip",children:c||i})})]})})});w0.displayName=Wr;var R0="TooltipArrow",C0=S.forwardRef((n,l)=>{const{__scopeTooltip:o,...i}=n,c=Ls(o);return tA(R0,o).isInside?null:_.jsx(v0,{...c,...i,ref:l})});C0.displayName=R0;function aA(n,l){const o=Math.abs(l.top-n.y),i=Math.abs(l.bottom-n.y),c=Math.abs(l.right-n.x),f=Math.abs(l.left-n.x);switch(Math.min(o,i,c,f)){case f:return"left";case c:return"right";case o:return"top";case i:return"bottom";default:throw new Error("unreachable")}}function rA(n,l,o=5){const i=[];switch(l){case"top":i.push({x:n.x-o,y:n.y+o},{x:n.x+o,y:n.y+o});break;case"bottom":i.push({x:n.x-o,y:n.y-o},{x:n.x+o,y:n.y-o});break;case"left":i.push({x:n.x+o,y:n.y-o},{x:n.x+o,y:n.y+o});break;case"right":i.push({x:n.x-o,y:n.y-o},{x:n.x-o,y:n.y+o});break}return i}function oA(n){const{top:l,right:o,bottom:i,left:c}=n;return[{x:c,y:l},{x:o,y:l},{x:o,y:i},{x:c,y:i}]}function lA(n,l){const{x:o,y:i}=n;let c=!1;for(let f=0,d=l.length-1;f<l.length;d=f++){const h=l[f],m=l[d],p=h.x,v=h.y,g=m.x,b=m.y;v>i!=b>i&&o<(g-p)*(i-v)/(b-v)+p&&(c=!c)}return c}function iA(n){const l=n.slice();return l.sort((o,i)=>o.x<i.x?-1:o.x>i.x?1:o.y<i.y?-1:o.y>i.y?1:0),sA(l)}function sA(n){if(n.length<=1)return n.slice();const l=[];for(let i=0;i<n.length;i++){const c=n[i];for(;l.length>=2;){const f=l[l.length-1],d=l[l.length-2];if((f.x-d.x)*(c.y-d.y)>=(f.y-d.y)*(c.x-d.x))l.pop();else break}l.push(c)}l.pop();const o=[];for(let i=n.length-1;i>=0;i--){const c=n[i];for(;o.length>=2;){const f=o[o.length-1],d=o[o.length-2];if((f.x-d.x)*(c.y-d.y)>=(f.y-d.y)*(c.x-d.x))o.pop();else break}o.push(c)}return o.pop(),l.length===1&&o.length===1&&l[0].x===o[0].x&&l[0].y===o[0].y?l:l.concat(o)}var cA=b0,uA=S0,fA=x0,dA=_0,hA=w0,mA=C0;function A0({delayDuration:n=0,...l}){return _.jsx(cA,{"data-slot":"tooltip-provider",delayDuration:n,...l})}function pA({...n}){return _.jsx(A0,{children:_.jsx(uA,{"data-slot":"tooltip",...n})})}function vA({...n}){return _.jsx(fA,{"data-slot":"tooltip-trigger",...n})}function gA({className:n,sideOffset:l=0,children:o,...i}){return _.jsx(dA,{children:_.jsxs(hA,{"data-slot":"tooltip-content",sideOffset:l,className:je("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-end-2 data-[side=right]:slide-in-from-start-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",n),...i,children:[o,_.jsx(mA,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const yA="sidebar_state",bA=3600*24*7,SA="16rem",xA="18rem",_A="3rem",wA="b",M0=S.createContext(null);function js(){const n=S.useContext(M0);if(!n)throw new Error("useSidebar must be used within a SidebarProvider.");return n}function EA({defaultOpen:n=!0,open:l,onOpenChange:o,className:i,style:c,children:f,...d}){const h=cw(),[m,p]=S.useState(!1),[v,g]=S.useState(n),b=l??v,w=S.useCallback(T=>{const z=typeof T=="function"?T(b):T;o?o(z):g(z),document.cookie=`${yA}=${z}; path=/; max-age=${bA}`},[o,b]),R=S.useCallback(()=>h?p(T=>!T):w(T=>!T),[h,w,p]);S.useEffect(()=>{const T=z=>{z.key===wA&&(z.metaKey||z.ctrlKey)&&(z.preventDefault(),R())};return window.addEventListener("keydown",T),()=>window.removeEventListener("keydown",T)},[R]);const E=b?"expanded":"collapsed",C=S.useMemo(()=>({state:E,open:b,setOpen:w,isMobile:h,openMobile:m,setOpenMobile:p,toggleSidebar:R}),[E,b,w,h,m,p,R]);return _.jsx(M0.Provider,{value:C,children:_.jsx(A0,{delayDuration:0,children:_.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":SA,"--sidebar-width-icon":_A,...c},className:je("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...d,children:f})})})}function RA({side:n="left",variant:l="sidebar",collapsible:o="offcanvas",className:i,children:c,...f}){const{isMobile:d,state:h,openMobile:m,setOpenMobile:p}=js();return o==="none"?_.jsx("div",{"data-slot":"sidebar",className:je("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...f,children:c}):d?_.jsx(bR,{open:m,onOpenChange:p,...f,children:_.jsxs(_R,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":xA},side:n,children:[_.jsxs(wR,{className:"sr-only",children:[_.jsx(ER,{children:"Sidebar"}),_.jsx(RR,{children:"Displays the mobile sidebar."})]}),_.jsx("div",{className:"flex h-full w-full flex-col",children:c})]})}):_.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":h,"data-collapsible":h==="collapsed"?o:"","data-variant":l,"data-side":n,"data-slot":"sidebar",children:[_.jsx("div",{"data-slot":"sidebar-gap",className:je("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",l==="floating"||l==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),_.jsx("div",{"data-slot":"sidebar-container",className:je("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",n==="left"?"start-0 group-data-[collapsible=offcanvas]:start-[calc(var(--sidebar-width)*-1)]":"end-0 group-data-[collapsible=offcanvas]:end-[calc(var(--sidebar-width)*-1)]",l==="floating"||l==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-e group-data-[side=right]:border-l",i),...f,children:_.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:c})})]})}function NO({className:n,onClick:l,...o}){const{toggleSidebar:i}=js();return _.jsxs(py,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:je("size-7",n),onClick:c=>{l==null||l(c),i()},...o,children:[_.jsx(lw,{}),_.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function CA({className:n,...l}){return _.jsx("main",{"data-slot":"sidebar-inset",className:je("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ms-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ms-2",n),...l})}function AA({className:n,...l}){return _.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:je("flex flex-col gap-2 p-2",n),...l})}function MA({className:n,...l}){return _.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:je("flex flex-col gap-2 p-2",n),...l})}function TA({className:n,...l}){return _.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:je("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",n),...l})}function T0({className:n,...l}){return _.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:je("relative flex w-full min-w-0 flex-col p-2",n),...l})}function O0({className:n,...l}){return _.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:je("w-full text-sm",n),...l})}function vl({className:n,...l}){return _.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:je("flex w-full min-w-0 flex-col gap-1",n),...l})}function gl({className:n,...l}){return _.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:je("group/menu-item relative",n),...l})}const OA=ay("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-start text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pe-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function yl({asChild:n=!1,isActive:l=!1,variant:o="default",size:i="default",tooltip:c,className:f,...d}){const h=n?Jg:"button",{isMobile:m,state:p}=js(),v=_.jsx(h,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":l,className:je(OA({variant:o,size:i}),f),...d});return c?(typeof c=="string"&&(c={children:c}),_.jsxs(pA,{children:[_.jsx(vA,{asChild:!0,children:v}),_.jsx(gA,{side:"right",align:"center",hidden:p!=="collapsed"||m,...c})]})):v}/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var DA={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const bt=(n,l,o,i)=>{const c=S.forwardRef(({color:f="currentColor",size:d=24,stroke:h=2,title:m,className:p,children:v,...g},b)=>S.createElement("svg",{ref:b,...DA[n],width:d,height:d,className:["tabler-icon",`tabler-icon-${l}`,p].join(" "),...n==="filled"?{fill:f}:{strokeWidth:h,stroke:f},...g},[m&&S.createElement("title",{key:"svg-title"},m),...i.map(([w,R])=>S.createElement(w,R)),...Array.isArray(v)?v:[v]]));return c.displayName=`${o}`,c};/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const NA=[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M9 8l1 0",key:"svg-1"}],["path",{d:"M9 12l1 0",key:"svg-2"}],["path",{d:"M9 16l1 0",key:"svg-3"}],["path",{d:"M14 8l1 0",key:"svg-4"}],["path",{d:"M14 12l1 0",key:"svg-5"}],["path",{d:"M14 16l1 0",key:"svg-6"}],["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16",key:"svg-7"}]],LA=bt("outline","building","Building",NA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const jA=[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]],zA=bt("outline","credit-card","CreditCard",jA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const UA=[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]],kA=bt("outline","dashboard","Dashboard",UA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const BA=[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]],PA=bt("outline","dots-vertical","DotsVertical",BA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const HA=[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]],GA=bt("outline","help","Help",HA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const VA=[["path",{d:"M5.636 5.636a9 9 0 1 0 12.728 12.728a9 9 0 0 0 -12.728 -12.728z",key:"svg-0"}],["path",{d:"M16.243 7.757a6 6 0 0 0 -8.486 0",key:"svg-1"}]],qA=bt("outline","inner-shadow-top","InnerShadowTop",VA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const YA=[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]],$A=bt("outline","logout","Logout",YA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const IA=[["path",{d:"M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z",key:"svg-0"}],["path",{d:"M3 7l9 6l9 -6",key:"svg-1"}]],XA=bt("outline","mail","Mail",IA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const KA=[["path",{d:"M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3",key:"svg-0"}],["path",{d:"M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]],QA=bt("outline","notification","Notification",KA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZA=[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]],FA=bt("outline","search","Search",ZA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const WA=[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]],JA=bt("outline","settings","Settings",WA);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const eM=[["path",{d:"M6 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M17 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-1"}],["path",{d:"M17 17h-11v-14h-2",key:"svg-2"}],["path",{d:"M6 5l14 1l-1 7h-13",key:"svg-3"}]],tM=bt("outline","shopping-cart","ShoppingCart",eM);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const nM=[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]],aM=bt("outline","user-circle","UserCircle",nM);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const rM=[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]],oM=bt("outline","users","Users",rM);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const lM=[["path",{d:"M4.929 4.929a10 10 0 1 1 14.141 14.141a10 10 0 0 1 -14.14 -14.14zm8.071 4.071a1 1 0 1 0 -2 0v2h-2a1 1 0 1 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0 -2h-2v-2z",key:"svg-0"}]],iM=bt("filled","circle-plus-filled","CirclePlusFilled",lM);function sM({items:n}){return _.jsx(T0,{children:_.jsxs(O0,{className:"flex flex-col gap-2",children:[_.jsx(vl,{children:_.jsxs(gl,{className:"flex items-center gap-2",children:[_.jsxs(yl,{tooltip:"Quick Create",className:"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear",children:[_.jsx(iM,{}),_.jsx("span",{children:"Quick Create"})]}),_.jsxs(py,{size:"icon",className:"size-8 group-data-[collapsible=icon]:opacity-0",variant:"outline",children:[_.jsx(XA,{}),_.jsx("span",{className:"sr-only",children:"Inbox"})]})]})}),_.jsx(vl,{children:n.map(l=>_.jsx(gl,{children:_.jsx(yl,{asChild:!0,tooltip:l.title,children:_.jsxs(Ss,{to:l.url,children:[l.icon&&_.jsx(l.icon,{}),_.jsx("span",{children:l.title})]})})},l.title))})]})})}function cM({items:n,...l}){return _.jsx(T0,{...l,children:_.jsx(O0,{children:_.jsx(vl,{children:n.map(o=>_.jsx(gl,{children:_.jsx(yl,{asChild:!0,children:_.jsxs("a",{href:o.url,children:[_.jsx(o.icon,{}),_.jsx("span",{children:o.title})]})})},o.title))})})})}var uM=Vg();function fM(){return uM.useSyncExternalStore(dM,()=>!0,()=>!1)}function dM(){return()=>{}}var xd="Avatar",[hM,LO]=xa(xd),[mM,D0]=hM(xd),N0=S.forwardRef((n,l)=>{const{__scopeAvatar:o,...i}=n,[c,f]=S.useState("idle");return _.jsx(mM,{scope:o,imageLoadingStatus:c,onImageLoadingStatusChange:f,children:_.jsx(Ve.span,{...i,ref:l})})});N0.displayName=xd;var L0="AvatarImage",j0=S.forwardRef((n,l)=>{const{__scopeAvatar:o,src:i,onLoadingStatusChange:c=()=>{},...f}=n,d=D0(L0,o),h=pM(i,f),m=bn(p=>{c(p),d.onImageLoadingStatusChange(p)});return an(()=>{h!=="idle"&&m(h)},[h,m]),h==="loaded"?_.jsx(Ve.img,{...f,ref:l,src:i}):null});j0.displayName=L0;var z0="AvatarFallback",U0=S.forwardRef((n,l)=>{const{__scopeAvatar:o,delayMs:i,...c}=n,f=D0(z0,o),[d,h]=S.useState(i===void 0);return S.useEffect(()=>{if(i!==void 0){const m=window.setTimeout(()=>h(!0),i);return()=>window.clearTimeout(m)}},[i]),d&&f.imageLoadingStatus!=="loaded"?_.jsx(Ve.span,{...c,ref:l}):null});U0.displayName=z0;function Ag(n,l){return n?l?(n.src!==l&&(n.src=l),n.complete&&n.naturalWidth>0?"loaded":"loading"):"error":"idle"}function pM(n,{referrerPolicy:l,crossOrigin:o}){const i=fM(),c=S.useRef(null),f=i?(c.current||(c.current=new window.Image),c.current):null,[d,h]=S.useState(()=>Ag(f,n));return an(()=>{h(Ag(f,n))},[f,n]),an(()=>{const m=g=>()=>{h(g)};if(!f)return;const p=m("loaded"),v=m("error");return f.addEventListener("load",p),f.addEventListener("error",v),l&&(f.referrerPolicy=l),typeof o=="string"&&(f.crossOrigin=o),()=>{f.removeEventListener("load",p),f.removeEventListener("error",v)}},[f,o,l]),d}var vM=N0,gM=j0,yM=U0;function Mg({className:n,...l}){return _.jsx(vM,{"data-slot":"avatar",className:je("relative flex size-8 shrink-0 overflow-hidden rounded-full",n),...l})}function Tg({className:n,...l}){return _.jsx(gM,{"data-slot":"avatar-image",className:je("aspect-square size-full",n),...l})}function Og({className:n,...l}){return _.jsx(yM,{"data-slot":"avatar-fallback",className:je("bg-muted flex size-full items-center justify-center rounded-full",n),...l})}function k0(n){const l=n+"CollectionProvider",[o,i]=xa(l),[c,f]=o(l,{collectionRef:{current:null},itemMap:new Map}),d=E=>{const{scope:C,children:T}=E,z=Ut.useRef(null),L=Ut.useRef(new Map).current;return _.jsx(c,{scope:C,itemMap:L,collectionRef:z,children:T})};d.displayName=l;const h=n+"CollectionSlot",m=Zr(h),p=Ut.forwardRef((E,C)=>{const{scope:T,children:z}=E,L=f(h,T),N=et(C,L.collectionRef);return _.jsx(m,{ref:N,children:z})});p.displayName=h;const v=n+"CollectionItemSlot",g="data-radix-collection-item",b=Zr(v),w=Ut.forwardRef((E,C)=>{const{scope:T,children:z,...L}=E,N=Ut.useRef(null),G=et(C,N),W=f(v,T);return Ut.useEffect(()=>(W.itemMap.set(N,{ref:N,...L}),()=>void W.itemMap.delete(N))),_.jsx(b,{[g]:"",ref:G,children:z})});w.displayName=v;function R(E){const C=f(n+"CollectionConsumer",E);return Ut.useCallback(()=>{const z=C.collectionRef.current;if(!z)return[];const L=Array.from(z.querySelectorAll(`[${g}]`));return Array.from(C.itemMap.values()).sort((W,V)=>L.indexOf(W.ref.current)-L.indexOf(V.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:d,Slot:p,ItemSlot:w},R,i]}var bM=S.createContext(void 0);function B0(n){const l=S.useContext(bM);return n||l||"ltr"}var Cf="rovingFocusGroup.onEntryFocus",SM={bubbles:!1,cancelable:!0},El="RovingFocusGroup",[If,P0,xM]=k0(El),[_M,H0]=xa(El,[xM]),[wM,EM]=_M(El),G0=S.forwardRef((n,l)=>_.jsx(If.Provider,{scope:n.__scopeRovingFocusGroup,children:_.jsx(If.Slot,{scope:n.__scopeRovingFocusGroup,children:_.jsx(RM,{...n,ref:l})})}));G0.displayName=El;var RM=S.forwardRef((n,l)=>{const{__scopeRovingFocusGroup:o,orientation:i,loop:c=!1,dir:f,currentTabStopId:d,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:v=!1,...g}=n,b=S.useRef(null),w=et(l,b),R=B0(f),[E,C]=ws({prop:d,defaultProp:h??null,onChange:m,caller:El}),[T,z]=S.useState(!1),L=bn(p),N=P0(o),G=S.useRef(!1),[W,V]=S.useState(0);return S.useEffect(()=>{const q=b.current;if(q)return q.addEventListener(Cf,L),()=>q.removeEventListener(Cf,L)},[L]),_.jsx(wM,{scope:o,orientation:i,dir:R,loop:c,currentTabStopId:E,onItemFocus:S.useCallback(q=>C(q),[C]),onItemShiftTab:S.useCallback(()=>z(!0),[]),onFocusableItemAdd:S.useCallback(()=>V(q=>q+1),[]),onFocusableItemRemove:S.useCallback(()=>V(q=>q-1),[]),children:_.jsx(Ve.div,{tabIndex:T||W===0?-1:0,"data-orientation":i,...g,ref:w,style:{outline:"none",...n.style},onMouseDown:be(n.onMouseDown,()=>{G.current=!0}),onFocus:be(n.onFocus,q=>{const ae=!G.current;if(q.target===q.currentTarget&&ae&&!T){const Z=new CustomEvent(Cf,SM);if(q.currentTarget.dispatchEvent(Z),!Z.defaultPrevented){const re=N().filter(O=>O.focusable),te=re.find(O=>O.active),le=re.find(O=>O.id===E),de=[te,le,...re].filter(Boolean).map(O=>O.ref.current);Y0(de,v)}}G.current=!1}),onBlur:be(n.onBlur,()=>z(!1))})})}),V0="RovingFocusGroupItem",q0=S.forwardRef((n,l)=>{const{__scopeRovingFocusGroup:o,focusable:i=!0,active:c=!1,tabStopId:f,children:d,...h}=n,m=Ya(),p=f||m,v=EM(V0,o),g=v.currentTabStopId===p,b=P0(o),{onFocusableItemAdd:w,onFocusableItemRemove:R,currentTabStopId:E}=v;return S.useEffect(()=>{if(i)return w(),()=>R()},[i,w,R]),_.jsx(If.ItemSlot,{scope:o,id:p,focusable:i,active:c,children:_.jsx(Ve.span,{tabIndex:g?0:-1,"data-orientation":v.orientation,...h,ref:l,onMouseDown:be(n.onMouseDown,C=>{i?v.onItemFocus(p):C.preventDefault()}),onFocus:be(n.onFocus,()=>v.onItemFocus(p)),onKeyDown:be(n.onKeyDown,C=>{if(C.key==="Tab"&&C.shiftKey){v.onItemShiftTab();return}if(C.target!==C.currentTarget)return;const T=MM(C,v.orientation,v.dir);if(T!==void 0){if(C.metaKey||C.ctrlKey||C.altKey||C.shiftKey)return;C.preventDefault();let L=b().filter(N=>N.focusable).map(N=>N.ref.current);if(T==="last")L.reverse();else if(T==="prev"||T==="next"){T==="prev"&&L.reverse();const N=L.indexOf(C.currentTarget);L=v.loop?TM(L,N+1):L.slice(N+1)}setTimeout(()=>Y0(L))}}),children:typeof d=="function"?d({isCurrentTabStop:g,hasTabStop:E!=null}):d})})});q0.displayName=V0;var CM={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function AM(n,l){return l!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function MM(n,l,o){const i=AM(n.key,o);if(!(l==="vertical"&&["ArrowLeft","ArrowRight"].includes(i))&&!(l==="horizontal"&&["ArrowUp","ArrowDown"].includes(i)))return CM[i]}function Y0(n,l=!1){const o=document.activeElement;for(const i of n)if(i===o||(i.focus({preventScroll:l}),document.activeElement!==o))return}function TM(n,l){return n.map((o,i)=>n[(l+i)%n.length])}var OM=G0,DM=q0,Xf=["Enter"," "],NM=["ArrowDown","PageUp","Home"],$0=["ArrowUp","PageDown","End"],LM=[...NM,...$0],jM={ltr:[...Xf,"ArrowRight"],rtl:[...Xf,"ArrowLeft"]},zM={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Rl="Menu",[bl,UM,kM]=k0(Rl),[Qa,I0]=xa(Rl,[kM,Ds,H0]),zs=Ds(),X0=H0(),[BM,Za]=Qa(Rl),[PM,Cl]=Qa(Rl),K0=n=>{const{__scopeMenu:l,open:o=!1,children:i,dir:c,onOpenChange:f,modal:d=!0}=n,h=zs(l),[m,p]=S.useState(null),v=S.useRef(!1),g=bn(f),b=B0(c);return S.useEffect(()=>{const w=()=>{v.current=!0,document.addEventListener("pointerdown",R,{capture:!0,once:!0}),document.addEventListener("pointermove",R,{capture:!0,once:!0})},R=()=>v.current=!1;return document.addEventListener("keydown",w,{capture:!0}),()=>{document.removeEventListener("keydown",w,{capture:!0}),document.removeEventListener("pointerdown",R,{capture:!0}),document.removeEventListener("pointermove",R,{capture:!0})}},[]),_.jsx(h0,{...h,children:_.jsx(BM,{scope:l,open:o,onOpenChange:g,content:m,onContentChange:p,children:_.jsx(PM,{scope:l,onClose:S.useCallback(()=>g(!1),[g]),isUsingKeyboardRef:v,dir:b,modal:d,children:i})})})};K0.displayName=Rl;var HM="MenuAnchor",_d=S.forwardRef((n,l)=>{const{__scopeMenu:o,...i}=n,c=zs(o);return _.jsx(m0,{...c,...i,ref:l})});_d.displayName=HM;var wd="MenuPortal",[GM,Q0]=Qa(wd,{forceMount:void 0}),Z0=n=>{const{__scopeMenu:l,forceMount:o,children:i,container:c}=n,f=Za(wd,l);return _.jsx(GM,{scope:l,forceMount:o,children:_.jsx(xn,{present:o||f.open,children:_.jsx(Rs,{asChild:!0,container:c,children:i})})})};Z0.displayName=wd;var Wt="MenuContent",[VM,Ed]=Qa(Wt),F0=S.forwardRef((n,l)=>{const o=Q0(Wt,n.__scopeMenu),{forceMount:i=o.forceMount,...c}=n,f=Za(Wt,n.__scopeMenu),d=Cl(Wt,n.__scopeMenu);return _.jsx(bl.Provider,{scope:n.__scopeMenu,children:_.jsx(xn,{present:i||f.open,children:_.jsx(bl.Slot,{scope:n.__scopeMenu,children:d.modal?_.jsx(qM,{...c,ref:l}):_.jsx(YM,{...c,ref:l})})})})}),qM=S.forwardRef((n,l)=>{const o=Za(Wt,n.__scopeMenu),i=S.useRef(null),c=et(l,i);return S.useEffect(()=>{const f=i.current;if(f)return Ty(f)},[]),_.jsx(Rd,{...n,ref:c,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:be(n.onFocusOutside,f=>f.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})}),YM=S.forwardRef((n,l)=>{const o=Za(Wt,n.__scopeMenu);return _.jsx(Rd,{...n,ref:l,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>o.onOpenChange(!1)})}),$M=Zr("MenuContent.ScrollLock"),Rd=S.forwardRef((n,l)=>{const{__scopeMenu:o,loop:i=!1,trapFocus:c,onOpenAutoFocus:f,onCloseAutoFocus:d,disableOutsidePointerEvents:h,onEntryFocus:m,onEscapeKeyDown:p,onPointerDownOutside:v,onFocusOutside:g,onInteractOutside:b,onDismiss:w,disableOutsideScroll:R,...E}=n,C=Za(Wt,o),T=Cl(Wt,o),z=zs(o),L=X0(o),N=UM(o),[G,W]=S.useState(null),V=S.useRef(null),q=et(l,V,C.onContentChange),ae=S.useRef(0),Z=S.useRef(""),re=S.useRef(0),te=S.useRef(null),le=S.useRef("right"),ue=S.useRef(0),de=R?ld:S.Fragment,O=R?{as:$M,allowPinchZoom:!0}:void 0,$=Q=>{var fe,me;const A=Z.current+Q,Y=N().filter(ge=>!ge.disabled),J=document.activeElement,F=(fe=Y.find(ge=>ge.ref.current===J))==null?void 0:fe.textValue,ee=Y.map(ge=>ge.textValue),oe=aT(ee,A,F),ne=(me=Y.find(ge=>ge.textValue===oe))==null?void 0:me.ref.current;(function ge(Ce){Z.current=Ce,window.clearTimeout(ae.current),Ce!==""&&(ae.current=window.setTimeout(()=>ge(""),1e3))})(A),ne&&setTimeout(()=>ne.focus())};S.useEffect(()=>()=>window.clearTimeout(ae.current),[]),Sy();const U=S.useCallback(Q=>{var Y,J;return le.current===((Y=te.current)==null?void 0:Y.side)&&oT(Q,(J=te.current)==null?void 0:J.area)},[]);return _.jsx(VM,{scope:o,searchRef:Z,onItemEnter:S.useCallback(Q=>{U(Q)&&Q.preventDefault()},[U]),onItemLeave:S.useCallback(Q=>{var A;U(Q)||((A=V.current)==null||A.focus(),W(null))},[U]),onTriggerLeave:S.useCallback(Q=>{U(Q)&&Q.preventDefault()},[U]),pointerGraceTimerRef:re,onPointerGraceIntentChange:S.useCallback(Q=>{te.current=Q},[]),children:_.jsx(de,{...O,children:_.jsx(od,{asChild:!0,trapped:c,onMountAutoFocus:be(f,Q=>{var A;Q.preventDefault(),(A=V.current)==null||A.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:_.jsx(Es,{asChild:!0,disableOutsidePointerEvents:h,onEscapeKeyDown:p,onPointerDownOutside:v,onFocusOutside:g,onInteractOutside:b,onDismiss:w,children:_.jsx(OM,{asChild:!0,...L,dir:T.dir,orientation:"vertical",loop:i,currentTabStopId:G,onCurrentTabStopIdChange:W,onEntryFocus:be(m,Q=>{T.isUsingKeyboardRef.current||Q.preventDefault()}),preventScrollOnEntryFocus:!0,children:_.jsx(p0,{role:"menu","aria-orientation":"vertical","data-state":hb(C.open),"data-radix-menu-content":"",dir:T.dir,...z,...E,ref:q,style:{outline:"none",...E.style},onKeyDown:be(E.onKeyDown,Q=>{const Y=Q.target.closest("[data-radix-menu-content]")===Q.currentTarget,J=Q.ctrlKey||Q.altKey||Q.metaKey,F=Q.key.length===1;Y&&(Q.key==="Tab"&&Q.preventDefault(),!J&&F&&$(Q.key));const ee=V.current;if(Q.target!==ee||!LM.includes(Q.key))return;Q.preventDefault();const ne=N().filter(fe=>!fe.disabled).map(fe=>fe.ref.current);$0.includes(Q.key)&&ne.reverse(),tT(ne)}),onBlur:be(n.onBlur,Q=>{Q.currentTarget.contains(Q.target)||(window.clearTimeout(ae.current),Z.current="")}),onPointerMove:be(n.onPointerMove,Sl(Q=>{const A=Q.target,Y=ue.current!==Q.clientX;if(Q.currentTarget.contains(A)&&Y){const J=Q.clientX>ue.current?"right":"left";le.current=J,ue.current=Q.clientX}}))})})})})})})});F0.displayName=Wt;var IM="MenuGroup",Cd=S.forwardRef((n,l)=>{const{__scopeMenu:o,...i}=n;return _.jsx(Ve.div,{role:"group",...i,ref:l})});Cd.displayName=IM;var XM="MenuLabel",W0=S.forwardRef((n,l)=>{const{__scopeMenu:o,...i}=n;return _.jsx(Ve.div,{...i,ref:l})});W0.displayName=XM;var ms="MenuItem",Dg="menu.itemSelect",Us=S.forwardRef((n,l)=>{const{disabled:o=!1,onSelect:i,...c}=n,f=S.useRef(null),d=Cl(ms,n.__scopeMenu),h=Ed(ms,n.__scopeMenu),m=et(l,f),p=S.useRef(!1),v=()=>{const g=f.current;if(!o&&g){const b=new CustomEvent(Dg,{bubbles:!0,cancelable:!0});g.addEventListener(Dg,w=>i==null?void 0:i(w),{once:!0}),vy(g,b),b.defaultPrevented?p.current=!1:d.onClose()}};return _.jsx(J0,{...c,ref:m,disabled:o,onClick:be(n.onClick,v),onPointerDown:g=>{var b;(b=n.onPointerDown)==null||b.call(n,g),p.current=!0},onPointerUp:be(n.onPointerUp,g=>{var b;p.current||(b=g.currentTarget)==null||b.click()}),onKeyDown:be(n.onKeyDown,g=>{const b=h.searchRef.current!=="";o||b&&g.key===" "||Xf.includes(g.key)&&(g.currentTarget.click(),g.preventDefault())})})});Us.displayName=ms;var J0=S.forwardRef((n,l)=>{const{__scopeMenu:o,disabled:i=!1,textValue:c,...f}=n,d=Ed(ms,o),h=X0(o),m=S.useRef(null),p=et(l,m),[v,g]=S.useState(!1),[b,w]=S.useState("");return S.useEffect(()=>{const R=m.current;R&&w((R.textContent??"").trim())},[f.children]),_.jsx(bl.ItemSlot,{scope:o,disabled:i,textValue:c??b,children:_.jsx(DM,{asChild:!0,...h,focusable:!i,children:_.jsx(Ve.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...f,ref:p,onPointerMove:be(n.onPointerMove,Sl(R=>{i?d.onItemLeave(R):(d.onItemEnter(R),R.defaultPrevented||R.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:be(n.onPointerLeave,Sl(R=>d.onItemLeave(R))),onFocus:be(n.onFocus,()=>g(!0)),onBlur:be(n.onBlur,()=>g(!1))})})})}),KM="MenuCheckboxItem",eb=S.forwardRef((n,l)=>{const{checked:o=!1,onCheckedChange:i,...c}=n;return _.jsx(ob,{scope:n.__scopeMenu,checked:o,children:_.jsx(Us,{role:"menuitemcheckbox","aria-checked":ps(o)?"mixed":o,...c,ref:l,"data-state":Md(o),onSelect:be(c.onSelect,()=>i==null?void 0:i(ps(o)?!0:!o),{checkForDefaultPrevented:!1})})})});eb.displayName=KM;var tb="MenuRadioGroup",[QM,ZM]=Qa(tb,{value:void 0,onValueChange:()=>{}}),nb=S.forwardRef((n,l)=>{const{value:o,onValueChange:i,...c}=n,f=bn(i);return _.jsx(QM,{scope:n.__scopeMenu,value:o,onValueChange:f,children:_.jsx(Cd,{...c,ref:l})})});nb.displayName=tb;var ab="MenuRadioItem",rb=S.forwardRef((n,l)=>{const{value:o,...i}=n,c=ZM(ab,n.__scopeMenu),f=o===c.value;return _.jsx(ob,{scope:n.__scopeMenu,checked:f,children:_.jsx(Us,{role:"menuitemradio","aria-checked":f,...i,ref:l,"data-state":Md(f),onSelect:be(i.onSelect,()=>{var d;return(d=c.onValueChange)==null?void 0:d.call(c,o)},{checkForDefaultPrevented:!1})})})});rb.displayName=ab;var Ad="MenuItemIndicator",[ob,FM]=Qa(Ad,{checked:!1}),lb=S.forwardRef((n,l)=>{const{__scopeMenu:o,forceMount:i,...c}=n,f=FM(Ad,o);return _.jsx(xn,{present:i||ps(f.checked)||f.checked===!0,children:_.jsx(Ve.span,{...c,ref:l,"data-state":Md(f.checked)})})});lb.displayName=Ad;var WM="MenuSeparator",ib=S.forwardRef((n,l)=>{const{__scopeMenu:o,...i}=n;return _.jsx(Ve.div,{role:"separator","aria-orientation":"horizontal",...i,ref:l})});ib.displayName=WM;var JM="MenuArrow",sb=S.forwardRef((n,l)=>{const{__scopeMenu:o,...i}=n,c=zs(o);return _.jsx(v0,{...c,...i,ref:l})});sb.displayName=JM;var eT="MenuSub",[jO,cb]=Qa(eT),ul="MenuSubTrigger",ub=S.forwardRef((n,l)=>{const o=Za(ul,n.__scopeMenu),i=Cl(ul,n.__scopeMenu),c=cb(ul,n.__scopeMenu),f=Ed(ul,n.__scopeMenu),d=S.useRef(null),{pointerGraceTimerRef:h,onPointerGraceIntentChange:m}=f,p={__scopeMenu:n.__scopeMenu},v=S.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return S.useEffect(()=>v,[v]),S.useEffect(()=>{const g=h.current;return()=>{window.clearTimeout(g),m(null)}},[h,m]),_.jsx(_d,{asChild:!0,...p,children:_.jsx(J0,{id:c.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":c.contentId,"data-state":hb(o.open),...n,ref:_s(l,c.onTriggerChange),onClick:g=>{var b;(b=n.onClick)==null||b.call(n,g),!(n.disabled||g.defaultPrevented)&&(g.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:be(n.onPointerMove,Sl(g=>{f.onItemEnter(g),!g.defaultPrevented&&!n.disabled&&!o.open&&!d.current&&(f.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{o.onOpenChange(!0),v()},100))})),onPointerLeave:be(n.onPointerLeave,Sl(g=>{var w,R;v();const b=(w=o.content)==null?void 0:w.getBoundingClientRect();if(b){const E=(R=o.content)==null?void 0:R.dataset.side,C=E==="right",T=C?-5:5,z=b[C?"left":"right"],L=b[C?"right":"left"];f.onPointerGraceIntentChange({area:[{x:g.clientX+T,y:g.clientY},{x:z,y:b.top},{x:L,y:b.top},{x:L,y:b.bottom},{x:z,y:b.bottom}],side:E}),window.clearTimeout(h.current),h.current=window.setTimeout(()=>f.onPointerGraceIntentChange(null),300)}else{if(f.onTriggerLeave(g),g.defaultPrevented)return;f.onPointerGraceIntentChange(null)}})),onKeyDown:be(n.onKeyDown,g=>{var w;const b=f.searchRef.current!=="";n.disabled||b&&g.key===" "||jM[i.dir].includes(g.key)&&(o.onOpenChange(!0),(w=o.content)==null||w.focus(),g.preventDefault())})})})});ub.displayName=ul;var fb="MenuSubContent",db=S.forwardRef((n,l)=>{const o=Q0(Wt,n.__scopeMenu),{forceMount:i=o.forceMount,...c}=n,f=Za(Wt,n.__scopeMenu),d=Cl(Wt,n.__scopeMenu),h=cb(fb,n.__scopeMenu),m=S.useRef(null),p=et(l,m);return _.jsx(bl.Provider,{scope:n.__scopeMenu,children:_.jsx(xn,{present:i||f.open,children:_.jsx(bl.Slot,{scope:n.__scopeMenu,children:_.jsx(Rd,{id:h.contentId,"aria-labelledby":h.triggerId,...c,ref:p,align:"start",side:d.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:v=>{var g;d.isUsingKeyboardRef.current&&((g=m.current)==null||g.focus()),v.preventDefault()},onCloseAutoFocus:v=>v.preventDefault(),onFocusOutside:be(n.onFocusOutside,v=>{v.target!==h.trigger&&f.onOpenChange(!1)}),onEscapeKeyDown:be(n.onEscapeKeyDown,v=>{d.onClose(),v.preventDefault()}),onKeyDown:be(n.onKeyDown,v=>{var w;const g=v.currentTarget.contains(v.target),b=zM[d.dir].includes(v.key);g&&b&&(f.onOpenChange(!1),(w=h.trigger)==null||w.focus(),v.preventDefault())})})})})})});db.displayName=fb;function hb(n){return n?"open":"closed"}function ps(n){return n==="indeterminate"}function Md(n){return ps(n)?"indeterminate":n?"checked":"unchecked"}function tT(n){const l=document.activeElement;for(const o of n)if(o===l||(o.focus(),document.activeElement!==l))return}function nT(n,l){return n.map((o,i)=>n[(l+i)%n.length])}function aT(n,l,o){const c=l.length>1&&Array.from(l).every(p=>p===l[0])?l[0]:l,f=o?n.indexOf(o):-1;let d=nT(n,Math.max(f,0));c.length===1&&(d=d.filter(p=>p!==o));const m=d.find(p=>p.toLowerCase().startsWith(c.toLowerCase()));return m!==o?m:void 0}function rT(n,l){const{x:o,y:i}=n;let c=!1;for(let f=0,d=l.length-1;f<l.length;d=f++){const h=l[f],m=l[d],p=h.x,v=h.y,g=m.x,b=m.y;v>i!=b>i&&o<(g-p)*(i-v)/(b-v)+p&&(c=!c)}return c}function oT(n,l){if(!l)return!1;const o={x:n.clientX,y:n.clientY};return rT(o,l)}function Sl(n){return l=>l.pointerType==="mouse"?n(l):void 0}var lT=K0,iT=_d,sT=Z0,cT=F0,uT=Cd,fT=W0,dT=Us,hT=eb,mT=nb,pT=rb,vT=lb,gT=ib,yT=sb,bT=ub,ST=db,ks="DropdownMenu",[xT,zO]=xa(ks,[I0]),St=I0(),[_T,mb]=xT(ks),pb=n=>{const{__scopeDropdownMenu:l,children:o,dir:i,open:c,defaultOpen:f,onOpenChange:d,modal:h=!0}=n,m=St(l),p=S.useRef(null),[v,g]=ws({prop:c,defaultProp:f??!1,onChange:d,caller:ks});return _.jsx(_T,{scope:l,triggerId:Ya(),triggerRef:p,contentId:Ya(),open:v,onOpenChange:g,onOpenToggle:S.useCallback(()=>g(b=>!b),[g]),modal:h,children:_.jsx(lT,{...m,open:v,onOpenChange:g,dir:i,modal:h,children:o})})};pb.displayName=ks;var vb="DropdownMenuTrigger",gb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,disabled:i=!1,...c}=n,f=mb(vb,o),d=St(o);return _.jsx(iT,{asChild:!0,...d,children:_.jsx(Ve.button,{type:"button",id:f.triggerId,"aria-haspopup":"menu","aria-expanded":f.open,"aria-controls":f.open?f.contentId:void 0,"data-state":f.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...c,ref:_s(l,f.triggerRef),onPointerDown:be(n.onPointerDown,h=>{!i&&h.button===0&&h.ctrlKey===!1&&(f.onOpenToggle(),f.open||h.preventDefault())}),onKeyDown:be(n.onKeyDown,h=>{i||(["Enter"," "].includes(h.key)&&f.onOpenToggle(),h.key==="ArrowDown"&&f.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(h.key)&&h.preventDefault())})})})});gb.displayName=vb;var wT="DropdownMenuPortal",yb=n=>{const{__scopeDropdownMenu:l,...o}=n,i=St(l);return _.jsx(sT,{...i,...o})};yb.displayName=wT;var bb="DropdownMenuContent",Sb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=mb(bb,o),f=St(o),d=S.useRef(!1);return _.jsx(cT,{id:c.contentId,"aria-labelledby":c.triggerId,...f,...i,ref:l,onCloseAutoFocus:be(n.onCloseAutoFocus,h=>{var m;d.current||(m=c.triggerRef.current)==null||m.focus(),d.current=!1,h.preventDefault()}),onInteractOutside:be(n.onInteractOutside,h=>{const m=h.detail.originalEvent,p=m.button===0&&m.ctrlKey===!0,v=m.button===2||p;(!c.modal||v)&&(d.current=!0)}),style:{...n.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Sb.displayName=bb;var ET="DropdownMenuGroup",xb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(uT,{...c,...i,ref:l})});xb.displayName=ET;var RT="DropdownMenuLabel",_b=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(fT,{...c,...i,ref:l})});_b.displayName=RT;var CT="DropdownMenuItem",wb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(dT,{...c,...i,ref:l})});wb.displayName=CT;var AT="DropdownMenuCheckboxItem",Eb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(hT,{...c,...i,ref:l})});Eb.displayName=AT;var MT="DropdownMenuRadioGroup",TT=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(mT,{...c,...i,ref:l})});TT.displayName=MT;var OT="DropdownMenuRadioItem",DT=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(pT,{...c,...i,ref:l})});DT.displayName=OT;var NT="DropdownMenuItemIndicator",Rb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(vT,{...c,...i,ref:l})});Rb.displayName=NT;var LT="DropdownMenuSeparator",Cb=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(gT,{...c,...i,ref:l})});Cb.displayName=LT;var jT="DropdownMenuArrow",zT=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(yT,{...c,...i,ref:l})});zT.displayName=jT;var UT="DropdownMenuSubTrigger",kT=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(bT,{...c,...i,ref:l})});kT.displayName=UT;var BT="DropdownMenuSubContent",PT=S.forwardRef((n,l)=>{const{__scopeDropdownMenu:o,...i}=n,c=St(o);return _.jsx(ST,{...c,...i,ref:l,style:{...n.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});PT.displayName=BT;var HT=pb,GT=gb,VT=yb,qT=Sb,YT=xb,$T=_b,IT=wb,XT=Eb,KT=Rb,QT=Cb;function ZT({...n}){return _.jsx(HT,{"data-slot":"dropdown-menu",...n})}function FT({...n}){return _.jsx(GT,{"data-slot":"dropdown-menu-trigger",...n})}function WT({className:n,sideOffset:l=4,...o}){return _.jsx(VT,{children:_.jsx(qT,{"data-slot":"dropdown-menu-content",sideOffset:l,className:je("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-end-2 data-[side=right]:slide-in-from-start-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",n),...o})})}function JT({...n}){return _.jsx(YT,{"data-slot":"dropdown-menu-group",...n})}function Fi({className:n,inset:l,variant:o="default",...i}){return _.jsx(IT,{"data-slot":"dropdown-menu-item","data-inset":l,"data-variant":o,className:je("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:ps-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...i})}function UO({className:n,children:l,checked:o,...i}){return _.jsxs(XT,{"data-slot":"dropdown-menu-checkbox-item",className:je("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pe-2 ps-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),checked:o,...i,children:[_.jsx("span",{className:"pointer-events-none absolute start-2 flex size-3.5 items-center justify-center",children:_.jsx(KT,{children:_.jsx(rw,{className:"size-4"})})}),l]})}function e2({className:n,inset:l,...o}){return _.jsx($T,{"data-slot":"dropdown-menu-label","data-inset":l,className:je("px-2 py-1.5 text-sm font-medium data-[inset]:ps-8",n),...o})}function Ng({className:n,...l}){return _.jsx(QT,{"data-slot":"dropdown-menu-separator",className:je("bg-border -mx-1 my-1 h-px",n),...l})}function t2({user:n,onLogout:l}){const{isMobile:o}=js();return _.jsx(vl,{children:_.jsx(gl,{children:_.jsxs(ZT,{children:[_.jsx(FT,{asChild:!0,children:_.jsxs(yl,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[_.jsxs(Mg,{className:"h-8 w-8 rounded-lg grayscale",children:[_.jsx(Tg,{src:n.avatar,alt:n.name}),_.jsx(Og,{className:"rounded-lg",children:"CN"})]}),_.jsxs("div",{className:"grid flex-1 text-start text-sm leading-tight",children:[_.jsx("span",{className:"truncate font-medium",children:n.name}),_.jsx("span",{className:"text-muted-foreground truncate text-xs",children:n.email})]}),_.jsx(PA,{className:"ms-auto size-4"})]})}),_.jsxs(WT,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:o?"bottom":"right",align:"end",sideOffset:4,children:[_.jsx(e2,{className:"p-0 font-normal",children:_.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-start text-sm",children:[_.jsxs(Mg,{className:"h-8 w-8 rounded-lg",children:[_.jsx(Tg,{src:n.avatar,alt:n.name}),_.jsx(Og,{className:"rounded-lg",children:"CN"})]}),_.jsxs("div",{className:"grid flex-1 text-start text-sm leading-tight",children:[_.jsx("span",{className:"truncate font-medium",children:n.name}),_.jsx("span",{className:"text-muted-foreground truncate text-xs",children:n.email})]})]})}),_.jsx(Ng,{}),_.jsxs(JT,{children:[_.jsxs(Fi,{children:[_.jsx(aM,{}),"الحساب"]}),_.jsxs(Fi,{children:[_.jsx(zA,{}),"الفواتير"]}),_.jsxs(Fi,{children:[_.jsx(QA,{}),"الإشعارات"]})]}),_.jsx(Ng,{}),_.jsxs(Fi,{onClick:l,children:[_.jsx($A,{}),"تسجيل الخروج"]})]})]})})})}const Lg={navMain:[{title:"الرئيسية",url:"/",icon:kA},{title:"الموظفين",url:"/employees",icon:oM},{title:"الطلبات",url:"/orders",icon:tM},{title:"الشركات",url:"/companies",icon:LA}],navSecondary:[{title:"الإعدادات",url:"/settings",icon:JA},{title:"المساعدة",url:"#",icon:GA},{title:"البحث",url:"#",icon:FA}]};function n2({...n}){const{user:l,logout:o}=Wg(),i=ys(),c={name:l?`${l.first_name} ${l.last_name}`:"Guest",email:(l==null?void 0:l.email)||"<EMAIL>",avatar:"/avatars/default.jpg"};return _.jsxs(RA,{collapsible:"offcanvas",...n,children:[_.jsx(AA,{children:_.jsx(vl,{children:_.jsx(gl,{children:_.jsx(yl,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:_.jsxs(Ss,{to:"/",children:[_.jsx(qA,{className:"!size-5"}),_.jsx("span",{className:"text-base font-semibold",children:"MyRunway"})]})})})})}),_.jsxs(TA,{children:[_.jsx(sM,{items:Lg.navMain}),_.jsx(cM,{items:Lg.navSecondary,className:"mt-auto"})]}),_.jsx(MA,{children:_.jsx(t2,{user:c,onLogout:()=>{o(),i({to:"/login"})}})})]})}function a2(){const n=M_(),{isAuthenticated:l}=Wg();return n.pathname==="/login"||!l?_.jsxs(_.Fragment,{children:[_.jsx(zf,{}),_.jsx(Kv,{})]}):_.jsx(_.Fragment,{children:_.jsxs(EA,{children:[_.jsx(n2,{variant:"inset"}),_.jsxs(CA,{children:[_.jsx(zf,{}),_.jsx(Kv,{})]})]})})}const mt=p_({component:a2}),r2=()=>yt(()=>import("./login-tFLs2Mxw.js"),__vite__mapDeps([0,1,2,3,4,5])),o2=ct("/login")({component:gt(r2,"component")}),l2=()=>yt(()=>import("./dashboard-BroB8hAU.js"),__vite__mapDeps([6,7,8,9,10,11,12,2,3,13,14,15])),i2=ct("/dashboard")({component:gt(l2,"component")}),s2=()=>yt(()=>import("./index-ebqzxrP_.js"),__vite__mapDeps([16,1,2,3,4,5])),c2=ct("/")({component:gt(s2,"component")}),u2=()=>yt(()=>import("./index-CIkfWiSn.js"),__vite__mapDeps([17,8])),f2=ct("/settings/")({component:gt(u2,"component")}),d2=()=>yt(()=>import("./index-DPK73jEt.js"),__vite__mapDeps([18,15,11,19,10,12,9,13,7,2,8,20,21,22])),h2=ct("/orders/")({component:gt(d2,"component")}),m2=()=>yt(()=>import("./index-B7Uq39qR.js"),__vite__mapDeps([23,15,11,19,10,12,9,13,7])),p2=ct("/employees/")({component:gt(m2,"component")}),v2=()=>yt(()=>import("./index--IE_NJJA.js"),__vite__mapDeps([24,15,11,19,10,12,9,13])),g2=ct("/companies/")({component:gt(v2,"component")}),y2=()=>yt(()=>import("./form-XyB-BgSJ.js"),__vite__mapDeps([25,15,11,2,3,26,12,9,4])),b2=ct("/orders/form")({component:gt(y2,"component"),validateSearch:n=>({id:n.id})}),S2=()=>yt(()=>import("./bulk-import-BR-XyOjB.js"),__vite__mapDeps([27,15,11,8,7,5,28,21,9,13,22,14])),x2=ct("/orders/bulk-import")({component:gt(S2,"component")}),_2=()=>yt(()=>import("./assign-CilY3stJ.js"),__vite__mapDeps([29,8,7,12,9,4,2,3,11,20,28,22])),w2=ct("/orders/assign")({component:gt(_2,"component")}),E2=()=>yt(()=>import("./form-C7KAM3Wc.js"),__vite__mapDeps([30,15,11,2,3,9,8,7])),R2=ct("/employees/form")({component:gt(E2,"component"),validateSearch:n=>({id:n.id})}),C2=()=>yt(()=>import("./form-CckWcHWV.js"),__vite__mapDeps([31,15,11,2,3,8,7])),A2=ct("/companies/form")({component:gt(C2,"component"),validateSearch:n=>({id:n.id})}),M2=()=>yt(()=>import("./index-BYvrSxmV.js"),__vite__mapDeps([32,11,2,3,26,12,9,4,19,10,13,7])),T2=ct("/settings/cancellation_templates/")({component:gt(M2,"component")}),O2=()=>yt(()=>import("./index-D3TAvStq.js"),__vite__mapDeps([33,15,11,19,10,12,9,13])),D2=ct("/companies/channels/")({component:gt(O2,"component"),validateSearch:n=>({company_id:n.company_id})}),N2=()=>yt(()=>import("./form-BL770Dfi.js"),__vite__mapDeps([34,15,11,2,3,8,7,26])),L2=ct("/companies/channels/form")({component:gt(N2,"component"),validateSearch:n=>({id:n.id,company_id:n.company_id})}),j2=o2.update({id:"/login",path:"/login",getParentRoute:()=>mt}),z2=i2.update({id:"/dashboard",path:"/dashboard",getParentRoute:()=>mt}),U2=c2.update({id:"/",path:"/",getParentRoute:()=>mt}),k2=f2.update({id:"/settings/",path:"/settings/",getParentRoute:()=>mt}),B2=h2.update({id:"/orders/",path:"/orders/",getParentRoute:()=>mt}),P2=p2.update({id:"/employees/",path:"/employees/",getParentRoute:()=>mt}),H2=g2.update({id:"/companies/",path:"/companies/",getParentRoute:()=>mt}),G2=b2.update({id:"/orders/form",path:"/orders/form",getParentRoute:()=>mt}),V2=x2.update({id:"/orders/bulk-import",path:"/orders/bulk-import",getParentRoute:()=>mt}),q2=w2.update({id:"/orders/assign",path:"/orders/assign",getParentRoute:()=>mt}),Y2=R2.update({id:"/employees/form",path:"/employees/form",getParentRoute:()=>mt}),$2=A2.update({id:"/companies/form",path:"/companies/form",getParentRoute:()=>mt}),I2=T2.update({id:"/settings/cancellation_templates/",path:"/settings/cancellation_templates/",getParentRoute:()=>mt}),X2=D2.update({id:"/companies/channels/",path:"/companies/channels/",getParentRoute:()=>mt}),K2=L2.update({id:"/companies/channels/form",path:"/companies/channels/form",getParentRoute:()=>mt}),Q2={IndexRoute:U2,DashboardRoute:z2,LoginRoute:j2,CompaniesFormRoute:$2,EmployeesFormRoute:Y2,OrdersAssignRoute:q2,OrdersBulkImportRoute:V2,OrdersFormRoute:G2,CompaniesIndexRoute:H2,EmployeesIndexRoute:P2,OrdersIndexRoute:B2,SettingsIndexRoute:k2,CompaniesChannelsFormRoute:K2,CompaniesChannelsIndexRoute:X2,SettingsCancellation_templatesIndexRoute:I2},Z2=mt._addFileChildren(Q2)._addFileTypes();Te.setConfig({baseUrl:"http://localhost:8000"});const F2=E_({routeTree:Z2,context:{},defaultPreload:"intent",scrollRestoration:!0,defaultStructuralSharing:!0,defaultPreloadStaleTime:0}),Af=document.getElementById("app");Af&&!Af.innerHTML&&t1.createRoot(Af).render(_.jsx(S.StrictMode,{children:_.jsx(I_,{children:_.jsx(A_,{router:F2})})}));export{CO as $,PA as A,py as B,yR as C,gR as D,Fi as E,Ng as F,ad as G,lO as H,DM as I,LA as J,oM as K,Ss as L,FA as M,wO as N,mR as O,Ve as P,cO as Q,Ut as R,tO as S,OO as T,RO as U,iO as V,sO as W,aO as X,fO as Y,nd as Z,bO as _,ys as a,AO as a0,EO as a1,uO as a2,Ya as a3,xn as a4,R2 as a5,rO as a6,nO as a7,oO as a8,A2 as a9,Es as aA,p0 as aB,v0 as aC,L2 as aD,gO as aE,pO as aF,yO as aG,Jg as aH,NO as aI,dO as aa,hO as ab,_O as ac,SO as ad,xO as ae,sw as af,D2 as ag,mO as ah,vO as ai,bs as aj,et as ak,BC as al,rw as am,Ds as an,h0 as ao,k0 as ap,m0 as aq,an as ar,Rs as as,$C as at,bn as au,Ty as av,Sy as aw,ld as ax,Zr as ay,od as az,J2 as b,bt as c,ny as d,je as e,ws as f,Kf as g,be as h,Vn as i,_ as j,xa as k,H0 as l,B0 as m,OM as n,ay as o,cw as p,vR as q,S as r,dR as s,pR as t,Wg as u,hR as v,ZT as w,FT as x,WT as y,UO as z};
