from http.client import HTT<PERSON>Exception
from django.db import transaction
from django.http import HttpRequest
from django.core.paginator import Paginator
from ninja import Router
from merchants.models import Office, OfficeEmployee
from .models import Role, User, UserBalanceUpdate, UserPointsUpdate, UserLocationUpdate
from .schemas import (
    CreateUserSchema,
    UpdateUserSchema,
    UserSchema,
    UserWalletSchema,
    UserPointsSchema,
    LoginSchema,
    LoginResponseSchema,
    LocationUpdateSchema,
    LocationHistorySchema,
    UserListResponseSchema,
    LocationHistoryResponseSchema,
    UserOfficeSchema,
)
from core.middleware import <PERSON><PERSON><PERSON><PERSON>, create_jwt_token

router = Router(tags=["accounts"])


# Authentication endpoints
# POST /api/accounts/login
@router.post("/login", response=LoginResponseSchema)
def login(request: HttpRequest, data: LoginSchema):
    user = User.objects.get(phone_number=data.phone_number)
    if user is None:
        raise HTTPException(401, "Invalid credentials")

    if not user.check_password(data.password):
        raise HTTPException(401, "Invalid credentials")

    if not user.is_active:
        raise HTTPException(401, "Account is disabled")

    office = Office.objects.get(id=data.office_id)
    user_offices = OfficeEmployee.objects.filter(user=user).values_list(
        "office_id", flat=True
    )
    if office.id not in user_offices:
        raise HTTPException(401, "Invalid credentials")

    token = create_jwt_token(user, office)
    return LoginResponseSchema(
        token=token.token,
        user=UserSchema.from_orm(user),
        office=UserOfficeSchema.from_orm(office),
    )


# POST /api/accounts/logout
@router.post("/logout", auth=AuthKey)
def logout(request: HttpRequest):
    # In a real implementation, you might want to blacklist the token
    # For now, we'll just return success
    return {"message": "Logged out successfully"}


# GET /api/accounts/me
@router.get("/me", auth=AuthKey)
def get_me(request: HttpRequest) -> UserSchema:
    return UserSchema.from_orm(request.user)


# GET /api/accounts/
# List all users with pagination
@router.get("/", auth=AuthKey, response=UserListResponseSchema)
def list_users(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    role: str = None,
    office_id: str = None,
):
    # Check if user has permission to list users
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HTTPException(403, "You don't have permission to list users")

    # Build queryset based on user role and filters
    if request.user.role == Role.ADMIN:
        users = User.objects.all()
    else:
        # Managers can only see users in their office
        # Get the office where the user is an employee
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        users = User.objects.filter(offices__office_id__in=user_offices)

    # Apply filters
    if role:
        users = users.filter(role=role)
    if office_id:
        users = users.filter(offices__office_id=office_id)

    # Remove duplicates and order by username
    users = users.distinct().order_by("username")

    # Paginate results
    paginator = Paginator(users, page_size)
    page_obj = paginator.get_page(page)

    return UserListResponseSchema(
        users=[UserSchema.from_orm(user) for user in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/accounts/
# Create a new user
@router.post("/", auth=AuthKey, response=UserSchema)
def create_user(request: HttpRequest, data: CreateUserSchema) -> UserSchema:
    office = Office.objects.get(id=data.office_id)

    # Check if user has permission to create users in this office
    # User must be an employee of the office or an admin
    if request.user.role != Role.ADMIN:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office.id not in user_offices:
            raise HTTPException(
                403, "You are not allowed to create a user for this office"
            )

    try:
        with transaction.atomic():
            user = User.objects.create(
                first_name=data.first_name,
                last_name=data.last_name,
                username=data.username,
                email=data.email,
                phone_number=data.phone_number,
                role=data.role,
                commission_fixed_rate=data.commission_fixed_rate,
            )
            user.set_password(data.password)
            user.save()

            OfficeEmployee.objects.create(
                user=user,
                office=office,
            )

            return UserSchema.from_orm(user)
    except Exception as e:
        raise HTTPException(400, str(e))


# GET /api/accounts/{user_id}
# Get a user by id
@router.get("/{user_id}", auth=AuthKey, response=UserSchema)
def get_user(request: HttpRequest, user_id: str) -> UserSchema:
    user = User.objects.get(id=user_id)
    return UserSchema.from_orm(user)


# PUT /api/accounts/{user_id}
# Update a user by id
@router.put("/{user_id}", auth=AuthKey, response=UserSchema)
def update_user(
    request: HttpRequest, user_id: str, data: UpdateUserSchema
) -> UserSchema:
    user = User.objects.get(id=user_id)

    # Check permissions: user can update themselves or admin can update anyone
    if user.id != request.user.id and request.user.role != Role.ADMIN:
        raise HTTPException(403, "You are not allowed to update this user")

    user.first_name = data.first_name
    user.last_name = data.last_name
    user.username = data.username
    user.email = data.email
    user.phone_number = data.phone_number
    user.role = data.role
    user.commission_fixed_rate = data.commission_fixed_rate
    user.current_location_lat = data.current_location_lat
    user.current_location_lng = data.current_location_lng
    user.save()

    return UserSchema.from_orm(user)


# DELETE /api/accounts/{user_id}
# Delete a user by id
@router.delete("/{user_id}", auth=AuthKey, response=str)
def delete_user(request: HttpRequest, user_id: str) -> UserSchema:
    if request.user.role != Role.ADMIN:
        raise HTTPException(403, "You are not allowed to delete this user")

    user = User.objects.get(id=user_id)
    user.delete()
    return "OK"


# Location management endpoints
# POST /api/accounts/me/location
@router.post("/me/location", auth=AuthKey, response=str)
def update_location(request: HttpRequest, data: LocationUpdateSchema):
    with transaction.atomic():
        # Update current location
        request.user.current_location_lat = data.latitude
        request.user.current_location_lng = data.longitude
        request.user.save()

        # Create location history record
        UserLocationUpdate.objects.create(
            user=request.user,
            location_lat=data.latitude,
            location_lng=data.longitude,
        )

    return "OK"


# GET /api/accounts/me/location/history
@router.get(
    "/me/location/history", auth=AuthKey, response=LocationHistoryResponseSchema
)
def get_location_history(request: HttpRequest, page: int = 1, page_size: int = 10):
    locations = UserLocationUpdate.objects.filter(user=request.user).order_by(
        "-update_time"
    )

    paginator = Paginator(locations, page_size)
    page_obj = paginator.get_page(page)

    return LocationHistoryResponseSchema(
        locations=[LocationHistorySchema.from_orm(location) for location in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# GET /api/accounts/me/offices
@router.get("/me/offices", auth=AuthKey, response=UserOfficeSchema)
def get_my_offices(request: HttpRequest):
    """Get all offices where the current user is an employee"""
    offices = OfficeEmployee.objects.filter(user=request.user).select_related("office")
    return {
        "offices": [
            {
                "id": str(office.office.id),
                "name": office.office.name,
                "slug": office.office.slug,
                "address": office.office.address,
                "phone_number": office.office.phone_number,
                "email": office.office.email,
            }
            for office in offices
        ]
    }


# GET /api/accounts/me/wallet
@router.get("/me/wallet", auth=AuthKey, response=UserWalletSchema)
def get_me_wallet(request: HttpRequest) -> UserWalletSchema:
    return UserWalletSchema.from_orm(request.user.wallet)


# PUT /api/accounts/me/wallet
@router.put("/me/wallet", auth=AuthKey, response=UserWalletSchema)
def update_me_wallet(request: HttpRequest, amount: float) -> UserWalletSchema:
    if request.user.role != Role.ADMIN:
        raise HTTPException(403, "You are not allowed to update this user")

    with transaction.atomic():
        # get if it's subtraction or addition
        change = amount - request.user.wallet.balance

        request.user.wallet.balance = amount
        request.user.wallet.save()

        UserBalanceUpdate.objects.create(
            user=request.user,
            balance_change=change,
        )

    return UserWalletSchema.from_orm(request.user.wallet)


# GET /api/accounts/me/points
@router.get("/me/points", auth=AuthKey, response=UserPointsSchema)
def get_me_points(request: HttpRequest) -> UserPointsSchema:
    return UserPointsSchema.from_orm(request.user.points)


# POST /api/accounts/me/points
# Add points to the user's wallet
@router.post("/me/points", auth=AuthKey, response=UserPointsSchema)
def add_points(request: HttpRequest, amount: int) -> UserPointsSchema:
    if request.user.role != Role.ADMIN:
        raise HTTPException(403, "You are not allowed to add points to this user")

    with transaction.atomic():
        change = amount - request.user.points.points

        request.user.points.points = amount
        request.user.points.save()

        UserPointsUpdate.objects.create(
            user=request.user,
            points_change=change,
        )

    return UserPointsSchema.from_orm(request.user.points)
