import{u as m,r,a as p,j as a,Q as h,Y as f}from"./index-DM6GIfB4.js";import{S as x}from"./site-header-BZV9tMVa.js";import{S as u,t}from"./index-B_SQ5DcE.js";import{D as y}from"./DataTableComponent-BhuAndc5.js";import"./sortable.esm-2IxWHftX.js";import"./checkbox-B00BLXKz.js";import"./select-BCFM5Vl0.js";import"./table-CLheJEBl.js";const A=function(){const{selectedOffice:c}=m(),[i,l]=r.useState([]),s=p(),n=async()=>{var e;try{const o=await h({query:{office_id:c.id}});l(((e=o.data)==null?void 0:e.companies)||[])}catch(o){console.error("Error fetching companies:",o),t.error("حدث خطأ أثناء تحميل بيانات الشركات")}};r.useEffect(()=>{n()},[c]);const d=async e=>{if(confirm("هل أنت متأكد من حذف هذه الشركة؟"))try{await f({path:{company_id:e.id}}),t.success("تم حذف الشركة بنجاح"),n()}catch(o){console.error("Error deleting company:",o),t.error("حدث خطأ أثناء حذف الشركة")}};return a.jsxs(a.Fragment,{children:[a.jsx(x,{title:"الشركات"}),a.jsxs("div",{className:"flex flex-col m-4",children:[a.jsx("h1",{className:"text-2xl font-bold",children:"الشركات"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"هنا يمكنك إدارة الشركات"})]}),a.jsx(u,{className:"mb-4"}),a.jsx(y,{idField:"id",pageSize:10,enablePagination:!1,showAddButton:!0,addButtonText:"إضافة شركة",onAddClick:()=>{s({to:"/companies/form",search:{id:void 0}})},data:i,columns:[{header:"رمز الشركة",accessorKey:"code",cell:({row:e})=>a.jsxs("div",{onClick:()=>{s({to:"/companies/channels",search:{company_id:e.id}})},className:"flex items-center gap-2",children:[a.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.original.color_code}}),a.jsx("span",{children:e.original.code})]})},{header:"الاسم",accessorKey:"name",cell:({row:e})=>a.jsx("div",{onClick:()=>{s({to:"/companies/channels",search:{company_id:e.id}})},className:"flex items-center gap-2",children:a.jsx("span",{children:e.original.name})})},{header:"العنوان",accessorKey:"address"},{header:"الهاتف",accessorKey:"phone"}],actionsColumn:{enableEdit:!1,enableDelete:!1,customActions:[{label:"تعديل",onClick:e=>{s({to:"/companies/form",search:{id:e.id}})}},{label:"قنوات",onClick:e=>{s({to:"/companies/channels",search:{company_id:e.id}})}},{label:"حذف",onClick:d,variant:"destructive"}]}})]})};export{A as component};
