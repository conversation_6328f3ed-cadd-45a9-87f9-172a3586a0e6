import{u as H,a as M,Z as Q,r as u,_ as U,Q as Z,$,j as e,B as j,a0 as I,a1 as J,a2 as K}from"./index-DM6GIfB4.js";import{S as W}from"./site-header-BZV9tMVa.js";import{I as i}from"./input-BhcMMszI.js";import{L as l}from"./label-D7aoP4f7.js";import{T as b}from"./textarea-ChEeGdgE.js";import{C as X}from"./checkbox-B00BLXKz.js";import{S as C,a as F,b as S,c as D,d as h}from"./select-BCFM5Vl0.js";import{D as Y,a as ee,b as ae,c as se,d as re,e as le,f as ne}from"./dialog-BrlEMtli.js";import{t as o,S as te}from"./index-B_SQ5DcE.js";const ge=function(){const{selectedOffice:d}=H(),f=M(),{id:p}=Q({from:"/orders/form"}),[n,x]=u.useState(!1),[E,A]=u.useState([]),[L,T]=u.useState([]),[O,y]=u.useState(!1),[t,_]=u.useState({code:"",name:"",address:"",phone:"",color_code:"#3B82F6"}),[v,k]=u.useState(!1),[s,N]=u.useState({code:"",notes:"",total_price:null,customer_name:"",customer_phone:"",customer_address:"",customer_company_id:null,breakable:!1,deadline_date:null,commission_fixed_rate:null,assigned_to_id:null,final_customer_payment:null,handling_status:"PENDING",cancellation_reason_template_id:null,cancellation_reason:null}),m=!!p;u.useEffect(()=>{G(),w(),m&&p&&q()},[p]);const G=async()=>{try{const a=await U({query:{office_id:d==null?void 0:d.id,page_size:100}});a.data&&A(a.data.templates)}catch(a){console.error("Error fetching cancellation templates:",a),o.error("حدث خطأ أثناء تحميل قوالب الإلغاء")}},w=async()=>{try{const a=await Z({query:{office_id:d==null?void 0:d.id,page_size:100}});a.data&&T(a.data.companies)}catch(a){console.error("Error fetching companies:",a),o.error("حدث خطأ أثناء تحميل الشركات")}},q=async()=>{try{x(!0);const a=await $({path:{order_id:p}});a.data&&N(a.data)}catch(a){console.error("Error fetching order:",a),o.error("حدث خطأ أثناء تحميل بيانات الطلب")}finally{x(!1)}},B=async a=>{if(a.preventDefault(),!d){o.error("يرجى اختيار مكتب");return}try{x(!0),m?(await I({path:{order_id:p},body:{code:s.code,notes:s.notes,total_price:s.total_price??null,customer_name:s.customer_name,customer_phone:s.customer_phone,customer_address:s.customer_address??"",customer_company_id:s.customer_company_id??null,breakable:s.breakable,deadline_date:s.deadline_date??null,commission_fixed_rate:s.commission_fixed_rate??null,assigned_to_id:s.assigned_to_id??null,final_customer_payment:s.final_customer_payment??null,handling_status:s.handling_status,cancellation_reason_template_id:s.cancellation_reason_template_id??null,cancellation_reason:s.cancellation_reason??null}}),o.success("تم تحديث الطلب بنجاح")):(await J({body:{office_id:d.id,code:s.code,notes:s.notes,total_price:s.total_price??null,customer_name:s.customer_name,customer_phone:s.customer_phone,customer_address:s.customer_address??"",customer_company_id:s.customer_company_id??null,breakable:s.breakable,deadline_date:s.deadline_date??null,commission_fixed_rate:s.commission_fixed_rate??null,assigned_to_id:s.assigned_to_id??null,final_customer_payment:null,handling_status:s.handling_status,cancellation_reason_template_id:s.cancellation_reason_template_id??null,cancellation_reason:s.cancellation_reason??null}}),o.success("تم إنشاء الطلب بنجاح")),f({to:"/orders"})}catch(r){console.error("Error saving order:",r),o.error("حدث خطأ أثناء حفظ الطلب")}finally{x(!1)}},c=(a,r)=>{N(g=>({...g,[a]:r}))},P=async a=>{if(!m||!p){o.error("لا يمكن تطبيق قالب الإلغاء إلا عند تعديل طلب موجود");return}const r=E.find(g=>g.id===a);if(r)try{x(!0),await I({path:{order_id:p},body:{code:s.code,notes:s.notes,total_price:s.total_price??null,customer_name:s.customer_name,customer_phone:s.customer_phone,customer_address:s.customer_address??"",customer_company_id:s.customer_company_id??null,breakable:s.breakable,deadline_date:s.deadline_date??null,commission_fixed_rate:s.commission_fixed_rate??null,assigned_to_id:s.assigned_to_id??null,final_customer_payment:s.final_customer_payment??null,handling_status:r.order_default_handling_status||"CANCELLED",cancellation_reason_template_id:r.id,cancellation_reason:r.description}}),o.success("تم تطبيق قالب الإلغاء بنجاح"),f({to:"/orders"})}catch(g){console.error("Error applying cancellation template:",g),o.error("حدث خطأ أثناء تطبيق قالب الإلغاء")}finally{x(!1)}},V=async()=>{if(!d){o.error("يرجى اختيار مكتب");return}if(!t.code.trim()||!t.name.trim()||!t.address.trim()||!t.phone.trim()){o.error("يرجى ملء جميع الحقول المطلوبة");return}try{k(!0);const a=await K({body:{office_id:d.id,code:t.code,name:t.name,address:t.address,phone:t.phone,color_code:t.color_code}});a.data&&(o.success("تم إنشاء الشركة بنجاح"),N(r=>({...r,customer_company_id:a.data.id})),y(!1),_({code:"",name:"",address:"",phone:"",color_code:"#3B82F6"}),w())}catch(a){console.error("Error creating company:",a),o.error("حدث خطأ أثناء إنشاء الشركة")}finally{k(!1)}},R=a=>a?new Date(a).toISOString().slice(0,16):"",z=a=>a?new Date(a).toISOString():null;return e.jsxs(e.Fragment,{children:[e.jsx(W,{title:"إدارة الطلبات"}),e.jsxs("div",{className:"flex flex-col m-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:m?"تعديل الطلب":"إضافة طلب جديد"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:m?"قم بتعديل بيانات الطلب":"أدخل بيانات الطلب الجديد"})]}),e.jsx(te,{className:"mb-4"}),e.jsxs("form",{onSubmit:B,className:"flex flex-col m-4 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"المعلومات الأساسية"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"code",children:"رمز الطلب *"}),e.jsx(i,{id:"code",value:s.code||"",onChange:a=>c("code",a.target.value),required:!0,disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"customer_name",children:"اسم العميل *"}),e.jsx(i,{id:"customer_name",value:s.customer_name||"",onChange:a=>c("customer_name",a.target.value),required:!0,disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"customer_phone",children:"رقم الهاتف *"}),e.jsx(i,{id:"customer_phone",value:s.customer_phone||"",onChange:a=>c("customer_phone",a.target.value),required:!0,disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"customer_address",children:"العنوان"}),e.jsx(b,{id:"customer_address",value:s.customer_address||"",onChange:a=>c("customer_address",a.target.value),disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"customer_company",children:"الشركة"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(C,{value:s.customer_company_id||"none",onValueChange:a=>c("customer_company_id",a==="none"?null:a),disabled:n,children:[e.jsx(F,{className:"flex-1",children:e.jsx(S,{placeholder:"اختر الشركة (اختياري)"})}),e.jsxs(D,{children:[e.jsx(h,{value:"none",children:"بدون شركة"}),L.map(a=>e.jsx(h,{value:a.id,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.color_code}}),e.jsx("span",{children:a.name})]})},a.id))]})]}),e.jsxs(Y,{open:O,onOpenChange:y,children:[e.jsx(ee,{asChild:!0,children:e.jsx(j,{type:"button",variant:"outline",disabled:n,className:"whitespace-nowrap",children:"إضافة شركة"})}),e.jsxs(ae,{className:"sm:max-w-[425px]",children:[e.jsxs(se,{children:[e.jsx(re,{children:"إضافة شركة جديدة"}),e.jsx(le,{children:"أضف شركة جديدة لربطها بالطلب"})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(l,{htmlFor:"company_code",className:"text-right",children:"الرمز"}),e.jsx(i,{id:"company_code",value:t.code,onChange:a=>_(r=>({...r,code:a.target.value})),className:"col-span-3",placeholder:"مثال: COMP001"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(l,{htmlFor:"company_name",className:"text-right",children:"الاسم"}),e.jsx(i,{id:"company_name",value:t.name,onChange:a=>_(r=>({...r,name:a.target.value})),className:"col-span-3",placeholder:"اسم الشركة"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(l,{htmlFor:"company_address",className:"text-right",children:"العنوان"}),e.jsx(i,{id:"company_address",value:t.address,onChange:a=>_(r=>({...r,address:a.target.value})),className:"col-span-3",placeholder:"عنوان الشركة"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(l,{htmlFor:"company_phone",className:"text-right",children:"الهاتف"}),e.jsx(i,{id:"company_phone",value:t.phone,onChange:a=>_(r=>({...r,phone:a.target.value})),className:"col-span-3",placeholder:"رقم الهاتف"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(l,{htmlFor:"company_color",className:"text-right",children:"اللون"}),e.jsxs("div",{className:"col-span-3 flex gap-2",children:[e.jsx("input",{type:"color",value:t.color_code,onChange:a=>_(r=>({...r,color_code:a.target.value})),className:"w-12 h-10 rounded border"}),e.jsx(i,{value:t.color_code,onChange:a=>_(r=>({...r,color_code:a.target.value})),className:"flex-1",placeholder:"#3B82F6"})]})]})]}),e.jsxs(ne,{children:[e.jsx(j,{type:"button",variant:"outline",onClick:()=>y(!1),disabled:v,children:"إلغاء"}),e.jsx(j,{type:"button",onClick:V,disabled:v,children:v?"جاري الإنشاء...":"إنشاء الشركة"})]})]})]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"المعلومات المالية"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"total_price",children:"السعر الإجمالي"}),e.jsx(i,{id:"total_price",type:"number",step:"0.01",value:s.total_price||"",onChange:a=>c("total_price",a.target.value?parseFloat(a.target.value):null),disabled:n})]}),m&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"final_customer_payment",children:"الدفع النهائي"}),e.jsx(i,{id:"final_customer_payment",type:"number",step:"0.01",value:s.final_customer_payment||"",onChange:a=>c("final_customer_payment",a.target.value?parseFloat(a.target.value):null),disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"commission_fixed_rate",children:"نسبة العمولة"}),e.jsx(i,{id:"commission_fixed_rate",type:"number",step:"0.01",value:s.commission_fixed_rate||"",onChange:a=>c("commission_fixed_rate",a.target.value?parseFloat(a.target.value):null),disabled:n})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"تفاصيل الطلب"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"notes",children:"ملاحظات"}),e.jsx(b,{id:"notes",value:s.notes||"",onChange:a=>c("notes",a.target.value),disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"deadline_date",children:"تاريخ الاستحقاق"}),e.jsx(i,{id:"deadline_date",type:"datetime-local",value:R(s.deadline_date),onChange:a=>c("deadline_date",z(a.target.value)),disabled:n})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"handling_status",children:"حالة الطلب *"}),m?e.jsxs(C,{value:s.handling_status||"PENDING",onValueChange:a=>c("handling_status",a),disabled:n,children:[e.jsx(F,{children:e.jsx(S,{})}),e.jsxs(D,{children:[e.jsx(h,{value:"PENDING",children:"في الانتظار"}),e.jsx(h,{value:"ASSIGNED",children:"تم التعيين"}),e.jsx(h,{value:"PROCESSING",children:"قيد المعالجة"}),e.jsx(h,{value:"CANCELLED",children:"ملغي"}),e.jsx(h,{value:"DELIVERED",children:"تم التسليم"})]})]}):e.jsxs("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-md border",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"في الانتظار"}),e.jsx("span",{className:"text-xs text-gray-500",children:"(سيتم تعيينها تلقائياً)"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"خيارات إضافية"}),e.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[e.jsx("div",{className:"scale-110",children:e.jsx(X,{id:"breakable",checked:s.breakable||!1,onCheckedChange:a=>c("breakable",a),disabled:n})}),e.jsx(l,{htmlFor:"breakable",className:"mr-2",children:"قابل للكسر"})]}),m&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"cancellation_template",children:"قالب سبب الإلغاء"}),e.jsxs(C,{onValueChange:P,disabled:n,children:[e.jsx(F,{children:e.jsx(S,{placeholder:"اختر قالب سبب الإلغاء"})}),e.jsx(D,{children:E.map(a=>e.jsx(h,{value:a.id,children:a.name},a.id))})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"اختيار قالب سيؤدي إلى تحديث حالة الطلب وإلغائه تلقائياً"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"cancellation_reason",children:"سبب الإلغاء المخصص"}),e.jsx(b,{id:"cancellation_reason",value:s.cancellation_reason||"",onChange:a=>c("cancellation_reason",a.target.value),disabled:n,placeholder:"اكتب سبب الإلغاء يدوياً (اختياري)"})]})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 space-x-reverse",children:[e.jsx(j,{type:"button",variant:"outline",onClick:()=>f({to:"/orders"}),disabled:n,children:"إلغاء"}),e.jsx(j,{type:"submit",disabled:n,children:n?"جاري الحفظ...":m?"تحديث الطلب":"إنشاء الطلب"})]})]})]})};export{ge as component};
