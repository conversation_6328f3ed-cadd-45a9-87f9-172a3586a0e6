import{c as xa,r as Ue,j as T,k as gc,P as us,e as oa,B as wr,u as xs,a1 as ds,g as mc,a as _c}from"./index-DM6GIfB4.js";import{S as Ec}from"./site-header-BZV9tMVa.js";import{t as ia,S as kc}from"./index-B_SQ5DcE.js";import{C as Br,a as Ur,c as jr,f as Hr,b as Zr}from"./card-D0yfVNGp.js";import{B as Dr}from"./badge-frfbuLAi.js";import{A as Xt,a as $t}from"./alert-B49BKRVQ.js";import{I as Qr}from"./IconCheck-DQhb2mdB.js";import{I as Nt}from"./IconX-q8FUGPcT.js";import{S as Tc,a as wc,b as Ac,c as Fc,d as nn}from"./select-BCFM5Vl0.js";import{T as Ka,a as Ya,b as $r,c as We,d as qa,e as Ge}from"./table-CLheJEBl.js";import{I as Sc}from"./IconLoader2-CgJzOld6.js";import{T as yc,a as Cc,b as e0,c as r0}from"./tabs-4OO8mSpV.js";/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rc=[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]],zt=xa("outline","alert-circle","AlertCircle",Rc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dc=[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M13 18l6 -6",key:"svg-1"}],["path",{d:"M13 6l6 6",key:"svg-2"}]],Oc=xa("outline","arrow-right","ArrowRight",Dc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nc=[["path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M7 11l5 5l5 -5",key:"svg-1"}],["path",{d:"M12 4l0 12",key:"svg-2"}]],Ic=xa("outline","download","Download",Nc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const bc=[["path",{d:"M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6",key:"svg-1"}]],ps=xa("outline","eye","Eye",bc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pc=[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M8 11h8v7h-8z",key:"svg-2"}],["path",{d:"M8 15h8",key:"svg-3"}],["path",{d:"M11 11v7",key:"svg-4"}]],Lc=xa("outline","file-spreadsheet","FileSpreadsheet",Pc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mc=[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}]],Bc=xa("outline","file","File",Mc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]],Da=xa("outline","refresh","Refresh",Uc);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const jc=[["path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M7 9l5 -5l5 5",key:"svg-1"}],["path",{d:"M12 4l0 12",key:"svg-2"}]],vs=xa("outline","upload","Upload",jc);var A0="Progress",F0=100,[Hc,m2]=gc(A0),[Vc,Wc]=Hc(A0),gs=Ue.forwardRef((e,a)=>{const{__scopeProgress:r,value:n=null,max:t,getValueLabel:s=Gc,...i}=e;(t||t===0)&&!sn(t)&&console.error(Xc(`${t}`,"Progress"));const c=sn(t)?t:F0;n!==null&&!cn(n,c)&&console.error($c(`${n}`,"Progress"));const o=cn(n,c)?n:null,f=It(o)?s(o,c):void 0;return T.jsx(Vc,{scope:r,value:o,max:c,children:T.jsx(us.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":It(o)?o:void 0,"aria-valuetext":f,role:"progressbar","data-state":Es(o,c),"data-value":o??void 0,"data-max":c,...i,ref:a})})});gs.displayName=A0;var ms="ProgressIndicator",_s=Ue.forwardRef((e,a)=>{const{__scopeProgress:r,...n}=e,t=Wc(ms,r);return T.jsx(us.div,{"data-state":Es(t.value,t.max),"data-value":t.value??void 0,"data-max":t.max,...n,ref:a})});_s.displayName=ms;function Gc(e,a){return`${Math.round(e/a*100)}%`}function Es(e,a){return e==null?"indeterminate":e===a?"complete":"loading"}function It(e){return typeof e=="number"}function sn(e){return It(e)&&!isNaN(e)&&e>0}function cn(e,a){return It(e)&&!isNaN(e)&&e<=a&&e>=0}function Xc(e,a){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${a}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${F0}\`.`}function $c(e,a){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${a}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${F0} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var ks=gs,zc=_s;const S0=Ue.forwardRef(({className:e,value:a,...r},n)=>T.jsx(ks,{ref:n,className:oa("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...r,children:T.jsx(zc,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})}));S0.displayName=ks.displayName;function Kc({onFileSelect:e,onFileRemove:a,acceptedTypes:r=[".xlsx",".csv"],maxSizeInMB:n=10,selectedFile:t,isProcessing:s=!1,className:i}){const[c,o]=Ue.useState(!1),[f,l]=Ue.useState(null),h=E=>{var b;const R="."+((b=E.name.split(".").pop())==null?void 0:b.toLowerCase());return r.includes(R)?E.size/(1024*1024)>n?`حجم الملف كبير جداً. الحد الأقصى: ${n} ميجابايت`:null:`نوع الملف غير مدعوم. الأنواع المدعومة: ${r.join(", ")}`},x=E=>{const R=h(E);if(R){l(R);return}l(null),e(E)},d=Ue.useCallback(E=>{E.preventDefault(),E.stopPropagation(),E.type==="dragenter"||E.type==="dragover"?o(!0):E.type==="dragleave"&&o(!1)},[]),p=Ue.useCallback(E=>{E.preventDefault(),E.stopPropagation(),o(!1),E.dataTransfer.files&&E.dataTransfer.files[0]&&x(E.dataTransfer.files[0])},[]),u=E=>{E.target.files&&E.target.files[0]&&x(E.target.files[0])},_=E=>{if(E===0)return"0 بايت";const R=1024,g=["بايت","كيلوبايت","ميجابايت","جيجابايت"],b=Math.floor(Math.log(E)/Math.log(R));return parseFloat((E/Math.pow(R,b)).toFixed(2))+" "+g[b]};return t?T.jsxs(Br,{className:oa("w-full",i),children:[T.jsx(Ur,{children:T.jsxs(jr,{className:"flex items-center gap-2",children:[T.jsx(Qr,{className:"h-5 w-5 text-green-600"}),"تم اختيار الملف"]})}),T.jsxs(Hr,{children:[T.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[T.jsxs("div",{className:"flex items-center gap-3",children:[T.jsx(Bc,{className:"h-8 w-8 text-green-600"}),T.jsxs("div",{children:[T.jsx("p",{className:"font-medium text-green-800",children:t.name}),T.jsx("p",{className:"text-sm text-green-600",children:_(t.size)})]})]}),!s&&T.jsx(wr,{variant:"ghost",size:"sm",onClick:a,className:"text-green-600 hover:text-green-800 hover:bg-green-100",children:T.jsx(Nt,{className:"h-4 w-4"})})]}),s&&T.jsxs("div",{className:"mt-4",children:[T.jsx("div",{className:"flex items-center gap-2 mb-2",children:T.jsx("span",{className:"text-sm text-muted-foreground",children:"جاري معالجة الملف..."})}),T.jsx(S0,{value:void 0,className:"w-full"})]})]})]}):T.jsxs(Br,{className:oa("w-full",i),children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"رفع ملف الطلبات"}),T.jsx(Zr,{children:"اختر ملف Excel (.xlsx) أو CSV (.csv) يحتوي على بيانات الطلبات"})]}),T.jsxs(Hr,{children:[T.jsxs("div",{className:oa("border-2 border-dashed rounded-lg p-8 text-center transition-colors",c?"border-primary bg-primary/5":"border-muted-foreground/25","hover:border-primary hover:bg-primary/5"),onDragEnter:d,onDragLeave:d,onDragOver:d,onDrop:p,children:[T.jsx(vs,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),T.jsxs("div",{className:"space-y-2",children:[T.jsx("p",{className:"text-lg font-medium",children:"اسحب الملف هنا أو انقر للاختيار"}),T.jsxs("p",{className:"text-sm text-muted-foreground",children:["الأنواع المدعومة: ",r.join(", ")," | الحد الأقصى: ",n," ميجابايت"]})]}),T.jsx("input",{type:"file",accept:r.join(","),onChange:u,className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"})]}),f&&T.jsxs(Xt,{variant:"destructive",className:"mt-4",children:[T.jsx(zt,{className:"h-4 w-4"}),T.jsx($t,{children:f})]}),T.jsxs("div",{className:"mt-4 text-xs text-muted-foreground",children:[T.jsx("p",{children:"ملاحظات مهمة:"}),T.jsxs("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[T.jsx("li",{children:"سيتم تجاهل الصف الأول والبحث عن أول صف يحتوي على عناوين الأعمدة"}),T.jsx("li",{children:"تأكد من وجود الأعمدة المطلوبة: رمز الطلب، اسم العميل، رقم الهاتف"}),T.jsx("li",{children:"الأعمدة الاختيارية: العنوان، السعر الإجمالي، ملاحظات"})]})]})]})]})}const a0=[{key:"code",label:"رمز الطلب",required:!0,type:"text"},{key:"customer_name",label:"اسم العميل",required:!0,type:"text"},{key:"customer_phone",label:"رقم الهاتف",required:!0,type:"text"},{key:"customer_address",label:"العنوان",required:!1,type:"text"},{key:"total_price",label:"السعر الإجمالي",required:!1,type:"number"},{key:"notes",label:"ملاحظات",required:!1,type:"text"},{key:"deadline_date",label:"تاريخ الاستحقاق",required:!1,type:"date"},{key:"commission_fixed_rate",label:"نسبة العمولة",required:!1,type:"number"},{key:"final_customer_payment",label:"دفع العميل النهائي",required:!1,type:"number"},{key:"breakable",label:"قابل للكسر",required:!1,type:"boolean"}];function Yc({fileColumns:e,sampleData:a,onMappingChange:r,onNext:n,onBack:t,className:s}){const[i,c]=Ue.useState({}),[o,f]=Ue.useState(!1),[l,h]=Ue.useState([]);Ue.useEffect(()=>{const E={};e.forEach(R=>{const g=R.toLowerCase();g.includes("code")||g.includes("رمز")||g.includes("كود")?E[R]="code":g.includes("name")||g.includes("اسم")||g.includes("عميل")?E[R]="customer_name":g.includes("phone")||g.includes("هاتف")||g.includes("تليفون")?E[R]="customer_phone":g.includes("address")||g.includes("عنوان")?E[R]="customer_address":g.includes("price")||g.includes("سعر")||g.includes("مبلغ")?E[R]="total_price":g.includes("note")||g.includes("ملاحظ")||g.includes("تعليق")?E[R]="notes":g.includes("deadline")||g.includes("استحقاق")||g.includes("موعد")?E[R]="deadline_date":g.includes("commission")||g.includes("عمولة")?E[R]="commission_fixed_rate":(g.includes("breakable")||g.includes("كسر")||g.includes("قابل"))&&(E[R]="breakable")}),c(E)},[e]),Ue.useEffect(()=>{r(i),x()},[i,r]);const x=()=>{const E=[],R=a0.filter(H=>H.required),g=Object.values(i).filter(Boolean);R.forEach(H=>{g.includes(H.key)||E.push(`الحقل المطلوب "${H.label}" غير مربوط`)}),g.filter((H,U)=>g.indexOf(H)!==U).length>0&&E.push("يوجد حقول مكررة في الربط"),h(E)},d=(E,R)=>{c(g=>({...g,[E]:R}))},p=()=>Object.values(i).filter(Boolean),u=E=>p().includes(E),_=l.length===0;return T.jsxs("div",{className:oa("space-y-6",s),children:[T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"ربط الأعمدة"}),T.jsx(Zr,{children:"اربط أعمدة الملف بحقول الطلب المطلوبة. الحقول المطلوبة مميزة بعلامة *"})]}),T.jsxs(Hr,{children:[T.jsx("div",{className:"space-y-4",children:e.map((E,R)=>T.jsxs("div",{className:"flex items-center gap-4",children:[T.jsxs("div",{className:"flex-1",children:[T.jsx("label",{className:"text-sm font-medium",children:E}),T.jsx("div",{className:"text-xs text-muted-foreground",children:"عمود من الملف"})]}),T.jsx("div",{className:"flex-1",children:T.jsxs(Tc,{value:i[E]||"none",onValueChange:g=>d(E,g==="none"?null:g),children:[T.jsx(wc,{children:T.jsx(Ac,{placeholder:"اختر الحقل المقابل"})}),T.jsxs(Fc,{children:[T.jsx(nn,{value:"none",children:"لا يوجد ربط"}),a0.map(g=>T.jsx(nn,{value:g.key,disabled:u(g.key)&&i[E]!==g.key,children:T.jsxs("div",{className:"flex items-center gap-2",children:[T.jsx("span",{children:g.label}),g.required&&T.jsx(Dr,{variant:"destructive",className:"text-xs",children:"مطلوب"}),u(g.key)&&i[E]!==g.key&&T.jsx(Dr,{variant:"secondary",className:"text-xs",children:"مستخدم"})]})},g.key))]})]})})]},R))}),l.length>0&&T.jsxs(Xt,{variant:"destructive",className:"mt-4",children:[T.jsx(zt,{className:"h-4 w-4"}),T.jsx($t,{children:T.jsx("ul",{className:"list-disc list-inside space-y-1",children:l.map((E,R)=>T.jsx("li",{children:E},R))})})]}),T.jsxs("div",{className:"flex items-center gap-2 mt-6",children:[T.jsxs(wr,{variant:"outline",onClick:()=>f(!o),className:"flex items-center gap-2",children:[T.jsx(ps,{className:"h-4 w-4"}),o?"إخفاء المعاينة":"معاينة البيانات"]}),_&&T.jsxs(Dr,{variant:"default",className:"flex items-center gap-1",children:[T.jsx(Qr,{className:"h-3 w-3"}),"جاهز للمتابعة"]})]})]})]}),o&&a.length>0&&T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"معاينة البيانات"}),T.jsxs(Zr,{children:["عرض أول ",Math.min(5,a.length)," صفوف من الملف مع الربط المحدد"]})]}),T.jsx(Hr,{children:T.jsx("div",{className:"overflow-x-auto",children:T.jsxs(Ka,{children:[T.jsx(Ya,{children:T.jsx($r,{children:Object.entries(i).filter(([E,R])=>R).map(([E,R])=>{var g;return T.jsx(We,{children:T.jsxs("div",{children:[T.jsx("div",{className:"font-medium",children:E}),T.jsxs("div",{className:"text-xs text-muted-foreground",children:["→ ",(g=a0.find(b=>b.key===R))==null?void 0:g.label]})]})},E)})})}),T.jsx(qa,{children:a.slice(0,5).map((E,R)=>T.jsx($r,{children:Object.entries(i).filter(([g,b])=>b).map(([g])=>T.jsx(Ge,{children:E[g]||"-"},g))},R))})]})})})]}),T.jsxs("div",{className:"flex justify-between",children:[T.jsx(wr,{variant:"outline",onClick:t,children:"السابق"}),T.jsx(wr,{onClick:n,disabled:!_,children:"التالي - بدء الاستيراد"})]})]})}function qc({data:e,mapping:a,onComplete:r,onBack:n,className:t}){const{selectedOffice:s}=xs(),[i,c]=Ue.useState(!1),[o,f]=Ue.useState(0),[l,h]=Ue.useState([]),[x,d]=Ue.useState(!1),p=e.length,u=l.filter(k=>k.success).length,_=l.filter(k=>!k.success).length,E=x?l.length/p*100:0,R=k=>{const v={office_id:(s==null?void 0:s.id)||"",code:"",notes:"",total_price:null,customer_name:"",customer_phone:"",customer_address:"",customer_company_id:null,breakable:!1,deadline_date:null,commission_fixed_rate:null,assigned_to_id:null,final_customer_payment:null,handling_status:"PENDING",cancellation_reason_template_id:null,cancellation_reason:null};return Object.entries(a).forEach(([I,P])=>{if(P&&k[I]!==void 0&&k[I]!==null){const N=k[I];switch(P){case"code":case"customer_name":case"customer_phone":case"customer_address":case"notes":v[P]=String(N).trim();break;case"total_price":case"commission_fixed_rate":case"final_customer_payment":const K=parseFloat(String(N));v[P]=isNaN(K)?null:K;break;case"breakable":v[P]=!!N||String(N).toLowerCase()==="true"||String(N)==="1";break;case"deadline_date":try{const Z=new Date(N);v[P]=isNaN(Z.getTime())?null:Z.toISOString()}catch{v[P]=null}break}}}),v},g=k=>{var v,I,P;return(v=k.code)!=null&&v.trim()?(I=k.customer_name)!=null&&I.trim()?(P=k.customer_phone)!=null&&P.trim()?s?null:"لم يتم اختيار مكتب":"رقم الهاتف مطلوب":"اسم العميل مطلوب":"رمز الطلب مطلوب"},b=async k=>{var N,K,Z;const v=e[k],I=R(v),P=g(I);if(P)return{rowIndex:k,success:!1,error:P,data:v};try{const J=await ds({body:I});return{rowIndex:k,success:!0,orderId:(N=J.data)==null?void 0:N.id,data:v}}catch(J){let se="خطأ غير معروف";return(Z=(K=J==null?void 0:J.response)==null?void 0:K.data)!=null&&Z.detail?se=J.response.data.detail:J!=null&&J.message&&(se=J.message),{rowIndex:k,success:!1,error:se,data:v}}},H=async()=>{if(!s){ia.error("يرجى اختيار مكتب");return}c(!0),d(!0),h([]),f(0);const k=[];for(let v=0;v<e.length;v++){f(v+1);const I=await b(v);k.push(I),h([...k]),await new Promise(P=>setTimeout(P,100))}c(!1),r(k),ia.success(`تم الانتهاء من المعالجة. نجح: ${k.filter(v=>v.success).length}, فشل: ${k.filter(v=>!v.success).length}`)},U=async k=>{const v=await b(k);h(I=>I.map(P=>P.rowIndex===k?v:P)),v.success?ia.success(`تم إنشاء الطلب بنجاح للصف ${k+1}`):ia.error(`فشل في إنشاء الطلب للصف ${k+1}: ${v.error}`)},S=k=>{var I;const v=(I=Object.entries(a).find(([P,N])=>N==="customer_phone"))==null?void 0:I[0];return v?String(k[v]||""):""};return x?T.jsxs("div",{className:oa("space-y-6",t),children:[T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsxs(jr,{className:"flex items-center gap-2",children:[i?T.jsx(Sc,{className:"h-5 w-5 animate-spin"}):T.jsx(Qr,{className:"h-5 w-5 text-green-600"}),i?"جاري المعالجة...":"تم الانتهاء من المعالجة"]}),T.jsx(Zr,{children:i?`معالجة الصف ${o} من ${p}`:`تمت معالجة ${p} صف`})]}),T.jsx(Hr,{children:T.jsxs("div",{className:"space-y-4",children:[T.jsx(S0,{value:E,className:"w-full"}),T.jsxs("div",{className:"flex gap-4",children:[T.jsxs(Dr,{variant:"default",className:"flex items-center gap-1",children:[T.jsx(Qr,{className:"h-3 w-3"}),"نجح: ",u]}),T.jsxs(Dr,{variant:"destructive",className:"flex items-center gap-1",children:[T.jsx(Nt,{className:"h-3 w-3"}),"فشل: ",_]}),T.jsxs(Dr,{variant:"secondary",children:["المتبقي: ",p-l.length]})]})]})})]}),l.length>0&&T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"تفاصيل النتائج"}),T.jsx(Zr,{children:"عرض تفصيلي لحالة كل طلب"})]}),T.jsx(Hr,{children:T.jsx("div",{className:"overflow-x-auto",children:T.jsxs(Ka,{children:[T.jsx(Ya,{children:T.jsxs($r,{children:[T.jsx(We,{children:"الصف"}),T.jsx(We,{children:"الحالة"}),T.jsx(We,{children:"رقم الهاتف"}),T.jsx(We,{children:"التفاصيل"}),T.jsx(We,{children:"الإجراءات"})]})}),T.jsx(qa,{children:l.map(k=>T.jsxs($r,{children:[T.jsx(Ge,{children:k.rowIndex+1}),T.jsx(Ge,{children:k.success?T.jsxs(Dr,{variant:"default",className:"flex items-center gap-1 w-fit",children:[T.jsx(Qr,{className:"h-3 w-3"}),"نجح"]}):T.jsxs(Dr,{variant:"destructive",className:"flex items-center gap-1 w-fit",children:[T.jsx(Nt,{className:"h-3 w-3"}),"فشل"]})}),T.jsx(Ge,{children:S(k.data)}),T.jsx(Ge,{className:"max-w-xs",children:k.success?T.jsx("span",{className:"text-sm text-green-600",children:"تم إنشاء الطلب بنجاح"}):T.jsx("span",{className:"text-sm text-red-600",children:k.error})}),T.jsx(Ge,{children:!k.success&&!i&&T.jsxs(wr,{variant:"outline",size:"sm",onClick:()=>U(k.rowIndex),className:"flex items-center gap-1",children:[T.jsx(Da,{className:"h-3 w-3"}),"إعادة المحاولة"]})})]},k.rowIndex))})]})})})]})]}):T.jsxs(Br,{className:oa("w-full",t),children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"جاهز للاستيراد"}),T.jsxs(Zr,{children:["سيتم إنشاء ",p," طلب جديد. تأكد من صحة البيانات قبل البدء."]})]}),T.jsx(Hr,{children:T.jsxs("div",{className:"space-y-4",children:[T.jsxs(Xt,{children:[T.jsx(zt,{className:"h-4 w-4"}),T.jsx($t,{children:T.jsxs("div",{className:"space-y-2",children:[T.jsx("p",{children:"سيتم إرسال طلب منفصل لكل صف في الملف. هذه العملية قد تستغرق بعض الوقت."}),T.jsxs("ul",{className:"list-disc list-inside text-sm space-y-1",children:[T.jsxs("li",{children:["عدد الطلبات: ",p]}),T.jsxs("li",{children:["المكتب المحدد: ",(s==null?void 0:s.name)||"غير محدد"]}),T.jsx("li",{children:"يمكنك إعادة المحاولة للطلبات الفاشلة لاحقاً"})]})]})})]}),T.jsxs("div",{className:"flex justify-between",children:[T.jsx(wr,{variant:"outline",onClick:n,children:"السابق"}),T.jsx(wr,{onClick:H,disabled:!s,children:"بدء الاستيراد"})]})]})})]})}function Jc({results:e,mapping:a,onRetryAll:r,onNewImport:n,onViewOrders:t,className:s}){const{selectedOffice:i}=xs(),[c,o]=Ue.useState(new Set),f=e.filter(E=>E.success),l=e.filter(E=>!E.success),h=e.length,x=E=>{const R={office_id:(i==null?void 0:i.id)||"",code:"",notes:"",total_price:null,customer_name:"",customer_phone:"",customer_address:"",customer_company_id:null,breakable:!1,deadline_date:null,commission_fixed_rate:null,assigned_to_id:null,final_customer_payment:null,handling_status:"PENDING",cancellation_reason_template_id:null,cancellation_reason:null};return Object.entries(a).forEach(([g,b])=>{if(b&&E[g]!==void 0&&E[g]!==null){const H=E[g];switch(b){case"code":case"customer_name":case"customer_phone":case"customer_address":case"notes":R[b]=String(H).trim();break;case"total_price":case"commission_fixed_rate":case"final_customer_payment":const U=parseFloat(String(H));R[b]=isNaN(U)?null:U;break;case"breakable":R[b]=!!H||String(H).toLowerCase()==="true"||String(H)==="1";break;case"deadline_date":try{const S=new Date(H);R[b]=isNaN(S.getTime())?null:S.toISOString()}catch{R[b]=null}break}}}),R},d=async E=>{var R,g;o(b=>new Set(b).add(E.rowIndex));try{const b=x(E.data);await ds({body:b}),ia.success(`تم إنشاء الطلب بنجاح للصف ${E.rowIndex+1}`)}catch(b){let H="خطأ غير معروف";(g=(R=b==null?void 0:b.response)==null?void 0:R.data)!=null&&g.detail?H=b.response.data.detail:b!=null&&b.message&&(H=b.message),ia.error(`فشل في إنشاء الطلب للصف ${E.rowIndex+1}: ${H}`)}finally{o(b=>{const H=new Set(b);return H.delete(E.rowIndex),H})}},p=E=>{var g;const R=(g=Object.entries(a).find(([b,H])=>H==="customer_phone"))==null?void 0:g[0];return R?String(E[R]||""):""},u=E=>{var g;const R=(g=Object.entries(a).find(([b,H])=>H==="customer_name"))==null?void 0:g[0];return R?String(E[R]||""):""},_=()=>{const E=l.map(H=>({"رقم الصف":H.rowIndex+1,"سبب الفشل":H.error,...H.data})),R=[Object.keys(E[0]||{}).join(","),...E.map(H=>Object.values(H).map(U=>`"${U}"`).join(","))].join(`
`),g=new Blob([R],{type:"text/csv;charset=utf-8;"}),b=document.createElement("a");b.href=URL.createObjectURL(g),b.download=`failed_orders_${new Date().toISOString().split("T")[0]}.csv`,b.click()};return T.jsxs("div",{className:oa("space-y-6",s),children:[T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsxs(jr,{className:"flex items-center gap-2",children:[T.jsx(Qr,{className:"h-5 w-5 text-green-600"}),"ملخص نتائج الاستيراد"]}),T.jsxs(Zr,{children:["تم معالجة ",h," طلب بإجمالي"]})]}),T.jsxs(Hr,{children:[T.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[T.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg border border-green-200",children:[T.jsx("div",{className:"text-2xl font-bold text-green-600",children:f.length}),T.jsx("div",{className:"text-sm text-green-600",children:"طلب تم إنشاؤه بنجاح"})]}),T.jsxs("div",{className:"text-center p-4 bg-red-50 rounded-lg border border-red-200",children:[T.jsx("div",{className:"text-2xl font-bold text-red-600",children:l.length}),T.jsx("div",{className:"text-sm text-red-600",children:"طلب فشل في الإنشاء"})]}),T.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg border border-blue-200",children:[T.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[h>0?Math.round(f.length/h*100):0,"%"]}),T.jsx("div",{className:"text-sm text-blue-600",children:"معدل النجاح"})]})]}),l.length>0&&T.jsxs(Xt,{variant:"destructive",className:"mt-4",children:[T.jsx(zt,{className:"h-4 w-4"}),T.jsxs($t,{children:["يوجد ",l.length," طلب فشل في الإنشاء. يمكنك مراجعة الأخطاء وإعادة المحاولة."]})]}),T.jsxs("div",{className:"flex flex-wrap gap-2 mt-6",children:[T.jsxs(wr,{onClick:t,className:"flex items-center gap-2",children:[T.jsx(ps,{className:"h-4 w-4"}),"عرض الطلبات"]}),T.jsx(wr,{variant:"outline",onClick:n,children:"استيراد جديد"}),l.length>0&&T.jsxs(T.Fragment,{children:[T.jsxs(wr,{variant:"outline",onClick:r,className:"flex items-center gap-2",children:[T.jsx(Da,{className:"h-4 w-4"}),"إعادة محاولة الفاشل"]}),T.jsxs(wr,{variant:"outline",onClick:_,className:"flex items-center gap-2",children:[T.jsx(Ic,{className:"h-4 w-4"}),"تصدير الفاشل"]})]})]})]})]}),T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"تفاصيل النتائج"}),T.jsx(Zr,{children:"عرض تفصيلي لجميع الطلبات المعالجة"})]}),T.jsx(Hr,{children:T.jsxs(yc,{defaultValue:"all",className:"w-full",children:[T.jsxs(Cc,{className:"grid w-full grid-cols-3",children:[T.jsxs(e0,{value:"all",children:["الكل (",h,")"]}),T.jsxs(e0,{value:"success",children:["نجح (",f.length,")"]}),T.jsxs(e0,{value:"failed",children:["فشل (",l.length,")"]})]}),T.jsx(r0,{value:"all",className:"mt-4",children:T.jsx("div",{className:"overflow-x-auto",children:T.jsxs(Ka,{children:[T.jsx(Ya,{children:T.jsxs($r,{children:[T.jsx(We,{children:"الصف"}),T.jsx(We,{children:"الحالة"}),T.jsx(We,{children:"اسم العميل"}),T.jsx(We,{children:"رقم الهاتف"}),T.jsx(We,{children:"التفاصيل"}),T.jsx(We,{children:"الإجراءات"})]})}),T.jsx(qa,{children:e.map(E=>T.jsxs($r,{children:[T.jsx(Ge,{children:E.rowIndex+1}),T.jsx(Ge,{children:E.success?T.jsxs(Dr,{variant:"default",className:"flex items-center gap-1 w-fit",children:[T.jsx(Qr,{className:"h-3 w-3"}),"نجح"]}):T.jsxs(Dr,{variant:"destructive",className:"flex items-center gap-1 w-fit",children:[T.jsx(Nt,{className:"h-3 w-3"}),"فشل"]})}),T.jsx(Ge,{children:u(E.data)}),T.jsx(Ge,{children:p(E.data)}),T.jsx(Ge,{className:"max-w-xs",children:E.success?T.jsx("span",{className:"text-sm text-green-600",children:"تم إنشاء الطلب بنجاح"}):T.jsx("span",{className:"text-sm text-red-600",children:E.error})}),T.jsx(Ge,{children:!E.success&&T.jsxs(wr,{variant:"outline",size:"sm",onClick:()=>d(E),disabled:c.has(E.rowIndex),className:"flex items-center gap-1",children:[c.has(E.rowIndex)?T.jsx(Da,{className:"h-3 w-3 animate-spin"}):T.jsx(Da,{className:"h-3 w-3"}),"إعادة المحاولة"]})})]},E.rowIndex))})]})})}),T.jsx(r0,{value:"success",className:"mt-4",children:T.jsx("div",{className:"overflow-x-auto",children:T.jsxs(Ka,{children:[T.jsx(Ya,{children:T.jsxs($r,{children:[T.jsx(We,{children:"الصف"}),T.jsx(We,{children:"اسم العميل"}),T.jsx(We,{children:"رقم الهاتف"}),T.jsx(We,{children:"معرف الطلب"})]})}),T.jsx(qa,{children:f.map(E=>T.jsxs($r,{children:[T.jsx(Ge,{children:E.rowIndex+1}),T.jsx(Ge,{children:u(E.data)}),T.jsx(Ge,{children:p(E.data)}),T.jsx(Ge,{className:"font-mono text-sm",children:E.orderId})]},E.rowIndex))})]})})}),T.jsx(r0,{value:"failed",className:"mt-4",children:T.jsx("div",{className:"overflow-x-auto",children:T.jsxs(Ka,{children:[T.jsx(Ya,{children:T.jsxs($r,{children:[T.jsx(We,{children:"الصف"}),T.jsx(We,{children:"اسم العميل"}),T.jsx(We,{children:"رقم الهاتف"}),T.jsx(We,{children:"سبب الفشل"}),T.jsx(We,{children:"الإجراءات"})]})}),T.jsx(qa,{children:l.map(E=>T.jsxs($r,{children:[T.jsx(Ge,{children:E.rowIndex+1}),T.jsx(Ge,{children:u(E.data)}),T.jsx(Ge,{children:p(E.data)}),T.jsx(Ge,{className:"max-w-xs",children:T.jsx("span",{className:"text-sm text-red-600",children:E.error})}),T.jsx(Ge,{children:T.jsxs(wr,{variant:"outline",size:"sm",onClick:()=>d(E),disabled:c.has(E.rowIndex),className:"flex items-center gap-1",children:[c.has(E.rowIndex)?T.jsx(Da,{className:"h-3 w-3 animate-spin"}):T.jsx(Da,{className:"h-3 w-3"}),"إعادة المحاولة"]})})]},E.rowIndex))})]})})})]})})]})]})}/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var Ts=1252,Zc=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],y0={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},C0=function(e){Zc.indexOf(e)!=-1&&(Ts=y0[0]=e)};function Qc(){C0(1252)}var Lr=function(e){C0(e)};function ws(){Lr(1200),Qc()}function fn(e){for(var a=[],r=0,n=e.length;r<n;++r)a[r]=e.charCodeAt(r);return a}function ef(e){for(var a=[],r=0;r<e.length>>1;++r)a[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return a.join("")}function As(e){for(var a=[],r=0;r<e.length>>1;++r)a[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return a.join("")}var Ga=function(e){var a=e.charCodeAt(0),r=e.charCodeAt(1);return a==255&&r==254?ef(e.slice(2)):a==254&&r==255?As(e.slice(2)):a==65279?e.slice(1):e},Tt=function(a){return String.fromCharCode(a)},on=function(a){return String.fromCharCode(a)},it,ca="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function ln(e){for(var a="",r=0,n=0,t=0,s=0,i=0,c=0,o=0,f=0;f<e.length;)r=e.charCodeAt(f++),s=r>>2,n=e.charCodeAt(f++),i=(r&3)<<4|n>>4,t=e.charCodeAt(f++),c=(n&15)<<2|t>>6,o=t&63,isNaN(n)?c=o=64:isNaN(t)&&(o=64),a+=ca.charAt(s)+ca.charAt(i)+ca.charAt(c)+ca.charAt(o);return a}function Sr(e){var a="",r=0,n=0,t=0,s=0,i=0,c=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var f=0;f<e.length;)s=ca.indexOf(e.charAt(f++)),i=ca.indexOf(e.charAt(f++)),r=s<<2|i>>4,a+=String.fromCharCode(r),c=ca.indexOf(e.charAt(f++)),n=(i&15)<<4|c>>2,c!==64&&(a+=String.fromCharCode(n)),o=ca.indexOf(e.charAt(f++)),t=(c&3)<<6|o,o!==64&&(a+=String.fromCharCode(t));return a}var Fe=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),wa=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(a,r){return r?new Buffer(a,r):new Buffer(a)}:Buffer.from.bind(Buffer)}return function(){}}();function ha(e){return Fe?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function hn(e){return Fe?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var Pr=function(a){return Fe?wa(a,"binary"):a.split("").map(function(r){return r.charCodeAt(0)&255})};function Aa(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var a=[],r=0;r<e.length;++r)a[r]=String.fromCharCode(e[r]);return a.join("")}function R0(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return R0(new Uint8Array(e));for(var a=new Array(e.length),r=0;r<e.length;++r)a[r]=e[r];return a}var sa=Fe?function(e){return Buffer.concat(e.map(function(a){return Buffer.isBuffer(a)?a:wa(a)}))}:function(e){if(typeof Uint8Array<"u"){var a=0,r=0;for(a=0;a<e.length;++a)r+=e[a].length;var n=new Uint8Array(r),t=0;for(a=0,r=0;a<e.length;r+=t,++a)if(t=e[a].length,e[a]instanceof Uint8Array)n.set(e[a],r);else{if(typeof e[a]=="string")throw"wtf";n.set(new Uint8Array(e[a]),r)}return n}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function rf(e){for(var a=[],r=0,n=e.length+250,t=ha(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)t[r++]=i;else if(i<2048)t[r++]=192|i>>6&31,t[r++]=128|i&63;else if(i>=55296&&i<57344){i=(i&1023)+64;var c=e.charCodeAt(++s)&1023;t[r++]=240|i>>8&7,t[r++]=128|i>>2&63,t[r++]=128|c>>6&15|(i&3)<<4,t[r++]=128|c&63}else t[r++]=224|i>>12&15,t[r++]=128|i>>6&63,t[r++]=128|i&63;r>n&&(a.push(t.slice(0,r)),r=0,t=ha(65535),n=65530)}return a.push(t.slice(0,r)),sa(a)}var mr=/\u0000/g,Xa=/[\u0001-\u0006]/g;function Ia(e){for(var a="",r=e.length-1;r>=0;)a+=e.charAt(r--);return a}function Mr(e,a){var r=""+e;return r.length>=a?r:je("0",a-r.length)+r}function D0(e,a){var r=""+e;return r.length>=a?r:je(" ",a-r.length)+r}function bt(e,a){var r=""+e;return r.length>=a?r:r+je(" ",a-r.length)}function af(e,a){var r=""+Math.round(e);return r.length>=a?r:je("0",a-r.length)+r}function tf(e,a){var r=""+e;return r.length>=a?r:je("0",a-r.length)+r}var un=Math.pow(2,32);function Ra(e,a){if(e>un||e<-un)return af(e,a);var r=Math.round(e);return tf(r,a)}function Pt(e,a){return a=a||0,e.length>=7+a&&(e.charCodeAt(a)|32)===103&&(e.charCodeAt(a+1)|32)===101&&(e.charCodeAt(a+2)|32)===110&&(e.charCodeAt(a+3)|32)===101&&(e.charCodeAt(a+4)|32)===114&&(e.charCodeAt(a+5)|32)===97&&(e.charCodeAt(a+6)|32)===108}var xn=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],t0=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function nf(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Te={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},dn={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},sf={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Lt(e,a,r){for(var n=e<0?-1:1,t=e*n,s=0,i=1,c=0,o=1,f=0,l=0,h=Math.floor(t);f<a&&(h=Math.floor(t),c=h*i+s,l=h*f+o,!(t-h<5e-8));)t=1/(t-h),s=i,i=c,o=f,f=l;if(l>a&&(f>a?(l=o,c=s):(l=f,c=i)),!r)return[0,n*c,l];var x=Math.floor(n*c/l);return[x,n*c-x*l,l]}function ga(e,a,r){if(e>2958465||e<0)return null;var n=e|0,t=Math.floor(86400*(e-n)),s=0,i=[],c={D:n,T:t,u:86400*(e-n)-t,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),a&&a.date1904&&(n+=1462),c.u>.9999&&(c.u=0,++t==86400&&(c.T=t=0,++n,++c.D)),n===60)i=r?[1317,10,29]:[1900,2,29],s=3;else if(n===0)i=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),i=[o.getFullYear(),o.getMonth()+1,o.getDate()],s=o.getDay(),n<60&&(s=(s+6)%7),r&&(s=uf(o,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=t%60,t=Math.floor(t/60),c.M=t%60,t=Math.floor(t/60),c.H=t,c.q=s,c}var Fs=new Date(1899,11,31,0,0,0),cf=Fs.getTime(),ff=new Date(1900,2,1,0,0,0);function Ss(e,a){var r=e.getTime();return a?r-=1461*24*60*60*1e3:e>=ff&&(r+=1440*60*1e3),(r-(cf+(e.getTimezoneOffset()-Fs.getTimezoneOffset())*6e4))/(1440*60*1e3)}function O0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function of(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function lf(e){var a=e<0?12:11,r=O0(e.toFixed(12));return r.length<=a||(r=e.toPrecision(10),r.length<=a)?r:e.toExponential(5)}function hf(e){var a=O0(e.toFixed(11));return a.length>(e<0?12:11)||a==="0"||a==="-0"?e.toPrecision(6):a}function ct(e){var a=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return a>=-4&&a<=-1?r=e.toPrecision(10+a):Math.abs(a)<=9?r=lf(e):a===10?r=e.toFixed(10).substr(0,12):r=hf(e),O0(of(r.toUpperCase()))}function Ea(e,a){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):ct(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Or(14,Ss(e,a&&a.date1904),a)}throw new Error("unsupported value in General format: "+e)}function uf(e,a){a[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function xf(e,a,r,n){var t="",s=0,i=0,c=r.y,o,f=0;switch(e){case 98:c=r.y+543;case 121:switch(a.length){case 1:case 2:o=c%100,f=2;break;default:o=c%1e4,f=4;break}break;case 109:switch(a.length){case 1:case 2:o=r.m,f=a.length;break;case 3:return t0[r.m-1][1];case 5:return t0[r.m-1][0];default:return t0[r.m-1][2]}break;case 100:switch(a.length){case 1:case 2:o=r.d,f=a.length;break;case 3:return xn[r.q][0];default:return xn[r.q][1]}break;case 104:switch(a.length){case 1:case 2:o=1+(r.H+11)%12,f=a.length;break;default:throw"bad hour format: "+a}break;case 72:switch(a.length){case 1:case 2:o=r.H,f=a.length;break;default:throw"bad hour format: "+a}break;case 77:switch(a.length){case 1:case 2:o=r.M,f=a.length;break;default:throw"bad minute format: "+a}break;case 115:if(a!="s"&&a!="ss"&&a!=".0"&&a!=".00"&&a!=".000")throw"bad second format: "+a;return r.u===0&&(a=="s"||a=="ss")?Mr(r.S,a.length):(n>=2?i=n===3?1e3:100:i=n===1?10:1,s=Math.round(i*(r.S+r.u)),s>=60*i&&(s=0),a==="s"?s===0?"0":""+s/i:(t=Mr(s,2+n),a==="ss"?t.substr(0,2):"."+t.substr(2,a.length-1)));case 90:switch(a){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+a}f=a.length===3?1:2;break;case 101:o=c,f=1;break}var l=f>0?Mr(o,f):"";return l}function fa(e){var a=3;if(e.length<=a)return e;for(var r=e.length%a,n=e.substr(0,r);r!=e.length;r+=a)n+=(n.length>0?",":"")+e.substr(r,a);return n}var ys=/%/g;function df(e,a,r){var n=a.replace(ys,""),t=a.length-n.length;return ea(e,n,r*Math.pow(10,2*t))+je("%",t)}function pf(e,a,r){for(var n=a.length-1;a.charCodeAt(n-1)===44;)--n;return ea(e,a.substr(0,n),r/Math.pow(10,3*(a.length-n)))}function Cs(e,a){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(a==0)return"0.0E+0";if(a<0)return"-"+Cs(e,-a);var t=e.indexOf(".");t===-1&&(t=e.indexOf("E"));var s=Math.floor(Math.log(a)*Math.LOG10E)%t;if(s<0&&(s+=t),r=(a/Math.pow(10,s)).toPrecision(n+1+(t+s)%t),r.indexOf("e")===-1){var i=Math.floor(Math.log(a)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,t)+"."+r.substr(2+t),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,o,f,l){return o+f+l.substr(0,(t+s)%t)+"."+l.substr(s)+"E"})}else r=a.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Rs=/# (\?+)( ?)\/( ?)(\d+)/;function vf(e,a,r){var n=parseInt(e[4],10),t=Math.round(a*n),s=Math.floor(t/n),i=t-s*n,c=n;return r+(s===0?"":""+s)+" "+(i===0?je(" ",e[1].length+1+e[4].length):D0(i,e[1].length)+e[2]+"/"+e[3]+Mr(c,e[4].length))}function gf(e,a,r){return r+(a===0?"":""+a)+je(" ",e[1].length+2+e[4].length)}var Ds=/^#*0*\.([0#]+)/,Os=/\).*[0#]/,Ns=/\(###\) ###\\?-####/;function pr(e){for(var a="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:a+=" ";break;case 48:a+="0";break;default:a+=String.fromCharCode(r)}return a}function pn(e,a){var r=Math.pow(10,a);return""+Math.round(e*r)/r}function vn(e,a){var r=e-Math.floor(e),n=Math.pow(10,a);return a<(""+Math.round(r*n)).length?0:Math.round(r*n)}function mf(e,a){return a<(""+Math.round((e-Math.floor(e))*Math.pow(10,a))).length?1:0}function _f(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Cr(e,a,r){if(e.charCodeAt(0)===40&&!a.match(Os)){var n=a.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Cr("n",n,r):"("+Cr("n",n,-r)+")"}if(a.charCodeAt(a.length-1)===44)return pf(e,a,r);if(a.indexOf("%")!==-1)return df(e,a,r);if(a.indexOf("E")!==-1)return Cs(a,r);if(a.charCodeAt(0)===36)return"$"+Cr(e,a.substr(a.charAt(1)==" "?2:1),r);var t,s,i,c,o=Math.abs(r),f=r<0?"-":"";if(a.match(/^00+$/))return f+Ra(o,a.length);if(a.match(/^[#?]+$/))return t=Ra(r,0),t==="0"&&(t=""),t.length>a.length?t:pr(a.substr(0,a.length-t.length))+t;if(s=a.match(Rs))return vf(s,o,f);if(a.match(/^#+0+$/))return f+Ra(o,a.length-a.indexOf("0"));if(s=a.match(Ds))return t=pn(r,s[1].length).replace(/^([^\.]+)$/,"$1."+pr(s[1])).replace(/\.$/,"."+pr(s[1])).replace(/\.(\d*)$/,function(p,u){return"."+u+je("0",pr(s[1]).length-u.length)}),a.indexOf("0.")!==-1?t:t.replace(/^0\./,".");if(a=a.replace(/^#+([0.])/,"$1"),s=a.match(/^(0*)\.(#*)$/))return f+pn(o,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=a.match(/^#{1,3},##0(\.?)$/))return f+fa(Ra(o,0));if(s=a.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Cr(e,a,-r):fa(""+(Math.floor(r)+mf(r,s[1].length)))+"."+Mr(vn(r,s[1].length),s[1].length);if(s=a.match(/^#,#*,#0/))return Cr(e,a.replace(/^#,#*,/,""),r);if(s=a.match(/^([0#]+)(\\?-([0#]+))+$/))return t=Ia(Cr(e,a.replace(/[\\-]/g,""),r)),i=0,Ia(Ia(a.replace(/\\/g,"")).replace(/[0#]/g,function(p){return i<t.length?t.charAt(i++):p==="0"?"0":""}));if(a.match(Ns))return t=Cr(e,"##########",r),"("+t.substr(0,3)+") "+t.substr(3,3)+"-"+t.substr(6);var l="";if(s=a.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=Lt(o,Math.pow(10,i)-1,!1),t=""+f,l=ea("n",s[1],c[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),t+=l+s[2]+"/"+s[3],l=bt(c[2],i),l.length<s[4].length&&(l=pr(s[4].substr(s[4].length-l.length))+l),t+=l,t;if(s=a.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=Lt(o,Math.pow(10,i)-1,!0),f+(c[0]||(c[1]?"":"0"))+" "+(c[1]?D0(c[1],i)+s[2]+"/"+s[3]+bt(c[2],i):je(" ",2*i+1+s[2].length+s[3].length));if(s=a.match(/^[#0?]+$/))return t=Ra(r,0),a.length<=t.length?t:pr(a.substr(0,a.length-t.length))+t;if(s=a.match(/^([#0?]+)\.([#0]+)$/)){t=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=t.indexOf(".");var h=a.indexOf(".")-i,x=a.length-t.length-h;return pr(a.substr(0,h)+t+a.substr(a.length-x))}if(s=a.match(/^00,000\.([#0]*0)$/))return i=vn(r,s[1].length),r<0?"-"+Cr(e,a,-r):fa(_f(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Mr(0,3-p.length):"")+p})+"."+Mr(i,s[1].length);switch(a){case"###,##0.00":return Cr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=fa(Ra(o,0));return d!=="0"?f+d:"";case"###,###.00":return Cr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Cr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+a+"|")}function Ef(e,a,r){for(var n=a.length-1;a.charCodeAt(n-1)===44;)--n;return ea(e,a.substr(0,n),r/Math.pow(10,3*(a.length-n)))}function kf(e,a,r){var n=a.replace(ys,""),t=a.length-n.length;return ea(e,n,r*Math.pow(10,2*t))+je("%",t)}function Is(e,a){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(a==0)return"0.0E+0";if(a<0)return"-"+Is(e,-a);var t=e.indexOf(".");t===-1&&(t=e.indexOf("E"));var s=Math.floor(Math.log(a)*Math.LOG10E)%t;if(s<0&&(s+=t),r=(a/Math.pow(10,s)).toPrecision(n+1+(t+s)%t),!r.match(/[Ee]/)){var i=Math.floor(Math.log(a)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,o,f,l){return o+f+l.substr(0,(t+s)%t)+"."+l.substr(s)+"E"})}else r=a.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Xr(e,a,r){if(e.charCodeAt(0)===40&&!a.match(Os)){var n=a.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Xr("n",n,r):"("+Xr("n",n,-r)+")"}if(a.charCodeAt(a.length-1)===44)return Ef(e,a,r);if(a.indexOf("%")!==-1)return kf(e,a,r);if(a.indexOf("E")!==-1)return Is(a,r);if(a.charCodeAt(0)===36)return"$"+Xr(e,a.substr(a.charAt(1)==" "?2:1),r);var t,s,i,c,o=Math.abs(r),f=r<0?"-":"";if(a.match(/^00+$/))return f+Mr(o,a.length);if(a.match(/^[#?]+$/))return t=""+r,r===0&&(t=""),t.length>a.length?t:pr(a.substr(0,a.length-t.length))+t;if(s=a.match(Rs))return gf(s,o,f);if(a.match(/^#+0+$/))return f+Mr(o,a.length-a.indexOf("0"));if(s=a.match(Ds))return t=(""+r).replace(/^([^\.]+)$/,"$1."+pr(s[1])).replace(/\.$/,"."+pr(s[1])),t=t.replace(/\.(\d*)$/,function(p,u){return"."+u+je("0",pr(s[1]).length-u.length)}),a.indexOf("0.")!==-1?t:t.replace(/^0\./,".");if(a=a.replace(/^#+([0.])/,"$1"),s=a.match(/^(0*)\.(#*)$/))return f+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=a.match(/^#{1,3},##0(\.?)$/))return f+fa(""+o);if(s=a.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Xr(e,a,-r):fa(""+r)+"."+je("0",s[1].length);if(s=a.match(/^#,#*,#0/))return Xr(e,a.replace(/^#,#*,/,""),r);if(s=a.match(/^([0#]+)(\\?-([0#]+))+$/))return t=Ia(Xr(e,a.replace(/[\\-]/g,""),r)),i=0,Ia(Ia(a.replace(/\\/g,"")).replace(/[0#]/g,function(p){return i<t.length?t.charAt(i++):p==="0"?"0":""}));if(a.match(Ns))return t=Xr(e,"##########",r),"("+t.substr(0,3)+") "+t.substr(3,3)+"-"+t.substr(6);var l="";if(s=a.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=Lt(o,Math.pow(10,i)-1,!1),t=""+f,l=ea("n",s[1],c[1]),l.charAt(l.length-1)==" "&&(l=l.substr(0,l.length-1)+"0"),t+=l+s[2]+"/"+s[3],l=bt(c[2],i),l.length<s[4].length&&(l=pr(s[4].substr(s[4].length-l.length))+l),t+=l,t;if(s=a.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=Lt(o,Math.pow(10,i)-1,!0),f+(c[0]||(c[1]?"":"0"))+" "+(c[1]?D0(c[1],i)+s[2]+"/"+s[3]+bt(c[2],i):je(" ",2*i+1+s[2].length+s[3].length));if(s=a.match(/^[#0?]+$/))return t=""+r,a.length<=t.length?t:pr(a.substr(0,a.length-t.length))+t;if(s=a.match(/^([#0]+)\.([#0]+)$/)){t=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=t.indexOf(".");var h=a.indexOf(".")-i,x=a.length-t.length-h;return pr(a.substr(0,h)+t+a.substr(a.length-x))}if(s=a.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Xr(e,a,-r):fa(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(p){return"00,"+(p.length<3?Mr(0,3-p.length):"")+p})+"."+Mr(0,s[1].length);switch(a){case"###,###":case"##,###":case"#,###":var d=fa(""+o);return d!=="0"?f+d:"";default:if(a.match(/\.[0#?]*$/))return Xr(e,a.slice(0,a.lastIndexOf(".")),r)+pr(a.slice(a.lastIndexOf(".")))}throw new Error("unsupported format |"+a+"|")}function ea(e,a,r){return(r|0)===r?Xr(e,a,r):Cr(e,a,r)}function Tf(e){for(var a=[],r=!1,n=0,t=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:a[a.length]=e.substr(t,n-t),t=n+1}if(a[a.length]=e.substr(t),r===!0)throw new Error("Format |"+e+"| unterminated string ");return a}var bs=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Ba(e){for(var a=0,r="",n="";a<e.length;)switch(r=e.charAt(a)){case"G":Pt(e,a)&&(a+=6),a++;break;case'"':for(;e.charCodeAt(++a)!==34&&a<e.length;);++a;break;case"\\":a+=2;break;case"_":a+=2;break;case"@":++a;break;case"B":case"b":if(e.charAt(a+1)==="1"||e.charAt(a+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(a,3).toUpperCase()==="A/P"||e.substr(a,5).toUpperCase()==="AM/PM"||e.substr(a,5).toUpperCase()==="上午/下午")return!0;++a;break;case"[":for(n=r;e.charAt(a++)!=="]"&&a<e.length;)n+=e.charAt(a);if(n.match(bs))return!0;break;case".":case"0":case"#":for(;a<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++a))>-1||r=="\\"&&e.charAt(a+1)=="-"&&"0#".indexOf(e.charAt(a+2))>-1););break;case"?":for(;e.charAt(++a)===r;);break;case"*":++a,(e.charAt(a)==" "||e.charAt(a)=="*")&&++a;break;case"(":case")":++a;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;a<e.length&&"0123456789".indexOf(e.charAt(++a))>-1;);break;case" ":++a;break;default:++a;break}return!1}function wf(e,a,r,n){for(var t=[],s="",i=0,c="",o="t",f,l,h,x="H";i<e.length;)switch(c=e.charAt(i)){case"G":if(!Pt(e,i))throw new Error("unrecognized character "+c+" in "+e);t[t.length]={t:"G",v:"General"},i+=7;break;case'"':for(s="";(h=e.charCodeAt(++i))!==34&&i<e.length;)s+=String.fromCharCode(h);t[t.length]={t:"t",v:s},++i;break;case"\\":var d=e.charAt(++i),p=d==="("||d===")"?d:"t";t[t.length]={t:p,v:d},++i;break;case"_":t[t.length]={t:"t",v:" "},i+=2;break;case"@":t[t.length]={t:"T",v:a},++i;break;case"B":case"b":if(e.charAt(i+1)==="1"||e.charAt(i+1)==="2"){if(f==null&&(f=ga(a,r,e.charAt(i+1)==="2"),f==null))return"";t[t.length]={t:"X",v:e.substr(i,2)},o=c,i+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(a<0||f==null&&(f=ga(a,r),f==null))return"";for(s=c;++i<e.length&&e.charAt(i).toLowerCase()===c;)s+=c;c==="m"&&o.toLowerCase()==="h"&&(c="M"),c==="h"&&(c=x),t[t.length]={t:c,v:s},o=c;break;case"A":case"a":case"上":var u={t:c,v:c};if(f==null&&(f=ga(a,r)),e.substr(i,3).toUpperCase()==="A/P"?(f!=null&&(u.v=f.H>=12?"P":"A"),u.t="T",x="h",i+=3):e.substr(i,5).toUpperCase()==="AM/PM"?(f!=null&&(u.v=f.H>=12?"PM":"AM"),u.t="T",i+=5,x="h"):e.substr(i,5).toUpperCase()==="上午/下午"?(f!=null&&(u.v=f.H>=12?"下午":"上午"),u.t="T",i+=5,x="h"):(u.t="t",++i),f==null&&u.t==="T")return"";t[t.length]=u,o=c;break;case"[":for(s=c;e.charAt(i++)!=="]"&&i<e.length;)s+=e.charAt(i);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(bs)){if(f==null&&(f=ga(a,r),f==null))return"";t[t.length]={t:"Z",v:s.toLowerCase()},o=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",Ba(e)||(t[t.length]={t:"t",v:s}));break;case".":if(f!=null){for(s=c;++i<e.length&&(c=e.charAt(i))==="0";)s+=c;t[t.length]={t:"s",v:s};break}case"0":case"#":for(s=c;++i<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(i))>-1;)s+=c;t[t.length]={t:"n",v:s};break;case"?":for(s=c;e.charAt(++i)===c;)s+=c;t[t.length]={t:c,v:s},o=c;break;case"*":++i,(e.charAt(i)==" "||e.charAt(i)=="*")&&++i;break;case"(":case")":t[t.length]={t:n===1?"t":c,v:c},++i;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=c;i<e.length&&"0123456789".indexOf(e.charAt(++i))>-1;)s+=e.charAt(i);t[t.length]={t:"D",v:s};break;case" ":t[t.length]={t:c,v:c},++i;break;case"$":t[t.length]={t:"t",v:"$"},++i;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c)===-1)throw new Error("unrecognized character "+c+" in "+e);t[t.length]={t:"t",v:c},++i;break}var _=0,E=0,R;for(i=t.length-1,o="t";i>=0;--i)switch(t[i].t){case"h":case"H":t[i].t=x,o="h",_<1&&(_=1);break;case"s":(R=t[i].v.match(/\.0+$/))&&(E=Math.max(E,R[0].length-1)),_<3&&(_=3);case"d":case"y":case"M":case"e":o=t[i].t;break;case"m":o==="s"&&(t[i].t="M",_<2&&(_=2));break;case"X":break;case"Z":_<1&&t[i].v.match(/[Hh]/)&&(_=1),_<2&&t[i].v.match(/[Mm]/)&&(_=2),_<3&&t[i].v.match(/[Ss]/)&&(_=3)}switch(_){case 0:break;case 1:f.u>=.5&&(f.u=0,++f.S),f.S>=60&&(f.S=0,++f.M),f.M>=60&&(f.M=0,++f.H);break;case 2:f.u>=.5&&(f.u=0,++f.S),f.S>=60&&(f.S=0,++f.M);break}var g="",b;for(i=0;i<t.length;++i)switch(t[i].t){case"t":case"T":case" ":case"D":break;case"X":t[i].v="",t[i].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":t[i].v=xf(t[i].t.charCodeAt(0),t[i].v,f,E),t[i].t="t";break;case"n":case"?":for(b=i+1;t[b]!=null&&((c=t[b].t)==="?"||c==="D"||(c===" "||c==="t")&&t[b+1]!=null&&(t[b+1].t==="?"||t[b+1].t==="t"&&t[b+1].v==="/")||t[i].t==="("&&(c===" "||c==="n"||c===")")||c==="t"&&(t[b].v==="/"||t[b].v===" "&&t[b+1]!=null&&t[b+1].t=="?"));)t[i].v+=t[b].v,t[b]={v:"",t:";"},++b;g+=t[i].v,i=b-1;break;case"G":t[i].t="t",t[i].v=Ea(a,r);break}var H="",U,S;if(g.length>0){g.charCodeAt(0)==40?(U=a<0&&g.charCodeAt(0)===45?-a:a,S=ea("n",g,U)):(U=a<0&&n>1?-a:a,S=ea("n",g,U),U<0&&t[0]&&t[0].t=="t"&&(S=S.substr(1),t[0].v="-"+t[0].v)),b=S.length-1;var k=t.length;for(i=0;i<t.length;++i)if(t[i]!=null&&t[i].t!="t"&&t[i].v.indexOf(".")>-1){k=i;break}var v=t.length;if(k===t.length&&S.indexOf("E")===-1){for(i=t.length-1;i>=0;--i)t[i]==null||"n?".indexOf(t[i].t)===-1||(b>=t[i].v.length-1?(b-=t[i].v.length,t[i].v=S.substr(b+1,t[i].v.length)):b<0?t[i].v="":(t[i].v=S.substr(0,b+1),b=-1),t[i].t="t",v=i);b>=0&&v<t.length&&(t[v].v=S.substr(0,b+1)+t[v].v)}else if(k!==t.length&&S.indexOf("E")===-1){for(b=S.indexOf(".")-1,i=k;i>=0;--i)if(!(t[i]==null||"n?".indexOf(t[i].t)===-1)){for(l=t[i].v.indexOf(".")>-1&&i===k?t[i].v.indexOf(".")-1:t[i].v.length-1,H=t[i].v.substr(l+1);l>=0;--l)b>=0&&(t[i].v.charAt(l)==="0"||t[i].v.charAt(l)==="#")&&(H=S.charAt(b--)+H);t[i].v=H,t[i].t="t",v=i}for(b>=0&&v<t.length&&(t[v].v=S.substr(0,b+1)+t[v].v),b=S.indexOf(".")+1,i=k;i<t.length;++i)if(!(t[i]==null||"n?(".indexOf(t[i].t)===-1&&i!==k)){for(l=t[i].v.indexOf(".")>-1&&i===k?t[i].v.indexOf(".")+1:0,H=t[i].v.substr(0,l);l<t[i].v.length;++l)b<S.length&&(H+=S.charAt(b++));t[i].v=H,t[i].t="t",v=i}}}for(i=0;i<t.length;++i)t[i]!=null&&"n?".indexOf(t[i].t)>-1&&(U=n>1&&a<0&&i>0&&t[i-1].v==="-"?-a:a,t[i].v=ea(t[i].t,t[i].v,U),t[i].t="t");var I="";for(i=0;i!==t.length;++i)t[i]!=null&&(I+=t[i].v);return I}var gn=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function mn(e,a){if(a==null)return!1;var r=parseFloat(a[2]);switch(a[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Af(e,a){var r=Tf(e),n=r.length,t=r[n-1].indexOf("@");if(n<4&&t>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof a!="number")return[4,r.length===4||t>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=t>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=t>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=t>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=a>0?r[0]:a<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var i=r[0].match(gn),c=r[1].match(gn);return mn(a,i)?[n,r[0]]:mn(a,c)?[n,r[1]]:[n,r[i!=null&&c!=null?2:1]]}return[n,s]}function Or(e,a,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Te)[e],n==null&&(n=r.table&&r.table[dn[e]]||Te[dn[e]]),n==null&&(n=sf[e]||"General");break}if(Pt(n,0))return Ea(a,r);a instanceof Date&&(a=Ss(a,r.date1904));var t=Af(n,a);if(Pt(t[1]))return Ea(a,r);if(a===!0)a="TRUE";else if(a===!1)a="FALSE";else if(a===""||a==null)return"";return wf(t[1],a,r,t[0])}function ma(e,a){if(typeof a!="number"){a=+a||-1;for(var r=0;r<392;++r){if(Te[r]==null){a<0&&(a=r);continue}if(Te[r]==e){a=r;break}}a<0&&(a=391)}return Te[a]=e,a}function Ps(){Te=nf()}var Ff={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Ls=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Sf(e){var a=typeof e=="number"?Te[e]:e;return a=a.replace(Ls,"(\\d+)"),new RegExp("^"+a+"$")}function yf(e,a,r){var n=-1,t=-1,s=-1,i=-1,c=-1,o=-1;(a.match(Ls)||[]).forEach(function(h,x){var d=parseInt(r[x+1],10);switch(h.toLowerCase().charAt(0)){case"y":n=d;break;case"d":s=d;break;case"h":i=d;break;case"s":o=d;break;case"m":i>=0?c=d:t=d;break}}),o>=0&&c==-1&&t>=0&&(c=t,t=-1);var f=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(t>=1?t:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);f.length==7&&(f="0"+f),f.length==8&&(f="20"+f);var l=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return i==-1&&c==-1&&o==-1?f:n==-1&&t==-1&&s==-1?l:f+"T"+l}var Cf=function(){var e={};e.version="1.2.0";function a(){for(var S=0,k=new Array(256),v=0;v!=256;++v)S=v,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,k[v]=S;return typeof Int32Array<"u"?new Int32Array(k):k}var r=a();function n(S){var k=0,v=0,I=0,P=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(I=0;I!=256;++I)P[I]=S[I];for(I=0;I!=256;++I)for(v=S[I],k=256+I;k<4096;k+=256)v=P[k]=v>>>8^S[v&255];var N=[];for(I=1;I!=16;++I)N[I-1]=typeof Int32Array<"u"?P.subarray(I*256,I*256+256):P.slice(I*256,I*256+256);return N}var t=n(r),s=t[0],i=t[1],c=t[2],o=t[3],f=t[4],l=t[5],h=t[6],x=t[7],d=t[8],p=t[9],u=t[10],_=t[11],E=t[12],R=t[13],g=t[14];function b(S,k){for(var v=k^-1,I=0,P=S.length;I<P;)v=v>>>8^r[(v^S.charCodeAt(I++))&255];return~v}function H(S,k){for(var v=k^-1,I=S.length-15,P=0;P<I;)v=g[S[P++]^v&255]^R[S[P++]^v>>8&255]^E[S[P++]^v>>16&255]^_[S[P++]^v>>>24]^u[S[P++]]^p[S[P++]]^d[S[P++]]^x[S[P++]]^h[S[P++]]^l[S[P++]]^f[S[P++]]^o[S[P++]]^c[S[P++]]^i[S[P++]]^s[S[P++]]^r[S[P++]];for(I+=15;P<I;)v=v>>>8^r[(v^S[P++])&255];return~v}function U(S,k){for(var v=k^-1,I=0,P=S.length,N=0,K=0;I<P;)N=S.charCodeAt(I++),N<128?v=v>>>8^r[(v^N)&255]:N<2048?(v=v>>>8^r[(v^(192|N>>6&31))&255],v=v>>>8^r[(v^(128|N&63))&255]):N>=55296&&N<57344?(N=(N&1023)+64,K=S.charCodeAt(I++)&1023,v=v>>>8^r[(v^(240|N>>8&7))&255],v=v>>>8^r[(v^(128|N>>2&63))&255],v=v>>>8^r[(v^(128|K>>6&15|(N&3)<<4))&255],v=v>>>8^r[(v^(128|K&63))&255]):(v=v>>>8^r[(v^(224|N>>12&15))&255],v=v>>>8^r[(v^(128|N>>6&63))&255],v=v>>>8^r[(v^(128|N&63))&255]);return~v}return e.table=r,e.bstr=b,e.buf=H,e.str=U,e}(),Se=function(){var a={};a.version="1.2.1";function r(m,C){for(var w=m.split("/"),A=C.split("/"),D=0,O=0,W=Math.min(w.length,A.length);D<W;++D){if(O=w[D].length-A[D].length)return O;if(w[D]!=A[D])return w[D]<A[D]?-1:1}return w.length-A.length}function n(m){if(m.charAt(m.length-1)=="/")return m.slice(0,-1).indexOf("/")===-1?m:n(m.slice(0,-1));var C=m.lastIndexOf("/");return C===-1?m:m.slice(0,C+1)}function t(m){if(m.charAt(m.length-1)=="/")return t(m.slice(0,-1));var C=m.lastIndexOf("/");return C===-1?m:m.slice(C+1)}function s(m,C){typeof C=="string"&&(C=new Date(C));var w=C.getHours();w=w<<6|C.getMinutes(),w=w<<5|C.getSeconds()>>>1,m.write_shift(2,w);var A=C.getFullYear()-1980;A=A<<4|C.getMonth()+1,A=A<<5|C.getDate(),m.write_shift(2,A)}function i(m){var C=m.read_shift(2)&65535,w=m.read_shift(2)&65535,A=new Date,D=w&31;w>>>=5;var O=w&15;w>>>=4,A.setMilliseconds(0),A.setFullYear(w+1980),A.setMonth(O-1),A.setDate(D);var W=C&31;C>>>=5;var q=C&63;return C>>>=6,A.setHours(C),A.setMinutes(q),A.setSeconds(W<<1),A}function c(m){ar(m,0);for(var C={},w=0;m.l<=m.length-4;){var A=m.read_shift(2),D=m.read_shift(2),O=m.l+D,W={};switch(A){case 21589:w=m.read_shift(1),w&1&&(W.mtime=m.read_shift(4)),D>5&&(w&2&&(W.atime=m.read_shift(4)),w&4&&(W.ctime=m.read_shift(4))),W.mtime&&(W.mt=new Date(W.mtime*1e3));break}m.l=O,C[A]=W}return C}var o;function f(){return o||(o={})}function l(m,C){if(m[0]==80&&m[1]==75)return tn(m,C);if((m[0]|32)==109&&(m[1]|32)==105)return hc(m,C);if(m.length<512)throw new Error("CFB file size "+m.length+" < 512");var w=3,A=512,D=0,O=0,W=0,q=0,V=0,G=[],X=m.slice(0,512);ar(X,0);var te=h(X);switch(w=te[0],w){case 3:A=512;break;case 4:A=4096;break;case 0:if(te[1]==0)return tn(m,C);default:throw new Error("Major Version: Expected 3 or 4 saw "+w)}A!==512&&(X=m.slice(0,A),ar(X,28));var oe=m.slice(0,A);x(X,w);var xe=X.read_shift(4,"i");if(w===3&&xe!==0)throw new Error("# Directory Sectors: Expected 0 saw "+xe);X.l+=4,W=X.read_shift(4,"i"),X.l+=4,X.chk("00100000","Mini Stream Cutoff Size: "),q=X.read_shift(4,"i"),D=X.read_shift(4,"i"),V=X.read_shift(4,"i"),O=X.read_shift(4,"i");for(var ce=-1,ue=0;ue<109&&(ce=X.read_shift(4,"i"),!(ce<0));++ue)G[ue]=ce;var ke=d(m,A);_(V,O,ke,A,G);var Me=R(ke,W,G,A);Me[W].name="!Directory",D>0&&q!==K&&(Me[q].name="!MiniFAT"),Me[G[0]].name="!FAT",Me.fat_addrs=G,Me.ssz=A;var Be={},cr=[],Ha=[],Va=[];g(W,Me,ke,cr,D,Be,Ha,q),p(Ha,Va,cr),cr.shift();var Wa={FileIndex:Ha,FullPaths:Va};return C&&C.raw&&(Wa.raw={header:oe,sectors:ke}),Wa}function h(m){if(m[m.l]==80&&m[m.l+1]==75)return[0,0];m.chk(Z,"Header Signature: "),m.l+=16;var C=m.read_shift(2,"u");return[m.read_shift(2,"u"),C]}function x(m,C){var w=9;switch(m.l+=2,w=m.read_shift(2)){case 9:if(C!=3)throw new Error("Sector Shift: Expected 9 saw "+w);break;case 12:if(C!=4)throw new Error("Sector Shift: Expected 12 saw "+w);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+w)}m.chk("0600","Mini Sector Shift: "),m.chk("000000000000","Reserved: ")}function d(m,C){for(var w=Math.ceil(m.length/C)-1,A=[],D=1;D<w;++D)A[D-1]=m.slice(D*C,(D+1)*C);return A[w-1]=m.slice(w*C),A}function p(m,C,w){for(var A=0,D=0,O=0,W=0,q=0,V=w.length,G=[],X=[];A<V;++A)G[A]=X[A]=A,C[A]=w[A];for(;q<X.length;++q)A=X[q],D=m[A].L,O=m[A].R,W=m[A].C,G[A]===A&&(D!==-1&&G[D]!==D&&(G[A]=G[D]),O!==-1&&G[O]!==O&&(G[A]=G[O])),W!==-1&&(G[W]=A),D!==-1&&A!=G[A]&&(G[D]=G[A],X.lastIndexOf(D)<q&&X.push(D)),O!==-1&&A!=G[A]&&(G[O]=G[A],X.lastIndexOf(O)<q&&X.push(O));for(A=1;A<V;++A)G[A]===A&&(O!==-1&&G[O]!==O?G[A]=G[O]:D!==-1&&G[D]!==D&&(G[A]=G[D]));for(A=1;A<V;++A)if(m[A].type!==0){if(q=A,q!=G[q])do q=G[q],C[A]=C[q]+"/"+C[A];while(q!==0&&G[q]!==-1&&q!=G[q]);G[A]=-1}for(C[0]+="/",A=1;A<V;++A)m[A].type!==2&&(C[A]+="/")}function u(m,C,w){for(var A=m.start,D=m.size,O=[],W=A;w&&D>0&&W>=0;)O.push(C.slice(W*N,W*N+N)),D-=N,W=va(w,W*4);return O.length===0?Je(0):sa(O).slice(0,m.size)}function _(m,C,w,A,D){var O=K;if(m===K){if(C!==0)throw new Error("DIFAT chain shorter than expected")}else if(m!==-1){var W=w[m],q=(A>>>2)-1;if(!W)return;for(var V=0;V<q&&(O=va(W,V*4))!==K;++V)D.push(O);_(va(W,A-4),C-1,w,A,D)}}function E(m,C,w,A,D){var O=[],W=[];D||(D=[]);var q=A-1,V=0,G=0;for(V=C;V>=0;){D[V]=!0,O[O.length]=V,W.push(m[V]);var X=w[Math.floor(V*4/A)];if(G=V*4&q,A<4+G)throw new Error("FAT boundary crossed: "+V+" 4 "+A);if(!m[X])break;V=va(m[X],G)}return{nodes:O,data:Cn([W])}}function R(m,C,w,A){var D=m.length,O=[],W=[],q=[],V=[],G=A-1,X=0,te=0,oe=0,xe=0;for(X=0;X<D;++X)if(q=[],oe=X+C,oe>=D&&(oe-=D),!W[oe]){V=[];var ce=[];for(te=oe;te>=0;){ce[te]=!0,W[te]=!0,q[q.length]=te,V.push(m[te]);var ue=w[Math.floor(te*4/A)];if(xe=te*4&G,A<4+xe)throw new Error("FAT boundary crossed: "+te+" 4 "+A);if(!m[ue]||(te=va(m[ue],xe),ce[te]))break}O[oe]={nodes:q,data:Cn([V])}}return O}function g(m,C,w,A,D,O,W,q){for(var V=0,G=A.length?2:0,X=C[m].data,te=0,oe=0,xe;te<X.length;te+=128){var ce=X.slice(te,te+128);ar(ce,64),oe=ce.read_shift(2),xe=L0(ce,0,oe-G),A.push(xe);var ue={name:xe,type:ce.read_shift(1),color:ce.read_shift(1),L:ce.read_shift(4,"i"),R:ce.read_shift(4,"i"),C:ce.read_shift(4,"i"),clsid:ce.read_shift(16),state:ce.read_shift(4,"i"),start:0,size:0},ke=ce.read_shift(2)+ce.read_shift(2)+ce.read_shift(2)+ce.read_shift(2);ke!==0&&(ue.ct=b(ce,ce.l-8));var Me=ce.read_shift(2)+ce.read_shift(2)+ce.read_shift(2)+ce.read_shift(2);Me!==0&&(ue.mt=b(ce,ce.l-8)),ue.start=ce.read_shift(4,"i"),ue.size=ce.read_shift(4,"i"),ue.size<0&&ue.start<0&&(ue.size=ue.type=0,ue.start=K,ue.name=""),ue.type===5?(V=ue.start,D>0&&V!==K&&(C[V].name="!StreamData")):ue.size>=4096?(ue.storage="fat",C[ue.start]===void 0&&(C[ue.start]=E(w,ue.start,C.fat_addrs,C.ssz)),C[ue.start].name=ue.name,ue.content=C[ue.start].data.slice(0,ue.size)):(ue.storage="minifat",ue.size<0?ue.size=0:V!==K&&ue.start!==K&&C[V]&&(ue.content=u(ue,C[V].data,(C[q]||{}).data))),ue.content&&ar(ue.content,0),O[xe]=ue,W.push(ue)}}function b(m,C){return new Date((Ar(m,C+4)/1e7*Math.pow(2,32)+Ar(m,C)/1e7-11644473600)*1e3)}function H(m,C){return f(),l(o.readFileSync(m),C)}function U(m,C){var w=C&&C.type;switch(w||Fe&&Buffer.isBuffer(m)&&(w="buffer"),w||"base64"){case"file":return H(m,C);case"base64":return l(Pr(Sr(m)),C);case"binary":return l(Pr(m),C)}return l(m,C)}function S(m,C){var w=C||{},A=w.root||"Root Entry";if(m.FullPaths||(m.FullPaths=[]),m.FileIndex||(m.FileIndex=[]),m.FullPaths.length!==m.FileIndex.length)throw new Error("inconsistent CFB structure");m.FullPaths.length===0&&(m.FullPaths[0]=A+"/",m.FileIndex[0]={name:A,type:5}),w.CLSID&&(m.FileIndex[0].clsid=w.CLSID),k(m)}function k(m){var C="Sh33tJ5";if(!Se.find(m,"/"+C)){var w=Je(4);w[0]=55,w[1]=w[3]=50,w[2]=54,m.FileIndex.push({name:C,type:2,content:w,size:4,L:69,R:69,C:69}),m.FullPaths.push(m.FullPaths[0]+C),v(m)}}function v(m,C){S(m);for(var w=!1,A=!1,D=m.FullPaths.length-1;D>=0;--D){var O=m.FileIndex[D];switch(O.type){case 0:A?w=!0:(m.FileIndex.pop(),m.FullPaths.pop());break;case 1:case 2:case 5:A=!0,isNaN(O.R*O.L*O.C)&&(w=!0),O.R>-1&&O.L>-1&&O.R==O.L&&(w=!0);break;default:w=!0;break}}if(!(!w&&!C)){var W=new Date(1987,1,19),q=0,V=Object.create?Object.create(null):{},G=[];for(D=0;D<m.FullPaths.length;++D)V[m.FullPaths[D]]=!0,m.FileIndex[D].type!==0&&G.push([m.FullPaths[D],m.FileIndex[D]]);for(D=0;D<G.length;++D){var X=n(G[D][0]);A=V[X],A||(G.push([X,{name:t(X).replace("/",""),type:1,clsid:se,ct:W,mt:W,content:null}]),V[X]=!0)}for(G.sort(function(xe,ce){return r(xe[0],ce[0])}),m.FullPaths=[],m.FileIndex=[],D=0;D<G.length;++D)m.FullPaths[D]=G[D][0],m.FileIndex[D]=G[D][1];for(D=0;D<G.length;++D){var te=m.FileIndex[D],oe=m.FullPaths[D];if(te.name=t(oe).replace("/",""),te.L=te.R=te.C=-(te.color=1),te.size=te.content?te.content.length:0,te.start=0,te.clsid=te.clsid||se,D===0)te.C=G.length>1?1:-1,te.size=0,te.type=5;else if(oe.slice(-1)=="/"){for(q=D+1;q<G.length&&n(m.FullPaths[q])!=oe;++q);for(te.C=q>=G.length?-1:q,q=D+1;q<G.length&&n(m.FullPaths[q])!=n(oe);++q);te.R=q>=G.length?-1:q,te.type=1}else n(m.FullPaths[D+1]||"")==n(oe)&&(te.R=D+1),te.type=2}}}function I(m,C){var w=C||{};if(w.fileType=="mad")return uc(m,w);switch(v(m),w.fileType){case"zip":return sc(m,w)}var A=function(xe){for(var ce=0,ue=0,ke=0;ke<xe.FileIndex.length;++ke){var Me=xe.FileIndex[ke];if(Me.content){var Be=Me.content.length;Be>0&&(Be<4096?ce+=Be+63>>6:ue+=Be+511>>9)}}for(var cr=xe.FullPaths.length+3>>2,Ha=ce+7>>3,Va=ce+127>>7,Wa=Ha+ue+cr+Va,pa=Wa+127>>7,Qt=pa<=109?0:Math.ceil((pa-109)/127);Wa+pa+Qt+127>>7>pa;)Qt=++pa<=109?0:Math.ceil((pa-109)/127);var qr=[1,Qt,pa,Va,cr,ue,ce,0];return xe.FileIndex[0].size=ce<<6,qr[7]=(xe.FileIndex[0].start=qr[0]+qr[1]+qr[2]+qr[3]+qr[4]+qr[5])+(qr[6]+7>>3),qr}(m),D=Je(A[7]<<9),O=0,W=0;{for(O=0;O<8;++O)D.write_shift(1,J[O]);for(O=0;O<8;++O)D.write_shift(2,0);for(D.write_shift(2,62),D.write_shift(2,3),D.write_shift(2,65534),D.write_shift(2,9),D.write_shift(2,6),O=0;O<3;++O)D.write_shift(2,0);for(D.write_shift(4,0),D.write_shift(4,A[2]),D.write_shift(4,A[0]+A[1]+A[2]+A[3]-1),D.write_shift(4,0),D.write_shift(4,4096),D.write_shift(4,A[3]?A[0]+A[1]+A[2]-1:K),D.write_shift(4,A[3]),D.write_shift(-4,A[1]?A[0]-1:K),D.write_shift(4,A[1]),O=0;O<109;++O)D.write_shift(-4,O<A[2]?A[1]+O:-1)}if(A[1])for(W=0;W<A[1];++W){for(;O<236+W*127;++O)D.write_shift(-4,O<A[2]?A[1]+O:-1);D.write_shift(-4,W===A[1]-1?K:W+1)}var q=function(xe){for(W+=xe;O<W-1;++O)D.write_shift(-4,O+1);xe&&(++O,D.write_shift(-4,K))};for(W=O=0,W+=A[1];O<W;++O)D.write_shift(-4,ee.DIFSECT);for(W+=A[2];O<W;++O)D.write_shift(-4,ee.FATSECT);q(A[3]),q(A[4]);for(var V=0,G=0,X=m.FileIndex[0];V<m.FileIndex.length;++V)X=m.FileIndex[V],X.content&&(G=X.content.length,!(G<4096)&&(X.start=W,q(G+511>>9)));for(q(A[6]+7>>3);D.l&511;)D.write_shift(-4,ee.ENDOFCHAIN);for(W=O=0,V=0;V<m.FileIndex.length;++V)X=m.FileIndex[V],X.content&&(G=X.content.length,!(!G||G>=4096)&&(X.start=W,q(G+63>>6)));for(;D.l&511;)D.write_shift(-4,ee.ENDOFCHAIN);for(O=0;O<A[4]<<2;++O){var te=m.FullPaths[O];if(!te||te.length===0){for(V=0;V<17;++V)D.write_shift(4,0);for(V=0;V<3;++V)D.write_shift(4,-1);for(V=0;V<12;++V)D.write_shift(4,0);continue}X=m.FileIndex[O],O===0&&(X.start=X.size?X.start-1:K);var oe=O===0&&w.root||X.name;if(G=2*(oe.length+1),D.write_shift(64,oe,"utf16le"),D.write_shift(2,G),D.write_shift(1,X.type),D.write_shift(1,X.color),D.write_shift(-4,X.L),D.write_shift(-4,X.R),D.write_shift(-4,X.C),X.clsid)D.write_shift(16,X.clsid,"hex");else for(V=0;V<4;++V)D.write_shift(4,0);D.write_shift(4,X.state||0),D.write_shift(4,0),D.write_shift(4,0),D.write_shift(4,0),D.write_shift(4,0),D.write_shift(4,X.start),D.write_shift(4,X.size),D.write_shift(4,0)}for(O=1;O<m.FileIndex.length;++O)if(X=m.FileIndex[O],X.size>=4096)if(D.l=X.start+1<<9,Fe&&Buffer.isBuffer(X.content))X.content.copy(D,D.l,0,X.size),D.l+=X.size+511&-512;else{for(V=0;V<X.size;++V)D.write_shift(1,X.content[V]);for(;V&511;++V)D.write_shift(1,0)}for(O=1;O<m.FileIndex.length;++O)if(X=m.FileIndex[O],X.size>0&&X.size<4096)if(Fe&&Buffer.isBuffer(X.content))X.content.copy(D,D.l,0,X.size),D.l+=X.size+63&-64;else{for(V=0;V<X.size;++V)D.write_shift(1,X.content[V]);for(;V&63;++V)D.write_shift(1,0)}if(Fe)D.l=D.length;else for(;D.l<D.length;)D.write_shift(1,0);return D}function P(m,C){var w=m.FullPaths.map(function(V){return V.toUpperCase()}),A=w.map(function(V){var G=V.split("/");return G[G.length-(V.slice(-1)=="/"?2:1)]}),D=!1;C.charCodeAt(0)===47?(D=!0,C=w[0].slice(0,-1)+C):D=C.indexOf("/")!==-1;var O=C.toUpperCase(),W=D===!0?w.indexOf(O):A.indexOf(O);if(W!==-1)return m.FileIndex[W];var q=!O.match(Xa);for(O=O.replace(mr,""),q&&(O=O.replace(Xa,"!")),W=0;W<w.length;++W)if((q?w[W].replace(Xa,"!"):w[W]).replace(mr,"")==O||(q?A[W].replace(Xa,"!"):A[W]).replace(mr,"")==O)return m.FileIndex[W];return null}var N=64,K=-2,Z="d0cf11e0a1b11ae1",J=[208,207,17,224,161,177,26,225],se="00000000000000000000000000000000",ee={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:K,FREESECT:-1,HEADER_SIGNATURE:Z,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:se,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ve(m,C,w){f();var A=I(m,w);o.writeFileSync(C,A)}function j(m){for(var C=new Array(m.length),w=0;w<m.length;++w)C[w]=String.fromCharCode(m[w]);return C.join("")}function he(m,C){var w=I(m,C);switch(C&&C.type||"buffer"){case"file":return f(),o.writeFileSync(C.filename,w),w;case"binary":return typeof w=="string"?w:j(w);case"base64":return ln(typeof w=="string"?w:j(w));case"buffer":if(Fe)return Buffer.isBuffer(w)?w:wa(w);case"array":return typeof w=="string"?Pr(w):w}return w}var ne;function F(m){try{var C=m.InflateRaw,w=new C;if(w._processChunk(new Uint8Array([3,0]),w._finishFlushFlag),w.bytesRead)ne=m;else throw new Error("zlib does not expose bytesRead")}catch(A){console.error("cannot use native zlib: "+(A.message||A))}}function B(m,C){if(!ne)return rn(m,C);var w=ne.InflateRaw,A=new w,D=A._processChunk(m.slice(m.l),A._finishFlushFlag);return m.l+=A.bytesRead,D}function M(m){return ne?ne.deflateRawSync(m):we(m)}var L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],z=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],ae=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function re(m){var C=(m<<1|m<<11)&139536|(m<<5|m<<15)&558144;return(C>>16|C>>8|C)&255}for(var Q=typeof Uint8Array<"u",Y=Q?new Uint8Array(256):[],ie=0;ie<256;++ie)Y[ie]=re(ie);function y(m,C){var w=Y[m&255];return C<=8?w>>>8-C:(w=w<<8|Y[m>>8&255],C<=16?w>>>16-C:(w=w<<8|Y[m>>16&255],w>>>24-C))}function me(m,C){var w=C&7,A=C>>>3;return(m[A]|(w<=6?0:m[A+1]<<8))>>>w&3}function pe(m,C){var w=C&7,A=C>>>3;return(m[A]|(w<=5?0:m[A+1]<<8))>>>w&7}function ge(m,C){var w=C&7,A=C>>>3;return(m[A]|(w<=4?0:m[A+1]<<8))>>>w&15}function le(m,C){var w=C&7,A=C>>>3;return(m[A]|(w<=3?0:m[A+1]<<8))>>>w&31}function fe(m,C){var w=C&7,A=C>>>3;return(m[A]|(w<=1?0:m[A+1]<<8))>>>w&127}function de(m,C,w){var A=C&7,D=C>>>3,O=(1<<w)-1,W=m[D]>>>A;return w<8-A||(W|=m[D+1]<<8-A,w<16-A)||(W|=m[D+2]<<16-A,w<24-A)||(W|=m[D+3]<<24-A),W&O}function Ae(m,C,w){var A=C&7,D=C>>>3;return A<=5?m[D]|=(w&7)<<A:(m[D]|=w<<A&255,m[D+1]=(w&7)>>8-A),C+3}function qe(m,C,w){var A=C&7,D=C>>>3;return w=(w&1)<<A,m[D]|=w,C+1}function sr(m,C,w){var A=C&7,D=C>>>3;return w<<=A,m[D]|=w&255,w>>>=8,m[D+1]=w,C+8}function ir(m,C,w){var A=C&7,D=C>>>3;return w<<=A,m[D]|=w&255,w>>>=8,m[D+1]=w&255,m[D+2]=w>>>8,C+16}function Er(m,C){var w=m.length,A=2*w>C?2*w:C+5,D=0;if(w>=C)return m;if(Fe){var O=hn(A);if(m.copy)m.copy(O);else for(;D<m.length;++D)O[D]=m[D];return O}else if(Q){var W=new Uint8Array(A);if(W.set)W.set(m);else for(;D<w;++D)W[D]=m[D];return W}return m.length=A,m}function Re(m){for(var C=new Array(m),w=0;w<m;++w)C[w]=0;return C}function Xe(m,C,w){var A=1,D=0,O=0,W=0,q=0,V=m.length,G=Q?new Uint16Array(32):Re(32);for(O=0;O<32;++O)G[O]=0;for(O=V;O<w;++O)m[O]=0;V=m.length;var X=Q?new Uint16Array(V):Re(V);for(O=0;O<V;++O)G[D=m[O]]++,A<D&&(A=D),X[O]=0;for(G[0]=0,O=1;O<=A;++O)G[O+16]=q=q+G[O-1]<<1;for(O=0;O<V;++O)q=m[O],q!=0&&(X[O]=G[q+16]++);var te=0;for(O=0;O<V;++O)if(te=m[O],te!=0)for(q=y(X[O],A)>>A-te,W=(1<<A+4-te)-1;W>=0;--W)C[q|W<<te]=te&15|O<<4;return A}var Oe=Q?new Uint16Array(512):Re(512),$e=Q?new Uint16Array(32):Re(32);if(!Q){for(var Ie=0;Ie<512;++Ie)Oe[Ie]=0;for(Ie=0;Ie<32;++Ie)$e[Ie]=0}(function(){for(var m=[],C=0;C<32;C++)m.push(5);Xe(m,$e,32);var w=[];for(C=0;C<=143;C++)w.push(8);for(;C<=255;C++)w.push(9);for(;C<=279;C++)w.push(7);for(;C<=287;C++)w.push(8);Xe(w,Oe,288)})();var kr=function(){for(var C=Q?new Uint8Array(32768):[],w=0,A=0;w<ae.length-1;++w)for(;A<ae[w+1];++A)C[A]=w;for(;A<32768;++A)C[A]=29;var D=Q?new Uint8Array(259):[];for(w=0,A=0;w<z.length-1;++w)for(;A<z[w+1];++A)D[A]=w;function O(q,V){for(var G=0;G<q.length;){var X=Math.min(65535,q.length-G),te=G+X==q.length;for(V.write_shift(1,+te),V.write_shift(2,X),V.write_shift(2,~X&65535);X-- >0;)V[V.l++]=q[G++]}return V.l}function W(q,V){for(var G=0,X=0,te=Q?new Uint16Array(32768):[];X<q.length;){var oe=Math.min(65535,q.length-X);if(oe<10){for(G=Ae(V,G,+(X+oe==q.length)),G&7&&(G+=8-(G&7)),V.l=G/8|0,V.write_shift(2,oe),V.write_shift(2,~oe&65535);oe-- >0;)V[V.l++]=q[X++];G=V.l*8;continue}G=Ae(V,G,+(X+oe==q.length)+2);for(var xe=0;oe-- >0;){var ce=q[X];xe=(xe<<5^ce)&32767;var ue=-1,ke=0;if((ue=te[xe])&&(ue|=X&-32768,ue>X&&(ue-=32768),ue<X))for(;q[ue+ke]==q[X+ke]&&ke<250;)++ke;if(ke>2){ce=D[ke],ce<=22?G=sr(V,G,Y[ce+1]>>1)-1:(sr(V,G,3),G+=5,sr(V,G,Y[ce-23]>>5),G+=3);var Me=ce<8?0:ce-4>>2;Me>0&&(ir(V,G,ke-z[ce]),G+=Me),ce=C[X-ue],G=sr(V,G,Y[ce]>>3),G-=3;var Be=ce<4?0:ce-2>>1;Be>0&&(ir(V,G,X-ue-ae[ce]),G+=Be);for(var cr=0;cr<ke;++cr)te[xe]=X&32767,xe=(xe<<5^q[X])&32767,++X;oe-=ke-1}else ce<=143?ce=ce+48:G=qe(V,G,1),G=sr(V,G,Y[ce]),te[xe]=X&32767,++X}G=sr(V,G,0)-1}return V.l=(G+7)/8|0,V.l}return function(V,G){return V.length<8?O(V,G):W(V,G)}}();function we(m){var C=Je(50+Math.floor(m.length*1.1)),w=kr(m,C);return C.slice(0,w)}var De=Q?new Uint16Array(32768):Re(32768),Le=Q?new Uint16Array(32768):Re(32768),be=Q?new Uint16Array(128):Re(128),Wr=1,en=1;function ac(m,C){var w=le(m,C)+257;C+=5;var A=le(m,C)+1;C+=5;var D=ge(m,C)+4;C+=4;for(var O=0,W=Q?new Uint8Array(19):Re(19),q=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],V=1,G=Q?new Uint8Array(8):Re(8),X=Q?new Uint8Array(8):Re(8),te=W.length,oe=0;oe<D;++oe)W[L[oe]]=O=pe(m,C),V<O&&(V=O),G[O]++,C+=3;var xe=0;for(G[0]=0,oe=1;oe<=V;++oe)X[oe]=xe=xe+G[oe-1]<<1;for(oe=0;oe<te;++oe)(xe=W[oe])!=0&&(q[oe]=X[xe]++);var ce=0;for(oe=0;oe<te;++oe)if(ce=W[oe],ce!=0){xe=Y[q[oe]]>>8-ce;for(var ue=(1<<7-ce)-1;ue>=0;--ue)be[xe|ue<<ce]=ce&7|oe<<3}var ke=[];for(V=1;ke.length<w+A;)switch(xe=be[fe(m,C)],C+=xe&7,xe>>>=3){case 16:for(O=3+me(m,C),C+=2,xe=ke[ke.length-1];O-- >0;)ke.push(xe);break;case 17:for(O=3+pe(m,C),C+=3;O-- >0;)ke.push(0);break;case 18:for(O=11+fe(m,C),C+=7;O-- >0;)ke.push(0);break;default:ke.push(xe),V<xe&&(V=xe);break}var Me=ke.slice(0,w),Be=ke.slice(w);for(oe=w;oe<286;++oe)Me[oe]=0;for(oe=A;oe<30;++oe)Be[oe]=0;return Wr=Xe(Me,De,286),en=Xe(Be,Le,30),C}function tc(m,C){if(m[0]==3&&!(m[1]&3))return[ha(C),2];for(var w=0,A=0,D=hn(C||1<<18),O=0,W=D.length>>>0,q=0,V=0;(A&1)==0;){if(A=pe(m,w),w+=3,A>>>1)A>>1==1?(q=9,V=5):(w=ac(m,w),q=Wr,V=en);else{w&7&&(w+=8-(w&7));var G=m[w>>>3]|m[(w>>>3)+1]<<8;if(w+=32,G>0)for(!C&&W<O+G&&(D=Er(D,O+G),W=D.length);G-- >0;)D[O++]=m[w>>>3],w+=8;continue}for(;;){!C&&W<O+32767&&(D=Er(D,O+32767),W=D.length);var X=de(m,w,q),te=A>>>1==1?Oe[X]:De[X];if(w+=te&15,te>>>=4,(te>>>8&255)===0)D[O++]=te;else{if(te==256)break;te-=257;var oe=te<8?0:te-4>>2;oe>5&&(oe=0);var xe=O+z[te];oe>0&&(xe+=de(m,w,oe),w+=oe),X=de(m,w,V),te=A>>>1==1?$e[X]:Le[X],w+=te&15,te>>>=4;var ce=te<4?0:te-2>>1,ue=ae[te];for(ce>0&&(ue+=de(m,w,ce),w+=ce),!C&&W<xe&&(D=Er(D,xe+100),W=D.length);O<xe;)D[O]=D[O-ue],++O}}}return C?[D,w+7>>>3]:[D.slice(0,O),w+7>>>3]}function rn(m,C){var w=m.slice(m.l||0),A=tc(w,C);return m.l+=A[1],A[0]}function an(m,C){if(m)typeof console<"u"&&console.error(C);else throw new Error(C)}function tn(m,C){var w=m;ar(w,0);var A=[],D=[],O={FileIndex:A,FullPaths:D};S(O,{root:C.root});for(var W=w.length-4;(w[W]!=80||w[W+1]!=75||w[W+2]!=5||w[W+3]!=6)&&W>=0;)--W;w.l=W+4,w.l+=4;var q=w.read_shift(2);w.l+=6;var V=w.read_shift(4);for(w.l=V,W=0;W<q;++W){w.l+=20;var G=w.read_shift(4),X=w.read_shift(4),te=w.read_shift(2),oe=w.read_shift(2),xe=w.read_shift(2);w.l+=8;var ce=w.read_shift(4),ue=c(w.slice(w.l+te,w.l+te+oe));w.l+=te+oe+xe;var ke=w.l;w.l=ce+4,nc(w,G,X,O,ue),w.l=ke}return O}function nc(m,C,w,A,D){m.l+=2;var O=m.read_shift(2),W=m.read_shift(2),q=i(m);if(O&8257)throw new Error("Unsupported ZIP encryption");for(var V=m.read_shift(4),G=m.read_shift(4),X=m.read_shift(4),te=m.read_shift(2),oe=m.read_shift(2),xe="",ce=0;ce<te;++ce)xe+=String.fromCharCode(m[m.l++]);if(oe){var ue=c(m.slice(m.l,m.l+oe));(ue[21589]||{}).mt&&(q=ue[21589].mt),((D||{})[21589]||{}).mt&&(q=D[21589].mt)}m.l+=oe;var ke=m.slice(m.l,m.l+G);switch(W){case 8:ke=B(m,X);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+W)}var Me=!1;O&8&&(V=m.read_shift(4),V==134695760&&(V=m.read_shift(4),Me=!0),G=m.read_shift(4),X=m.read_shift(4)),G!=C&&an(Me,"Bad compressed size: "+C+" != "+G),X!=w&&an(Me,"Bad uncompressed size: "+w+" != "+X),Zt(A,xe,ke,{unsafe:!0,mt:q})}function sc(m,C){var w=C||{},A=[],D=[],O=Je(1),W=w.compression?8:0,q=0,V=0,G=0,X=0,te=0,oe=m.FullPaths[0],xe=oe,ce=m.FileIndex[0],ue=[],ke=0;for(V=1;V<m.FullPaths.length;++V)if(xe=m.FullPaths[V].slice(oe.length),ce=m.FileIndex[V],!(!ce.size||!ce.content||xe=="Sh33tJ5")){var Me=X,Be=Je(xe.length);for(G=0;G<xe.length;++G)Be.write_shift(1,xe.charCodeAt(G)&127);Be=Be.slice(0,Be.l),ue[te]=Cf.buf(ce.content,0);var cr=ce.content;W==8&&(cr=M(cr)),O=Je(30),O.write_shift(4,67324752),O.write_shift(2,20),O.write_shift(2,q),O.write_shift(2,W),ce.mt?s(O,ce.mt):O.write_shift(4,0),O.write_shift(-4,ue[te]),O.write_shift(4,cr.length),O.write_shift(4,ce.content.length),O.write_shift(2,Be.length),O.write_shift(2,0),X+=O.length,A.push(O),X+=Be.length,A.push(Be),X+=cr.length,A.push(cr),O=Je(46),O.write_shift(4,33639248),O.write_shift(2,0),O.write_shift(2,20),O.write_shift(2,q),O.write_shift(2,W),O.write_shift(4,0),O.write_shift(-4,ue[te]),O.write_shift(4,cr.length),O.write_shift(4,ce.content.length),O.write_shift(2,Be.length),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(4,0),O.write_shift(4,Me),ke+=O.l,D.push(O),ke+=Be.length,D.push(Be),++te}return O=Je(22),O.write_shift(4,101010256),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,te),O.write_shift(2,te),O.write_shift(4,ke),O.write_shift(4,X),O.write_shift(2,0),sa([sa(A),sa(D),O])}var kt={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ic(m,C){if(m.ctype)return m.ctype;var w=m.name||"",A=w.match(/\.([^\.]+)$/);return A&&kt[A[1]]||C&&(A=(w=C).match(/[\.\\]([^\.\\])+$/),A&&kt[A[1]])?kt[A[1]]:"application/octet-stream"}function cc(m){for(var C=ln(m),w=[],A=0;A<C.length;A+=76)w.push(C.slice(A,A+76));return w.join(`\r
`)+`\r
`}function fc(m){var C=m.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(G){var X=G.charCodeAt(0).toString(16).toUpperCase();return"="+(X.length==1?"0"+X:X)});C=C.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),C.charAt(0)==`
`&&(C="=0D"+C.slice(1)),C=C.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var w=[],A=C.split(`\r
`),D=0;D<A.length;++D){var O=A[D];if(O.length==0){w.push("");continue}for(var W=0;W<O.length;){var q=76,V=O.slice(W,W+q);V.charAt(q-1)=="="?q--:V.charAt(q-2)=="="?q-=2:V.charAt(q-3)=="="&&(q-=3),V=O.slice(W,W+q),W+=q,W<O.length&&(V+="="),w.push(V)}}return w.join(`\r
`)}function oc(m){for(var C=[],w=0;w<m.length;++w){for(var A=m[w];w<=m.length&&A.charAt(A.length-1)=="=";)A=A.slice(0,A.length-1)+m[++w];C.push(A)}for(var D=0;D<C.length;++D)C[D]=C[D].replace(/[=][0-9A-Fa-f]{2}/g,function(O){return String.fromCharCode(parseInt(O.slice(1),16))});return Pr(C.join(`\r
`))}function lc(m,C,w){for(var A="",D="",O="",W,q=0;q<10;++q){var V=C[q];if(!V||V.match(/^\s*$/))break;var G=V.match(/^(.*?):\s*([^\s].*)$/);if(G)switch(G[1].toLowerCase()){case"content-location":A=G[2].trim();break;case"content-type":O=G[2].trim();break;case"content-transfer-encoding":D=G[2].trim();break}}switch(++q,D.toLowerCase()){case"base64":W=Pr(Sr(C.slice(q).join("")));break;case"quoted-printable":W=oc(C.slice(q));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+D)}var X=Zt(m,A.slice(w.length),W,{unsafe:!0});O&&(X.ctype=O)}function hc(m,C){if(j(m.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var w=C&&C.root||"",A=(Fe&&Buffer.isBuffer(m)?m.toString("binary"):j(m)).split(`\r
`),D=0,O="";for(D=0;D<A.length;++D)if(O=A[D],!!/^Content-Location:/i.test(O)&&(O=O.slice(O.indexOf("file")),w||(w=O.slice(0,O.lastIndexOf("/")+1)),O.slice(0,w.length)!=w))for(;w.length>0&&(w=w.slice(0,w.length-1),w=w.slice(0,w.lastIndexOf("/")+1),O.slice(0,w.length)!=w););var W=(A[1]||"").match(/boundary="(.*?)"/);if(!W)throw new Error("MAD cannot find boundary");var q="--"+(W[1]||""),V=[],G=[],X={FileIndex:V,FullPaths:G};S(X);var te,oe=0;for(D=0;D<A.length;++D){var xe=A[D];xe!==q&&xe!==q+"--"||(oe++&&lc(X,A.slice(te,D),w),te=D)}return X}function uc(m,C){var w=C||{},A=w.boundary||"SheetJS";A="------="+A;for(var D=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+A.slice(2)+'"',"","",""],O=m.FullPaths[0],W=O,q=m.FileIndex[0],V=1;V<m.FullPaths.length;++V)if(W=m.FullPaths[V].slice(O.length),q=m.FileIndex[V],!(!q.size||!q.content||W=="Sh33tJ5")){W=W.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ke){return"_x"+ke.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ke){return"_u"+ke.charCodeAt(0).toString(16)+"_"});for(var G=q.content,X=Fe&&Buffer.isBuffer(G)?G.toString("binary"):j(G),te=0,oe=Math.min(1024,X.length),xe=0,ce=0;ce<=oe;++ce)(xe=X.charCodeAt(ce))>=32&&xe<128&&++te;var ue=te>=oe*4/5;D.push(A),D.push("Content-Location: "+(w.root||"file:///C:/SheetJS/")+W),D.push("Content-Transfer-Encoding: "+(ue?"quoted-printable":"base64")),D.push("Content-Type: "+ic(q,W)),D.push(""),D.push(ue?fc(X):cc(X))}return D.push(A+`--\r
`),D.join(`\r
`)}function xc(m){var C={};return S(C,m),C}function Zt(m,C,w,A){var D=A&&A.unsafe;D||S(m);var O=!D&&Se.find(m,C);if(!O){var W=m.FullPaths[0];C.slice(0,W.length)==W?W=C:(W.slice(-1)!="/"&&(W+="/"),W=(W+C).replace("//","/")),O={name:t(C),type:2},m.FileIndex.push(O),m.FullPaths.push(W),D||Se.utils.cfb_gc(m)}return O.content=w,O.size=w?w.length:0,A&&(A.CLSID&&(O.clsid=A.CLSID),A.mt&&(O.mt=A.mt),A.ct&&(O.ct=A.ct)),O}function dc(m,C){S(m);var w=Se.find(m,C);if(w){for(var A=0;A<m.FileIndex.length;++A)if(m.FileIndex[A]==w)return m.FileIndex.splice(A,1),m.FullPaths.splice(A,1),!0}return!1}function pc(m,C,w){S(m);var A=Se.find(m,C);if(A){for(var D=0;D<m.FileIndex.length;++D)if(m.FileIndex[D]==A)return m.FileIndex[D].name=t(w),m.FullPaths[D]=w,!0}return!1}function vc(m){v(m,!0)}return a.find=P,a.read=U,a.parse=l,a.write=he,a.writeFile=ve,a.utils={cfb_new:xc,cfb_add:Zt,cfb_del:dc,cfb_mov:pc,cfb_gc:vc,ReadShift:Ja,CheckField:ni,prep_blob:ar,bconcat:sa,use_zlib:F,_deflateRaw:we,_inflateRaw:rn,consts:ee},a}();function Rf(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var a=File(e);a.open("r"),a.encoding="binary";var r=a.read();return a.close(),r}catch(n){if(!n.message||!n.message.match(/onstruct/))throw n}throw new Error("Cannot access file "+e)}function zr(e){for(var a=Object.keys(e),r=[],n=0;n<a.length;++n)Object.prototype.hasOwnProperty.call(e,a[n])&&r.push(a[n]);return r}function N0(e){for(var a=[],r=zr(e),n=0;n!==r.length;++n)a[e[r[n]]]=r[n];return a}var Mt=new Date(1899,11,30,0,0,0);function _r(e,a){var r=e.getTime(),n=Mt.getTime()+(e.getTimezoneOffset()-Mt.getTimezoneOffset())*6e4;return(r-n)/(1440*60*1e3)}var Ms=new Date,Df=Mt.getTime()+(Ms.getTimezoneOffset()-Mt.getTimezoneOffset())*6e4,_n=Ms.getTimezoneOffset();function Kt(e){var a=new Date;return a.setTime(e*24*60*60*1e3+Df),a.getTimezoneOffset()!==_n&&a.setTime(a.getTime()+(a.getTimezoneOffset()-_n)*6e4),a}function Of(e){var a=0,r=0,n=!1,t=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!t)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=t.length;++s)if(t[s]){switch(r=1,s>3&&(n=!0),t[s].slice(t[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+t[s].slice(t[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(n)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}a+=r*parseInt(t[s],10)}return a}var En=new Date("2017-02-19T19:06:09.000Z"),Bs=isNaN(En.getFullYear())?new Date("2/19/17"):En,Nf=Bs.getFullYear()==2017;function rr(e,a){var r=new Date(e);if(Nf)return a>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):a<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Bs.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var t=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+t[0],+t[1]-1,+t[2],+t[3]||0,+t[4]||0,+t[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function ka(e,a){if(Fe&&Buffer.isBuffer(e)){if(a){if(e[0]==255&&e[1]==254)return za(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return za(As(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(a){if(e[0]==255&&e[1]==254)return za(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return za(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var n=[],t=0;t!=e.length;++t)n.push(String.fromCharCode(e[t]));return n.join("")}function tr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(a[r]=tr(e[r]));return a}function je(e,a){for(var r="";r.length<a;)r+=e;return r}function Vr(e){var a=Number(e);if(!isNaN(a))return isFinite(a)?a:NaN;if(!/\d/.test(e))return a;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(a=Number(n))||(n=n.replace(/[(](.*)[)]/,function(t,s){return r=-r,s}),!isNaN(a=Number(n)))?a/r:a}var If=["january","february","march","april","may","june","july","august","september","october","november","december"];function La(e){var a=new Date(e),r=new Date(NaN),n=a.getYear(),t=a.getMonth(),s=a.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&If.indexOf(i)==-1)return r}else if(i.match(/[a-z]/))return r;return n<0||n>8099?r:(t>0||s>1)&&n!=101?a:e.match(/[^-0-9:,\/\\]/)?r:a}var bf=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,n,t){if(e||typeof n=="string")return r.split(n);for(var s=r.split(n),i=[s[0]],c=1;c<s.length;++c)i.push(t),i.push(s[c]);return i}}();function Us(e){return e?e.content&&e.type?ka(e.content,!0):e.data?Ga(e.data):e.asNodeBuffer&&Fe?Ga(e.asNodeBuffer().toString("binary")):e.asBinary?Ga(e.asBinary()):e._data&&e._data.getContent?Ga(ka(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function js(e){if(!e)return null;if(e.data)return fn(e.data);if(e.asNodeBuffer&&Fe)return e.asNodeBuffer();if(e._data&&e._data.getContent){var a=e._data.getContent();return typeof a=="string"?fn(a):Array.prototype.slice.call(a)}return e.content&&e.type?e.content:null}function Pf(e){return e&&e.name.slice(-4)===".bin"?js(e):Us(e)}function Rr(e,a){for(var r=e.FullPaths||zr(e.files),n=a.toLowerCase().replace(/[\/]/g,"\\"),t=n.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(n==i||t==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function I0(e,a){var r=Rr(e,a);if(r==null)throw new Error("Cannot find file "+a+" in zip");return r}function Ke(e,a,r){if(!r)return Pf(I0(e,a));if(!a)return null;try{return Ke(e,a)}catch{return null}}function Fr(e,a,r){if(!r)return Us(I0(e,a));if(!a)return null;try{return Fr(e,a)}catch{return null}}function Lf(e,a,r){return js(I0(e,a))}function kn(e){for(var a=e.FullPaths||zr(e.files),r=[],n=0;n<a.length;++n)a[n].slice(-1)!="/"&&r.push(a[n].replace(/^Root Entry[\/]/,""));return r.sort()}function Mf(e,a,r){if(e.FullPaths){if(typeof r=="string"){var n;return Fe?n=wa(r):n=rf(r),Se.utils.cfb_add(e,a,n)}Se.utils.cfb_add(e,a,r)}else e.file(a,r)}function Hs(e,a){switch(a.type){case"base64":return Se.read(e,{type:"base64"});case"binary":return Se.read(e,{type:"binary"});case"buffer":case"array":return Se.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+a.type)}function $a(e,a){if(e.charAt(0)=="/")return e.slice(1);var r=a.split("/");a.slice(-1)!="/"&&r.pop();for(var n=e.split("/");n.length!==0;){var t=n.shift();t===".."?r.pop():t!=="."&&r.push(t)}return r.join("/")}var Vs=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Bf=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,Tn=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,Uf=/<[^>]*>/g,dr=Vs.match(Tn)?Tn:Uf,jf=/<\w*:/,Hf=/<(\/?)\w+:/;function _e(e,a,r){for(var n={},t=0,s=0;t!==e.length&&!((s=e.charCodeAt(t))===32||s===10||s===13);++t);if(a||(n[0]=e.slice(0,t)),t===e.length)return n;var i=e.match(Bf),c=0,o="",f=0,l="",h="",x=1;if(i)for(f=0;f!=i.length;++f){for(h=i[f],s=0;s!=h.length&&h.charCodeAt(s)!==61;++s);for(l=h.slice(0,s).trim();h.charCodeAt(s+1)==32;)++s;for(x=(t=h.charCodeAt(s+1))==34||t==39?1:0,o=h.slice(s+1+x,h.length-x),c=0;c!=l.length&&l.charCodeAt(c)!==58;++c);if(c===l.length)l.indexOf("_")>0&&(l=l.slice(0,l.indexOf("_"))),n[l]=o,n[l.toLowerCase()]=o;else{var d=(c===5&&l.slice(0,5)==="xmlns"?"xmlns":"")+l.slice(c+1);if(n[d]&&l.slice(c-3,c)=="ext")continue;n[d]=o,n[d.toLowerCase()]=o}}return n}function Kr(e){return e.replace(Hf,"<$1")}var Ws={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Vf=N0(Ws),Ce=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,a=/_x([\da-fA-F]{4})_/ig;return function r(n){var t=n+"",s=t.indexOf("<![CDATA[");if(s==-1)return t.replace(e,function(c,o){return Ws[c]||String.fromCharCode(parseInt(o,c.indexOf("x")>-1?16:10))||c}).replace(a,function(c,o){return String.fromCharCode(parseInt(o,16))});var i=t.indexOf("]]>");return r(t.slice(0,s))+t.slice(s+9,i)+r(t.slice(i+3))}}(),Wf=/[&<>'"]/g,Gf=/[\u0000-\u001f]/g;function b0(e){var a=e+"";return a.replace(Wf,function(r){return Vf[r]}).replace(/\n/g,"<br/>").replace(Gf,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}var wn=function(){var e=/&#(\d+);/g;function a(r,n){return String.fromCharCode(parseInt(n,10))}return function(n){return n.replace(e,a)}}();function Pe(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function n0(e){for(var a="",r=0,n=0,t=0,s=0,i=0,c=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){a+=String.fromCharCode(n);continue}if(t=e.charCodeAt(r++),n>191&&n<224){i=(n&31)<<6,i|=t&63,a+=String.fromCharCode(i);continue}if(s=e.charCodeAt(r++),n<240){a+=String.fromCharCode((n&15)<<12|(t&63)<<6|s&63);continue}i=e.charCodeAt(r++),c=((n&7)<<18|(t&63)<<12|(s&63)<<6|i&63)-65536,a+=String.fromCharCode(55296+(c>>>10&1023)),a+=String.fromCharCode(56320+(c&1023))}return a}function An(e){var a=ha(2*e.length),r,n,t=1,s=0,i=0,c;for(n=0;n<e.length;n+=t)t=1,(c=e.charCodeAt(n))<128?r=c:c<224?(r=(c&31)*64+(e.charCodeAt(n+1)&63),t=2):c<240?(r=(c&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),t=3):(t=4,r=(c&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,i=55296+(r>>>10&1023),r=56320+(r&1023)),i!==0&&(a[s++]=i&255,a[s++]=i>>>8,i=0),a[s++]=r%256,a[s++]=r>>>8;return a.slice(0,s).toString("ucs2")}function Fn(e){return wa(e,"binary").toString("utf8")}var wt="foo bar bazâð£",Ne=Fe&&(Fn(wt)==n0(wt)&&Fn||An(wt)==n0(wt)&&An)||n0,za=Fe?function(e){return wa(e,"utf8").toString("binary")}:function(e){for(var a=[],r=0,n=0,t=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:a.push(String.fromCharCode(n));break;case n<2048:a.push(String.fromCharCode(192+(n>>6))),a.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,t=e.charCodeAt(r++)-56320+(n<<10),a.push(String.fromCharCode(240+(t>>18&7))),a.push(String.fromCharCode(144+(t>>12&63))),a.push(String.fromCharCode(128+(t>>6&63))),a.push(String.fromCharCode(128+(t&63)));break;default:a.push(String.fromCharCode(224+(n>>12))),a.push(String.fromCharCode(128+(n>>6&63))),a.push(String.fromCharCode(128+(n&63)))}return a.join("")},ft=function(){var e={};return function(r,n){var t=r+"|"+(n||"");return e[t]?e[t]:e[t]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",n||"")}}(),Gs=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(a){return[new RegExp("&"+a[0]+";","ig"),a[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),t=0;t<e.length;++t)n=n.replace(e[t][0],e[t][1]);return n}}(),Xf=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),$f=/<\/?(?:vt:)?variant>/g,zf=/<(?:vt:)([^>]*)>([\s\S]*)</;function Sn(e,a){var r=_e(e),n=e.match(Xf(r.baseType))||[],t=[];if(n.length!=r.size){if(a.WTF)throw new Error("unexpected vector length "+n.length+" != "+r.size);return t}return n.forEach(function(s){var i=s.replace($f,"").match(zf);i&&t.push({v:Ne(i[2]),t:i[1]})}),t}var Kf=/(^\s|\s$|\n)/;function Yf(e){return zr(e).map(function(a){return" "+a+'="'+e[a]+'"'}).join("")}function qf(e,a,r){return"<"+e+(r!=null?Yf(r):"")+(a!=null?(a.match(Kf)?' xml:space="preserve"':"")+">"+a+"</"+e:"/")+">"}function P0(e){if(Fe&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return Ne(Aa(R0(e)));throw new Error("Bad input format: expected Buffer or string")}var ot=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,Jf={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},Zf=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];function Qf(e,a){for(var r=1-2*(e[a+7]>>>7),n=((e[a+7]&127)<<4)+(e[a+6]>>>4&15),t=e[a+6]&15,s=5;s>=0;--s)t=t*256+e[a+s];return n==2047?t==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,t+=Math.pow(2,52)),r*Math.pow(2,n-52)*t)}function eo(e,a,r){var n=(a<0||1/a==-1/0?1:0)<<7,t=0,s=0,i=n?-a:a;isFinite(i)?i==0?t=s=0:(t=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-t),t<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?t=-1022:(s-=Math.pow(2,52),t+=1023)):(t=2047,s=isNaN(a)?26985:0);for(var c=0;c<=5;++c,s/=256)e[r+c]=s&255;e[r+6]=(t&15)<<4|s&15,e[r+7]=t>>4|n}var yn=function(e){for(var a=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var t=0,s=e[0][n].length;t<s;t+=r)a.push.apply(a,e[0][n].slice(t,t+r));return a},Cn=Fe?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(a){return Buffer.isBuffer(a)?a:wa(a)})):yn(e)}:yn,Rn=function(e,a,r){for(var n=[],t=a;t<r;t+=2)n.push(String.fromCharCode(Jr(e,t)));return n.join("").replace(mr,"")},L0=Fe?function(e,a,r){return Buffer.isBuffer(e)?e.toString("utf16le",a,r).replace(mr,""):Rn(e,a,r)}:Rn,Dn=function(e,a,r){for(var n=[],t=a;t<a+r;++t)n.push(("0"+e[t].toString(16)).slice(-2));return n.join("")},Xs=Fe?function(e,a,r){return Buffer.isBuffer(e)?e.toString("hex",a,a+r):Dn(e,a,r)}:Dn,On=function(e,a,r){for(var n=[],t=a;t<r;t++)n.push(String.fromCharCode(Oa(e,t)));return n.join("")},pt=Fe?function(a,r,n){return Buffer.isBuffer(a)?a.toString("utf8",r,n):On(a,r,n)}:On,$s=function(e,a){var r=Ar(e,a);return r>0?pt(e,a+4,a+4+r-1):""},zs=$s,Ks=function(e,a){var r=Ar(e,a);return r>0?pt(e,a+4,a+4+r-1):""},Ys=Ks,qs=function(e,a){var r=2*Ar(e,a);return r>0?pt(e,a+4,a+4+r-1):""},Js=qs,Zs=function(a,r){var n=Ar(a,r);return n>0?L0(a,r+4,r+4+n):""},Qs=Zs,ei=function(e,a){var r=Ar(e,a);return r>0?pt(e,a+4,a+4+r):""},ri=ei,ai=function(e,a){return Qf(e,a)},Bt=ai,ti=function(a){return Array.isArray(a)||typeof Uint8Array<"u"&&a instanceof Uint8Array};Fe&&(zs=function(a,r){if(!Buffer.isBuffer(a))return $s(a,r);var n=a.readUInt32LE(r);return n>0?a.toString("utf8",r+4,r+4+n-1):""},Ys=function(a,r){if(!Buffer.isBuffer(a))return Ks(a,r);var n=a.readUInt32LE(r);return n>0?a.toString("utf8",r+4,r+4+n-1):""},Js=function(a,r){if(!Buffer.isBuffer(a))return qs(a,r);var n=2*a.readUInt32LE(r);return a.toString("utf16le",r+4,r+4+n-1)},Qs=function(a,r){if(!Buffer.isBuffer(a))return Zs(a,r);var n=a.readUInt32LE(r);return a.toString("utf16le",r+4,r+4+n)},ri=function(a,r){if(!Buffer.isBuffer(a))return ei(a,r);var n=a.readUInt32LE(r);return a.toString("utf8",r+4,r+4+n)},Bt=function(a,r){return Buffer.isBuffer(a)?a.readDoubleLE(r):ai(a,r)},ti=function(a){return Buffer.isBuffer(a)||Array.isArray(a)||typeof Uint8Array<"u"&&a instanceof Uint8Array});var Oa=function(e,a){return e[a]},Jr=function(e,a){return e[a+1]*256+e[a]},ro=function(e,a){var r=e[a+1]*256+e[a];return r<32768?r:(65535-r+1)*-1},Ar=function(e,a){return e[a+3]*(1<<24)+(e[a+2]<<16)+(e[a+1]<<8)+e[a]},va=function(e,a){return e[a+3]<<24|e[a+2]<<16|e[a+1]<<8|e[a]},ao=function(e,a){return e[a]<<24|e[a+1]<<16|e[a+2]<<8|e[a+3]};function Ja(e,a){var r="",n,t,s=[],i,c,o,f;switch(a){case"dbcs":if(f=this.l,Fe&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(Jr(this,f)),f+=2;e*=2;break;case"utf8":r=pt(this,this.l,this.l+e);break;case"utf16le":e*=2,r=L0(this,this.l,this.l+e);break;case"wstr":return Ja.call(this,e,"dbcs");case"lpstr-ansi":r=zs(this,this.l),e=4+Ar(this,this.l);break;case"lpstr-cp":r=Ys(this,this.l),e=4+Ar(this,this.l);break;case"lpwstr":r=Js(this,this.l),e=4+2*Ar(this,this.l);break;case"lpp4":e=4+Ar(this,this.l),r=Qs(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Ar(this,this.l),r=ri(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(i=Oa(this,this.l+e++))!==0;)s.push(Tt(i));r=s.join("");break;case"_wstr":for(e=0,r="";(i=Jr(this,this.l+e))!==0;)s.push(Tt(i)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",f=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(f)!==-1)return i=Oa(this,f),this.l=f+1,c=Ja.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(Tt(Jr(this,f))),f+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",f=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(f)!==-1)return i=Oa(this,f),this.l=f+1,c=Ja.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(Tt(Oa(this,f))),f+=1}r=s.join("");break;default:switch(e){case 1:return n=Oa(this,this.l),this.l++,n;case 2:return n=(a==="i"?ro:Jr)(this,this.l),this.l+=2,n;case 4:case-4:return a==="i"||(this[this.l+3]&128)===0?(n=(e>0?va:ao)(this,this.l),this.l+=4,n):(t=Ar(this,this.l),this.l+=4,t);case 8:case-8:if(a==="f")return e==8?t=Bt(this,this.l):t=Bt([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,t;e=8;case 16:r=Xs(this,this.l,e);break}}return this.l+=e,r}var to=function(e,a,r){e[r]=a&255,e[r+1]=a>>>8&255,e[r+2]=a>>>16&255,e[r+3]=a>>>24&255},no=function(e,a,r){e[r]=a&255,e[r+1]=a>>8&255,e[r+2]=a>>16&255,e[r+3]=a>>24&255},so=function(e,a,r){e[r]=a&255,e[r+1]=a>>>8&255};function io(e,a,r){var n=0,t=0;if(r==="dbcs"){for(t=0;t!=a.length;++t)so(this,a.charCodeAt(t),this.l+2*t);n=2*a.length}else if(r==="sbcs"){for(a=a.replace(/[^\x00-\x7F]/g,"_"),t=0;t!=a.length;++t)this[this.l+t]=a.charCodeAt(t)&255;n=a.length}else if(r==="hex"){for(;t<e;++t)this[this.l++]=parseInt(a.slice(2*t,2*t+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(t=0;t<Math.min(a.length,e);++t){var i=a.charCodeAt(t);this[this.l++]=i&255,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=a&255;break;case 2:n=2,this[this.l]=a&255,a>>>=8,this[this.l+1]=a&255;break;case 3:n=3,this[this.l]=a&255,a>>>=8,this[this.l+1]=a&255,a>>>=8,this[this.l+2]=a&255;break;case 4:n=4,to(this,a,this.l);break;case 8:if(n=8,r==="f"){eo(this,a,this.l);break}case 16:break;case-4:n=4,no(this,a,this.l);break}return this.l+=n,this}function ni(e,a){var r=Xs(this,this.l,e.length>>1);if(r!==e)throw new Error(a+"Expected "+e+" saw "+r);this.l+=e.length>>1}function ar(e,a){e.l=a,e.read_shift=Ja,e.chk=ni,e.write_shift=io}function xr(e,a){e.l+=a}function Je(e){var a=ha(e);return ar(a,0),a}function ta(e,a,r){if(e){var n,t,s;ar(e,e.l||0);for(var i=e.length,c=0,o=0;e.l<i;){c=e.read_shift(1),c&128&&(c=(c&127)+((e.read_shift(1)&127)<<7));var f=Gt[c]||Gt[65535];for(n=e.read_shift(1),s=n&127,t=1;t<4&&n&128;++t)s+=((n=e.read_shift(1))&127)<<7*t;o=e.l+s;var l=f.f&&f.f(e,s,r);if(e.l=o,a(l,f,c))return}}}function x0(){var e=[],a=Fe?256:2048,r=function(f){var l=Je(f);return ar(l,0),l},n=r(a),t=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},s=function(f){return n&&f<n.length-n.l?n:(t(),n=r(Math.max(f+1,a)))},i=function(){return t(),sa(e)},c=function(f){t(),n=f,n.l==null&&(n.l=n.length),s(a)};return{next:s,push:c,end:i,_bufs:e}}function Za(e,a,r){var n=tr(e);if(a.s?(n.cRel&&(n.c+=a.s.c),n.rRel&&(n.r+=a.s.r)):(n.cRel&&(n.c+=a.c),n.rRel&&(n.r+=a.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function Nn(e,a,r){var n=tr(e);return n.s=Za(n.s,a.s,r),n.e=Za(n.e,a.s,r),n}function Qa(e,a){if(e.cRel&&e.c<0)for(e=tr(e);e.c<0;)e.c+=a>8?16384:256;if(e.rRel&&e.r<0)for(e=tr(e);e.r<0;)e.r+=a>8?1048576:a>5?65536:16384;var r=Ee(e);return!e.cRel&&e.cRel!=null&&(r=oo(r)),!e.rRel&&e.rRel!=null&&(r=co(r)),r}function s0(e,a){return e.s.r==0&&!e.s.rRel&&e.e.r==(a.biff>=12?1048575:a.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+Ze(e.s.c)+":"+(e.e.cRel?"":"$")+Ze(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(a.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+nr(e.s.r)+":"+(e.e.rRel?"":"$")+nr(e.e.r):Qa(e.s,a.biff)+":"+Qa(e.e,a.biff)}function M0(e){return parseInt(fo(e),10)-1}function nr(e){return""+(e+1)}function co(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function fo(e){return e.replace(/\$(\d+)$/,"$1")}function B0(e){for(var a=lo(e),r=0,n=0;n!==a.length;++n)r=26*r+a.charCodeAt(n)-64;return r-1}function Ze(e){if(e<0)throw new Error("invalid column "+e);var a="";for(++e;e;e=Math.floor((e-1)/26))a=String.fromCharCode((e-1)%26+65)+a;return a}function oo(e){return e.replace(/^([A-Z])/,"$$$1")}function lo(e){return e.replace(/^\$([A-Z])/,"$1")}function ho(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function gr(e){for(var a=0,r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=48&&t<=57?a=10*a+(t-48):t>=65&&t<=90&&(r=26*r+(t-64))}return{c:r-1,r:a-1}}function Ee(e){for(var a=e.c+1,r="";a;a=(a-1)/26|0)r=String.fromCharCode((a-1)%26+65)+r;return r+(e.r+1)}function Ua(e){var a=e.indexOf(":");return a==-1?{s:gr(e),e:gr(e)}:{s:gr(e.slice(0,a)),e:gr(e.slice(a+1))}}function ye(e,a){return typeof a>"u"||typeof a=="number"?ye(e.s,e.e):(typeof e!="string"&&(e=Ee(e)),typeof a!="string"&&(a=Ee(a)),e==a?e:e+":"+a)}function He(e){var a={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,t=0,s=e.length;for(r=0;n<s&&!((t=e.charCodeAt(n)-64)<1||t>26);++n)r=26*r+t;for(a.s.c=--r,r=0;n<s&&!((t=e.charCodeAt(n)-48)<0||t>9);++n)r=10*r+t;if(a.s.r=--r,n===s||t!=10)return a.e.c=a.s.c,a.e.r=a.s.r,a;for(++n,r=0;n!=s&&!((t=e.charCodeAt(n)-64)<1||t>26);++n)r=26*r+t;for(a.e.c=--r,r=0;n!=s&&!((t=e.charCodeAt(n)-48)<0||t>9);++n)r=10*r+t;return a.e.r=--r,a}function In(e,a){var r=e.t=="d"&&a instanceof Date;if(e.z!=null)try{return e.w=Or(e.z,r?_r(a):a)}catch{}try{return e.w=Or((e.XF||{}).numFmtId||(r?14:0),r?_r(a):a)}catch{return""+a}}function aa(e,a,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?ya[e.v]||e.v:a==null?In(e,e.v):In(e,a))}function da(e,a){var r=a&&a.sheet?a.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function si(e,a,r){var n=r||{},t=e?Array.isArray(e):n.dense,s=e||(t?[]:{}),i=0,c=0;if(s&&n.origin!=null){if(typeof n.origin=="number")i=n.origin;else{var o=typeof n.origin=="string"?gr(n.origin):n.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var f={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var l=He(s["!ref"]);f.s.c=l.s.c,f.s.r=l.s.r,f.e.c=Math.max(f.e.c,l.e.c),f.e.r=Math.max(f.e.r,l.e.r),i==-1&&(f.e.r=i=l.e.r+1)}for(var h=0;h!=a.length;++h)if(a[h]){if(!Array.isArray(a[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var x=0;x!=a[h].length;++x)if(!(typeof a[h][x]>"u")){var d={v:a[h][x]},p=i+h,u=c+x;if(f.s.r>p&&(f.s.r=p),f.s.c>u&&(f.s.c=u),f.e.r<p&&(f.e.r=p),f.e.c<u&&(f.e.c=u),a[h][x]&&typeof a[h][x]=="object"&&!Array.isArray(a[h][x])&&!(a[h][x]instanceof Date))d=a[h][x];else if(Array.isArray(d.v)&&(d.f=a[h][x][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else if(n.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=n.dateNF||Te[14],n.cellDates?(d.t="d",d.w=Or(d.z,_r(d.v))):(d.t="n",d.v=_r(d.v),d.w=Or(d.z,d.v))):d.t="s";if(t)s[p]||(s[p]=[]),s[p][u]&&s[p][u].z&&(d.z=s[p][u].z),s[p][u]=d;else{var _=Ee({c:u,r:p});s[_]&&s[_].z&&(d.z=s[_].z),s[_]=d}}}return f.s.c<1e7&&(s["!ref"]=ye(f)),s}function ja(e,a){return si(null,e,a)}function uo(e){return e.read_shift(4,"i")}function ur(e){var a=e.read_shift(4);return a===0?"":e.read_shift(a,"dbcs")}function xo(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function U0(e,a){var r=e.l,n=e.read_shift(1),t=ur(e),s=[],i={t,h:t};if((n&1)!==0){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push(xo(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+a,i}var po=U0;function Nr(e){var a=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:a,iStyleRef:r}}function Fa(e){var a=e.read_shift(2);return a+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:a}}var vo=ur;function j0(e){var a=e.read_shift(4);return a===0||a===4294967295?"":e.read_shift(a,"dbcs")}var go=ur,d0=j0;function H0(e){var a=e.slice(e.l,e.l+4),r=a[0]&1,n=a[0]&2;e.l+=4;var t=n===0?Bt([0,0,0,0,a[0]&252,a[1],a[2],a[3]],0):va(a,0)>>2;return r?t/100:t}function ii(e){var a={s:{},e:{}};return a.s.r=e.read_shift(4),a.e.r=e.read_shift(4),a.s.c=e.read_shift(4),a.e.c=e.read_shift(4),a}var Sa=ii;function lr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function mo(e){var a={},r=e.read_shift(1),n=r>>>1,t=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),c=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:a.auto=1;break;case 1:a.index=t;var f=_a[t];f&&(a.rgb=ht(f));break;case 2:a.rgb=ht([i,c,o]);break;case 3:a.theme=t;break}return s!=0&&(a.tint=s>0?s/32767:s/32768),a}function _o(e){var a=e.read_shift(1);e.l++;var r={fBold:a&1,fItalic:a&2,fUnderline:a&4,fStrikeout:a&8,fOutline:a&16,fShadow:a&32,fCondense:a&64,fExtend:a&128};return r}function ci(e,a){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},n=e.read_shift(4);switch(n){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(n>400)throw new Error("Unsupported Clipboard: "+n.toString(16));return e.l-=4,e.read_shift(0,a==1?"lpstr":"lpwstr")}function Eo(e){return ci(e,1)}function ko(e){return ci(e,2)}var V0=2,Tr=3,At=11,bn=12,Ut=19,Ft=64,To=65,wo=71,Ao=4108,Fo=4126,er=80,fi=81,So=[er,fi],yo={1:{n:"CodePage",t:V0},2:{n:"Category",t:er},3:{n:"PresentationFormat",t:er},4:{n:"ByteCount",t:Tr},5:{n:"LineCount",t:Tr},6:{n:"ParagraphCount",t:Tr},7:{n:"SlideCount",t:Tr},8:{n:"NoteCount",t:Tr},9:{n:"HiddenCount",t:Tr},10:{n:"MultimediaClipCount",t:Tr},11:{n:"ScaleCrop",t:At},12:{n:"HeadingPairs",t:Ao},13:{n:"TitlesOfParts",t:Fo},14:{n:"Manager",t:er},15:{n:"Company",t:er},16:{n:"LinksUpToDate",t:At},17:{n:"CharacterCount",t:Tr},19:{n:"SharedDoc",t:At},22:{n:"HyperlinksChanged",t:At},23:{n:"AppVersion",t:Tr,p:"version"},24:{n:"DigSig",t:To},26:{n:"ContentType",t:er},27:{n:"ContentStatus",t:er},28:{n:"Language",t:er},29:{n:"Version",t:er},255:{},2147483648:{n:"Locale",t:Ut},2147483651:{n:"Behavior",t:Ut},1919054434:{}},Co={1:{n:"CodePage",t:V0},2:{n:"Title",t:er},3:{n:"Subject",t:er},4:{n:"Author",t:er},5:{n:"Keywords",t:er},6:{n:"Comments",t:er},7:{n:"Template",t:er},8:{n:"LastAuthor",t:er},9:{n:"RevNumber",t:er},10:{n:"EditTime",t:Ft},11:{n:"LastPrinted",t:Ft},12:{n:"CreatedDate",t:Ft},13:{n:"ModifiedDate",t:Ft},14:{n:"PageCount",t:Tr},15:{n:"WordCount",t:Tr},16:{n:"CharCount",t:Tr},17:{n:"Thumbnail",t:wo},18:{n:"Application",t:er},19:{n:"DocSecurity",t:Tr},255:{},2147483648:{n:"Locale",t:Ut},2147483651:{n:"Behavior",t:Ut},1919054434:{}},Pn={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Ro=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Do(e){return e.map(function(a){return[a>>16&255,a>>8&255,a&255]})}var Oo=Do([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),_a=tr(Oo),ya={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},oi={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},Ln={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};function No(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Io(e){var a=No();if(!e||!e.match)return a;var r={};if((e.match(dr)||[]).forEach(function(n){var t=_e(n);switch(t[0].replace(jf,"<")){case"<?xml":break;case"<Types":a.xmlns=t["xmlns"+(t[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[t.Extension]=t.ContentType;break;case"<Override":a[Ln[t.ContentType]]!==void 0&&a[Ln[t.ContentType]].push(t.PartName);break}}),a.xmlns!==Jf.CT)throw new Error("Unknown Namespace: "+a.xmlns);return a.calcchain=a.calcchains.length>0?a.calcchains[0]:"",a.sst=a.strs.length>0?a.strs[0]:"",a.style=a.styles.length>0?a.styles[0]:"",a.defaults=r,delete a.calcchains,a}var Na={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function p0(e){var a=e.lastIndexOf("/");return e.slice(0,a+1)+"_rels/"+e.slice(a+1)+".rels"}function et(e,a){var r={"!id":{}};if(!e)return r;a.charAt(0)!=="/"&&(a="/"+a);var n={};return(e.match(dr)||[]).forEach(function(t){var s=_e(t);if(s[0]==="<Relationship"){var i={};i.Type=s.Type,i.Target=s.Target,i.Id=s.Id,s.TargetMode&&(i.TargetMode=s.TargetMode);var c=s.TargetMode==="External"?s.Target:$a(s.Target,a);r[c]=i,n[s.Id]=i}}),r["!id"]=n,r}var bo="application/vnd.oasis.opendocument.spreadsheet";function Po(e,a){for(var r=P0(e),n,t;n=ot.exec(r);)switch(n[3]){case"manifest":break;case"file-entry":if(t=_e(n[0],!1),t.path=="/"&&t.type!==bo)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(a&&a.WTF)throw n}}var rt=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],Lo=function(){for(var e=new Array(rt.length),a=0;a<rt.length;++a){var r=rt[a],n="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[a]=new RegExp("<"+n+"[^>]*>([\\s\\S]*?)</"+n+">")}return e}();function li(e){var a={};e=Ne(e);for(var r=0;r<rt.length;++r){var n=rt[r],t=e.match(Lo[r]);t!=null&&t.length>0&&(a[n[1]]=Ce(t[1])),n[2]==="date"&&a[n[1]]&&(a[n[1]]=rr(a[n[1]]))}return a}var Mo=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function hi(e,a,r,n){var t=[];if(typeof e=="string")t=Sn(e,n);else for(var s=0;s<e.length;++s)t=t.concat(e[s].map(function(l){return{v:l}}));var i=typeof a=="string"?Sn(a,n).map(function(l){return l.v}):a,c=0,o=0;if(i.length>0)for(var f=0;f!==t.length;f+=2){switch(o=+t[f+1].v,t[f].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=o,r.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=o,r.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":r.Chartsheets=o,r.ChartNames=i.slice(c,c+o);break}c+=o}}function Bo(e,a,r){var n={};return a||(a={}),e=Ne(e),Mo.forEach(function(t){var s=(e.match(ft(t[0]))||[])[1];switch(t[2]){case"string":s&&(a[t[1]]=Ce(s));break;case"bool":a[t[1]]=s==="true";break;case"raw":var i=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));i&&i.length>0&&(n[t[1]]=i[1]);break}}),n.HeadingPairs&&n.TitlesOfParts&&hi(n.HeadingPairs,n.TitlesOfParts,a,r),a}var Uo=/<[^>]+>[^<]*/g;function jo(e,a){var r={},n="",t=e.match(Uo);if(t)for(var s=0;s!=t.length;++s){var i=t[s],c=_e(i);switch(c[0]){case"<?xml":break;case"<Properties":break;case"<property":n=Ce(c.name);break;case"</property>":n=null;break;default:if(i.indexOf("<vt:")===0){var o=i.split(">"),f=o[0].slice(4),l=o[1];switch(f){case"lpstr":case"bstr":case"lpwstr":r[n]=Ce(l);break;case"bool":r[n]=Pe(l);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[n]=parseInt(l,10);break;case"r4":case"r8":case"decimal":r[n]=parseFloat(l);break;case"filetime":case"date":r[n]=rr(l);break;case"cy":case"error":r[n]=Ce(l);break;default:if(f.slice(-1)=="/")break;a.WTF&&typeof console<"u"&&console.warn("Unexpected",i,f,o)}}else if(i.slice(0,2)!=="</"){if(a.WTF)throw new Error(i)}}}return r}var Ho={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},i0;function Vo(e,a,r){i0||(i0=N0(Ho)),a=i0[a]||a,e[a]=r}function W0(e){var a=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+a/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function ui(e,a,r){var n=e.l,t=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return t}function xi(e,a,r){var n=e.read_shift(0,"lpwstr");return n}function di(e,a,r){return a===31?xi(e):ui(e,a,r)}function v0(e,a,r){return di(e,a,r===!1?0:4)}function Wo(e,a){if(!a)throw new Error("VtUnalignedString must have positive length");return di(e,a,0)}function Go(e){for(var a=e.read_shift(4),r=[],n=0;n!=a;++n){var t=e.l;r[n]=e.read_shift(0,"lpwstr").replace(mr,""),e.l-t&2&&(e.l+=2)}return r}function Xo(e){for(var a=e.read_shift(4),r=[],n=0;n!=a;++n)r[n]=e.read_shift(0,"lpstr-cp").replace(mr,"");return r}function $o(e){var a=e.l,r=jt(e,fi);e[e.l]==0&&e[e.l+1]==0&&e.l-a&2&&(e.l+=2);var n=jt(e,Tr);return[r,n]}function zo(e){for(var a=e.read_shift(4),r=[],n=0;n<a/2;++n)r.push($o(e));return r}function Mn(e,a){for(var r=e.read_shift(4),n={},t=0;t!=r;++t){var s=e.read_shift(4),i=e.read_shift(4);n[s]=e.read_shift(i,a===1200?"utf16le":"utf8").replace(mr,"").replace(Xa,"!"),a===1200&&i%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),n}function pi(e){var a=e.read_shift(4),r=e.slice(e.l,e.l+a);return e.l+=a,(a&3)>0&&(e.l+=4-(a&3)&3),r}function Ko(e){var a={};return a.Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a}function jt(e,a,r){var n=e.read_shift(2),t,s=r||{};if(e.l+=2,a!==bn&&n!==a&&So.indexOf(a)===-1&&!((a&65534)==4126&&(n&65534)==4126))throw new Error("Expected type "+a+" saw "+n);switch(a===bn?n:a){case 2:return t=e.read_shift(2,"i"),s.raw||(e.l+=2),t;case 3:return t=e.read_shift(4,"i"),t;case 11:return e.read_shift(4)!==0;case 19:return t=e.read_shift(4),t;case 30:return ui(e,n,4).replace(mr,"");case 31:return xi(e);case 64:return W0(e);case 65:return pi(e);case 71:return Ko(e);case 80:return v0(e,n,!s.raw).replace(mr,"");case 81:return Wo(e,n).replace(mr,"");case 4108:return zo(e);case 4126:case 4127:return n==4127?Go(e):Xo(e);default:throw new Error("TypedPropertyValue unrecognized type "+a+" "+n)}}function Bn(e,a){var r=e.l,n=e.read_shift(4),t=e.read_shift(4),s=[],i=0,c=0,o=-1,f={};for(i=0;i!=t;++i){var l=e.read_shift(4),h=e.read_shift(4);s[i]=[l,h+r]}s.sort(function(R,g){return R[1]-g[1]});var x={};for(i=0;i!=t;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&a)switch(a[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],d=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1);break}if((!a||i==0)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(a){var p=a[s[i][0]];if(x[p.n]=jt(e,p.t,{raw:!0}),p.p==="version"&&(x[p.n]=String(x[p.n]>>16)+"."+("0000"+String(x[p.n]&65535)).slice(-4)),p.n=="CodePage")switch(x[p.n]){case 0:x[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:Lr(c=x[p.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+x[p.n])}}else if(s[i][0]===1){if(c=x.CodePage=jt(e,V0),Lr(c),o!==-1){var u=e.l;e.l=s[o][1],f=Mn(e,c),e.l=u}}else if(s[i][0]===0){if(c===0){o=i,e.l=s[i+1][1];continue}f=Mn(e,c)}else{var _=f[s[i][0]],E;switch(e[e.l]){case 65:e.l+=4,E=pi(e);break;case 30:e.l+=4,E=v0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,E=v0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,E=e.read_shift(4,"i");break;case 19:e.l+=4,E=e.read_shift(4);break;case 5:e.l+=4,E=e.read_shift(8,"f");break;case 11:e.l+=4,E=Ve(e,4);break;case 64:e.l+=4,E=rr(W0(e));break;default:throw new Error("unparsed value: "+e[e.l])}x[_]=E}}return e.l=r+n,x}function Un(e,a,r){var n=e.content;if(!n)return{};ar(n,0);var t,s,i,c,o=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),l=n.read_shift(16);if(l!==Se.utils.consts.HEADER_CLSID&&l!==r)throw new Error("Bad PropertySet CLSID "+l);if(t=n.read_shift(4),t!==1&&t!==2)throw new Error("Unrecognized #Sets: "+t);if(s=n.read_shift(16),c=n.read_shift(4),t===1&&c!==n.l)throw new Error("Length mismatch: "+c+" !== "+n.l);t===2&&(i=n.read_shift(16),o=n.read_shift(4));var h=Bn(n,a),x={SystemIdentifier:f};for(var d in h)x[d]=h[d];if(x.FMTID=s,t===1)return x;if(o-n.l==2&&(n.l+=2),n.l!==o)throw new Error("Length mismatch 2: "+n.l+" !== "+o);var p;try{p=Bn(n,null)}catch{}for(d in p)x[d]=p[d];return x.FMTID=[s,i],x}function na(e,a){return e.read_shift(a),null}function Yo(e,a,r){for(var n=[],t=e.l+a;e.l<t;)n.push(r(e,t-e.l));if(t!==e.l)throw new Error("Slurp error");return n}function Ve(e,a){return e.read_shift(a)===1}function Ye(e){return e.read_shift(2,"u")}function vi(e,a){return Yo(e,a,Ye)}function qo(e){var a=e.read_shift(1),r=e.read_shift(1);return r===1?a:a===1}function vt(e,a,r){var n=e.read_shift(r&&r.biff>=12?2:1),t="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(t="dbcs-cont")}else r.biff==12&&(t="wstr");r.biff>=2&&r.biff<=5&&(t="cpstr");var i=n?e.read_shift(n,t):"";return i}function Jo(e){var a=e.read_shift(2),r=e.read_shift(1),n=r&4,t=r&8,s=1+(r&1),i=0,c,o={};t&&(i=e.read_shift(2)),n&&(c=e.read_shift(4));var f=s==2?"dbcs-cont":"sbcs-cont",l=a===0?"":e.read_shift(a,f);return t&&(e.l+=4*i),n&&(e.l+=c),o.t=l,t||(o.raw="<t>"+o.t+"</t>",o.r=o.t),o}function Ta(e,a,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(a,"cpstr");if(r.biff>=12)return e.read_shift(a,"dbcs-cont")}var t=e.read_shift(1);return t===0?n=e.read_shift(a,"sbcs-cont"):n=e.read_shift(a,"dbcs-cont"),n}function gt(e,a,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):Ta(e,n,r)}function Ca(e,a,r){if(r.biff>5)return gt(e,a,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Zo(e){var a=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[a,r]}function Qo(e){var a=e.read_shift(4),r=e.l,n=!1;a>24&&(e.l+=a-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(n=!0),e.l=r);var t=e.read_shift((n?a-24:a)>>1,"utf16le").replace(mr,"");return n&&(e.l+=24),t}function el(e){for(var a=e.read_shift(2),r="";a-- >0;)r+="../";var n=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var t=e.read_shift(4);if(t===0)return r+n.replace(/\\/g,"/");var s=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace(mr,"");return r+i}function rl(e,a){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return Qo(e);case"0303000000000000c000000000000046":return el(e);default:throw new Error("Unsupported Moniker "+r)}}function St(e){var a=e.read_shift(4),r=a>0?e.read_shift(a,"utf16le").replace(mr,""):"";return r}function al(e,a){var r=e.l+a,n=e.read_shift(4);if(n!==2)throw new Error("Unrecognized streamVersion: "+n);var t=e.read_shift(2);e.l+=2;var s,i,c,o,f="",l,h;t&16&&(s=St(e,r-e.l)),t&128&&(i=St(e,r-e.l)),(t&257)===257&&(c=St(e,r-e.l)),(t&257)===1&&(o=rl(e,r-e.l)),t&8&&(f=St(e,r-e.l)),t&32&&(l=e.read_shift(16)),t&64&&(h=W0(e)),e.l=r;var x=i||c||o||"";x&&f&&(x+="#"+f),x||(x="#"+f),t&2&&x.charAt(0)=="/"&&x.charAt(1)!="/"&&(x="file://"+x);var d={Target:x};return l&&(d.guid=l),h&&(d.time=h),s&&(d.Tooltip=s),d}function gi(e){var a=e.read_shift(1),r=e.read_shift(1),n=e.read_shift(1),t=e.read_shift(1);return[a,r,n,t]}function mi(e,a){var r=gi(e);return r[3]=0,r}function Yr(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return{r:a,c:r,ixfe:n}}function tl(e){var a=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:a,flags:r}}function nl(e,a,r){return a===0?"":Ca(e,a,r)}function sl(e,a,r){var n=r.biff>8?4:2,t=e.read_shift(n),s=e.read_shift(n,"i"),i=e.read_shift(n,"i");return[t,s,i]}function _i(e){var a=e.read_shift(2),r=H0(e);return[a,r]}function il(e,a,r){e.l+=4,a-=4;var n=e.l+a,t=vt(e,a,r),s=e.read_shift(2);if(n-=e.l,s!==n)throw new Error("Malformed AddinUdf: padding = "+n+" != "+s);return e.l+=s,t}function Yt(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),t=e.read_shift(2);return{s:{c:n,r:a},e:{c:t,r}}}function Ei(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(1),t=e.read_shift(1);return{s:{c:n,r:a},e:{c:t,r}}}var cl=Ei;function ki(e){e.l+=4;var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,a,n]}function fl(e){var a={};return e.l+=4,e.l+=16,a.fSharedNote=e.read_shift(2),e.l+=4,a}function ol(e){var a={};return e.l+=4,e.cf=e.read_shift(2),a}function fr(e){e.l+=2,e.l+=e.read_shift(2)}var ll={0:fr,4:fr,5:fr,6:fr,7:ol,8:fr,9:fr,10:fr,11:fr,12:fr,13:fl,14:fr,15:fr,16:fr,17:fr,18:fr,19:fr,20:fr,21:ki};function hl(e,a){for(var r=e.l+a,n=[];e.l<r;){var t=e.read_shift(2);e.l-=2;try{n.push(ll[t](e,r-e.l))}catch{return e.l=r,n}}return e.l!=r&&(e.l=r),n}function yt(e,a){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),a-=2,a>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(a>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(a),r}function ul(e,a){return a===0||e.read_shift(2),1200}function xl(e,a,r){if(r.enc)return e.l+=a,"";var n=e.l,t=Ca(e,0,r);return e.read_shift(a+n-e.l),t}function dl(e,a,r){var n=r&&r.biff==8||a==2?e.read_shift(2):(e.l+=a,0);return{fDialog:n&16,fBelow:n&64,fRight:n&128}}function pl(e,a,r){var n=e.read_shift(4),t=e.read_shift(1)&3,s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=vt(e,0,r);return i.length===0&&(i="Sheet1"),{pos:n,hs:t,dt:s,name:i}}function vl(e,a){for(var r=e.l+a,n=e.read_shift(4),t=e.read_shift(4),s=[],i=0;i!=t&&e.l<r;++i)s.push(Jo(e));return s.Count=n,s.Unique=t,s}function gl(e,a){var r={};return r.dsst=e.read_shift(2),e.l+=a-2,r}function ml(e){var a={};a.r=e.read_shift(2),a.c=e.read_shift(2),a.cnt=e.read_shift(2)-a.c;var r=e.read_shift(2);e.l+=4;var n=e.read_shift(1);return e.l+=3,n&7&&(a.level=n&7),n&32&&(a.hidden=!0),n&64&&(a.hpt=r/20),a}function _l(e){var a=tl(e);if(a.type!=2211)throw new Error("Invalid Future Record "+a.type);var r=e.read_shift(4);return r!==0}function El(e){return e.read_shift(2),e.read_shift(4)}function jn(e,a,r){var n=0;r&&r.biff==2||(n=e.read_shift(2));var t=e.read_shift(2);r&&r.biff==2&&(n=1-(t>>15),t&=32767);var s={Unsynced:n&1,DyZero:(n&2)>>1,ExAsc:(n&4)>>2,ExDsc:(n&8)>>3};return[s,t]}function kl(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=e.read_shift(2),o=e.read_shift(2),f=e.read_shift(2);return{Pos:[a,r],Dim:[n,t],Flags:s,CurTab:i,FirstTab:c,Selected:o,TabRatio:f}}function Tl(e,a,r){if(r&&r.biff>=2&&r.biff<5)return{};var n=e.read_shift(2);return{RTL:n&64}}function wl(){}function Al(e,a,r){var n={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return n.name=vt(e,0,r),n}function Fl(e){var a=Yr(e);return a.isst=e.read_shift(4),a}function Sl(e,a,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=e.l+a,t=Yr(e);r.biff==2&&e.l++;var s=gt(e,n-e.l,r);return t.val=s,t}function yl(e,a,r){var n=e.read_shift(2),t=Ca(e,0,r);return[n,t]}var Cl=Ca;function Hn(e,a,r){var n=e.l+a,t=r.biff==8||!r.biff?4:2,s=e.read_shift(t),i=e.read_shift(t),c=e.read_shift(2),o=e.read_shift(2);return e.l=n,{s:{r:s,c},e:{r:i,c:o}}}function Rl(e){var a=e.read_shift(2),r=e.read_shift(2),n=_i(e);return{r:a,c:r,ixfe:n[0],rknum:n[1]}}function Dl(e,a){for(var r=e.l+a-2,n=e.read_shift(2),t=e.read_shift(2),s=[];e.l<r;)s.push(_i(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-t+1)throw new Error("MulRK length mismatch");return{r:n,c:t,C:i,rkrec:s}}function Ol(e,a){for(var r=e.l+a-2,n=e.read_shift(2),t=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-t+1)throw new Error("MulBlank length mismatch");return{r:n,c:t,C:i,ixfe:s}}function Nl(e,a,r,n){var t={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),o=e.read_shift(2);return t.patternType=Ro[c>>26],n.cellStyles&&(t.alc=s&7,t.fWrap=s>>3&1,t.alcV=s>>4&7,t.fJustLast=s>>7&1,t.trot=s>>8&255,t.cIndent=s>>16&15,t.fShrinkToFit=s>>20&1,t.iReadOrder=s>>22&2,t.fAtrNum=s>>26&1,t.fAtrFnt=s>>27&1,t.fAtrAlc=s>>28&1,t.fAtrBdr=s>>29&1,t.fAtrPat=s>>30&1,t.fAtrProt=s>>31&1,t.dgLeft=i&15,t.dgRight=i>>4&15,t.dgTop=i>>8&15,t.dgBottom=i>>12&15,t.icvLeft=i>>16&127,t.icvRight=i>>23&127,t.grbitDiag=i>>30&3,t.icvTop=c&127,t.icvBottom=c>>7&127,t.icvDiag=c>>14&127,t.dgDiag=c>>21&15,t.icvFore=o&127,t.icvBack=o>>7&127,t.fsxButton=o>>14&1),t}function Il(e,a,r){var n={};return n.ifnt=e.read_shift(2),n.numFmtId=e.read_shift(2),n.flags=e.read_shift(2),n.fStyle=n.flags>>2&1,a-=6,n.data=Nl(e,a,n.fStyle,r),n}function bl(e){e.l+=4;var a=[e.read_shift(2),e.read_shift(2)];if(a[0]!==0&&a[0]--,a[1]!==0&&a[1]--,a[0]>7||a[1]>7)throw new Error("Bad Gutters: "+a.join("|"));return a}function Vn(e,a,r){var n=Yr(e);(r.biff==2||a==9)&&++e.l;var t=qo(e);return n.val=t,n.t=t===!0||t===!1?"b":"e",n}function Pl(e,a,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=Yr(e),t=lr(e);return n.val=t,n}var Wn=nl;function Ll(e,a,r){var n=e.l+a,t=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,s==1025||s==14849)return[s,t];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Ta(e,s),c=[];n>e.l;)c.push(gt(e));return[s,t,i,c]}function Gn(e,a,r){var n=e.read_shift(2),t,s={fBuiltIn:n&1,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return r.sbcch===14849&&(t=il(e,a-2,r)),s.body=t||e.read_shift(a-2),typeof t=="string"&&(s.Name=t),s}var Ml=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Xn(e,a,r){var n=e.l+a,t=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(r&&r.biff==2?1:2),o=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),o=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var f=Ta(e,i,r);t&32&&(f=Ml[f.charCodeAt(0)]);var l=n-e.l;r&&r.biff==2&&--l;var h=n==e.l||c===0||!(l>0)?[]:mx(e,l,r,c);return{chKey:s,Name:f,itab:o,rgce:h}}function Ti(e,a,r){if(r.biff<8)return Bl(e,a,r);for(var n=[],t=e.l+a,s=e.read_shift(r.biff>8?4:2);s--!==0;)n.push(sl(e,r.biff>8?12:6,r));if(e.l!=t)throw new Error("Bad ExternSheet: "+e.l+" != "+t);return n}function Bl(e,a,r){e[e.l+1]==3&&e[e.l]++;var n=vt(e,a,r);return n.charCodeAt(0)==3?n.slice(1):n}function Ul(e,a,r){if(r.biff<8){e.l+=a;return}var n=e.read_shift(2),t=e.read_shift(2),s=Ta(e,n,r),i=Ta(e,t,r);return[s,i]}function jl(e,a,r){var n=Ei(e);e.l++;var t=e.read_shift(1);return a-=8,[_x(e,a,r),t,n]}function $n(e,a,r){var n=cl(e);switch(r.biff){case 2:e.l++,a-=7;break;case 3:case 4:e.l+=2,a-=8;break;default:e.l+=6,a-=12}return[n,vx(e,a,r)]}function Hl(e){var a=e.read_shift(4)!==0,r=e.read_shift(4)!==0,n=e.read_shift(4);return[a,r,n]}function Vl(e,a,r){if(!(r.biff<8)){var n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=Ca(e,0,r);return r.biff<8&&e.read_shift(1),[{r:n,c:t},c,i,s]}}function Wl(e,a,r){return Vl(e,a,r)}function Gl(e,a){for(var r=[],n=e.read_shift(2);n--;)r.push(Yt(e));return r}function Xl(e,a,r){if(r&&r.biff<8)return zl(e,a,r);var n=ki(e),t=hl(e,a-22,n[1]);return{cmo:n,ft:t}}var $l={8:function(e,a){var r=e.l+a;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var t=e.read_shift(1);return e.l+=t,e.l=r,{fmt:n}}};function zl(e,a,r){e.l+=4;var n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,a-=36;var i=[];return i.push(($l[n]||xr)(e,a,r)),{cmo:[t,n,s],ft:i}}function Kl(e,a,r){var n=e.l,t="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1],i;[0,5,7,11,12,14].indexOf(s)==-1?e.l+=6:i=Zo(e,6,r);var c=e.read_shift(2);e.read_shift(2),Ye(e,2);var o=e.read_shift(2);e.l+=o;for(var f=1;f<e.lens.length-1;++f){if(e.l-n!=e.lens[f])throw new Error("TxO: bad continue record");var l=e[e.l],h=Ta(e,e.lens[f+1]-e.lens[f]-1);if(t+=h,t.length>=(l?c:2*c))break}if(t.length!==c&&t.length!==c*2)throw new Error("cchText: "+c+" != "+t.length);return e.l=n+a,{t}}catch{return e.l=n+a,{t}}}function Yl(e,a){var r=Yt(e);e.l+=16;var n=al(e,a-24);return[r,n]}function ql(e,a){e.read_shift(2);var r=Yt(e),n=e.read_shift((a-10)/2,"dbcs-cont");return n=n.replace(mr,""),[r,n]}function Jl(e){var a=[0,0],r;return r=e.read_shift(2),a[0]=Pn[r]||r,r=e.read_shift(2),a[1]=Pn[r]||r,a}function Zl(e){for(var a=e.read_shift(2),r=[];a-- >0;)r.push(mi(e));return r}function Ql(e){for(var a=e.read_shift(2),r=[];a-- >0;)r.push(mi(e));return r}function eh(e){e.l+=2;var a={cxfs:0,crc:0};return a.cxfs=e.read_shift(2),a.crc=e.read_shift(4),a}function wi(e,a,r){if(!r.cellStyles)return xr(e,a);var n=r&&r.biff>=12?4:2,t=e.read_shift(n),s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var f={s:t,e:s,w:i,ixfe:c,flags:o};return(r.biff>=5||!r.biff)&&(f.level=o>>8&7),f}function rh(e,a){var r={};return a<32||(e.l+=16,r.header=lr(e),r.footer=lr(e),e.l+=2),r}function ah(e,a,r){var n={area:!1};if(r.biff!=5)return e.l+=a,n;var t=e.read_shift(1);return e.l+=3,t&16&&(n.area=!0),n}var th=Yr,nh=vi,sh=gt;function ih(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),t={fmt:a,env:r,len:n,data:e.slice(e.l,e.l+n)};return e.l+=n,t}function ch(e,a,r){r.biffguess&&r.biff==5&&(r.biff=2);var n=Yr(e);++e.l;var t=Ca(e,a-7,r);return n.t="str",n.val=t,n}function fh(e){var a=Yr(e);++e.l;var r=lr(e);return a.t="n",a.val=r,a}function oh(e){var a=Yr(e);++e.l;var r=e.read_shift(2);return a.t="n",a.val=r,a}function lh(e){var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,"sbcs-cont")}function hh(e,a){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=a-13}function uh(e,a,r){var n=e.l+a,t=Yr(e),s=e.read_shift(2),i=Ta(e,s,r);return e.l=n,t.t="str",t.val=i,t}var xh=[2,3,48,49,131,139,140,245],zn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},a=N0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(c,o){var f=[],l=ha(1);switch(o.type){case"base64":l=Pr(Sr(c));break;case"binary":l=Pr(c);break;case"buffer":case"array":l=c;break}ar(l,0);var h=l.read_shift(1),x=!!(h&136),d=!1,p=!1;switch(h){case 2:break;case 3:break;case 48:d=!0,x=!0;break;case 49:d=!0,x=!0;break;case 131:break;case 139:break;case 140:p=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+h.toString(16))}var u=0,_=521;h==2&&(u=l.read_shift(2)),l.l+=3,h!=2&&(u=l.read_shift(4)),u>1048576&&(u=1e6),h!=2&&(_=l.read_shift(2));var E=l.read_shift(2),R=o.codepage||1252;h!=2&&(l.l+=16,l.read_shift(1),l[l.l]!==0&&(R=e[l[l.l]]),l.l+=1,l.l+=2),p&&(l.l+=36);for(var g=[],b={},H=Math.min(l.length,h==2?521:_-10-(d?264:0)),U=p?32:11;l.l<H&&l[l.l]!=13;)switch(b={},b.name=it.utils.decode(R,l.slice(l.l,l.l+U)).replace(/[\u0000\r\n].*$/g,""),l.l+=U,b.type=String.fromCharCode(l.read_shift(1)),h!=2&&!p&&(b.offset=l.read_shift(4)),b.len=l.read_shift(1),h==2&&(b.offset=l.read_shift(2)),b.dec=l.read_shift(1),b.name.length&&g.push(b),h!=2&&(l.l+=p?13:14),b.type){case"B":(!d||b.len!=8)&&o.WTF&&console.log("Skipping "+b.name+":"+b.type);break;case"G":case"P":o.WTF&&console.log("Skipping "+b.name+":"+b.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+b.type)}if(l[l.l]!==13&&(l.l=_-1),l.read_shift(1)!==13)throw new Error("DBF Terminator not found "+l.l+" "+l[l.l]);l.l=_;var S=0,k=0;for(f[0]=[],k=0;k!=g.length;++k)f[0][k]=g[k].name;for(;u-- >0;){if(l[l.l]===42){l.l+=E;continue}for(++l.l,f[++S]=[],k=0,k=0;k!=g.length;++k){var v=l.slice(l.l,l.l+g[k].len);l.l+=g[k].len,ar(v,0);var I=it.utils.decode(R,v);switch(g[k].type){case"C":I.trim().length&&(f[S][k]=I.replace(/\s+$/,""));break;case"D":I.length===8?f[S][k]=new Date(+I.slice(0,4),+I.slice(4,6)-1,+I.slice(6,8)):f[S][k]=I;break;case"F":f[S][k]=parseFloat(I.trim());break;case"+":case"I":f[S][k]=p?v.read_shift(-4,"i")^2147483648:v.read_shift(4,"i");break;case"L":switch(I.trim().toUpperCase()){case"Y":case"T":f[S][k]=!0;break;case"N":case"F":f[S][k]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+I+"|")}break;case"M":if(!x)throw new Error("DBF Unexpected MEMO for type "+h.toString(16));f[S][k]="##MEMO##"+(p?parseInt(I.trim(),10):v.read_shift(4));break;case"N":I=I.replace(/\u0000/g,"").trim(),I&&I!="."&&(f[S][k]=+I||0);break;case"@":f[S][k]=new Date(v.read_shift(-8,"f")-621356832e5);break;case"T":f[S][k]=new Date((v.read_shift(4)-2440588)*864e5+v.read_shift(4));break;case"Y":f[S][k]=v.read_shift(4,"i")/1e4+v.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":f[S][k]=-v.read_shift(-8,"f");break;case"B":if(d&&g[k].len==8){f[S][k]=v.read_shift(8,"f");break}case"G":case"P":v.l+=g[k].len;break;case"0":if(g[k].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+g[k].type)}}}if(h!=2&&l.l<l.length&&l[l.l++]!=26)throw new Error("DBF EOF Marker missing "+(l.l-1)+" of "+l.length+" "+l[l.l-1].toString(16));return o&&o.sheetRows&&(f=f.slice(0,o.sheetRows)),o.DBF=g,f}function n(c,o){var f=o||{};f.dateNF||(f.dateNF="yyyymmdd");var l=ja(r(c,f),f);return l["!cols"]=f.DBF.map(function(h){return{wch:h.len,DBF:h}}),delete f.DBF,l}function t(c,o){try{return da(n(c,o),o)}catch(f){if(o&&o.WTF)throw f}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function i(c,o){var f=o||{};if(+f.codepage>=0&&Lr(+f.codepage),f.type=="string")throw new Error("Cannot write DBF to JS string");var l=x0(),h=w0(c,{header:1,raw:!0,cellDates:!0}),x=h[0],d=h.slice(1),p=c["!cols"]||[],u=0,_=0,E=0,R=1;for(u=0;u<x.length;++u){if(((p[u]||{}).DBF||{}).name){x[u]=p[u].DBF.name,++E;continue}if(x[u]!=null){if(++E,typeof x[u]=="number"&&(x[u]=x[u].toString(10)),typeof x[u]!="string")throw new Error("DBF Invalid column name "+x[u]+" |"+typeof x[u]+"|");if(x.indexOf(x[u])!==u){for(_=0;_<1024;++_)if(x.indexOf(x[u]+"_"+_)==-1){x[u]+="_"+_;break}}}}var g=He(c["!ref"]),b=[],H=[],U=[];for(u=0;u<=g.e.c-g.s.c;++u){var S="",k="",v=0,I=[];for(_=0;_<d.length;++_)d[_][u]!=null&&I.push(d[_][u]);if(I.length==0||x[u]==null){b[u]="?";continue}for(_=0;_<I.length;++_){switch(typeof I[_]){case"number":k="B";break;case"string":k="C";break;case"boolean":k="L";break;case"object":k=I[_]instanceof Date?"D":"C";break;default:k="C"}v=Math.max(v,String(I[_]).length),S=S&&S!=k?"C":k}v>250&&(v=250),k=((p[u]||{}).DBF||{}).type,k=="C"&&p[u].DBF.len>v&&(v=p[u].DBF.len),S=="B"&&k=="N"&&(S="N",U[u]=p[u].DBF.dec,v=p[u].DBF.len),H[u]=S=="C"||k=="N"?v:s[S]||0,R+=H[u],b[u]=S}var P=l.next(32);for(P.write_shift(4,318902576),P.write_shift(4,d.length),P.write_shift(2,296+32*E),P.write_shift(2,R),u=0;u<4;++u)P.write_shift(4,0);for(P.write_shift(4,0|(+a[Ts]||3)<<8),u=0,_=0;u<x.length;++u)if(x[u]!=null){var N=l.next(32),K=(x[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);N.write_shift(1,K,"sbcs"),N.write_shift(1,b[u]=="?"?"C":b[u],"sbcs"),N.write_shift(4,_),N.write_shift(1,H[u]||s[b[u]]||0),N.write_shift(1,U[u]||0),N.write_shift(1,2),N.write_shift(4,0),N.write_shift(1,0),N.write_shift(4,0),N.write_shift(4,0),_+=H[u]||s[b[u]]||0}var Z=l.next(264);for(Z.write_shift(4,13),u=0;u<65;++u)Z.write_shift(4,0);for(u=0;u<d.length;++u){var J=l.next(R);for(J.write_shift(1,0),_=0;_<x.length;++_)if(x[_]!=null)switch(b[_]){case"L":J.write_shift(1,d[u][_]==null?63:d[u][_]?84:70);break;case"B":J.write_shift(8,d[u][_]||0,"f");break;case"N":var se="0";for(typeof d[u][_]=="number"&&(se=d[u][_].toFixed(U[_]||0)),E=0;E<H[_]-se.length;++E)J.write_shift(1,32);J.write_shift(1,se,"sbcs");break;case"D":d[u][_]?(J.write_shift(4,("0000"+d[u][_].getFullYear()).slice(-4),"sbcs"),J.write_shift(2,("00"+(d[u][_].getMonth()+1)).slice(-2),"sbcs"),J.write_shift(2,("00"+d[u][_].getDate()).slice(-2),"sbcs")):J.write_shift(8,"00000000","sbcs");break;case"C":var ee=String(d[u][_]!=null?d[u][_]:"").slice(0,H[_]);for(J.write_shift(1,ee,"sbcs"),E=0;E<H[_]-ee.length;++E)J.write_shift(1,32);break}}return l.next(1).write_shift(1,26),l.end()}return{to_workbook:t,to_sheet:n,from_sheet:i}}(),dh=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},a=new RegExp("\x1BN("+zr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(x,d){var p=e[d];return typeof p=="number"?on(p):p},n=function(x,d,p){var u=d.charCodeAt(0)-32<<4|p.charCodeAt(0)-48;return u==59?x:on(u)};e["|"]=254;function t(x,d){switch(d.type){case"base64":return s(Sr(x),d);case"binary":return s(x,d);case"buffer":return s(Fe&&Buffer.isBuffer(x)?x.toString("binary"):Aa(x),d);case"array":return s(ka(x),d)}throw new Error("Unrecognized type "+d.type)}function s(x,d){var p=x.split(/[\n\r]+/),u=-1,_=-1,E=0,R=0,g=[],b=[],H=null,U={},S=[],k=[],v=[],I=0,P;for(+d.codepage>=0&&Lr(+d.codepage);E!==p.length;++E){I=0;var N=p[E].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(a,r),K=N.replace(/;;/g,"\0").split(";").map(function(L){return L.replace(/\u0000/g,";")}),Z=K[0],J;if(N.length>0)switch(Z){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":K[1].charAt(0)=="P"&&b.push(N.slice(3).replace(/;;/g,";"));break;case"C":var se=!1,ee=!1,ve=!1,j=!1,he=-1,ne=-1;for(R=1;R<K.length;++R)switch(K[R].charAt(0)){case"A":break;case"X":_=parseInt(K[R].slice(1))-1,ee=!0;break;case"Y":for(u=parseInt(K[R].slice(1))-1,ee||(_=0),P=g.length;P<=u;++P)g[P]=[];break;case"K":J=K[R].slice(1),J.charAt(0)==='"'?J=J.slice(1,J.length-1):J==="TRUE"?J=!0:J==="FALSE"?J=!1:isNaN(Vr(J))?isNaN(La(J).getDate())||(J=rr(J)):(J=Vr(J),H!==null&&Ba(H)&&(J=Kt(J))),se=!0;break;case"E":j=!0;var F=ba(K[R].slice(1),{r:u,c:_});g[u][_]=[g[u][_],F];break;case"S":ve=!0,g[u][_]=[g[u][_],"S5S"];break;case"G":break;case"R":he=parseInt(K[R].slice(1))-1;break;case"C":ne=parseInt(K[R].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+N)}if(se&&(g[u][_]&&g[u][_].length==2?g[u][_][0]=J:g[u][_]=J,H=null),ve){if(j)throw new Error("SYLK shared formula cannot have own formula");var B=he>-1&&g[he][ne];if(!B||!B[1])throw new Error("SYLK shared formula cannot find base");g[u][_][1]=Ii(B[1],{r:u-he,c:_-ne})}break;case"F":var M=0;for(R=1;R<K.length;++R)switch(K[R].charAt(0)){case"X":_=parseInt(K[R].slice(1))-1,++M;break;case"Y":for(u=parseInt(K[R].slice(1))-1,P=g.length;P<=u;++P)g[P]=[];break;case"M":I=parseInt(K[R].slice(1))/20;break;case"F":break;case"G":break;case"P":H=b[parseInt(K[R].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(v=K[R].slice(1).split(" "),P=parseInt(v[0],10);P<=parseInt(v[1],10);++P)I=parseInt(v[2],10),k[P-1]=I===0?{hidden:!0}:{wch:I},Ma(k[P-1]);break;case"C":_=parseInt(K[R].slice(1))-1,k[_]||(k[_]={});break;case"R":u=parseInt(K[R].slice(1))-1,S[u]||(S[u]={}),I>0?(S[u].hpt=I,S[u].hpx=ut(I)):I===0&&(S[u].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+N)}M<1&&(H=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+N)}}return S.length>0&&(U["!rows"]=S),k.length>0&&(U["!cols"]=k),d&&d.sheetRows&&(g=g.slice(0,d.sheetRows)),[g,U]}function i(x,d){var p=t(x,d),u=p[0],_=p[1],E=ja(u,d);return zr(_).forEach(function(R){E[R]=_[R]}),E}function c(x,d){return da(i(x,d),d)}function o(x,d,p,u){var _="C;Y"+(p+1)+";X"+(u+1)+";K";switch(x.t){case"n":_+=x.v||0,x.f&&!x.F&&(_+=";E"+e1(x.f,{r:p,c:u}));break;case"b":_+=x.v?"TRUE":"FALSE";break;case"e":_+=x.w||x.v;break;case"d":_+='"'+(x.w||x.v)+'"';break;case"s":_+='"'+x.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return _}function f(x,d){d.forEach(function(p,u){var _="F;W"+(u+1)+" "+(u+1)+" ";p.hidden?_+="0":(typeof p.width=="number"&&!p.wpx&&(p.wpx=Vt(p.width)),typeof p.wpx=="number"&&!p.wch&&(p.wch=Wt(p.wpx)),typeof p.wch=="number"&&(_+=Math.round(p.wch))),_.charAt(_.length-1)!=" "&&x.push(_)})}function l(x,d){d.forEach(function(p,u){var _="F;";p.hidden?_+="M0;":p.hpt?_+="M"+20*p.hpt+";":p.hpx&&(_+="M"+20*Di(p.hpx)+";"),_.length>2&&x.push(_+"R"+(u+1))})}function h(x,d){var p=["ID;PWXL;N;E"],u=[],_=He(x["!ref"]),E,R=Array.isArray(x),g=`\r
`;p.push("P;PGeneral"),p.push("F;P0;DG0G8;M255"),x["!cols"]&&f(p,x["!cols"]),x["!rows"]&&l(p,x["!rows"]),p.push("B;Y"+(_.e.r-_.s.r+1)+";X"+(_.e.c-_.s.c+1)+";D"+[_.s.c,_.s.r,_.e.c,_.e.r].join(" "));for(var b=_.s.r;b<=_.e.r;++b)for(var H=_.s.c;H<=_.e.c;++H){var U=Ee({r:b,c:H});E=R?(x[b]||[])[H]:x[U],!(!E||E.v==null&&(!E.f||E.F))&&u.push(o(E,x,b,H))}return p.join(g)+g+u.join(g)+g+"E"+g}return{to_workbook:c,to_sheet:i,from_sheet:h}}(),ph=function(){function e(s,i){switch(i.type){case"base64":return a(Sr(s),i);case"binary":return a(s,i);case"buffer":return a(Fe&&Buffer.isBuffer(s)?s.toString("binary"):Aa(s),i);case"array":return a(ka(s),i)}throw new Error("Unrecognized type "+i.type)}function a(s,i){for(var c=s.split(`
`),o=-1,f=-1,l=0,h=[];l!==c.length;++l){if(c[l].trim()==="BOT"){h[++o]=[],f=0;continue}if(!(o<0)){var x=c[l].trim().split(","),d=x[0],p=x[1];++l;for(var u=c[l]||"";(u.match(/["]/g)||[]).length&1&&l<c.length-1;)u+=`
`+c[++l];switch(u=u.trim(),+d){case-1:if(u==="BOT"){h[++o]=[],f=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?h[o][f]=!0:u==="FALSE"?h[o][f]=!1:isNaN(Vr(p))?isNaN(La(p).getDate())?h[o][f]=p:h[o][f]=rr(p):h[o][f]=Vr(p),++f;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),h[o][f++]=u!==""?u:null;break}if(u==="EOD")break}}return i&&i.sheetRows&&(h=h.slice(0,i.sheetRows)),h}function r(s,i){return ja(e(s,i),i)}function n(s,i){return da(r(s,i),i)}var t=function(){var s=function(o,f,l,h,x){o.push(f),o.push(l+","+h),o.push('"'+x.replace(/"/g,'""')+'"')},i=function(o,f,l,h){o.push(f+","+l),o.push(f==1?'"'+h.replace(/"/g,'""')+'"':h)};return function(o){var f=[],l=He(o["!ref"]),h,x=Array.isArray(o);s(f,"TABLE",0,1,"sheetjs"),s(f,"VECTORS",0,l.e.r-l.s.r+1,""),s(f,"TUPLES",0,l.e.c-l.s.c+1,""),s(f,"DATA",0,0,"");for(var d=l.s.r;d<=l.e.r;++d){i(f,-1,0,"BOT");for(var p=l.s.c;p<=l.e.c;++p){var u=Ee({r:d,c:p});if(h=x?(o[d]||[])[p]:o[u],!h){i(f,1,0,"");continue}switch(h.t){case"n":var _=h.w;!_&&h.v!=null&&(_=h.v),_==null?h.f&&!h.F?i(f,1,0,"="+h.f):i(f,1,0,""):i(f,0,_,"V");break;case"b":i(f,0,h.v?1:0,h.v?"TRUE":"FALSE");break;case"s":i(f,1,0,isNaN(h.v)?h.v:'="'+h.v+'"');break;case"d":h.w||(h.w=Or(h.z||Te[14],_r(rr(h.v)))),i(f,0,h.w,"V");break;default:i(f,1,0,"")}}}i(f,-1,0,"EOD");var E=`\r
`,R=f.join(E);return R}}();return{to_workbook:n,to_sheet:r,from_sheet:t}}(),vh=function(){function e(h){return h.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function a(h){return h.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(h,x){for(var d=h.split(`
`),p=-1,u=-1,_=0,E=[];_!==d.length;++_){var R=d[_].trim().split(":");if(R[0]==="cell"){var g=gr(R[1]);if(E.length<=g.r)for(p=E.length;p<=g.r;++p)E[p]||(E[p]=[]);switch(p=g.r,u=g.c,R[2]){case"t":E[p][u]=e(R[3]);break;case"v":E[p][u]=+R[3];break;case"vtf":var b=R[R.length-1];case"vtc":switch(R[3]){case"nl":E[p][u]=!!+R[4];break;default:E[p][u]=+R[4];break}R[2]=="vtf"&&(E[p][u]=[E[p][u],b])}}}return x&&x.sheetRows&&(E=E.slice(0,x.sheetRows)),E}function n(h,x){return ja(r(h,x),x)}function t(h,x){return da(n(h,x),x)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,c=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function f(h){if(!h||!h["!ref"])return"";for(var x=[],d=[],p,u="",_=Ua(h["!ref"]),E=Array.isArray(h),R=_.s.r;R<=_.e.r;++R)for(var g=_.s.c;g<=_.e.c;++g)if(u=Ee({r:R,c:g}),p=E?(h[R]||[])[g]:h[u],!(!p||p.v==null||p.t==="z")){switch(d=["cell",u,"t"],p.t){case"s":case"str":d.push(a(p.v));break;case"n":p.f?(d[2]="vtf",d[3]="n",d[4]=p.v,d[5]=a(p.f)):(d[2]="v",d[3]=p.v);break;case"b":d[2]="vt"+(p.f?"f":"c"),d[3]="nl",d[4]=p.v?"1":"0",d[5]=a(p.f||(p.v?"TRUE":"FALSE"));break;case"d":var b=_r(rr(p.v));d[2]="vtc",d[3]="nd",d[4]=""+b,d[5]=p.w||Or(p.z||Te[14],b);break;case"e":continue}x.push(d.join(":"))}return x.push("sheet:c:"+(_.e.c-_.s.c+1)+":r:"+(_.e.r-_.s.r+1)+":tvf:1"),x.push("valueformat:1:text-wiki"),x.join(`
`)}function l(h){return[s,i,c,i,f(h),o].join(`
`)}return{to_workbook:t,to_sheet:n,from_sheet:l}}(),lt=function(){function e(l,h,x,d,p){p.raw?h[x][d]=l:l===""||(l==="TRUE"?h[x][d]=!0:l==="FALSE"?h[x][d]=!1:isNaN(Vr(l))?isNaN(La(l).getDate())?h[x][d]=l:h[x][d]=rr(l):h[x][d]=Vr(l))}function a(l,h){var x=h||{},d=[];if(!l||l.length===0)return d;for(var p=l.split(/[\r\n]/),u=p.length-1;u>=0&&p[u].length===0;)--u;for(var _=10,E=0,R=0;R<=u;++R)E=p[R].indexOf(" "),E==-1?E=p[R].length:E++,_=Math.max(_,E);for(R=0;R<=u;++R){d[R]=[];var g=0;for(e(p[R].slice(0,_).trim(),d,R,g,x),g=1;g<=(p[R].length-_)/10+1;++g)e(p[R].slice(_+(g-1)*10,_+g*10).trim(),d,R,g,x)}return x.sheetRows&&(d=d.slice(0,x.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function t(l){for(var h={},x=!1,d=0,p=0;d<l.length;++d)(p=l.charCodeAt(d))==34?x=!x:!x&&p in r&&(h[p]=(h[p]||0)+1);p=[];for(d in h)Object.prototype.hasOwnProperty.call(h,d)&&p.push([h[d],d]);if(!p.length){h=n;for(d in h)Object.prototype.hasOwnProperty.call(h,d)&&p.push([h[d],d])}return p.sort(function(u,_){return u[0]-_[0]||n[u[1]]-n[_[1]]}),r[p.pop()[1]]||44}function s(l,h){var x=h||{},d="",p=x.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};l.slice(0,4)=="sep="?l.charCodeAt(5)==13&&l.charCodeAt(6)==10?(d=l.charAt(4),l=l.slice(7)):l.charCodeAt(5)==13||l.charCodeAt(5)==10?(d=l.charAt(4),l=l.slice(6)):d=t(l.slice(0,1024)):x&&x.FS?d=x.FS:d=t(l.slice(0,1024));var _=0,E=0,R=0,g=0,b=0,H=d.charCodeAt(0),U=!1,S=0,k=l.charCodeAt(0);l=l.replace(/\r\n/mg,`
`);var v=x.dateNF!=null?Sf(x.dateNF):null;function I(){var P=l.slice(g,b),N={};if(P.charAt(0)=='"'&&P.charAt(P.length-1)=='"'&&(P=P.slice(1,-1).replace(/""/g,'"')),P.length===0)N.t="z";else if(x.raw)N.t="s",N.v=P;else if(P.trim().length===0)N.t="s",N.v=P;else if(P.charCodeAt(0)==61)P.charCodeAt(1)==34&&P.charCodeAt(P.length-1)==34?(N.t="s",N.v=P.slice(2,-1).replace(/""/g,'"')):a1(P)?(N.t="n",N.f=P.slice(1)):(N.t="s",N.v=P);else if(P=="TRUE")N.t="b",N.v=!0;else if(P=="FALSE")N.t="b",N.v=!1;else if(!isNaN(R=Vr(P)))N.t="n",x.cellText!==!1&&(N.w=P),N.v=R;else if(!isNaN(La(P).getDate())||v&&P.match(v)){N.z=x.dateNF||Te[14];var K=0;v&&P.match(v)&&(P=yf(P,x.dateNF,P.match(v)||[]),K=1),x.cellDates?(N.t="d",N.v=rr(P,K)):(N.t="n",N.v=_r(rr(P,K))),x.cellText!==!1&&(N.w=Or(N.z,N.v instanceof Date?_r(N.v):N.v)),x.cellNF||delete N.z}else N.t="s",N.v=P;if(N.t=="z"||(x.dense?(p[_]||(p[_]=[]),p[_][E]=N):p[Ee({c:E,r:_})]=N),g=b+1,k=l.charCodeAt(g),u.e.c<E&&(u.e.c=E),u.e.r<_&&(u.e.r=_),S==H)++E;else if(E=0,++_,x.sheetRows&&x.sheetRows<=_)return!0}e:for(;b<l.length;++b)switch(S=l.charCodeAt(b)){case 34:k===34&&(U=!U);break;case H:case 10:case 13:if(!U&&I())break e;break}return b-g>0&&I(),p["!ref"]=ye(u),p}function i(l,h){return!(h&&h.PRN)||h.FS||l.slice(0,4)=="sep="||l.indexOf("	")>=0||l.indexOf(",")>=0||l.indexOf(";")>=0?s(l,h):ja(a(l,h),h)}function c(l,h){var x="",d=h.type=="string"?[0,0,0,0]:J0(l,h);switch(h.type){case"base64":x=Sr(l);break;case"binary":x=l;break;case"buffer":h.codepage==65001?x=l.toString("utf8"):h.codepage&&typeof it<"u"||(x=Fe&&Buffer.isBuffer(l)?l.toString("binary"):Aa(l));break;case"array":x=ka(l);break;case"string":x=l;break;default:throw new Error("Unrecognized type "+h.type)}return d[0]==239&&d[1]==187&&d[2]==191?x=Ne(x.slice(3)):h.type!="string"&&h.type!="buffer"&&h.codepage==65001?x=Ne(x):h.type=="binary"&&typeof it<"u",x.slice(0,19)=="socialcalc:version:"?vh.to_sheet(h.type=="string"?x:Ne(x),h):i(x,h)}function o(l,h){return da(c(l,h),h)}function f(l){for(var h=[],x=He(l["!ref"]),d,p=Array.isArray(l),u=x.s.r;u<=x.e.r;++u){for(var _=[],E=x.s.c;E<=x.e.c;++E){var R=Ee({r:u,c:E});if(d=p?(l[u]||[])[E]:l[R],!d||d.v==null){_.push("          ");continue}for(var g=(d.w||(aa(d),d.w)||"").slice(0,10);g.length<10;)g+=" ";_.push(g+(E===0?" ":""))}h.push(_.join(""))}return h.join(`
`)}return{to_workbook:o,to_sheet:c,from_sheet:f}}();function gh(e,a){var r=a||{},n=!!r.WTF;r.WTF=!0;try{var t=dh.to_workbook(e,r);return r.WTF=n,t}catch(s){if(r.WTF=n,!s.message.match(/SYLK bad record ID/)&&n)throw s;return lt.to_workbook(e,a)}}var at=function(){function e(F,B,M){if(F){ar(F,F.l||0);for(var L=M.Enum||he;F.l<F.length;){var z=F.read_shift(2),ae=L[z]||L[65535],re=F.read_shift(2),Q=F.l+re,Y=ae.f&&ae.f(F,re,M);if(F.l=Q,B(Y,ae,z))return}}}function a(F,B){switch(B.type){case"base64":return r(Pr(Sr(F)),B);case"binary":return r(Pr(F),B);case"buffer":case"array":return r(F,B)}throw"Unsupported type "+B.type}function r(F,B){if(!F)return F;var M=B||{},L=M.dense?[]:{},z="Sheet1",ae="",re=0,Q={},Y=[],ie=[],y={s:{r:0,c:0},e:{r:0,c:0}},me=M.sheetRows||0;if(F[2]==0&&(F[3]==8||F[3]==9)&&F.length>=16&&F[14]==5&&F[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(F[2]==2)M.Enum=he,e(F,function(fe,de,Ae){switch(Ae){case 0:M.vers=fe,fe>=4096&&(M.qpro=!0);break;case 6:y=fe;break;case 204:fe&&(ae=fe);break;case 222:ae=fe;break;case 15:case 51:M.qpro||(fe[1].v=fe[1].v.slice(1));case 13:case 14:case 16:Ae==14&&(fe[2]&112)==112&&(fe[2]&15)>1&&(fe[2]&15)<15&&(fe[1].z=M.dateNF||Te[14],M.cellDates&&(fe[1].t="d",fe[1].v=Kt(fe[1].v))),M.qpro&&fe[3]>re&&(L["!ref"]=ye(y),Q[z]=L,Y.push(z),L=M.dense?[]:{},y={s:{r:0,c:0},e:{r:0,c:0}},re=fe[3],z=ae||"Sheet"+(re+1),ae="");var qe=M.dense?(L[fe[0].r]||[])[fe[0].c]:L[Ee(fe[0])];if(qe){qe.t=fe[1].t,qe.v=fe[1].v,fe[1].z!=null&&(qe.z=fe[1].z),fe[1].f!=null&&(qe.f=fe[1].f);break}M.dense?(L[fe[0].r]||(L[fe[0].r]=[]),L[fe[0].r][fe[0].c]=fe[1]):L[Ee(fe[0])]=fe[1];break}},M);else if(F[2]==26||F[2]==14)M.Enum=ne,F[2]==14&&(M.qpro=!0,F.l=0),e(F,function(fe,de,Ae){switch(Ae){case 204:z=fe;break;case 22:fe[1].v=fe[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(fe[3]>re&&(L["!ref"]=ye(y),Q[z]=L,Y.push(z),L=M.dense?[]:{},y={s:{r:0,c:0},e:{r:0,c:0}},re=fe[3],z="Sheet"+(re+1)),me>0&&fe[0].r>=me)break;M.dense?(L[fe[0].r]||(L[fe[0].r]=[]),L[fe[0].r][fe[0].c]=fe[1]):L[Ee(fe[0])]=fe[1],y.e.c<fe[0].c&&(y.e.c=fe[0].c),y.e.r<fe[0].r&&(y.e.r=fe[0].r);break;case 27:fe[14e3]&&(ie[fe[14e3][0]]=fe[14e3][1]);break;case 1537:ie[fe[0]]=fe[1],fe[0]==re&&(z=fe[1]);break}},M);else throw new Error("Unrecognized LOTUS BOF "+F[2]);if(L["!ref"]=ye(y),Q[ae||z]=L,Y.push(ae||z),!ie.length)return{SheetNames:Y,Sheets:Q};for(var pe={},ge=[],le=0;le<ie.length;++le)Q[Y[le]]?(ge.push(ie[le]||Y[le]),pe[ie[le]]=Q[ie[le]]||Q[Y[le]]):(ge.push(ie[le]),pe[ie[le]]={"!ref":"A1"});return{SheetNames:ge,Sheets:pe}}function n(F,B){var M=B||{};if(+M.codepage>=0&&Lr(+M.codepage),M.type=="string")throw new Error("Cannot write WK1 to JS string");var L=x0(),z=He(F["!ref"]),ae=Array.isArray(F),re=[];br(L,0,s(1030)),br(L,6,o(z));for(var Q=Math.min(z.e.r,8191),Y=z.s.r;Y<=Q;++Y)for(var ie=nr(Y),y=z.s.c;y<=z.e.c;++y){Y===z.s.r&&(re[y]=Ze(y));var me=re[y]+ie,pe=ae?(F[Y]||[])[y]:F[me];if(!(!pe||pe.t=="z"))if(pe.t=="n")(pe.v|0)==pe.v&&pe.v>=-32768&&pe.v<=32767?br(L,13,d(Y,y,pe.v)):br(L,14,u(Y,y,pe.v));else{var ge=aa(pe);br(L,15,h(Y,y,ge.slice(0,239)))}}return br(L,1),L.end()}function t(F,B){var M=B||{};if(+M.codepage>=0&&Lr(+M.codepage),M.type=="string")throw new Error("Cannot write WK3 to JS string");var L=x0();br(L,0,i(F));for(var z=0,ae=0;z<F.SheetNames.length;++z)(F.Sheets[F.SheetNames[z]]||{})["!ref"]&&br(L,27,j(F.SheetNames[z],ae++));var re=0;for(z=0;z<F.SheetNames.length;++z){var Q=F.Sheets[F.SheetNames[z]];if(!(!Q||!Q["!ref"])){for(var Y=He(Q["!ref"]),ie=Array.isArray(Q),y=[],me=Math.min(Y.e.r,8191),pe=Y.s.r;pe<=me;++pe)for(var ge=nr(pe),le=Y.s.c;le<=Y.e.c;++le){pe===Y.s.r&&(y[le]=Ze(le));var fe=y[le]+ge,de=ie?(Q[pe]||[])[le]:Q[fe];if(!(!de||de.t=="z"))if(de.t=="n")br(L,23,I(pe,le,re,de.v));else{var Ae=aa(de);br(L,22,S(pe,le,re,Ae.slice(0,239)))}}++re}}return br(L,1),L.end()}function s(F){var B=Je(2);return B.write_shift(2,F),B}function i(F){var B=Je(26);B.write_shift(2,4096),B.write_shift(2,4),B.write_shift(4,0);for(var M=0,L=0,z=0,ae=0;ae<F.SheetNames.length;++ae){var re=F.SheetNames[ae],Q=F.Sheets[re];if(!(!Q||!Q["!ref"])){++z;var Y=Ua(Q["!ref"]);M<Y.e.r&&(M=Y.e.r),L<Y.e.c&&(L=Y.e.c)}}return M>8191&&(M=8191),B.write_shift(2,M),B.write_shift(1,z),B.write_shift(1,L),B.write_shift(2,0),B.write_shift(2,0),B.write_shift(1,1),B.write_shift(1,2),B.write_shift(4,0),B.write_shift(4,0),B}function c(F,B,M){var L={s:{c:0,r:0},e:{c:0,r:0}};return B==8&&M.qpro?(L.s.c=F.read_shift(1),F.l++,L.s.r=F.read_shift(2),L.e.c=F.read_shift(1),F.l++,L.e.r=F.read_shift(2),L):(L.s.c=F.read_shift(2),L.s.r=F.read_shift(2),B==12&&M.qpro&&(F.l+=2),L.e.c=F.read_shift(2),L.e.r=F.read_shift(2),B==12&&M.qpro&&(F.l+=2),L.s.c==65535&&(L.s.c=L.e.c=L.s.r=L.e.r=0),L)}function o(F){var B=Je(8);return B.write_shift(2,F.s.c),B.write_shift(2,F.s.r),B.write_shift(2,F.e.c),B.write_shift(2,F.e.r),B}function f(F,B,M){var L=[{c:0,r:0},{t:"n",v:0},0,0];return M.qpro&&M.vers!=20768?(L[0].c=F.read_shift(1),L[3]=F.read_shift(1),L[0].r=F.read_shift(2),F.l+=2):(L[2]=F.read_shift(1),L[0].c=F.read_shift(2),L[0].r=F.read_shift(2)),L}function l(F,B,M){var L=F.l+B,z=f(F,B,M);if(z[1].t="s",M.vers==20768){F.l++;var ae=F.read_shift(1);return z[1].v=F.read_shift(ae,"utf8"),z}return M.qpro&&F.l++,z[1].v=F.read_shift(L-F.l,"cstr"),z}function h(F,B,M){var L=Je(7+M.length);L.write_shift(1,255),L.write_shift(2,B),L.write_shift(2,F),L.write_shift(1,39);for(var z=0;z<L.length;++z){var ae=M.charCodeAt(z);L.write_shift(1,ae>=128?95:ae)}return L.write_shift(1,0),L}function x(F,B,M){var L=f(F,B,M);return L[1].v=F.read_shift(2,"i"),L}function d(F,B,M){var L=Je(7);return L.write_shift(1,255),L.write_shift(2,B),L.write_shift(2,F),L.write_shift(2,M,"i"),L}function p(F,B,M){var L=f(F,B,M);return L[1].v=F.read_shift(8,"f"),L}function u(F,B,M){var L=Je(13);return L.write_shift(1,255),L.write_shift(2,B),L.write_shift(2,F),L.write_shift(8,M,"f"),L}function _(F,B,M){var L=F.l+B,z=f(F,B,M);if(z[1].v=F.read_shift(8,"f"),M.qpro)F.l=L;else{var ae=F.read_shift(2);b(F.slice(F.l,F.l+ae),z),F.l+=ae}return z}function E(F,B,M){var L=B&32768;return B&=-32769,B=(L?F:0)+(B>=8192?B-16384:B),(L?"":"$")+(M?Ze(B):nr(B))}var R={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function b(F,B){ar(F,0);for(var M=[],L=0,z="",ae="",re="",Q="";F.l<F.length;){var Y=F[F.l++];switch(Y){case 0:M.push(F.read_shift(8,"f"));break;case 1:ae=E(B[0].c,F.read_shift(2),!0),z=E(B[0].r,F.read_shift(2),!1),M.push(ae+z);break;case 2:{var ie=E(B[0].c,F.read_shift(2),!0),y=E(B[0].r,F.read_shift(2),!1);ae=E(B[0].c,F.read_shift(2),!0),z=E(B[0].r,F.read_shift(2),!1),M.push(ie+y+":"+ae+z)}break;case 3:if(F.l<F.length){console.error("WK1 premature formula end");return}break;case 4:M.push("("+M.pop()+")");break;case 5:M.push(F.read_shift(2));break;case 6:{for(var me="";Y=F[F.l++];)me+=String.fromCharCode(Y);M.push('"'+me.replace(/"/g,'""')+'"')}break;case 8:M.push("-"+M.pop());break;case 23:M.push("+"+M.pop());break;case 22:M.push("NOT("+M.pop()+")");break;case 20:case 21:Q=M.pop(),re=M.pop(),M.push(["AND","OR"][Y-20]+"("+re+","+Q+")");break;default:if(Y<32&&g[Y])Q=M.pop(),re=M.pop(),M.push(re+g[Y]+Q);else if(R[Y]){if(L=R[Y][1],L==69&&(L=F[F.l++]),L>M.length){console.error("WK1 bad formula parse 0x"+Y.toString(16)+":|"+M.join("|")+"|");return}var pe=M.slice(-L);M.length-=L,M.push(R[Y][0]+"("+pe.join(",")+")")}else return Y<=7?console.error("WK1 invalid opcode "+Y.toString(16)):Y<=24?console.error("WK1 unsupported op "+Y.toString(16)):Y<=30?console.error("WK1 invalid opcode "+Y.toString(16)):Y<=115?console.error("WK1 unsupported function opcode "+Y.toString(16)):console.error("WK1 unrecognized opcode "+Y.toString(16))}}M.length==1?B[1].f=""+M[0]:console.error("WK1 bad formula parse |"+M.join("|")+"|")}function H(F){var B=[{c:0,r:0},{t:"n",v:0},0];return B[0].r=F.read_shift(2),B[3]=F[F.l++],B[0].c=F[F.l++],B}function U(F,B){var M=H(F);return M[1].t="s",M[1].v=F.read_shift(B-4,"cstr"),M}function S(F,B,M,L){var z=Je(6+L.length);z.write_shift(2,F),z.write_shift(1,M),z.write_shift(1,B),z.write_shift(1,39);for(var ae=0;ae<L.length;++ae){var re=L.charCodeAt(ae);z.write_shift(1,re>=128?95:re)}return z.write_shift(1,0),z}function k(F,B){var M=H(F);M[1].v=F.read_shift(2);var L=M[1].v>>1;if(M[1].v&1)switch(L&7){case 0:L=(L>>3)*5e3;break;case 1:L=(L>>3)*500;break;case 2:L=(L>>3)/20;break;case 3:L=(L>>3)/200;break;case 4:L=(L>>3)/2e3;break;case 5:L=(L>>3)/2e4;break;case 6:L=(L>>3)/16;break;case 7:L=(L>>3)/64;break}return M[1].v=L,M}function v(F,B){var M=H(F),L=F.read_shift(4),z=F.read_shift(4),ae=F.read_shift(2);if(ae==65535)return L===0&&z===3221225472?(M[1].t="e",M[1].v=15):L===0&&z===3489660928?(M[1].t="e",M[1].v=42):M[1].v=0,M;var re=ae&32768;return ae=(ae&32767)-16446,M[1].v=(1-re*2)*(z*Math.pow(2,ae+32)+L*Math.pow(2,ae)),M}function I(F,B,M,L){var z=Je(14);if(z.write_shift(2,F),z.write_shift(1,M),z.write_shift(1,B),L==0)return z.write_shift(4,0),z.write_shift(4,0),z.write_shift(2,65535),z;var ae=0,re=0,Q=0,Y=0;return L<0&&(ae=1,L=-L),re=Math.log2(L)|0,L/=Math.pow(2,re-31),Y=L>>>0,(Y&2147483648)==0&&(L/=2,++re,Y=L>>>0),L-=Y,Y|=2147483648,Y>>>=0,L*=Math.pow(2,32),Q=L>>>0,z.write_shift(4,Q),z.write_shift(4,Y),re+=16383+(ae?32768:0),z.write_shift(2,re),z}function P(F,B){var M=v(F);return F.l+=B-14,M}function N(F,B){var M=H(F),L=F.read_shift(4);return M[1].v=L>>6,M}function K(F,B){var M=H(F),L=F.read_shift(8,"f");return M[1].v=L,M}function Z(F,B){var M=K(F);return F.l+=B-10,M}function J(F,B){return F[F.l+B-1]==0?F.read_shift(B,"cstr"):""}function se(F,B){var M=F[F.l++];M>B-1&&(M=B-1);for(var L="";L.length<M;)L+=String.fromCharCode(F[F.l++]);return L}function ee(F,B,M){if(!(!M.qpro||B<21)){var L=F.read_shift(1);F.l+=17,F.l+=1,F.l+=2;var z=F.read_shift(B-21,"cstr");return[L,z]}}function ve(F,B){for(var M={},L=F.l+B;F.l<L;){var z=F.read_shift(2);if(z==14e3){for(M[z]=[0,""],M[z][0]=F.read_shift(2);F[F.l];)M[z][1]+=String.fromCharCode(F[F.l]),F.l++;F.l++}}return M}function j(F,B){var M=Je(5+F.length);M.write_shift(2,14e3),M.write_shift(2,B);for(var L=0;L<F.length;++L){var z=F.charCodeAt(L);M[M.l++]=z>127?95:z}return M[M.l++]=0,M}var he={0:{n:"BOF",f:Ye},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:c},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:x},14:{n:"NUMBER",f:p},15:{n:"LABEL",f:l},16:{n:"FORMULA",f:_},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:l},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:J},222:{n:"SHEETNAMELP",f:se},65535:{n:""}},ne={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:U},23:{n:"NUMBER17",f:v},24:{n:"NUMBER18",f:k},25:{n:"FORMULA19",f:P},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:ve},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:N},38:{n:"??"},39:{n:"NUMBER27",f:K},40:{n:"FORMULA28",f:Z},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:J},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ee},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:t,to_workbook:a}}();function mh(e){var a={},r=e.match(dr),n=0,t=!1;if(r)for(;n!=r.length;++n){var s=_e(r[n]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":a.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;a.cp=y0[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":a.outline=1;break;case"</outline>":break;case"<rFont":a.name=s.val;break;case"<sz":a.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":a.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":a.uval="double";break;case"singleAccounting":a.uval="single-accounting";break;case"doubleAccounting":a.uval="double-accounting";break}case"<u>":case"<u/>":a.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":case"<b/>":a.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":case"<i/>":a.i=1;break;case"</i>":break;case"<color":s.rgb&&(a.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":a.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":a.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;default:if(s[0].charCodeAt(1)!==47&&!t)throw new Error("Unrecognized rich format "+s[0])}}return a}var _h=function(){var e=ft("t"),a=ft("rPr");function r(s){var i=s.match(e);if(!i)return{t:"s",v:""};var c={t:"s",v:Ce(i[1])},o=s.match(a);return o&&(c.s=mh(o[1])),c}var n=/<(?:\w+:)?r>/g,t=/<\/(?:\w+:)?r>/;return function(i){return i.replace(n,"").split(t).map(r).filter(function(c){return c.v})}}(),Eh=function(){var a=/(\r\n|\n)/g;function r(t,s,i){var c=[];t.u&&c.push("text-decoration: underline;"),t.uval&&c.push("text-underline-style:"+t.uval+";"),t.sz&&c.push("font-size:"+t.sz+"pt;"),t.outline&&c.push("text-effect: outline;"),t.shadow&&c.push("text-shadow: auto;"),s.push('<span style="'+c.join("")+'">'),t.b&&(s.push("<b>"),i.push("</b>")),t.i&&(s.push("<i>"),i.push("</i>")),t.strike&&(s.push("<s>"),i.push("</s>"));var o=t.valign||"";return o=="superscript"||o=="super"?o="sup":o=="subscript"&&(o="sub"),o!=""&&(s.push("<"+o+">"),i.push("</"+o+">")),i.push("</span>"),t}function n(t){var s=[[],t.v,[]];return t.v?(t.s&&r(t.s,s[0],s[2]),s[0].join("")+s[1].replace(a,"<br/>")+s[2].join("")):""}return function(s){return s.map(n).join("")}}(),kh=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Th=/<(?:\w+:)?r>/,wh=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function G0(e,a){var r=a?a.cellHTML:!0,n={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(n.t=Ce(Ne(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),n.r=Ne(e),r&&(n.h=b0(n.t))):e.match(Th)&&(n.r=Ne(e),n.t=Ce(Ne((e.replace(wh,"").match(kh)||[]).join("").replace(dr,""))),r&&(n.h=Eh(_h(n.r)))),n):{t:""}}var Ah=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Fh=/<(?:\w+:)?(?:si|sstItem)>/g,Sh=/<\/(?:\w+:)?(?:si|sstItem)>/;function yh(e,a){var r=[],n="";if(!e)return r;var t=e.match(Ah);if(t){n=t[2].replace(Fh,"").split(Sh);for(var s=0;s!=n.length;++s){var i=G0(n[s].trim(),a);i!=null&&(r[r.length]=i)}t=_e(t[1]),r.Count=t.count,r.Unique=t.uniqueCount}return r}function Ch(e){return[e.read_shift(4),e.read_shift(4)]}function Rh(e,a){var r=[],n=!1;return ta(e,function(s,i,c){switch(c){case 159:r.Count=s[0],r.Unique=s[1];break;case 19:r.push(s);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(i.T,!n||a.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r}function Ai(e){for(var a=[],r=e.split(""),n=0;n<r.length;++n)a[n]=r[n].charCodeAt(0);return a}function ra(e,a){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),a>=4&&(e.l+=a-4),r}function Dh(e){var a={};return a.id=e.read_shift(0,"lpp4"),a.R=ra(e,4),a.U=ra(e,4),a.W=ra(e,4),a}function Oh(e){for(var a=e.read_shift(4),r=e.l+a-4,n={},t=e.read_shift(4),s=[];t-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(n.name=e.read_shift(0,"lpp4"),n.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return n}function Nh(e){var a=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)a.push(Oh(e));return a}function Ih(e){var a=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)a.push(e.read_shift(0,"lpp4"));return a}function bh(e){var a={};return e.read_shift(4),e.l+=4,a.id=e.read_shift(0,"lpp4"),a.name=e.read_shift(0,"lpp4"),a.R=ra(e,4),a.U=ra(e,4),a.W=ra(e,4),a}function Ph(e){var a=bh(e);if(a.ename=e.read_shift(0,"8lpp4"),a.blksz=e.read_shift(4),a.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return a}function Fi(e,a){var r=e.l+a,n={};n.Flags=e.read_shift(4)&63,e.l+=4,n.AlgID=e.read_shift(4);var t=!1;switch(n.AlgID){case 26126:case 26127:case 26128:t=n.Flags==36;break;case 26625:t=n.Flags==4;break;case 0:t=n.Flags==16||n.Flags==4||n.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+n.AlgID}if(!t)throw new Error("Encryption Flags/AlgID mismatch");return n.AlgIDHash=e.read_shift(4),n.KeySize=e.read_shift(4),n.ProviderType=e.read_shift(4),e.l+=8,n.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,n}function Si(e,a){var r={},n=e.l+a;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,n),e.l=n,r}function Lh(e){var a=ra(e);switch(a.Minor){case 2:return[a.Minor,Mh(e)];case 3:return[a.Minor,Bh()];case 4:return[a.Minor,Uh(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+a.Minor)}function Mh(e){var a=e.read_shift(4);if((a&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),n=Fi(e,r),t=Si(e,e.length-e.l);return{t:"Std",h:n,v:t}}function Bh(){throw new Error("File is password-protected: ECMA-376 Extensible")}function Uh(e){var a=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),n={};return r.replace(dr,function(s){var i=_e(s);switch(Kr(i[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":a.forEach(function(c){n[c]=i[c]});break;case"<dataIntegrity":n.encryptedHmacKey=i.encryptedHmacKey,n.encryptedHmacValue=i.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":n.uri=i.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":n.encs.push(i);break;default:throw i[0]}}),n}function jh(e,a){var r={},n=r.EncryptionVersionInfo=ra(e,4);if(a-=4,n.Minor!=2)throw new Error("unrecognized minor version code: "+n.Minor);if(n.Major>4||n.Major<2)throw new Error("unrecognized major version code: "+n.Major);r.Flags=e.read_shift(4),a-=4;var t=e.read_shift(4);return a-=4,r.EncryptionHeader=Fi(e,t),a-=t,r.EncryptionVerifier=Si(e,a),r}function Hh(e){var a={},r=a.EncryptionVersionInfo=ra(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return a.Salt=e.read_shift(16),a.EncryptedVerifier=e.read_shift(16),a.EncryptedVerifierHash=e.read_shift(16),a}function Vh(e){var a=0,r,n=Ai(e),t=n.length+1,s,i,c,o,f;for(r=ha(t),r[0]=n.length,s=1;s!=t;++s)r[s]=n[s-1];for(s=t-1;s>=0;--s)i=r[s],c=(a&16384)===0?0:1,o=a<<1&32767,f=c|o,a=f^i;return a^52811}var yi=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],a=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],n=function(i){return(i/2|i*128)&255},t=function(i,c){return n(i^c)},s=function(i){for(var c=a[i.length-1],o=104,f=i.length-1;f>=0;--f)for(var l=i[f],h=0;h!=7;++h)l&64&&(c^=r[o]),l*=2,--o;return c};return function(i){for(var c=Ai(i),o=s(c),f=c.length,l=ha(16),h=0;h!=16;++h)l[h]=0;var x,d,p;for((f&1)===1&&(x=o>>8,l[f]=t(e[0],x),--f,x=o&255,d=c[c.length-1],l[f]=t(d,x));f>0;)--f,x=o>>8,l[f]=t(c[f],x),--f,x=o&255,l[f]=t(c[f],x);for(f=15,p=15-c.length;p>0;)x=o>>8,l[f]=t(e[p],x),--f,--p,x=o&255,l[f]=t(c[f],x),--f,--p;return l}}(),Wh=function(e,a,r,n,t){t||(t=a),n||(n=yi(e));var s,i;for(s=0;s!=a.length;++s)i=a[s],i^=n[r],i=(i>>5|i<<3)&255,t[s]=i,++r;return[t,r,n]},Gh=function(e){var a=0,r=yi(e);return function(n){var t=Wh("",n,a,r);return a=t[1],t[0]}};function Xh(e,a,r,n){var t={key:Ye(e),verificationBytes:Ye(e)};return r.password&&(t.verifier=Vh(r.password)),n.valid=t.verificationBytes===t.verifier,n.valid&&(n.insitu=Gh(r.password)),t}function $h(e,a,r){var n=r||{};return n.Info=e.read_shift(2),e.l-=2,n.Info===1?n.Data=Hh(e):n.Data=jh(e,a),n}function zh(e,a,r){var n={Type:r.biff>=8?e.read_shift(2):0};return n.Type?$h(e,a-2,n):Xh(e,r.biff>=8?a:a-2,r,n),n}var Kh=function(){function e(t,s){switch(s.type){case"base64":return a(Sr(t),s);case"binary":return a(t,s);case"buffer":return a(Fe&&Buffer.isBuffer(t)?t.toString("binary"):Aa(t),s);case"array":return a(ka(t),s)}throw new Error("Unrecognized type "+s.type)}function a(t,s){var i=s||{},c=i.dense?[]:{},o=t.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var f={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(l,h){Array.isArray(c)&&(c[h]=[]);for(var x=/\\\w+\b/g,d=0,p,u=-1;p=x.exec(l);){switch(p[0]){case"\\cell":var _=l.slice(d,x.lastIndex-p[0].length);if(_[0]==" "&&(_=_.slice(1)),++u,_.length){var E={v:_,t:"s"};Array.isArray(c)?c[h][u]=E:c[Ee({r:h,c:u})]=E}break}d=x.lastIndex}u>f.e.c&&(f.e.c=u)}),c["!ref"]=ye(f),c}function r(t,s){return da(e(t,s),s)}function n(t){for(var s=["{\\rtf1\\ansi"],i=He(t["!ref"]),c,o=Array.isArray(t),f=i.s.r;f<=i.e.r;++f){s.push("\\trowd\\trautofit1");for(var l=i.s.c;l<=i.e.c;++l)s.push("\\cellx"+(l+1));for(s.push("\\pard\\intbl"),l=i.s.c;l<=i.e.c;++l){var h=Ee({r:f,c:l});c=o?(t[f]||[])[l]:t[h],!(!c||c.v==null&&(!c.f||c.F))&&(s.push(" "+(c.w||(aa(c),c.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Yh(e){var a=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]}function ht(e){for(var a=0,r=1;a!=3;++a)r=r*256+(e[a]>255?255:e[a]<0?0:e[a]);return r.toString(16).toUpperCase().slice(1)}function qh(e){var a=e[0]/255,r=e[1]/255,n=e[2]/255,t=Math.max(a,r,n),s=Math.min(a,r,n),i=t-s;if(i===0)return[0,0,a];var c=0,o=0,f=t+s;switch(o=i/(f>1?2-f:f),t){case a:c=((r-n)/i+6)%6;break;case r:c=(n-a)/i+2;break;case n:c=(a-r)/i+4;break}return[c/6,o,f/2]}function Jh(e){var a=e[0],r=e[1],n=e[2],t=r*2*(n<.5?n:1-n),s=n-t/2,i=[s,s,s],c=6*a,o;if(r!==0)switch(c|0){case 0:case 6:o=t*c,i[0]+=t,i[1]+=o;break;case 1:o=t*(2-c),i[0]+=o,i[1]+=t;break;case 2:o=t*(c-2),i[1]+=t,i[2]+=o;break;case 3:o=t*(4-c),i[1]+=o,i[2]+=t;break;case 4:o=t*(c-4),i[2]+=t,i[0]+=o;break;case 5:o=t*(6-c),i[2]+=o,i[0]+=t;break}for(var f=0;f!=3;++f)i[f]=Math.round(i[f]*255);return i}function Ht(e,a){if(a===0)return e;var r=qh(Yh(e));return a<0?r[2]=r[2]*(1+a):r[2]=1-(1-r[2])*(1-a),ht(Jh(r))}var Ci=6,Zh=15,Qh=1,vr=Ci;function Vt(e){return Math.floor((e+Math.round(128/vr)/256)*vr)}function Wt(e){return Math.floor((e-5)/vr*100+.5)/100}function g0(e){return Math.round((e*vr+5)/vr*256)/256}function c0(e){return g0(Wt(Vt(e)))}function X0(e){var a=Math.abs(e-c0(e)),r=vr;if(a>.005)for(vr=Qh;vr<Zh;++vr)Math.abs(e-c0(e))<=a&&(a=Math.abs(e-c0(e)),r=vr);vr=r}function Ma(e){e.width?(e.wpx=Vt(e.width),e.wch=Wt(e.wpx),e.MDW=vr):e.wpx?(e.wch=Wt(e.wpx),e.width=g0(e.wch),e.MDW=vr):typeof e.wch=="number"&&(e.width=g0(e.wch),e.wpx=Vt(e.width),e.MDW=vr),e.customWidth&&delete e.customWidth}var eu=96,Ri=eu;function Di(e){return e*96/Ri}function ut(e){return e*Ri/96}var ru={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function au(e,a,r,n){a.Borders=[];var t={},s=!1;(e[0].match(dr)||[]).forEach(function(i){var c=_e(i);switch(Kr(c[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":t={},c.diagonalUp&&(t.diagonalUp=Pe(c.diagonalUp)),c.diagonalDown&&(t.diagonalDown=Pe(c.diagonalDown)),a.Borders.push(t);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in borders")}})}function tu(e,a,r,n){a.Fills=[];var t={},s=!1;(e[0].match(dr)||[]).forEach(function(i){var c=_e(i);switch(Kr(c[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":t={},a.Fills.push(t);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":a.Fills.push(t),t={};break;case"<patternFill":case"<patternFill>":c.patternType&&(t.patternType=c.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":t.bgColor||(t.bgColor={}),c.indexed&&(t.bgColor.indexed=parseInt(c.indexed,10)),c.theme&&(t.bgColor.theme=parseInt(c.theme,10)),c.tint&&(t.bgColor.tint=parseFloat(c.tint)),c.rgb&&(t.bgColor.rgb=c.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":t.fgColor||(t.fgColor={}),c.theme&&(t.fgColor.theme=parseInt(c.theme,10)),c.tint&&(t.fgColor.tint=parseFloat(c.tint)),c.rgb!=null&&(t.fgColor.rgb=c.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fills")}})}function nu(e,a,r,n){a.Fonts=[];var t={},s=!1;(e[0].match(dr)||[]).forEach(function(i){var c=_e(i);switch(Kr(c[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":a.Fonts.push(t),t={};break;case"<name":c.val&&(t.name=Ne(c.val));break;case"<name/>":case"</name>":break;case"<b":t.bold=c.val?Pe(c.val):1;break;case"<b/>":t.bold=1;break;case"<i":t.italic=c.val?Pe(c.val):1;break;case"<i/>":t.italic=1;break;case"<u":switch(c.val){case"none":t.underline=0;break;case"single":t.underline=1;break;case"double":t.underline=2;break;case"singleAccounting":t.underline=33;break;case"doubleAccounting":t.underline=34;break}break;case"<u/>":t.underline=1;break;case"<strike":t.strike=c.val?Pe(c.val):1;break;case"<strike/>":t.strike=1;break;case"<outline":t.outline=c.val?Pe(c.val):1;break;case"<outline/>":t.outline=1;break;case"<shadow":t.shadow=c.val?Pe(c.val):1;break;case"<shadow/>":t.shadow=1;break;case"<condense":t.condense=c.val?Pe(c.val):1;break;case"<condense/>":t.condense=1;break;case"<extend":t.extend=c.val?Pe(c.val):1;break;case"<extend/>":t.extend=1;break;case"<sz":c.val&&(t.sz=+c.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":c.val&&(t.vertAlign=c.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":c.val&&(t.family=parseInt(c.val,10));break;case"<family/>":case"</family>":break;case"<scheme":c.val&&(t.scheme=c.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(c.val=="1")break;c.codepage=y0[parseInt(c.val,10)];break;case"<color":if(t.color||(t.color={}),c.auto&&(t.color.auto=Pe(c.auto)),c.rgb)t.color.rgb=c.rgb.slice(-6);else if(c.indexed){t.color.index=parseInt(c.indexed,10);var o=_a[t.color.index];t.color.index==81&&(o=_a[1]),o||(o=_a[1]),t.color.rgb=o[0].toString(16)+o[1].toString(16)+o[2].toString(16)}else c.theme&&(t.color.theme=parseInt(c.theme,10),c.tint&&(t.color.tint=parseFloat(c.tint)),c.theme&&r.themeElements&&r.themeElements.clrScheme&&(t.color.rgb=Ht(r.themeElements.clrScheme[t.color.theme].rgb,t.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fonts")}})}function su(e,a,r){a.NumberFmt=[];for(var n=zr(Te),t=0;t<n.length;++t)a.NumberFmt[n[t]]=Te[n[t]];var s=e[0].match(dr);if(s)for(t=0;t<s.length;++t){var i=_e(s[t]);switch(Kr(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var c=Ce(Ne(i.formatCode)),o=parseInt(i.numFmtId,10);if(a.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&a.NumberFmt[o]!=null;--o);a.NumberFmt[o]=c}ma(c,o)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}var Ct=["numFmtId","fillId","fontId","borderId","xfId"],Rt=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function iu(e,a,r){a.CellXf=[];var n,t=!1;(e[0].match(dr)||[]).forEach(function(s){var i=_e(s),c=0;switch(Kr(i[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(n=i,delete n[0],c=0;c<Ct.length;++c)n[Ct[c]]&&(n[Ct[c]]=parseInt(n[Ct[c]],10));for(c=0;c<Rt.length;++c)n[Rt[c]]&&(n[Rt[c]]=Pe(n[Rt[c]]));if(a.NumberFmt&&n.numFmtId>392){for(c=392;c>60;--c)if(a.NumberFmt[n.numFmtId]==a.NumberFmt[c]){n.numFmtId=c;break}}a.CellXf.push(n);break;case"</xf>":break;case"<alignment":case"<alignment/>":var o={};i.vertical&&(o.vertical=i.vertical),i.horizontal&&(o.horizontal=i.horizontal),i.textRotation!=null&&(o.textRotation=i.textRotation),i.indent&&(o.indent=i.indent),i.wrapText&&(o.wrapText=Pe(i.wrapText)),n.alignment=o;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":t=!0;break;case"</AlternateContent>":t=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;default:if(r&&r.WTF&&!t)throw new Error("unrecognized "+i[0]+" in cellXfs")}})}var cu=function(){var a=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,n=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,t=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,s=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(c,o,f){var l={};if(!c)return l;c=c.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var h;return(h=c.match(a))&&su(h,l,f),(h=c.match(t))&&nu(h,l,o,f),(h=c.match(n))&&tu(h,l,o,f),(h=c.match(s))&&au(h,l,o,f),(h=c.match(r))&&iu(h,l,f),l}}();function fu(e,a){var r=e.read_shift(2),n=ur(e);return[r,n]}function ou(e,a,r){var n={};n.sz=e.read_shift(2)/20;var t=_o(e);t.fItalic&&(n.italic=1),t.fCondense&&(n.condense=1),t.fExtend&&(n.extend=1),t.fShadow&&(n.shadow=1),t.fOutline&&(n.outline=1),t.fStrikeout&&(n.strike=1);var s=e.read_shift(2);switch(s===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var i=e.read_shift(1);i!=0&&(n.underline=i);var c=e.read_shift(1);c>0&&(n.family=c);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=mo(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=ur(e),n}var lu=xr;function hu(e,a){var r=e.l+a,n=e.read_shift(2),t=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:t}}var uu=xr;function xu(e,a,r){var n={};n.NumberFmt=[];for(var t in Te)n.NumberFmt[t]=Te[t];n.CellXf=[],n.Fonts=[];var s=[],i=!1;return ta(e,function(o,f,l){switch(l){case 44:n.NumberFmt[o[0]]=o[1],ma(o[1],o[0]);break;case 43:n.Fonts.push(o),o.color.theme!=null&&a&&a.themeElements&&a.themeElements.clrScheme&&(o.color.rgb=Ht(a.themeElements.clrScheme[o.color.theme].rgb,o.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:s[s.length-1]==617&&n.CellXf.push(o);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(l),i=!0;break;case 38:s.pop(),i=!1;break;default:if(f.T>0)s.push(l);else if(f.T<0)s.pop();else if(!i||r.WTF&&s[s.length-1]!=37)throw new Error("Unexpected record 0x"+l.toString(16))}}),n}var du=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function pu(e,a,r){a.themeElements.clrScheme=[];var n={};(e[0].match(dr)||[]).forEach(function(t){var s=_e(t);switch(s[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":n.rgb=s.val;break;case"<a:sysClr":n.rgb=s.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":s[0].charAt(1)==="/"?(a.themeElements.clrScheme[du.indexOf(s[0])]=n,n={}):n.name=s[0].slice(3,s[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+s[0]+" in clrScheme")}})}function vu(){}function gu(){}var mu=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,_u=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Eu=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function ku(e,a,r){a.themeElements={};var n;[["clrScheme",mu,pu],["fontScheme",_u,vu],["fmtScheme",Eu,gu]].forEach(function(t){if(!(n=e.match(t[1])))throw new Error(t[0]+" not found in themeElements");t[2](n,a,r)})}var Tu=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Oi(e,a){(!e||e.length===0)&&(e=wu());var r,n={};if(!(r=e.match(Tu)))throw new Error("themeElements not found in theme");return ku(r[0],n,a),n.raw=e,n}function wu(e,a){var r=[Vs];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Au(e,a,r){var n=e.l+a,t=e.read_shift(4);if(t!==124226){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;var i;try{i=Hs(s,{type:"array"})}catch{return}var c=Fr(i,"theme/theme/theme1.xml",!0);if(c)return Oi(c,r)}}function Fu(e){return e.read_shift(4)}function Su(e){var a={};switch(a.xclrType=e.read_shift(2),a.nTintShade=e.read_shift(2),a.xclrType){case 0:e.l+=4;break;case 1:a.xclrValue=yu(e,4);break;case 2:a.xclrValue=gi(e);break;case 3:a.xclrValue=Fu(e);break;case 4:e.l+=4;break}return e.l+=8,a}function yu(e,a){return xr(e,a)}function Cu(e,a){return xr(e,a)}function Ru(e){var a=e.read_shift(2),r=e.read_shift(2)-4,n=[a];switch(a){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:n[1]=Su(e);break;case 6:n[1]=Cu(e,r);break;case 14:case 15:n[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+a+" "+r)}return n}function Du(e,a){var r=e.l+a;e.l+=2;var n=e.read_shift(2);e.l+=2;for(var t=e.read_shift(2),s=[];t-- >0;)s.push(Ru(e,r-e.l));return{ixfe:n,ext:s}}function Ou(e,a){a.forEach(function(r){r[0]})}function Nu(e,a){return{flags:e.read_shift(4),version:e.read_shift(4),name:ur(e)}}function Iu(e){for(var a=[],r=e.read_shift(4);r-- >0;)a.push([e.read_shift(4),e.read_shift(4)]);return a}function bu(e){return e.l+=4,e.read_shift(4)!=0}function Pu(e,a,r){var n={Types:[],Cell:[],Value:[]},t=r||{},s=[],i=!1,c=2;return ta(e,function(o,f,l){switch(l){case 335:n.Types.push({name:o.name});break;case 51:o.forEach(function(h){c==1?n.Cell.push({type:n.Types[h[0]-1].name,index:h[1]}):c==0&&n.Value.push({type:n.Types[h[0]-1].name,index:h[1]})});break;case 337:c=o?1:0;break;case 338:c=2;break;case 35:s.push(l),i=!0;break;case 36:s.pop(),i=!1;break;default:if(!f.T){if(!i||t.WTF&&s[s.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}}),n}function Lu(e,a,r){var n={Types:[],Cell:[],Value:[]};if(!e)return n;var t=!1,s=2,i;return e.replace(dr,function(c){var o=_e(c);switch(Kr(o[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":n.Types.push({name:o.name});break;case"</metadataType>":break;case"<futureMetadata":for(var f=0;f<n.Types.length;++f)n.Types[f].name==o.name&&(i=n.Types[f]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":s==1?n.Cell.push({type:n.Types[o.t-1].name,index:+o.v}):s==0&&n.Value.push({type:n.Types[o.t-1].name,index:+o.v});break;case"</rc>":break;case"<cellMetadata":s=1;break;case"</cellMetadata>":s=2;break;case"<valueMetadata":s=0;break;case"</valueMetadata>":s=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+o.i);break;default:if(!t&&r.WTF)throw new Error("unrecognized "+o[0]+" in metadata")}return c}),n}function Mu(e){var a=[];if(!e)return a;var r=1;return(e.match(dr)||[]).forEach(function(n){var t=_e(n);switch(t[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete t[0],t.i?r=t.i:t.i=r,a.push(t);break}}),a}function Bu(e){var a={};a.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),a.r=Ee(r);var n=e.read_shift(1);return n&2&&(a.l="1"),n&8&&(a.a="1"),a}function Uu(e,a,r){var n=[];return ta(e,function(s,i,c){switch(c){case 63:n.push(s);break;default:if(!i.T)throw new Error("Unexpected record 0x"+c.toString(16))}}),n}function ju(e,a,r,n){if(!e)return e;var t=n||{},s=!1;ta(e,function(c,o,f){switch(f){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(!o.T){if(!s||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}},t)}function Hu(e,a){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return a["!id"][r].Target}function Kn(e,a,r,n){var t=Array.isArray(e),s;a.forEach(function(i){var c=gr(i.ref);if(t?(e[c.r]||(e[c.r]=[]),s=e[c.r][c.c]):s=e[i.ref],!s){s={t:"z"},t?e[c.r][c.c]=s:e[i.ref]=s;var o=He(e["!ref"]||"BDWGO1000001:A1");o.s.r>c.r&&(o.s.r=c.r),o.e.r<c.r&&(o.e.r=c.r),o.s.c>c.c&&(o.s.c=c.c),o.e.c<c.c&&(o.e.c=c.c);var f=ye(o);f!==e["!ref"]&&(e["!ref"]=f)}s.c||(s.c=[]);var l={a:i.author,t:i.t,r:i.r,T:r};i.h&&(l.h=i.h);for(var h=s.c.length-1;h>=0;--h){if(!r&&s.c[h].T)return;r&&!s.c[h].T&&s.c.splice(h,1)}if(r&&n){for(h=0;h<n.length;++h)if(l.a==n[h].id){l.a=n[h].name||l.a;break}}s.c.push(l)})}function Vu(e,a){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],n=[],t=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);t&&t[1]&&t[1].split(/<\/\w*:?author>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?author[^>]*>(.*)/);c&&r.push(c[1])}});var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?comment[^>]*>/);if(c){var o=_e(c[0]),f={author:o.authorId&&r[o.authorId]||"sheetjsghost",ref:o.ref,guid:o.guid},l=gr(o.ref);if(!(a.sheetRows&&a.sheetRows<=l.r)){var h=i.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),x=!!h&&!!h[1]&&G0(h[1])||{r:"",t:"",h:""};f.r=x.r,x.r=="<t></t>"&&(x.t=x.h=""),f.t=(x.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),a.cellHTML&&(f.h=x.h),n.push(f)}}}}),n}function Wu(e,a){var r=[],n=!1,t={},s=0;return e.replace(dr,function(c,o){var f=_e(c);switch(Kr(f[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":t={author:f.personId,guid:f.id,ref:f.ref,T:1};break;case"</threadedComment>":t.t!=null&&r.push(t);break;case"<text>":case"<text":s=o+c.length;break;case"</text>":t.t=e.slice(s,o).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":n=!0;break;case"</mentions>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&a.WTF)throw new Error("unrecognized "+f[0]+" in threaded comments")}return c}),r}function Gu(e,a){var r=[],n=!1;return e.replace(dr,function(s){var i=_e(s);switch(Kr(i[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:i.displayname,id:i.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&a.WTF)throw new Error("unrecognized "+i[0]+" in threaded comments")}return s}),r}function Xu(e){var a={};a.iauthor=e.read_shift(4);var r=Sa(e);return a.rfx=r.s,a.ref=Ee(r.s),e.l+=16,a}var $u=ur;function zu(e,a){var r=[],n=[],t={},s=!1;return ta(e,function(c,o,f){switch(f){case 632:n.push(c);break;case 635:t=c;break;case 637:t.t=c.t,t.h=c.h,t.r=c.r;break;case 636:if(t.author=n[t.iauthor],delete t.iauthor,a.sheetRows&&t.rfx&&a.sheetRows<=t.rfx.r)break;t.t||(t.t=""),delete t.rfx,r.push(t);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!o.T){if(!s||a.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}}),r}var Ku="application/vnd.ms-office.vbaProject";function Yu(e){var a=Se.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,n){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var t=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Se.utils.cfb_add(a,t,e.FileIndex[n].content)}}),Se.write(a)}function qu(){return{"!type":"dialog"}}function Ju(){return{"!type":"dialog"}}function Zu(){return{"!type":"macro"}}function Qu(){return{"!type":"macro"}}var ba=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,a={r:0,c:0};function r(n,t,s,i){var c=!1,o=!1;s.length==0?o=!0:s.charAt(0)=="["&&(o=!0,s=s.slice(1,-1)),i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1));var f=s.length>0?parseInt(s,10)|0:0,l=i.length>0?parseInt(i,10)|0:0;return c?l+=a.c:--l,o?f+=a.r:--f,t+(c?"":"$")+Ze(l)+(o?"":"$")+nr(f)}return function(t,s){return a=s,t.replace(e,r)}}(),Ni=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,e1=function(){return function(a,r){return a.replace(Ni,function(n,t,s,i,c,o){var f=B0(i)-(s?0:r.c),l=M0(o)-(c?0:r.r),h=l==0?"":c?l+1:"["+l+"]",x=f==0?"":s?f+1:"["+f+"]";return t+"R"+h+"C"+x})}}();function Ii(e,a){return e.replace(Ni,function(r,n,t,s,i,c){return n+(t=="$"?t+s:Ze(B0(s)+a.c))+(i=="$"?i+c:nr(M0(c)+a.r))})}function r1(e,a,r){var n=Ua(a),t=n.s,s=gr(r),i={r:s.r-t.r,c:s.c-t.c};return Ii(e,i)}function a1(e){return e.length!=1}function Yn(e){return e.replace(/_xlfn\./g,"")}function ze(e){e.l+=1}function ua(e,a){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function bi(e,a,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Pi(e);r.biff==12&&(n=4)}var t=e.read_shift(n),s=e.read_shift(n),i=ua(e),c=ua(e);return{s:{r:t,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Pi(e){var a=ua(e),r=ua(e),n=e.read_shift(1),t=e.read_shift(1);return{s:{r:a[0],c:n,cRel:a[1],rRel:a[2]},e:{r:r[0],c:t,cRel:r[1],rRel:r[2]}}}function t1(e,a,r){if(r.biff<8)return Pi(e);var n=e.read_shift(r.biff==12?4:2),t=e.read_shift(r.biff==12?4:2),s=ua(e),i=ua(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:t,c:i[0],cRel:i[1],rRel:i[2]}}}function Li(e,a,r){if(r&&r.biff>=2&&r.biff<=5)return n1(e);var n=e.read_shift(r&&r.biff==12?4:2),t=ua(e);return{r:n,c:t[0],cRel:t[1],rRel:t[2]}}function n1(e){var a=ua(e),r=e.read_shift(1);return{r:a[0],c:r,cRel:a[1],rRel:a[2]}}function s1(e){var a=e.read_shift(2),r=e.read_shift(2);return{r:a,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function i1(e,a,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return c1(e);var t=e.read_shift(n>=12?4:2),s=e.read_shift(2),i=(s&16384)>>14,c=(s&32768)>>15;if(s&=16383,c==1)for(;t>524287;)t-=1048576;if(i==1)for(;s>8191;)s=s-16384;return{r:t,c:s,cRel:i,rRel:c}}function c1(e){var a=e.read_shift(2),r=e.read_shift(1),n=(a&32768)>>15,t=(a&16384)>>14;return a&=16383,n==1&&a>=8192&&(a=a-16384),t==1&&r>=128&&(r=r-256),{r:a,c:r,cRel:t,rRel:n}}function f1(e,a,r){var n=(e[e.l++]&96)>>5,t=bi(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,t]}function o1(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=bi(e,s,r);return[n,t,i]}function l1(e,a,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function h1(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[n,t]}function u1(e,a,r){var n=(e[e.l++]&96)>>5,t=t1(e,a-1,r);return[n,t]}function x1(e,a,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function qn(e){var a=e[e.l+1]&1,r=1;return e.l+=4,[a,r]}function d1(e,a,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),t=[],s=0;s<=n;++s)t.push(e.read_shift(r&&r.biff==2?1:2));return t}function p1(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function v1(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function g1(e){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(2)]}function m1(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function Mi(e){var a=e.read_shift(1),r=e.read_shift(1);return[a,r]}function _1(e){return e.read_shift(2),Mi(e)}function E1(e){return e.read_shift(2),Mi(e)}function k1(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=Li(e,0,r);return[n,t]}function T1(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=i1(e,0,r);return[n,t]}function w1(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=Li(e,0,r);return[n,t,s]}function A1(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=e.read_shift(r&&r.biff<=3?1:2);return[Fx[t],ji[t],n]}function F1(e,a,r){var n=e[e.l++],t=e.read_shift(1),s=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:S1(e);return[t,(s[0]===0?ji:Ax)[s[1]]]}function S1(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function y1(e,a,r){e.l+=r&&r.biff==2?3:4}function C1(e,a,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function R1(e){return e.l++,ya[e.read_shift(1)]}function D1(e){return e.l++,e.read_shift(2)}function O1(e){return e.l++,e.read_shift(1)!==0}function N1(e){return e.l++,lr(e)}function I1(e,a,r){return e.l++,vt(e,a-1,r)}function b1(e,a){var r=[e.read_shift(1)];if(a==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Ve(e,1)?"TRUE":"FALSE",a!=12&&(e.l+=7);break;case 37:case 16:r[1]=ya[e[e.l]],e.l+=a==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=lr(e);break;case 2:r[1]=Ca(e,0,{biff:a>0&&a<8?2:a});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function P1(e,a,r){for(var n=e.read_shift(r.biff==12?4:2),t=[],s=0;s!=n;++s)t.push((r.biff==12?Sa:Yt)(e));return t}function L1(e,a,r){var n=0,t=0;r.biff==12?(n=e.read_shift(4),t=e.read_shift(4)):(t=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--t==0&&(t=256));for(var s=0,i=[];s!=n&&(i[s]=[]);++s)for(var c=0;c!=t;++c)i[s][c]=b1(e,r.biff);return i}function M1(e,a,r){var n=e.read_shift(1)>>>5&3,t=!r||r.biff>=8?4:2,s=e.read_shift(t);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,s]}function B1(e,a,r){if(r.biff==5)return U1(e);var n=e.read_shift(1)>>>5&3,t=e.read_shift(2),s=e.read_shift(4);return[n,t,s]}function U1(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[a,r,n]}function j1(e,a,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function H1(e,a,r){var n=e.read_shift(1)>>>5&3,t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function V1(e,a,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function W1(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[n,t]}var G1=xr,X1=xr,$1=xr;function mt(e,a,r){return e.l+=2,[s1(e)]}function $0(e){return e.l+=6,[]}var z1=mt,K1=$0,Y1=$0,q1=mt;function Bi(e){return e.l+=2,[Ye(e),e.read_shift(2)&1]}var J1=mt,Z1=Bi,Q1=$0,ex=mt,rx=mt,ax=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function tx(e){e.l+=2;var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),t=e.read_shift(2),s=e.read_shift(2),i=ax[r>>2&31];return{ixti:a,coltype:r&3,rt:i,idx:n,c:t,C:s}}function nx(e){return e.l+=2,[e.read_shift(4)]}function sx(e,a,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function ix(e,a,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function cx(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[a,r]}function fx(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[a,r]}function ox(e){return e.l+=4,[0,0]}var Jn={1:{n:"PtgExp",f:C1},2:{n:"PtgTbl",f:$1},3:{n:"PtgAdd",f:ze},4:{n:"PtgSub",f:ze},5:{n:"PtgMul",f:ze},6:{n:"PtgDiv",f:ze},7:{n:"PtgPower",f:ze},8:{n:"PtgConcat",f:ze},9:{n:"PtgLt",f:ze},10:{n:"PtgLe",f:ze},11:{n:"PtgEq",f:ze},12:{n:"PtgGe",f:ze},13:{n:"PtgGt",f:ze},14:{n:"PtgNe",f:ze},15:{n:"PtgIsect",f:ze},16:{n:"PtgUnion",f:ze},17:{n:"PtgRange",f:ze},18:{n:"PtgUplus",f:ze},19:{n:"PtgUminus",f:ze},20:{n:"PtgPercent",f:ze},21:{n:"PtgParen",f:ze},22:{n:"PtgMissArg",f:ze},23:{n:"PtgStr",f:I1},26:{n:"PtgSheet",f:sx},27:{n:"PtgEndSheet",f:ix},28:{n:"PtgErr",f:R1},29:{n:"PtgBool",f:O1},30:{n:"PtgInt",f:D1},31:{n:"PtgNum",f:N1},32:{n:"PtgArray",f:x1},33:{n:"PtgFunc",f:A1},34:{n:"PtgFuncVar",f:F1},35:{n:"PtgName",f:M1},36:{n:"PtgRef",f:k1},37:{n:"PtgArea",f:f1},38:{n:"PtgMemArea",f:j1},39:{n:"PtgMemErr",f:G1},40:{n:"PtgMemNoMem",f:X1},41:{n:"PtgMemFunc",f:H1},42:{n:"PtgRefErr",f:V1},43:{n:"PtgAreaErr",f:l1},44:{n:"PtgRefN",f:T1},45:{n:"PtgAreaN",f:u1},46:{n:"PtgMemAreaN",f:cx},47:{n:"PtgMemNoMemN",f:fx},57:{n:"PtgNameX",f:B1},58:{n:"PtgRef3d",f:w1},59:{n:"PtgArea3d",f:o1},60:{n:"PtgRefErr3d",f:W1},61:{n:"PtgAreaErr3d",f:h1},255:{}},lx={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},hx={1:{n:"PtgElfLel",f:Bi},2:{n:"PtgElfRw",f:ex},3:{n:"PtgElfCol",f:z1},6:{n:"PtgElfRwV",f:rx},7:{n:"PtgElfColV",f:q1},10:{n:"PtgElfRadical",f:J1},11:{n:"PtgElfRadicalS",f:Q1},13:{n:"PtgElfColS",f:K1},15:{n:"PtgElfColSV",f:Y1},16:{n:"PtgElfRadicalLel",f:Z1},25:{n:"PtgList",f:tx},29:{n:"PtgSxName",f:nx},255:{}},ux={0:{n:"PtgAttrNoop",f:ox},1:{n:"PtgAttrSemi",f:m1},2:{n:"PtgAttrIf",f:v1},4:{n:"PtgAttrChoose",f:d1},8:{n:"PtgAttrGoto",f:p1},16:{n:"PtgAttrSum",f:y1},32:{n:"PtgAttrBaxcel",f:qn},33:{n:"PtgAttrBaxcel",f:qn},64:{n:"PtgAttrSpace",f:_1},65:{n:"PtgAttrSpaceSemi",f:E1},128:{n:"PtgAttrIfError",f:g1},255:{}};function _t(e,a,r,n){if(n.biff<8)return xr(e,a);for(var t=e.l+a,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=L1(e,0,n),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=P1(e,r[i][1],n),s.push(r[i][2]);break;case"PtgExp":n&&n.biff==12&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return a=t-e.l,a!==0&&s.push(xr(e,a)),s}function Et(e,a,r){for(var n=e.l+a,t,s,i=[];n!=e.l;)a=n-e.l,s=e[e.l],t=Jn[s]||Jn[lx[s]],(s===24||s===25)&&(t=(s===24?hx:ux)[e[e.l+1]]),!t||!t.f?xr(e,a):i.push([t.n,t.f(e,a,r)]);return i}function xx(e){for(var a=[],r=0;r<e.length;++r){for(var n=e[r],t=[],s=0;s<n.length;++s){var i=n[s];if(i)switch(i[0]){case 2:t.push('"'+i[1].replace(/"/g,'""')+'"');break;default:t.push(i[1])}else t.push("")}a.push(t.join(","))}return a.join(";")}var dx={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function px(e,a){if(!e&&!(a&&a.biff<=5&&a.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ui(e,a,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[a]))return e.SheetNames[a];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[a];if(r.biff<8)return a>1e4&&(a-=65536),a<0&&(a=-a),a==0?"":e.XTI[a-1];if(!n)return"SH33TJSERR1";var t="";if(r.biff>8)switch(e[n[0]][0]){case 357:return t=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?t:t+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return t=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?t:t+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[n[0]][0][3]?(t=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?t:t+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function Zn(e,a,r){var n=Ui(e,a,r);return n=="#REF"?n:px(n,r)}function or(e,a,r,n,t){var s=t&&t.biff||8,i={s:{c:0,r:0}},c=[],o,f,l,h=0,x=0,d,p="";if(!e[0]||!e[0][0])return"";for(var u=-1,_="",E=0,R=e[0].length;E<R;++E){var g=e[0][E];switch(g[0]){case"PtgUminus":c.push("-"+c.pop());break;case"PtgUplus":c.push("+"+c.pop());break;case"PtgPercent":c.push(c.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=c.pop(),f=c.pop(),u>=0){switch(e[0][u][1][0]){case 0:_=je(" ",e[0][u][1][1]);break;case 1:_=je("\r",e[0][u][1][1]);break;default:if(_="",t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}f=f+_,u=-1}c.push(f+dx[g[0]]+o);break;case"PtgIsect":o=c.pop(),f=c.pop(),c.push(f+" "+o);break;case"PtgUnion":o=c.pop(),f=c.pop(),c.push(f+","+o);break;case"PtgRange":o=c.pop(),f=c.pop(),c.push(f+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":l=Za(g[1][1],i,t),c.push(Qa(l,s));break;case"PtgRefN":l=r?Za(g[1][1],r,t):g[1][1],c.push(Qa(l,s));break;case"PtgRef3d":h=g[1][1],l=Za(g[1][2],i,t),p=Zn(n,h,t),c.push(p+"!"+Qa(l,s));break;case"PtgFunc":case"PtgFuncVar":var b=g[1][0],H=g[1][1];b||(b=0),b&=127;var U=b==0?[]:c.slice(-b);c.length-=b,H==="User"&&(H=U.shift()),c.push(H+"("+U.join(",")+")");break;case"PtgBool":c.push(g[1]?"TRUE":"FALSE");break;case"PtgInt":c.push(g[1]);break;case"PtgNum":c.push(String(g[1]));break;case"PtgStr":c.push('"'+g[1].replace(/"/g,'""')+'"');break;case"PtgErr":c.push(g[1]);break;case"PtgAreaN":d=Nn(g[1][1],r?{s:r}:i,t),c.push(s0(d,t));break;case"PtgArea":d=Nn(g[1][1],i,t),c.push(s0(d,t));break;case"PtgArea3d":h=g[1][1],d=g[1][2],p=Zn(n,h,t),c.push(p+"!"+s0(d,t));break;case"PtgAttrSum":c.push("SUM("+c.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":x=g[1][2];var S=(n.names||[])[x-1]||(n[0]||[])[x],k=S?S.Name:"SH33TJSNAME"+String(x);k&&k.slice(0,6)=="_xlfn."&&!t.xlfn&&(k=k.slice(6)),c.push(k);break;case"PtgNameX":var v=g[1][1];x=g[1][2];var I;if(t.biff<=5)v<0&&(v=-v),n[v]&&(I=n[v][x]);else{var P="";if(((n[v]||[])[0]||[])[0]==14849||(((n[v]||[])[0]||[])[0]==1025?n[v][x]&&n[v][x].itab>0&&(P=n.SheetNames[n[v][x].itab-1]+"!"):P=n.SheetNames[x-1]+"!"),n[v]&&n[v][x])P+=n[v][x].Name;else if(n[0]&&n[0][x])P+=n[0][x].Name;else{var N=(Ui(n,v,t)||"").split(";;");N[x-1]?P=N[x-1]:P+="SH33TJSERRX"}c.push(P);break}I||(I={Name:"SH33TJSERRY"}),c.push(I.Name);break;case"PtgParen":var K="(",Z=")";if(u>=0){switch(_="",e[0][u][1][0]){case 2:K=je(" ",e[0][u][1][1])+K;break;case 3:K=je("\r",e[0][u][1][1])+K;break;case 4:Z=je(" ",e[0][u][1][1])+Z;break;case 5:Z=je("\r",e[0][u][1][1])+Z;break;default:if(t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}c.push(K+c.pop()+Z);break;case"PtgRefErr":c.push("#REF!");break;case"PtgRefErr3d":c.push("#REF!");break;case"PtgExp":l={c:g[1][1],r:g[1][0]};var J={c:r.c,r:r.r};if(n.sharedf[Ee(l)]){var se=n.sharedf[Ee(l)];c.push(or(se,i,J,n,t))}else{var ee=!1;for(o=0;o!=n.arrayf.length;++o)if(f=n.arrayf[o],!(l.c<f[0].s.c||l.c>f[0].e.c)&&!(l.r<f[0].s.r||l.r>f[0].e.r)){c.push(or(f[1],i,J,n,t)),ee=!0;break}ee||c.push(g[1])}break;case"PtgArray":c.push("{"+xx(g[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=E;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":c.push("");break;case"PtgAreaErr":c.push("#REF!");break;case"PtgAreaErr3d":c.push("#REF!");break;case"PtgList":c.push("Table"+g[1].idx+"[#"+g[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(g));default:throw new Error("Unrecognized Formula Token: "+String(g))}var ve=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(t.biff!=3&&u>=0&&ve.indexOf(e[0][E][0])==-1){g=e[0][u];var j=!0;switch(g[1][0]){case 4:j=!1;case 0:_=je(" ",g[1][1]);break;case 5:j=!1;case 1:_=je("\r",g[1][1]);break;default:if(_="",t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+g[1][0])}c.push((j?_:"")+c.pop()+(j?"":_)),u=-1}}if(c.length>1&&t.WTF)throw new Error("bad formula stack");return c[0]}function vx(e,a,r){var n=e.l+a,t=r.biff==2?1:2,s,i=e.read_shift(t);if(i==65535)return[[],xr(e,a-2)];var c=Et(e,i,r);return a!==i+t&&(s=_t(e,a-i-t,c,r)),e.l=n,[c,s]}function gx(e,a,r){var n=e.l+a,t=r.biff==2?1:2,s,i=e.read_shift(t);if(i==65535)return[[],xr(e,a-2)];var c=Et(e,i,r);return a!==i+t&&(s=_t(e,a-i-t,c,r)),e.l=n,[c,s]}function mx(e,a,r,n){var t=e.l+a,s=Et(e,n,r),i;return t!==e.l&&(i=_t(e,t-e.l,s,r)),[s,i]}function _x(e,a,r){var n=e.l+a,t,s=e.read_shift(2),i=Et(e,s,r);return s==65535?[[],xr(e,a-2)]:(a!==s+2&&(t=_t(e,n-s-2,i,r)),[i,t])}function Ex(e){var a;if(Jr(e,e.l+6)!==65535)return[lr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return a=e[e.l+2]===1,e.l+=8,[a,"b"];case 2:return a=e[e.l+2],e.l+=8,[a,"e"];case 3:return e.l+=8,["","s"]}return[]}function f0(e,a,r){var n=e.l+a,t=Yr(e);r.biff==2&&++e.l;var s=Ex(e),i=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=gx(e,n-e.l,r);return{cell:t,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function qt(e,a,r){var n=e.read_shift(4),t=Et(e,n,r),s=e.read_shift(4),i=s>0?_t(e,s,t,r):null;return[t,i]}var kx=qt,Jt=qt,Tx=qt,wx=qt,Ax={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},ji={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Fx={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Qn(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(a,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function o0(e){var a=e.split(":"),r=a[0].split(".")[0];return[r,a[0].split(".")[1]+(a.length>1?":"+(a[1].split(".")[1]||a[1].split(".")[0]):"")]}var tt={},Pa={};function nt(e,a){if(e){var r=[.7,.7,.75,.75,.3,.3];a=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Hi(e,a,r,n,t,s){try{n.cellNF&&(e.z=Te[a])}catch(c){if(n.WTF)throw c}if(!(e.t==="z"&&!n.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=rr(e.v)),(!n||n.cellText!==!1)&&e.t!=="z")try{if(Te[a]==null&&ma(Ff[a]||"General",a),e.t==="e")e.w=e.w||ya[e.v];else if(a===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ct(e.v);else if(e.t==="d"){var i=_r(e.v);(i|0)===i?e.w=i.toString(10):e.w=ct(i)}else{if(e.v===void 0)return"";e.w=Ea(e.v,Pa)}else e.t==="d"?e.w=Or(a,_r(e.v),Pa):e.w=Or(a,e.v,Pa)}catch(c){if(n.WTF)throw c}if(n.cellStyles&&r!=null)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Ht(t.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),n.WTF&&(e.s.fgColor.raw_rgb=t.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Ht(t.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),n.WTF&&(e.s.bgColor.raw_rgb=t.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(n.WTF&&s.Fills)throw c}}}function Sx(e,a){var r=He(a);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=ye(r))}var yx=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Cx=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,Rx=/<(?:\w:)?hyperlink [^>]*>/mg,Dx=/"(\w*:\w*)"/,Ox=/<(?:\w:)?col\b[^>]*[\/]?>/g,Nx=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Ix=/<(?:\w:)?pageMargins[^>]*\/>/g,Vi=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,bx=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Px=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Lx(e,a,r,n,t,s,i){if(!e)return e;n||(n={"!id":{}});var c=a.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},f="",l="",h=e.match(Cx);h?(f=e.slice(0,h.index),l=e.slice(h.index+h[0].length)):f=l=e;var x=f.match(Vi);x?z0(x[0],c,t,r):(x=f.match(bx))&&Mx(x[0],x[1]||"",c,t,r);var d=(f.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=f.slice(d,d+50).match(Dx);p&&Sx(c,p[1])}var u=f.match(Px);u&&u[1]&&Wx(u[1],t);var _=[];if(a.cellStyles){var E=f.match(Ox);E&&jx(_,E)}h&&Gx(h[1],c,a,o,s,i);var R=l.match(Nx);R&&(c["!autofilter"]=Hx(R[0]));var g=[],b=l.match(yx);if(b)for(d=0;d!=b.length;++d)g[d]=He(b[d].slice(b[d].indexOf('"')+1));var H=l.match(Rx);H&&Bx(c,H,n);var U=l.match(Ix);if(U&&(c["!margins"]=Ux(_e(U[0]))),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=ye(o)),a.sheetRows>0&&c["!ref"]){var S=He(c["!ref"]);a.sheetRows<=+S.e.r&&(S.e.r=a.sheetRows-1,S.e.r>o.e.r&&(S.e.r=o.e.r),S.e.r<S.s.r&&(S.s.r=S.e.r),S.e.c>o.e.c&&(S.e.c=o.e.c),S.e.c<S.s.c&&(S.s.c=S.e.c),c["!fullref"]=c["!ref"],c["!ref"]=ye(S))}return _.length>0&&(c["!cols"]=_),g.length>0&&(c["!merges"]=g),c}function z0(e,a,r,n){var t=_e(e);r.Sheets[n]||(r.Sheets[n]={}),t.codeName&&(r.Sheets[n].CodeName=Ce(Ne(t.codeName)))}function Mx(e,a,r,n,t){z0(e.slice(0,e.indexOf(">")),r,n,t)}function Bx(e,a,r){for(var n=Array.isArray(e),t=0;t!=a.length;++t){var s=_e(Ne(a[t]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Ce(s.location))):(s.Target="#"+Ce(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=He(s.ref),o=c.s.r;o<=c.e.r;++o)for(var f=c.s.c;f<=c.e.c;++f){var l=Ee({c:f,r:o});n?(e[o]||(e[o]=[]),e[o][f]||(e[o][f]={t:"z",v:void 0}),e[o][f].l=s):(e[l]||(e[l]={t:"z",v:void 0}),e[l].l=s)}}}function Ux(e){var a={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(a[r]=parseFloat(e[r]))}),a}function jx(e,a){for(var r=!1,n=0;n!=a.length;++n){var t=_e(a[n],!0);t.hidden&&(t.hidden=Pe(t.hidden));var s=parseInt(t.min,10)-1,i=parseInt(t.max,10)-1;for(t.outlineLevel&&(t.level=+t.outlineLevel||0),delete t.min,delete t.max,t.width=+t.width,!r&&t.width&&(r=!0,X0(t.width)),Ma(t);s<=i;)e[s++]=tr(t)}}function Hx(e){var a={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return a}var Vx=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Wx(e,a){a.Views||(a.Views=[{}]),(e.match(Vx)||[]).forEach(function(r,n){var t=_e(r);a.Views[n]||(a.Views[n]={}),+t.zoomScale&&(a.Views[n].zoom=+t.zoomScale),Pe(t.rightToLeft)&&(a.Views[n].RTL=!0)})}var Gx=function(){var e=/<(?:\w+:)?c[ \/>]/,a=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,n=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,t=/ref=["']([^"']*)["']/,s=ft("v"),i=ft("f");return function(o,f,l,h,x,d){for(var p=0,u="",_=[],E=[],R=0,g=0,b=0,H="",U,S,k=0,v=0,I,P,N=0,K=0,Z=Array.isArray(d.CellXf),J,se=[],ee=[],ve=Array.isArray(f),j=[],he={},ne=!1,F=!!l.sheetStubs,B=o.split(a),M=0,L=B.length;M!=L;++M){u=B[M].trim();var z=u.length;if(z!==0){var ae=0;e:for(p=0;p<z;++p)switch(u[p]){case">":if(u[p-1]!="/"){++p;break e}if(l&&l.cellStyles){if(S=_e(u.slice(ae,p),!0),k=S.r!=null?parseInt(S.r,10):k+1,v=-1,l.sheetRows&&l.sheetRows<k)continue;he={},ne=!1,S.ht&&(ne=!0,he.hpt=parseFloat(S.ht),he.hpx=ut(he.hpt)),S.hidden=="1"&&(ne=!0,he.hidden=!0),S.outlineLevel!=null&&(ne=!0,he.level=+S.outlineLevel),ne&&(j[k-1]=he)}break;case"<":ae=p;break}if(ae>=p)break;if(S=_e(u.slice(ae,p),!0),k=S.r!=null?parseInt(S.r,10):k+1,v=-1,!(l.sheetRows&&l.sheetRows<k)){h.s.r>k-1&&(h.s.r=k-1),h.e.r<k-1&&(h.e.r=k-1),l&&l.cellStyles&&(he={},ne=!1,S.ht&&(ne=!0,he.hpt=parseFloat(S.ht),he.hpx=ut(he.hpt)),S.hidden=="1"&&(ne=!0,he.hidden=!0),S.outlineLevel!=null&&(ne=!0,he.level=+S.outlineLevel),ne&&(j[k-1]=he)),_=u.slice(p).split(e);for(var re=0;re!=_.length&&_[re].trim().charAt(0)=="<";++re);for(_=_.slice(re),p=0;p!=_.length;++p)if(u=_[p].trim(),u.length!==0){if(E=u.match(r),R=p,g=0,b=0,u="<c "+(u.slice(0,1)=="<"?">":"")+u,E!=null&&E.length===2){for(R=0,H=E[1],g=0;g!=H.length&&!((b=H.charCodeAt(g)-64)<1||b>26);++g)R=26*R+b;--R,v=R}else++v;for(g=0;g!=u.length&&u.charCodeAt(g)!==62;++g);if(++g,S=_e(u.slice(0,g),!0),S.r||(S.r=Ee({r:k-1,c:v})),H=u.slice(g),U={t:""},(E=H.match(s))!=null&&E[1]!==""&&(U.v=Ce(E[1])),l.cellFormula){if((E=H.match(i))!=null&&E[1]!==""){if(U.f=Ce(Ne(E[1])).replace(/\r\n/g,`
`),l.xlfn||(U.f=Yn(U.f)),E[0].indexOf('t="array"')>-1)U.F=(H.match(t)||[])[1],U.F.indexOf(":")>-1&&se.push([He(U.F),U.F]);else if(E[0].indexOf('t="shared"')>-1){P=_e(E[0]);var Q=Ce(Ne(E[1]));l.xlfn||(Q=Yn(Q)),ee[parseInt(P.si,10)]=[P,Q,S.r]}}else(E=H.match(/<f[^>]*\/>/))&&(P=_e(E[0]),ee[P.si]&&(U.f=r1(ee[P.si][1],ee[P.si][2],S.r)));var Y=gr(S.r);for(g=0;g<se.length;++g)Y.r>=se[g][0].s.r&&Y.r<=se[g][0].e.r&&Y.c>=se[g][0].s.c&&Y.c<=se[g][0].e.c&&(U.F=se[g][1])}if(S.t==null&&U.v===void 0)if(U.f||U.F)U.v=0,U.t="n";else if(F)U.t="z";else continue;else U.t=S.t||"n";switch(h.s.c>v&&(h.s.c=v),h.e.c<v&&(h.e.c=v),U.t){case"n":if(U.v==""||U.v==null){if(!F)continue;U.t="z"}else U.v=parseFloat(U.v);break;case"s":if(typeof U.v>"u"){if(!F)continue;U.t="z"}else I=tt[parseInt(U.v,10)],U.v=I.t,U.r=I.r,l.cellHTML&&(U.h=I.h);break;case"str":U.t="s",U.v=U.v!=null?Ne(U.v):"",l.cellHTML&&(U.h=b0(U.v));break;case"inlineStr":E=H.match(n),U.t="s",E!=null&&(I=G0(E[1]))?(U.v=I.t,l.cellHTML&&(U.h=I.h)):U.v="";break;case"b":U.v=Pe(U.v);break;case"d":l.cellDates?U.v=rr(U.v,1):(U.v=_r(rr(U.v,1)),U.t="n");break;case"e":(!l||l.cellText!==!1)&&(U.w=U.v),U.v=oi[U.v];break}if(N=K=0,J=null,Z&&S.s!==void 0&&(J=d.CellXf[S.s],J!=null&&(J.numFmtId!=null&&(N=J.numFmtId),l.cellStyles&&J.fillId!=null&&(K=J.fillId))),Hi(U,N,K,l,x,d),l.cellDates&&Z&&U.t=="n"&&Ba(Te[N])&&(U.t="d",U.v=Kt(U.v)),S.cm&&l.xlmeta){var ie=(l.xlmeta.Cell||[])[+S.cm-1];ie&&ie.type=="XLDAPR"&&(U.D=!0)}if(ve){var y=gr(S.r);f[y.r]||(f[y.r]=[]),f[y.r][y.c]=U}else f[S.r]=U}}}}j.length>0&&(f["!rows"]=j)}}();function Xx(e,a){var r={},n=e.l+a;r.r=e.read_shift(4),e.l+=4;var t=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=t/20),r}var $x=Sa;function zx(){}function Kx(e,a){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=vo(e),r}function Yx(e){var a=Nr(e);return[a]}function qx(e){var a=Fa(e);return[a]}function Jx(e){var a=Nr(e),r=e.read_shift(1);return[a,r,"b"]}function Zx(e){var a=Fa(e),r=e.read_shift(1);return[a,r,"b"]}function Qx(e){var a=Nr(e),r=e.read_shift(1);return[a,r,"e"]}function ed(e){var a=Fa(e),r=e.read_shift(1);return[a,r,"e"]}function rd(e){var a=Nr(e),r=e.read_shift(4);return[a,r,"s"]}function ad(e){var a=Fa(e),r=e.read_shift(4);return[a,r,"s"]}function td(e){var a=Nr(e),r=lr(e);return[a,r,"n"]}function Wi(e){var a=Fa(e),r=lr(e);return[a,r,"n"]}function nd(e){var a=Nr(e),r=H0(e);return[a,r,"n"]}function sd(e){var a=Fa(e),r=H0(e);return[a,r,"n"]}function id(e){var a=Nr(e),r=U0(e);return[a,r,"is"]}function cd(e){var a=Nr(e),r=ur(e);return[a,r,"str"]}function fd(e){var a=Fa(e),r=ur(e);return[a,r,"str"]}function od(e,a,r){var n=e.l+a,t=Nr(e);t.r=r["!row"];var s=e.read_shift(1),i=[t,s,"b"];if(r.cellFormula){e.l+=2;var c=Jt(e,n-e.l,r);i[3]=or(c,null,t,r.supbooks,r)}else e.l=n;return i}function ld(e,a,r){var n=e.l+a,t=Nr(e);t.r=r["!row"];var s=e.read_shift(1),i=[t,s,"e"];if(r.cellFormula){e.l+=2;var c=Jt(e,n-e.l,r);i[3]=or(c,null,t,r.supbooks,r)}else e.l=n;return i}function hd(e,a,r){var n=e.l+a,t=Nr(e);t.r=r["!row"];var s=lr(e),i=[t,s,"n"];if(r.cellFormula){e.l+=2;var c=Jt(e,n-e.l,r);i[3]=or(c,null,t,r.supbooks,r)}else e.l=n;return i}function ud(e,a,r){var n=e.l+a,t=Nr(e);t.r=r["!row"];var s=ur(e),i=[t,s,"str"];if(r.cellFormula){e.l+=2;var c=Jt(e,n-e.l,r);i[3]=or(c,null,t,r.supbooks,r)}else e.l=n;return i}var xd=Sa;function dd(e,a){var r=e.l+a,n=Sa(e),t=j0(e),s=ur(e),i=ur(e),c=ur(e);e.l=r;var o={rfx:n,relId:t,loc:s,display:c};return i&&(o.Tooltip=i),o}function pd(){}function vd(e,a,r){var n=e.l+a,t=ii(e),s=e.read_shift(1),i=[t];if(i[2]=s,r.cellFormula){var c=kx(e,n-e.l,r);i[1]=c}else e.l=n;return i}function gd(e,a,r){var n=e.l+a,t=Sa(e),s=[t];if(r.cellFormula){var i=wx(e,n-e.l,r);s[1]=i,e.l=n}else e.l=n;return s}var md=["left","right","top","bottom","header","footer"];function _d(e){var a={};return md.forEach(function(r){a[r]=lr(e)}),a}function Ed(e){var a=e.read_shift(2);return e.l+=28,{RTL:a&32}}function kd(){}function Td(){}function wd(e,a,r,n,t,s,i){if(!e)return e;var c=a||{};n||(n={"!id":{}});var o=c.dense?[]:{},f,l={s:{r:2e6,c:2e6},e:{r:0,c:0}},h=!1,x=!1,d,p,u,_,E,R,g,b,H,U=[];c.biff=12,c["!row"]=0;var S=0,k=!1,v=[],I={},P=c.supbooks||t.supbooks||[[]];if(P.sharedf=I,P.arrayf=v,P.SheetNames=t.SheetNames||t.Sheets.map(function(ve){return ve.name}),!c.supbooks&&(c.supbooks=P,t.Names))for(var N=0;N<t.Names.length;++N)P[0][N+1]=t.Names[N];var K=[],Z=[],J=!1;Gt[16]={n:"BrtShortReal",f:Wi};var se;if(ta(e,function(j,he,ne){if(!x)switch(ne){case 148:f=j;break;case 0:d=j,c.sheetRows&&c.sheetRows<=d.r&&(x=!0),b=nr(_=d.r),c["!row"]=d.r,(j.hidden||j.hpt||j.level!=null)&&(j.hpt&&(j.hpx=ut(j.hpt)),Z[j.r]=j);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(p={t:j[2]},j[2]){case"n":p.v=j[1];break;case"s":g=tt[j[1]],p.v=g.t,p.r=g.r;break;case"b":p.v=!!j[1];break;case"e":p.v=j[1],c.cellText!==!1&&(p.w=ya[p.v]);break;case"str":p.t="s",p.v=j[1];break;case"is":p.t="s",p.v=j[1].t;break}if((u=i.CellXf[j[0].iStyleRef])&&Hi(p,u.numFmtId,null,c,s,i),E=j[0].c==-1?E+1:j[0].c,c.dense?(o[_]||(o[_]=[]),o[_][E]=p):o[Ze(E)+b]=p,c.cellFormula){for(k=!1,S=0;S<v.length;++S){var F=v[S];d.r>=F[0].s.r&&d.r<=F[0].e.r&&E>=F[0].s.c&&E<=F[0].e.c&&(p.F=ye(F[0]),k=!0)}!k&&j.length>3&&(p.f=j[3])}if(l.s.r>d.r&&(l.s.r=d.r),l.s.c>E&&(l.s.c=E),l.e.r<d.r&&(l.e.r=d.r),l.e.c<E&&(l.e.c=E),c.cellDates&&u&&p.t=="n"&&Ba(Te[u.numFmtId])){var B=ga(p.v);B&&(p.t="d",p.v=new Date(B.y,B.m-1,B.d,B.H,B.M,B.S,B.u))}se&&(se.type=="XLDAPR"&&(p.D=!0),se=void 0);break;case 1:case 12:if(!c.sheetStubs||h)break;p={t:"z",v:void 0},E=j[0].c==-1?E+1:j[0].c,c.dense?(o[_]||(o[_]=[]),o[_][E]=p):o[Ze(E)+b]=p,l.s.r>d.r&&(l.s.r=d.r),l.s.c>E&&(l.s.c=E),l.e.r<d.r&&(l.e.r=d.r),l.e.c<E&&(l.e.c=E),se&&(se.type=="XLDAPR"&&(p.D=!0),se=void 0);break;case 176:U.push(j);break;case 49:se=((c.xlmeta||{}).Cell||[])[j-1];break;case 494:var M=n["!id"][j.relId];for(M?(j.Target=M.Target,j.loc&&(j.Target+="#"+j.loc),j.Rel=M):j.relId==""&&(j.Target="#"+j.loc),_=j.rfx.s.r;_<=j.rfx.e.r;++_)for(E=j.rfx.s.c;E<=j.rfx.e.c;++E)c.dense?(o[_]||(o[_]=[]),o[_][E]||(o[_][E]={t:"z",v:void 0}),o[_][E].l=j):(R=Ee({c:E,r:_}),o[R]||(o[R]={t:"z",v:void 0}),o[R].l=j);break;case 426:if(!c.cellFormula)break;v.push(j),H=c.dense?o[_][E]:o[Ze(E)+b],H.f=or(j[1],l,{r:d.r,c:E},P,c),H.F=ye(j[0]);break;case 427:if(!c.cellFormula)break;I[Ee(j[0].s)]=j[1],H=c.dense?o[_][E]:o[Ze(E)+b],H.f=or(j[1],l,{r:d.r,c:E},P,c);break;case 60:if(!c.cellStyles)break;for(;j.e>=j.s;)K[j.e--]={width:j.w/256,hidden:!!(j.flags&1),level:j.level},J||(J=!0,X0(j.w/256)),Ma(K[j.e+1]);break;case 161:o["!autofilter"]={ref:ye(j)};break;case 476:o["!margins"]=j;break;case 147:t.Sheets[r]||(t.Sheets[r]={}),j.name&&(t.Sheets[r].CodeName=j.name),(j.above||j.left)&&(o["!outline"]={above:j.above,left:j.left});break;case 137:t.Views||(t.Views=[{}]),t.Views[0]||(t.Views[0]={}),j.RTL&&(t.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:h=!0;break;case 36:h=!1;break;case 37:h=!0;break;case 38:h=!1;break;default:if(!he.T){if(!h||c.WTF)throw new Error("Unexpected record 0x"+ne.toString(16))}}},c),delete c.supbooks,delete c["!row"],!o["!ref"]&&(l.s.r<2e6||f&&(f.e.r>0||f.e.c>0||f.s.r>0||f.s.c>0))&&(o["!ref"]=ye(f||l)),c.sheetRows&&o["!ref"]){var ee=He(o["!ref"]);c.sheetRows<=+ee.e.r&&(ee.e.r=c.sheetRows-1,ee.e.r>l.e.r&&(ee.e.r=l.e.r),ee.e.r<ee.s.r&&(ee.s.r=ee.e.r),ee.e.c>l.e.c&&(ee.e.c=l.e.c),ee.e.c<ee.s.c&&(ee.s.c=ee.e.c),o["!fullref"]=o["!ref"],o["!ref"]=ye(ee))}return U.length>0&&(o["!merges"]=U),K.length>0&&(o["!cols"]=K),Z.length>0&&(o["!rows"]=Z),o}function Ad(e){var a=[],r=e.match(/^<c:numCache>/),n;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(s){var i=s.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);i&&(a[+i[1]]=r?+i[2]:i[2])});var t=Ce((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(s){n=s.replace(/<.*?>/g,"")}),[a,t,n]}function Fd(e,a,r,n,t,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,f="A",l={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(h){var x=Ad(h);l.s.r=l.s.c=0,l.e.c=c,f=Ze(c),x[0].forEach(function(d,p){i[f+nr(p)]={t:"n",v:d,z:x[1]},o=p}),l.e.r<o&&(l.e.r=o),++c}),c>0&&(i["!ref"]=ye(l)),i}function Sd(e,a,r,n,t){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i,c=e.match(Vi);return c&&z0(c[0],s,t,r),(i=e.match(/drawing r:id="(.*?)"/))&&(s["!rel"]=i[1]),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}function yd(e,a){e.l+=10;var r=ur(e);return{name:r}}function Cd(e,a,r,n,t){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return ta(e,function(o,f,l){switch(l){case 550:s["!rel"]=o;break;case 651:t.Sheets[r]||(t.Sheets[r]={}),o.name&&(t.Sheets[r].CodeName=o.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!(f.T>0)){if(!(f.T<0)){if(!i||a.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}}},a),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}var Gi=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Rd=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Dd=[],Od=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function es(e,a){for(var r=0;r!=e.length;++r)for(var n=e[r],t=0;t!=a.length;++t){var s=a[t];if(n[s[0]]==null)n[s[0]]=s[1];else switch(s[2]){case"bool":typeof n[s[0]]=="string"&&(n[s[0]]=Pe(n[s[0]]));break;case"int":typeof n[s[0]]=="string"&&(n[s[0]]=parseInt(n[s[0]],10));break}}}function rs(e,a){for(var r=0;r!=a.length;++r){var n=a[r];if(e[n[0]]==null)e[n[0]]=n[1];else switch(n[2]){case"bool":typeof e[n[0]]=="string"&&(e[n[0]]=Pe(e[n[0]]));break;case"int":typeof e[n[0]]=="string"&&(e[n[0]]=parseInt(e[n[0]],10));break}}}function Xi(e){rs(e.WBProps,Gi),rs(e.CalcPr,Od),es(e.WBView,Rd),es(e.Sheets,Dd),Pa.date1904=Pe(e.WBProps.date1904)}var Nd="][*?/\\".split("");function Id(e,a){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return Nd.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}var bd=/<\w+:workbook/;function Pd(e,a){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},n=!1,t="xmlns",s={},i=0;if(e.replace(dr,function(o,f){var l=_e(o);switch(Kr(l[0])){case"<?xml":break;case"<workbook":o.match(bd)&&(t="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[t];break;case"</workbook>":break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Gi.forEach(function(h){if(l[h[0]]!=null)switch(h[2]){case"bool":r.WBProps[h[0]]=Pe(l[h[0]]);break;case"int":r.WBProps[h[0]]=parseInt(l[h[0]],10);break;default:r.WBProps[h[0]]=l[h[0]]}}),l.codeName&&(r.WBProps.CodeName=Ne(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Ce(Ne(l.name)),delete l[0],r.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":n=!0;break;case"</definedNames>":n=!1;break;case"<definedName":s={},s.Name=Ne(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),Pe(l.hidden||"0")&&(s.Hidden=!0),i=f+o.length;break;case"</definedName>":s.Ref=Ce(Ne(e.slice(i,f))),r.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete l[0],r.CalcPr=l;break;case"<calcPr/>":delete l[0],r.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":n=!0;break;case"</AlternateContent>":n=!1;break;case"<revisionPtr":break;default:if(!n&&a.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o}),Zf.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return Xi(r),r}function Ld(e,a){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=d0(e),r.name=ur(e),r}function Md(e,a){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var t=a>8?ur(e):"";return t.length>0&&(r.CodeName=t),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function Bd(e,a){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=a-8,r}function Ud(e,a,r){var n=e.l+a;e.l+=4,e.l+=1;var t=e.read_shift(4),s=go(e),i=Tx(e,0,r),c=j0(e);e.l=n;var o={Name:s,Ptg:i};return t<268435455&&(o.Sheet=t),c&&(o.Comment=c),o}function jd(e,a){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},n=[],t=!1;a||(a={}),a.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],Gt[16]={n:"BrtFRTArchID$",f:Bd},ta(e,function(o,f,l){switch(l){case 156:i.SheetNames.push(o.name),r.Sheets.push(o);break;case 153:r.WBProps=o;break;case 39:o.Sheet!=null&&(a.SID=o.Sheet),o.Ref=or(o.Ptg,null,null,i,a),delete a.SID,delete o.Ptg,s.push(o);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([l,o]):i[0]=[l,o],i[i.length-1].XTI=[];break;case 362:i.length===0&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(o),i.XTI=i.XTI.concat(o);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:n.push(l),t=!0;break;case 36:n.pop(),t=!1;break;case 37:n.push(l),t=!0;break;case 38:n.pop(),t=!1;break;case 16:break;default:if(!f.T){if(!t||a.WTF&&n[n.length-1]!=37&&n[n.length-1]!=35)throw new Error("Unexpected record 0x"+l.toString(16))}}},a),Xi(r),r.Names=s,r.supbooks=i,r}function Hd(e,a,r){return a.slice(-4)===".bin"?jd(e,r):Pd(e,r)}function Vd(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?wd(e,n,r,t,s,i,c):Lx(e,n,r,t,s,i,c)}function Wd(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?Cd(e,n,r,t,s):Sd(e,n,r,t,s)}function Gd(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?Zu():Qu()}function Xd(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?qu():Ju()}function $d(e,a,r,n){return a.slice(-4)===".bin"?xu(e,r,n):cu(e,r,n)}function zd(e,a,r){return Oi(e,r)}function Kd(e,a,r){return a.slice(-4)===".bin"?Rh(e,r):yh(e,r)}function Yd(e,a,r){return a.slice(-4)===".bin"?zu(e,r):Vu(e,r)}function qd(e,a,r){return a.slice(-4)===".bin"?Uu(e):Mu(e)}function Jd(e,a,r,n){return r.slice(-4)===".bin"?ju(e,a,r,n):void 0}function Zd(e,a,r){return a.slice(-4)===".bin"?Pu(e,a,r):Lu(e,a,r)}var $i=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,zi=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Ir(e,a){var r=e.split(/\s+/),n=[];if(n[0]=r[0],r.length===1)return n;var t=e.match($i),s,i,c,o;if(t)for(o=0;o!=t.length;++o)s=t[o].match(zi),(i=s[1].indexOf(":"))===-1?n[s[1]]=s[2].slice(1,s[2].length-1):(s[1].slice(0,6)==="xmlns:"?c="xmlns"+s[1].slice(6):c=s[1].slice(i+1),n[c]=s[2].slice(1,s[2].length-1));return n}function Qd(e){var a=e.split(/\s+/),r={};if(a.length===1)return r;var n=e.match($i),t,s,i,c;if(n)for(c=0;c!=n.length;++c)t=n[c].match(zi),(s=t[1].indexOf(":"))===-1?r[t[1]]=t[2].slice(1,t[2].length-1):(t[1].slice(0,6)==="xmlns:"?i="xmlns"+t[1].slice(6):i=t[1].slice(s+1),r[i]=t[2].slice(1,t[2].length-1));return r}var st;function ep(e,a){var r=st[e]||Ce(e);return r==="General"?Ea(a):Or(r,a)}function rp(e,a,r,n){var t=n;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":t=Pe(n);break;case"i2":case"int":t=parseInt(n,10);break;case"r4":case"float":t=parseFloat(n);break;case"date":case"dateTime.tz":t=rr(n);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Ce(a)]=t}function ap(e,a,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||ya[e.v]:a==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ct(e.v):e.w=Ea(e.v):e.w=ep(a||"General",e.v)}catch(s){if(r.WTF)throw s}try{var n=st[a]||a||"General";if(r.cellNF&&(e.z=n),r.cellDates&&e.t=="n"&&Ba(n)){var t=ga(e.v);t&&(e.t="d",e.v=new Date(t.y,t.m-1,t.d,t.H,t.M,t.S,t.u))}}catch(s){if(r.WTF)throw s}}}function tp(e,a,r){if(r.cellStyles&&a.Interior){var n=a.Interior;n.Pattern&&(n.patternType=ru[n.Pattern]||n.Pattern)}e[a.ID]=a}function np(e,a,r,n,t,s,i,c,o,f){var l="General",h=n.StyleID,x={};f=f||{};var d=[],p=0;for(h===void 0&&c&&(h=c.StyleID),h===void 0&&i&&(h=i.StyleID);s[h]!==void 0&&(s[h].nf&&(l=s[h].nf),s[h].Interior&&d.push(s[h].Interior),!!s[h].Parent);)h=s[h].Parent;switch(r.Type){case"Boolean":n.t="b",n.v=Pe(e);break;case"String":n.t="s",n.r=wn(Ce(e)),n.v=e.indexOf("<")>-1?Ce(a||e).replace(/<.*?>/g,""):n.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),n.v=(rr(e)-new Date(Date.UTC(1899,11,30)))/(1440*60*1e3),n.v!==n.v?n.v=Ce(e):n.v<60&&(n.v=n.v-1),(!l||l=="General")&&(l="yyyy-mm-dd");case"Number":n.v===void 0&&(n.v=+e),n.t||(n.t="n");break;case"Error":n.t="e",n.v=oi[e],f.cellText!==!1&&(n.w=e);break;default:e==""&&a==""?n.t="z":(n.t="s",n.v=wn(a||e));break}if(ap(n,l,f),f.cellFormula!==!1)if(n.Formula){var u=Ce(n.Formula);u.charCodeAt(0)==61&&(u=u.slice(1)),n.f=ba(u,t),delete n.Formula,n.ArrayRange=="RC"?n.F=ba("RC:RC",t):n.ArrayRange&&(n.F=ba(n.ArrayRange,t),o.push([He(n.F),n.F]))}else for(p=0;p<o.length;++p)t.r>=o[p][0].s.r&&t.r<=o[p][0].e.r&&t.c>=o[p][0].s.c&&t.c<=o[p][0].e.c&&(n.F=o[p][1]);f.cellStyles&&(d.forEach(function(_){!x.patternType&&_.patternType&&(x.patternType=_.patternType)}),n.s=x),n.StyleID!==void 0&&(n.ixfe=n.StyleID)}function sp(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function l0(e,a){var r=a||{};Ps();var n=Ga(P0(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(n=Ne(n));var t=n.slice(0,1024).toLowerCase(),s=!1;if(t=t.replace(/".*?"/g,""),(t.indexOf(">")&1023)>Math.min(t.indexOf(",")&1023,t.indexOf(";")&1023)){var i=tr(r);return i.type="string",lt.to_workbook(n,i)}if(t.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(de){t.indexOf("<"+de)>=0&&(s=!0)}),s)return dp(n,r);st={"General Number":"General","General Date":Te[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":Te[15],"Short Date":Te[14],"Long Time":Te[19],"Medium Time":Te[18],"Short Time":Te[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:Te[2],Standard:Te[4],Percent:Te[10],Scientific:Te[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,o=[],f,l={},h=[],x=r.dense?[]:{},d="",p={},u={},_=Ir('<Data ss:Type="String">'),E=0,R=0,g=0,b={s:{r:2e6,c:2e6},e:{r:0,c:0}},H={},U={},S="",k=0,v=[],I={},P={},N=0,K=[],Z=[],J={},se=[],ee,ve=!1,j=[],he=[],ne={},F=0,B=0,M={Sheets:[],WBProps:{date1904:!1}},L={};ot.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"");for(var z="";c=ot.exec(n);)switch(c[3]=(z=c[3]).toLowerCase()){case"data":if(z=="data"){if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break}if(o[o.length-1][1])break;c[1]==="/"?np(n.slice(E,c.index),S,_,o[o.length-1][0]=="comment"?J:p,{c:R,r:g},H,se[R],u,j,r):(S="",_=Ir(c[0]),E=c.index+c[0].length);break;case"cell":if(c[1]==="/")if(Z.length>0&&(p.c=Z),(!r.sheetRows||r.sheetRows>g)&&p.v!==void 0&&(r.dense?(x[g]||(x[g]=[]),x[g][R]=p):x[Ze(R)+nr(g)]=p),p.HRef&&(p.l={Target:Ce(p.HRef)},p.HRefScreenTip&&(p.l.Tooltip=p.HRefScreenTip),delete p.HRef,delete p.HRefScreenTip),(p.MergeAcross||p.MergeDown)&&(F=R+(parseInt(p.MergeAcross,10)|0),B=g+(parseInt(p.MergeDown,10)|0),v.push({s:{c:R,r:g},e:{c:F,r:B}})),!r.sheetStubs)p.MergeAcross?R=F+1:++R;else if(p.MergeAcross||p.MergeDown){for(var ae=R;ae<=F;++ae)for(var re=g;re<=B;++re)(ae>R||re>g)&&(r.dense?(x[re]||(x[re]=[]),x[re][ae]={t:"z"}):x[Ze(ae)+nr(re)]={t:"z"});R=F+1}else++R;else p=Qd(c[0]),p.Index&&(R=+p.Index-1),R<b.s.c&&(b.s.c=R),R>b.e.c&&(b.e.c=R),c[0].slice(-2)==="/>"&&++R,Z=[];break;case"row":c[1]==="/"||c[0].slice(-2)==="/>"?(g<b.s.r&&(b.s.r=g),g>b.e.r&&(b.e.r=g),c[0].slice(-2)==="/>"&&(u=Ir(c[0]),u.Index&&(g=+u.Index-1)),R=0,++g):(u=Ir(c[0]),u.Index&&(g=+u.Index-1),ne={},(u.AutoFitHeight=="0"||u.Height)&&(ne.hpx=parseInt(u.Height,10),ne.hpt=Di(ne.hpx),he[g]=ne),u.Hidden=="1"&&(ne.hidden=!0,he[g]=ne));break;case"worksheet":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"));h.push(d),b.s.r<=b.e.r&&b.s.c<=b.e.c&&(x["!ref"]=ye(b),r.sheetRows&&r.sheetRows<=b.e.r&&(x["!fullref"]=x["!ref"],b.e.r=r.sheetRows-1,x["!ref"]=ye(b))),v.length&&(x["!merges"]=v),se.length>0&&(x["!cols"]=se),he.length>0&&(x["!rows"]=he),l[d]=x}else b={s:{r:2e6,c:2e6},e:{r:0,c:0}},g=R=0,o.push([c[3],!1]),f=Ir(c[0]),d=Ce(f.Name),x=r.dense?[]:{},v=[],j=[],he=[],L={name:d,Hidden:0},M.Sheets.push(L);break;case"table":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else{if(c[0].slice(-2)=="/>")break;o.push([c[3],!1]),se=[],ve=!1}break;case"style":c[1]==="/"?tp(H,U,r):U=Ir(c[0]);break;case"numberformat":U.nf=Ce(Ir(c[0]).Format||"General"),st[U.nf]&&(U.nf=st[U.nf]);for(var Q=0;Q!=392&&Te[Q]!=U.nf;++Q);if(Q==392){for(Q=57;Q!=392;++Q)if(Te[Q]==null){ma(U.nf,Q);break}}break;case"column":if(o[o.length-1][0]!=="table")break;if(ee=Ir(c[0]),ee.Hidden&&(ee.hidden=!0,delete ee.Hidden),ee.Width&&(ee.wpx=parseInt(ee.Width,10)),!ve&&ee.wpx>10){ve=!0,vr=Ci;for(var Y=0;Y<se.length;++Y)se[Y]&&Ma(se[Y])}ve&&Ma(ee),se[ee.Index-1||se.length]=ee;for(var ie=0;ie<+ee.Span;++ie)se[se.length]=tr(ee);break;case"namedrange":if(c[1]==="/")break;M.Names||(M.Names=[]);var y=_e(c[0]),me={Name:y.Name,Ref:ba(y.RefersTo.slice(1),{r:0,c:0})};M.Sheets.length>0&&(me.Sheet=M.Sheets.length-1),M.Names.push(me);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(c[0].slice(-2)==="/>")break;c[1]==="/"?S+=n.slice(k,c.index):k=c.index+c[0].length;break;case"interior":if(!r.cellStyles)break;U.Interior=Ir(c[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(c[0].slice(-2)==="/>")break;c[1]==="/"?Vo(I,z,n.slice(N,c.index)):N=c.index+c[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else o.push([c[3],!1]);break;case"comment":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"));sp(J),Z.push(J)}else o.push([c[3],!1]),f=Ir(c[0]),J={a:f.Author};break;case"autofilter":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else if(c[0].charAt(c[0].length-2)!=="/"){var pe=Ir(c[0]);x["!autofilter"]={ref:ba(pe.Range).replace(/\$/g,"")},o.push([c[3],!0])}break;case"name":break;case"datavalidation":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(c[1]==="/"){if((f=o.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break;case"null":break;default:if(o.length==0&&c[3]=="document"||o.length==0&&c[3]=="uof")return cs(n,r);var ge=!0;switch(o[o.length-1][0]){case"officedocumentsettings":switch(c[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:ge=!1}break;case"componentoptions":switch(c[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:ge=!1}break;case"excelworkbook":switch(c[3]){case"date1904":M.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:ge=!1}break;case"workbookoptions":switch(c[3]){case"owcversion":break;case"height":break;case"width":break;default:ge=!1}break;case"worksheetoptions":switch(c[3]){case"visible":if(c[0].slice(-2)!=="/>")if(c[1]==="/")switch(n.slice(N,c.index)){case"SheetHidden":L.Hidden=1;break;case"SheetVeryHidden":L.Hidden=2;break}else N=c.index+c[0].length;break;case"header":x["!margins"]||nt(x["!margins"]={},"xlml"),isNaN(+_e(c[0]).Margin)||(x["!margins"].header=+_e(c[0]).Margin);break;case"footer":x["!margins"]||nt(x["!margins"]={},"xlml"),isNaN(+_e(c[0]).Margin)||(x["!margins"].footer=+_e(c[0]).Margin);break;case"pagemargins":var le=_e(c[0]);x["!margins"]||nt(x["!margins"]={},"xlml"),isNaN(+le.Top)||(x["!margins"].top=+le.Top),isNaN(+le.Left)||(x["!margins"].left=+le.Left),isNaN(+le.Right)||(x["!margins"].right=+le.Right),isNaN(+le.Bottom)||(x["!margins"].bottom=+le.Bottom);break;case"displayrighttoleft":M.Views||(M.Views=[]),M.Views[0]||(M.Views[0]={}),M.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:ge=!1}break;case"pivottable":case"pivotcache":switch(c[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:ge=!1}break;case"pagebreaks":switch(c[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:ge=!1}break;case"autofilter":switch(c[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:ge=!1}break;case"querytable":switch(c[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:ge=!1}break;case"datavalidation":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:ge=!1}break;case"sorting":case"conditionalformatting":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:ge=!1}break;case"mapinfo":case"schema":case"data":switch(c[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:ge=!1}break;case"smarttags":break;default:ge=!1;break}if(ge||c[3].match(/!\[CDATA/))break;if(!o[o.length-1][1])throw"Unrecognized tag: "+c[3]+"|"+o.join("|");if(o[o.length-1][0]==="customdocumentproperties"){if(c[0].slice(-2)==="/>")break;c[1]==="/"?rp(P,z,K,n.slice(N,c.index)):(K=c,N=c.index+c[0].length);break}if(r.WTF)throw"Unrecognized tag: "+c[3]+"|"+o.join("|")}var fe={};return!r.bookSheets&&!r.bookProps&&(fe.Sheets=l),fe.SheetNames=h,fe.Workbook=M,fe.SSF=tr(Te),fe.Props=I,fe.Custprops=P,fe}function m0(e,a){switch(q0(a=a||{}),a.type||"base64"){case"base64":return l0(Sr(e),a);case"binary":case"buffer":case"file":return l0(e,a);case"array":return l0(Aa(e),a)}}function ip(e){var a={},r=e.content;if(r.l=28,a.AnsiUserType=r.read_shift(0,"lpstr-ansi"),a.AnsiClipboardFormat=Eo(r),r.length-r.l<=4)return a;var n=r.read_shift(4);if(n==0||n>40||(r.l-=4,a.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(n=r.read_shift(4),n!==1907505652)||(a.UnicodeClipboardFormat=ko(r),n=r.read_shift(4),n==0||n>40))return a;r.l-=4,a.Reserved2=r.read_shift(0,"lpwstr")}var cp=[60,1084,2066,2165,2175];function fp(e,a,r,n,t){var s=n,i=[],c=r.slice(r.l,r.l+s);if(t&&t.enc&&t.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:t.enc.insitu(c)}i.push(c),r.l+=s;for(var o=Jr(r,r.l),f=_0[o],l=0;f!=null&&cp.indexOf(o)>-1;)s=Jr(r,r.l+2),l=r.l+4,o==2066?l+=4:(o==2165||o==2175)&&(l+=12),c=r.slice(l,r.l+4+s),i.push(c),r.l+=4+s,f=_0[o=Jr(r,r.l)];var h=sa(i);ar(h,0);var x=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(x),x+=i[d].length;if(h.length<n)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+n;return a.f(h,h.length,t)}function Gr(e,a,r){if(e.t!=="z"&&e.XF){var n=0;try{n=e.z||e.XF.numFmtId||0,a.cellNF&&(e.z=Te[n])}catch(s){if(a.WTF)throw s}if(!a||a.cellText!==!1)try{e.t==="e"?e.w=e.w||ya[e.v]:n===0||n=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ct(e.v):e.w=Ea(e.v):e.w=Or(n,e.v,{date1904:!!r,dateNF:a&&a.dateNF})}catch(s){if(a.WTF)throw s}if(a.cellDates&&n&&e.t=="n"&&Ba(Te[n]||String(n))){var t=ga(e.v);t&&(e.t="d",e.v=new Date(t.y,t.m-1,t.d,t.H,t.M,t.S,t.u))}}}function Dt(e,a,r){return{v:e,ixfe:a,t:r}}function op(e,a){var r={opts:{}},n={},t=a.dense?[]:{},s={},i={},c=null,o=[],f="",l={},h,x="",d,p,u,_,E={},R=[],g,b,H=[],U=[],S={Sheets:[],WBProps:{date1904:!1},Views:[{}]},k={},v=function(we){return we<8?_a[we]:we<64&&U[we-8]||_a[we]},I=function(we,De,Le){var be=De.XF.data;if(!(!be||!be.patternType||!Le||!Le.cellStyles)){De.s={},De.s.patternType=be.patternType;var Wr;(Wr=ht(v(be.icvFore)))&&(De.s.fgColor={rgb:Wr}),(Wr=ht(v(be.icvBack)))&&(De.s.bgColor={rgb:Wr})}},P=function(we,De,Le){if(!(ne>1)&&!(Le.sheetRows&&we.r>=Le.sheetRows)){if(Le.cellStyles&&De.XF&&De.XF.data&&I(we,De,Le),delete De.ixfe,delete De.XF,h=we,x=Ee(we),(!i||!i.s||!i.e)&&(i={s:{r:0,c:0},e:{r:0,c:0}}),we.r<i.s.r&&(i.s.r=we.r),we.c<i.s.c&&(i.s.c=we.c),we.r+1>i.e.r&&(i.e.r=we.r+1),we.c+1>i.e.c&&(i.e.c=we.c+1),Le.cellFormula&&De.f){for(var be=0;be<R.length;++be)if(!(R[be][0].s.c>we.c||R[be][0].s.r>we.r)&&!(R[be][0].e.c<we.c||R[be][0].e.r<we.r)){De.F=ye(R[be][0]),(R[be][0].s.c!=we.c||R[be][0].s.r!=we.r)&&delete De.f,De.f&&(De.f=""+or(R[be][1],i,we,j,N));break}}Le.dense?(t[we.r]||(t[we.r]=[]),t[we.r][we.c]=De):t[x]=De}},N={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:R,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!a&&!!a.cellStyles,WTF:!!a&&!!a.wtf};a.password&&(N.password=a.password);var K,Z=[],J=[],se=[],ee=[],ve=!1,j=[];j.SheetNames=N.snames,j.sharedf=N.sharedf,j.arrayf=N.arrayf,j.names=[],j.XTI=[];var he=0,ne=0,F=0,B=[],M=[],L;N.codepage=1200,Lr(1200);for(var z=!1;e.l<e.length-1;){var ae=e.l,re=e.read_shift(2);if(re===0&&he===10)break;var Q=e.l===e.length?0:e.read_shift(2),Y=_0[re];if(Y&&Y.f){if(a.bookSheets&&he===133&&re!==133)break;if(he=re,Y.r===2||Y.r==12){var ie=e.read_shift(2);if(Q-=2,!N.enc&&ie!==re&&((ie&255)<<8|ie>>8)!==re)throw new Error("rt mismatch: "+ie+"!="+re);Y.r==12&&(e.l+=10,Q-=10)}var y={};if(re===10?y=Y.f(e,Q,N):y=fp(re,Y,e,Q,N),ne==0&&[9,521,1033,2057].indexOf(he)===-1)continue;switch(re){case 34:r.opts.Date1904=S.WBProps.date1904=y;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(N.enc||(e.l=0),N.enc=y,!a.password)throw new Error("File is password-protected");if(y.valid==null)throw new Error("Encryption scheme unsupported");if(!y.valid)throw new Error("Password is incorrect");break;case 92:N.lastuser=y;break;case 66:var me=Number(y);switch(me){case 21010:me=1200;break;case 32768:me=1e4;break;case 32769:me=1252;break}Lr(N.codepage=me),z=!0;break;case 317:N.rrtabid=y;break;case 25:N.winlocked=y;break;case 439:r.opts.RefreshAll=y;break;case 12:r.opts.CalcCount=y;break;case 16:r.opts.CalcDelta=y;break;case 17:r.opts.CalcIter=y;break;case 13:r.opts.CalcMode=y;break;case 14:r.opts.CalcPrecision=y;break;case 95:r.opts.CalcSaveRecalc=y;break;case 15:N.CalcRefMode=y;break;case 2211:r.opts.FullCalc=y;break;case 129:y.fDialog&&(t["!type"]="dialog"),y.fBelow||((t["!outline"]||(t["!outline"]={})).above=!0),y.fRight||((t["!outline"]||(t["!outline"]={})).left=!0);break;case 224:H.push(y);break;case 430:j.push([y]),j[j.length-1].XTI=[];break;case 35:case 547:j[j.length-1].push(y);break;case 24:case 536:L={Name:y.Name,Ref:or(y.rgce,i,null,j,N)},y.itab>0&&(L.Sheet=y.itab-1),j.names.push(L),j[0]||(j[0]=[],j[0].XTI=[]),j[j.length-1].push(y),y.Name=="_xlnm._FilterDatabase"&&y.itab>0&&y.rgce&&y.rgce[0]&&y.rgce[0][0]&&y.rgce[0][0][0]=="PtgArea3d"&&(M[y.itab-1]={ref:ye(y.rgce[0][0][1][2])});break;case 22:N.ExternCount=y;break;case 23:j.length==0&&(j[0]=[],j[0].XTI=[]),j[j.length-1].XTI=j[j.length-1].XTI.concat(y),j.XTI=j.XTI.concat(y);break;case 2196:if(N.biff<8)break;L!=null&&(L.Comment=y[1]);break;case 18:t["!protect"]=y;break;case 19:y!==0&&N.WTF&&console.error("Password verifier: "+y);break;case 133:s[y.pos]=y,N.snames.push(y.name);break;case 10:{if(--ne)break;if(i.e){if(i.e.r>0&&i.e.c>0){if(i.e.r--,i.e.c--,t["!ref"]=ye(i),a.sheetRows&&a.sheetRows<=i.e.r){var pe=i.e.r;i.e.r=a.sheetRows-1,t["!fullref"]=t["!ref"],t["!ref"]=ye(i),i.e.r=pe}i.e.r++,i.e.c++}Z.length>0&&(t["!merges"]=Z),J.length>0&&(t["!objects"]=J),se.length>0&&(t["!cols"]=se),ee.length>0&&(t["!rows"]=ee),S.Sheets.push(k)}f===""?l=t:n[f]=t,t=a.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(N.biff===8&&(N.biff={9:2,521:3,1033:4}[re]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[y.BIFFVer]||8),N.biffguess=y.BIFFVer==0,y.BIFFVer==0&&y.dt==4096&&(N.biff=5,z=!0,Lr(N.codepage=28591)),N.biff==8&&y.BIFFVer==0&&y.dt==16&&(N.biff=2),ne++)break;if(t=a.dense?[]:{},N.biff<8&&!z&&(z=!0,Lr(N.codepage=a.codepage||1252)),N.biff<5||y.BIFFVer==0&&y.dt==4096){f===""&&(f="Sheet1"),i={s:{r:0,c:0},e:{r:0,c:0}};var ge={pos:e.l-Q,name:f};s[ge.pos]=ge,N.snames.push(f)}else f=(s[ae]||{name:""}).name;y.dt==32&&(t["!type"]="chart"),y.dt==64&&(t["!type"]="macro"),Z=[],J=[],N.arrayf=R=[],se=[],ee=[],ve=!1,k={Hidden:(s[ae]||{hs:0}).hs,name:f}}break;case 515:case 3:case 2:t["!type"]=="chart"&&(a.dense?(t[y.r]||[])[y.c]:t[Ee({c:y.c,r:y.r})])&&++y.c,g={ixfe:y.ixfe,XF:H[y.ixfe]||{},v:y.val,t:"n"},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a);break;case 5:case 517:g={ixfe:y.ixfe,XF:H[y.ixfe],v:y.val,t:y.t},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a);break;case 638:g={ixfe:y.ixfe,XF:H[y.ixfe],v:y.rknum,t:"n"},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a);break;case 189:for(var le=y.c;le<=y.C;++le){var fe=y.rkrec[le-y.c][0];g={ixfe:fe,XF:H[fe],v:y.rkrec[le-y.c][1],t:"n"},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:le,r:y.r},g,a)}break;case 6:case 518:case 1030:{if(y.val=="String"){c=y;break}if(g=Dt(y.val,y.cell.ixfe,y.tt),g.XF=H[g.ixfe],a.cellFormula){var de=y.formula;if(de&&de[0]&&de[0][0]&&de[0][0][0]=="PtgExp"){var Ae=de[0][0][1][0],qe=de[0][0][1][1],sr=Ee({r:Ae,c:qe});E[sr]?g.f=""+or(y.formula,i,y.cell,j,N):g.F=((a.dense?(t[Ae]||[])[qe]:t[sr])||{}).F}else g.f=""+or(y.formula,i,y.cell,j,N)}F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P(y.cell,g,a),c=y}break;case 7:case 519:if(c)c.val=y,g=Dt(y,c.cell.ixfe,"s"),g.XF=H[g.ixfe],a.cellFormula&&(g.f=""+or(c.formula,i,c.cell,j,N)),F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P(c.cell,g,a),c=null;else throw new Error("String record expects Formula");break;case 33:case 545:{R.push(y);var ir=Ee(y[0].s);if(d=a.dense?(t[y[0].s.r]||[])[y[0].s.c]:t[ir],a.cellFormula&&d){if(!c||!ir||!d)break;d.f=""+or(y[1],i,y[0],j,N),d.F=ye(y[0])}}break;case 1212:{if(!a.cellFormula)break;if(x){if(!c)break;E[Ee(c.cell)]=y[0],d=a.dense?(t[c.cell.r]||[])[c.cell.c]:t[Ee(c.cell)],(d||{}).f=""+or(y[0],i,h,j,N)}}break;case 253:g=Dt(o[y.isst].t,y.ixfe,"s"),o[y.isst].h&&(g.h=o[y.isst].h),g.XF=H[g.ixfe],F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a);break;case 513:a.sheetStubs&&(g={ixfe:y.ixfe,XF:H[y.ixfe],t:"z"},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a));break;case 190:if(a.sheetStubs)for(var Er=y.c;Er<=y.C;++Er){var Re=y.ixfe[Er-y.c];g={ixfe:Re,XF:H[Re],t:"z"},F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:Er,r:y.r},g,a)}break;case 214:case 516:case 4:g=Dt(y.val,y.ixfe,"s"),g.XF=H[g.ixfe],F>0&&(g.z=B[g.ixfe>>8&63]),Gr(g,a,r.opts.Date1904),P({c:y.c,r:y.r},g,a);break;case 0:case 512:ne===1&&(i=y);break;case 252:o=y;break;case 1054:if(N.biff==4){B[F++]=y[1];for(var Xe=0;Xe<F+163&&Te[Xe]!=y[1];++Xe);Xe>=163&&ma(y[1],F+163)}else ma(y[1],y[0]);break;case 30:{B[F++]=y;for(var Oe=0;Oe<F+163&&Te[Oe]!=y;++Oe);Oe>=163&&ma(y,F+163)}break;case 229:Z=Z.concat(y);break;case 93:J[y.cmo[0]]=N.lastobj=y;break;case 438:N.lastobj.TxO=y;break;case 127:N.lastobj.ImData=y;break;case 440:for(_=y[0].s.r;_<=y[0].e.r;++_)for(u=y[0].s.c;u<=y[0].e.c;++u)d=a.dense?(t[_]||[])[u]:t[Ee({c:u,r:_})],d&&(d.l=y[1]);break;case 2048:for(_=y[0].s.r;_<=y[0].e.r;++_)for(u=y[0].s.c;u<=y[0].e.c;++u)d=a.dense?(t[_]||[])[u]:t[Ee({c:u,r:_})],d&&d.l&&(d.l.Tooltip=y[1]);break;case 28:{if(N.biff<=5&&N.biff>=2)break;d=a.dense?(t[y[0].r]||[])[y[0].c]:t[Ee(y[0])];var $e=J[y[2]];d||(a.dense?(t[y[0].r]||(t[y[0].r]=[]),d=t[y[0].r][y[0].c]={t:"z"}):d=t[Ee(y[0])]={t:"z"},i.e.r=Math.max(i.e.r,y[0].r),i.s.r=Math.min(i.s.r,y[0].r),i.e.c=Math.max(i.e.c,y[0].c),i.s.c=Math.min(i.s.c,y[0].c)),d.c||(d.c=[]),p={a:y[1],t:$e.TxO.t},d.c.push(p)}break;case 2173:Ou(H[y.ixfe],y.ext);break;case 125:{if(!N.cellStyles)break;for(;y.e>=y.s;)se[y.e--]={width:y.w/256,level:y.level||0,hidden:!!(y.flags&1)},ve||(ve=!0,X0(y.w/256)),Ma(se[y.e+1])}break;case 520:{var Ie={};y.level!=null&&(ee[y.r]=Ie,Ie.level=y.level),y.hidden&&(ee[y.r]=Ie,Ie.hidden=!0),y.hpt&&(ee[y.r]=Ie,Ie.hpt=y.hpt,Ie.hpx=ut(y.hpt))}break;case 38:case 39:case 40:case 41:t["!margins"]||nt(t["!margins"]={}),t["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[re]]=y;break;case 161:t["!margins"]||nt(t["!margins"]={}),t["!margins"].header=y.header,t["!margins"].footer=y.footer;break;case 574:y.RTL&&(S.Views[0].RTL=!0);break;case 146:U=y;break;case 2198:K=y;break;case 140:b=y;break;case 442:f?k.CodeName=y||k.name:S.WBProps.CodeName=y||"ThisWorkbook";break}}else Y||console.error("Missing Info for XLS Record 0x"+re.toString(16)),e.l+=Q}return r.SheetNames=zr(s).sort(function(kr,we){return Number(kr)-Number(we)}).map(function(kr){return s[kr].name}),a.bookSheets||(r.Sheets=n),!r.SheetNames.length&&l["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=l)):r.Preamble=l,r.Sheets&&M.forEach(function(kr,we){r.Sheets[r.SheetNames[we]]["!autofilter"]=kr}),r.Strings=o,r.SSF=tr(Te),N.enc&&(r.Encryption=N.enc),K&&(r.Themes=K),r.Metadata={},b!==void 0&&(r.Metadata.Country=b),j.names.length>0&&(S.Names=j.names),r.Workbook=S,r}var as={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function lp(e,a,r){var n=Se.find(e,"/!DocumentSummaryInformation");if(n&&n.size>0)try{var t=Un(n,yo,as.DSI);for(var s in t)a[s]=t[s]}catch(f){if(r.WTF)throw f}var i=Se.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=Un(i,Co,as.SI);for(var o in c)a[o]==null&&(a[o]=c[o])}catch(f){if(r.WTF)throw f}a.HeadingPairs&&a.TitlesOfParts&&(hi(a.HeadingPairs,a.TitlesOfParts,a,r),delete a.HeadingPairs,delete a.TitlesOfParts)}function Ki(e,a){a||(a={}),q0(a),ws(),a.codepage&&C0(a.codepage);var r,n;if(e.FullPaths){if(Se.find(e,"/encryption"))throw new Error("File is password-protected");r=Se.find(e,"!CompObj"),n=Se.find(e,"/Workbook")||Se.find(e,"/Book")}else{switch(a.type){case"base64":e=Pr(Sr(e));break;case"binary":e=Pr(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}ar(e,0),n={content:e}}var t,s;if(r&&ip(r),a.bookProps&&!a.bookSheets)t={};else{var i=Fe?"buffer":"array";if(n&&n.content)t=op(n.content,a);else if((s=Se.find(e,"PerfectOffice_MAIN"))&&s.content)t=at.to_workbook(s.content,(a.type=i,a));else if((s=Se.find(e,"NativeContent_MAIN"))&&s.content)t=at.to_workbook(s.content,(a.type=i,a));else throw(s=Se.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");a.bookVBA&&e.FullPaths&&Se.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(t.vbaraw=Yu(e))}var c={};return e.FullPaths&&lp(e,c,a),t.Props=t.Custprops=c,a.bookFiles&&(t.cfb=e),t}var Gt={0:{f:Xx},1:{f:Yx},2:{f:nd},3:{f:Qx},4:{f:Jx},5:{f:td},6:{f:cd},7:{f:rd},8:{f:ud},9:{f:hd},10:{f:od},11:{f:ld},12:{f:qx},13:{f:sd},14:{f:ed},15:{f:Zx},16:{f:Wi},17:{f:fd},18:{f:ad},19:{f:U0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Ud},40:{},42:{},43:{f:ou},44:{f:fu},45:{f:lu},46:{f:uu},47:{f:hu},48:{},49:{f:uo},50:{},51:{f:Iu},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:wi},62:{f:id},63:{f:Bu},64:{f:kd},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:xr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Ed},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Kx},148:{f:$x,p:16},151:{f:pd},152:{},153:{f:Md},154:{},155:{},156:{f:Ld},157:{},158:{},159:{T:1,f:Ch},160:{T:-1},161:{T:1,f:Sa},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:xd},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Nu},336:{T:-1},337:{f:bu,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:d0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Ti},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:vd},427:{f:gd},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:_d},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:zx},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:dd},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:d0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:$u},633:{T:1},634:{T:-1},635:{T:1,f:Xu},636:{T:-1},637:{f:po},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:yd},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Td},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},_0={6:{f:f0},10:{f:na},12:{f:Ye},13:{f:Ye},14:{f:Ve},15:{f:Ve},16:{f:lr},17:{f:Ve},18:{f:Ve},19:{f:Ye},20:{f:Wn},21:{f:Wn},23:{f:Ti},24:{f:Xn},25:{f:Ve},26:{},27:{},28:{f:Wl},29:{},34:{f:Ve},35:{f:Gn},38:{f:lr},39:{f:lr},40:{f:lr},41:{f:lr},42:{f:Ve},43:{f:Ve},47:{f:zh},49:{f:Al},51:{f:Ye},60:{},61:{f:kl},64:{f:Ve},65:{f:wl},66:{f:Ye},77:{},80:{},81:{},82:{},85:{f:Ye},89:{},90:{},91:{},92:{f:xl},93:{f:Xl},94:{},95:{f:Ve},96:{},97:{},99:{f:Ve},125:{f:wi},128:{f:bl},129:{f:dl},130:{f:Ye},131:{f:Ve},132:{f:Ve},133:{f:pl},134:{},140:{f:Jl},141:{f:Ye},144:{},146:{f:Ql},151:{},152:{},153:{},154:{},155:{},156:{f:Ye},157:{},158:{},160:{f:nh},161:{f:rh},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Dl},190:{f:Ol},193:{f:na},197:{},198:{},199:{},200:{},201:{},202:{f:Ve},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Ye},220:{},221:{f:Ve},222:{},224:{f:Il},225:{f:ul},226:{f:na},227:{},229:{f:Gl},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:vl},253:{f:Fl},255:{f:gl},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:vi},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Ve},353:{f:na},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Ll},431:{f:Ve},432:{},433:{},434:{},437:{},438:{f:Kl},439:{f:Ve},440:{f:Yl},441:{},442:{f:gt},443:{},444:{f:Ye},445:{},446:{},448:{f:na},449:{f:El,r:2},450:{f:na},512:{f:Hn},513:{f:th},515:{f:Pl},516:{f:Sl},517:{f:Vn},519:{f:sh},520:{f:ml},523:{},545:{f:$n},549:{f:jn},566:{},574:{f:Tl},638:{f:Rl},659:{},1048:{},1054:{f:yl},1084:{},1212:{f:jl},2048:{f:ql},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:yt},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:na},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:eh,r:12},2173:{f:Du,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Ve,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:Ul,r:12},2197:{},2198:{f:Au,r:12},2199:{},2200:{},2201:{},2202:{f:Hl,r:12},2203:{f:na},2204:{},2205:{},2206:{},2207:{},2211:{f:_l},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Ye},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:ah},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:Zl},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Hn},1:{},2:{f:oh},3:{f:fh},4:{f:ch},5:{f:Vn},7:{f:lh},8:{},9:{f:yt},11:{},22:{f:Ye},30:{f:Cl},31:{},32:{},33:{f:$n},36:{},37:{f:jn},50:{f:hh},62:{},52:{},67:{},68:{f:Ye},69:{},86:{},126:{},127:{f:ih},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:uh},223:{},234:{},354:{},421:{},518:{f:f0},521:{f:yt},536:{f:Xn},547:{f:Gn},561:{},579:{},1030:{f:f0},1033:{f:yt},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function br(e,a,r,n){var t=a;if(!isNaN(t)){var s=(r||[]).length||0,i=e.next(4);i.write_shift(2,t),i.write_shift(2,s),s>0&&ti(r)&&e.push(r)}}function ts(e,a){var r=a||{},n=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var t=e.match(/<table/i);if(!t)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=t.index,c=s&&s.index||e.length,o=bf(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),f=-1,l=0,h=0,x=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<o.length;++i){var u=o[i].trim(),_=u.slice(0,3).toLowerCase();if(_=="<tr"){if(++f,r.sheetRows&&r.sheetRows<=f){--f;break}l=0;continue}if(!(_!="<td"&&_!="<th")){var E=u.split(/<\/t[dh]>/i);for(c=0;c<E.length;++c){var R=E[c].trim();if(R.match(/<t[dh]/i)){for(var g=R,b=0;g.charAt(0)=="<"&&(b=g.indexOf(">"))>-1;)g=g.slice(b+1);for(var H=0;H<p.length;++H){var U=p[H];U.s.c==l&&U.s.r<f&&f<=U.e.r&&(l=U.e.c+1,H=-1)}var S=_e(R.slice(0,R.indexOf(">")));x=S.colspan?+S.colspan:1,((h=+S.rowspan)>1||x>1)&&p.push({s:{r:f,c:l},e:{r:f+(h||1)-1,c:l+x-1}});var k=S.t||S["data-t"]||"";if(!g.length){l+=x;continue}if(g=Gs(g),d.s.r>f&&(d.s.r=f),d.e.r<f&&(d.e.r=f),d.s.c>l&&(d.s.c=l),d.e.c<l&&(d.e.c=l),!g.length){l+=x;continue}var v={t:"s",v:g};r.raw||!g.trim().length||k=="s"||(g==="TRUE"?v={t:"b",v:!0}:g==="FALSE"?v={t:"b",v:!1}:isNaN(Vr(g))?isNaN(La(g).getDate())||(v={t:"d",v:rr(g)},r.cellDates||(v={t:"n",v:_r(v.v)}),v.z=r.dateNF||Te[14]):v={t:"n",v:Vr(g)}),r.dense?(n[f]||(n[f]=[]),n[f][l]=v):n[Ee({r:f,c:l})]=v,l+=x}}}}return n["!ref"]=ye(d),p.length&&(n["!merges"]=p),n}function hp(e,a,r,n){for(var t=e["!merges"]||[],s=[],i=a.s.c;i<=a.e.c;++i){for(var c=0,o=0,f=0;f<t.length;++f)if(!(t[f].s.r>r||t[f].s.c>i)&&!(t[f].e.r<r||t[f].e.c<i)){if(t[f].s.r<r||t[f].s.c<i){c=-1;break}c=t[f].e.r-t[f].s.r+1,o=t[f].e.c-t[f].s.c+1;break}if(!(c<0)){var l=Ee({r,c:i}),h=n.dense?(e[r]||[])[i]:e[l],x=h&&h.v!=null&&(h.h||b0(h.w||(aa(h),h.w)||""))||"",d={};c>1&&(d.rowspan=c),o>1&&(d.colspan=o),n.editable?x='<span contenteditable="true">'+x+"</span>":h&&(d["data-t"]=h&&h.t||"z",h.v!=null&&(d["data-v"]=h.v),h.z!=null&&(d["data-z"]=h.z),h.l&&(h.l.Target||"#").charAt(0)!="#"&&(x='<a href="'+h.l.Target+'">'+x+"</a>")),d.id=(n.id||"sjs")+"-"+l,s.push(qf("td",x,d))}}var p="<tr>";return p+s.join("")+"</tr>"}var up='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',xp="</body></html>";function dp(e,a){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return da(ts(r[0],a),a);var n=Z0();return r.forEach(function(t,s){Q0(n,ts(t,a),"Sheet"+(s+1))}),n}function pp(e,a,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function vp(e,a){var r=a||{},n=r.header!=null?r.header:up,t=r.footer!=null?r.footer:xp,s=[n],i=Ua(e["!ref"]);r.dense=Array.isArray(e),s.push(pp(e,i,r));for(var c=i.s.r;c<=i.e.r;++c)s.push(hp(e,i,c,r));return s.push("</table>"+t),s.join("")}function Yi(e,a,r){var n=r||{},t=0,s=0;if(n.origin!=null)if(typeof n.origin=="number")t=n.origin;else{var i=typeof n.origin=="string"?gr(n.origin):n.origin;t=i.r,s=i.c}var c=a.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,c.length),f={s:{r:0,c:0},e:{r:t,c:s}};if(e["!ref"]){var l=Ua(e["!ref"]);f.s.r=Math.min(f.s.r,l.s.r),f.s.c=Math.min(f.s.c,l.s.c),f.e.r=Math.max(f.e.r,l.e.r),f.e.c=Math.max(f.e.c,l.e.c),t==-1&&(f.e.r=t=l.e.r+1)}var h=[],x=0,d=e["!rows"]||(e["!rows"]=[]),p=0,u=0,_=0,E=0,R=0,g=0;for(e["!cols"]||(e["!cols"]=[]);p<c.length&&u<o;++p){var b=c[p];if(ns(b)){if(n.display)continue;d[u]={hidden:!0}}var H=b.children;for(_=E=0;_<H.length;++_){var U=H[_];if(!(n.display&&ns(U))){var S=U.hasAttribute("data-v")?U.getAttribute("data-v"):U.hasAttribute("v")?U.getAttribute("v"):Gs(U.innerHTML),k=U.getAttribute("data-z")||U.getAttribute("z");for(x=0;x<h.length;++x){var v=h[x];v.s.c==E+s&&v.s.r<u+t&&u+t<=v.e.r&&(E=v.e.c+1-s,x=-1)}g=+U.getAttribute("colspan")||1,((R=+U.getAttribute("rowspan")||1)>1||g>1)&&h.push({s:{r:u+t,c:E+s},e:{r:u+t+(R||1)-1,c:E+s+(g||1)-1}});var I={t:"s",v:S},P=U.getAttribute("data-t")||U.getAttribute("t")||"";S!=null&&(S.length==0?I.t=P||"z":n.raw||S.trim().length==0||P=="s"||(S==="TRUE"?I={t:"b",v:!0}:S==="FALSE"?I={t:"b",v:!1}:isNaN(Vr(S))?isNaN(La(S).getDate())||(I={t:"d",v:rr(S)},n.cellDates||(I={t:"n",v:_r(I.v)}),I.z=n.dateNF||Te[14]):I={t:"n",v:Vr(S)})),I.z===void 0&&k!=null&&(I.z=k);var N="",K=U.getElementsByTagName("A");if(K&&K.length)for(var Z=0;Z<K.length&&!(K[Z].hasAttribute("href")&&(N=K[Z].getAttribute("href"),N.charAt(0)!="#"));++Z);N&&N.charAt(0)!="#"&&(I.l={Target:N}),n.dense?(e[u+t]||(e[u+t]=[]),e[u+t][E+s]=I):e[Ee({c:E+s,r:u+t})]=I,f.e.c<E+s&&(f.e.c=E+s),E+=g}}++u}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),f.e.r=Math.max(f.e.r,u-1+t),e["!ref"]=ye(f),u>=o&&(e["!fullref"]=ye((f.e.r=c.length-p+u-1+t,f))),e}function qi(e,a){var r=a||{},n=r.dense?[]:{};return Yi(n,e,a)}function gp(e,a){return da(qi(e,a),a)}function ns(e){var a="",r=mp(e);return r&&(a=r(e).getPropertyValue("display")),a||(a=e.style&&e.style.display),a==="none"}function mp(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function _p(e){var a=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(n,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Ce(a.replace(/<[^>]*>/g,""));return[r]}var ss={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Ji(e,a){var r=a||{},n=P0(e),t=[],s,i,c={name:""},o="",f=0,l,h,x={},d=[],p=r.dense?[]:{},u,_,E={value:""},R="",g=0,b=[],H=-1,U=-1,S={s:{r:1e6,c:1e7},e:{r:0,c:0}},k=0,v={},I=[],P={},N=0,K=0,Z=[],J=1,se=1,ee=[],ve={Names:[]},j={},he=["",""],ne=[],F={},B="",M=0,L=!1,z=!1,ae=0;for(ot.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");u=ot.exec(n);)switch(u[3]=u[3].replace(/_.*$/,"")){case"table":case"工作表":u[1]==="/"?(S.e.c>=S.s.c&&S.e.r>=S.s.r?p["!ref"]=ye(S):p["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=S.e.r&&(p["!fullref"]=p["!ref"],S.e.r=r.sheetRows-1,p["!ref"]=ye(S)),I.length&&(p["!merges"]=I),Z.length&&(p["!rows"]=Z),l.name=l.名称||l.name,typeof JSON<"u"&&JSON.stringify(l),d.push(l.name),x[l.name]=p,z=!1):u[0].charAt(u[0].length-2)!=="/"&&(l=_e(u[0],!1),H=U=-1,S.s.r=S.s.c=1e7,S.e.r=S.e.c=0,p=r.dense?[]:{},I=[],Z=[],z=!0);break;case"table-row-group":u[1]==="/"?--k:++k;break;case"table-row":case"行":if(u[1]==="/"){H+=J,J=1;break}if(h=_e(u[0],!1),h.行号?H=h.行号-1:H==-1&&(H=0),J=+h["number-rows-repeated"]||1,J<10)for(ae=0;ae<J;++ae)k>0&&(Z[H+ae]={level:k});U=-1;break;case"covered-table-cell":u[1]!=="/"&&++U,r.sheetStubs&&(r.dense?(p[H]||(p[H]=[]),p[H][U]={t:"z"}):p[Ee({r:H,c:U})]={t:"z"}),R="",b=[];break;case"table-cell":case"数据":if(u[0].charAt(u[0].length-2)==="/")++U,E=_e(u[0],!1),se=parseInt(E["number-columns-repeated"]||"1",10),_={t:"z",v:null},E.formula&&r.cellFormula!=!1&&(_.f=Qn(Ce(E.formula))),(E.数据类型||E["value-type"])=="string"&&(_.t="s",_.v=Ce(E["string-value"]||""),r.dense?(p[H]||(p[H]=[]),p[H][U]=_):p[Ee({r:H,c:U})]=_),U+=se-1;else if(u[1]!=="/"){++U,R="",g=0,b=[],se=1;var re=J?H+J-1:H;if(U>S.e.c&&(S.e.c=U),U<S.s.c&&(S.s.c=U),H<S.s.r&&(S.s.r=H),re>S.e.r&&(S.e.r=re),E=_e(u[0],!1),ne=[],F={},_={t:E.数据类型||E["value-type"],v:null},r.cellFormula)if(E.formula&&(E.formula=Ce(E.formula)),E["number-matrix-columns-spanned"]&&E["number-matrix-rows-spanned"]&&(N=parseInt(E["number-matrix-rows-spanned"],10)||0,K=parseInt(E["number-matrix-columns-spanned"],10)||0,P={s:{r:H,c:U},e:{r:H+N-1,c:U+K-1}},_.F=ye(P),ee.push([P,_.F])),E.formula)_.f=Qn(E.formula);else for(ae=0;ae<ee.length;++ae)H>=ee[ae][0].s.r&&H<=ee[ae][0].e.r&&U>=ee[ae][0].s.c&&U<=ee[ae][0].e.c&&(_.F=ee[ae][1]);switch((E["number-columns-spanned"]||E["number-rows-spanned"])&&(N=parseInt(E["number-rows-spanned"],10)||0,K=parseInt(E["number-columns-spanned"],10)||0,P={s:{r:H,c:U},e:{r:H+N-1,c:U+K-1}},I.push(P)),E["number-columns-repeated"]&&(se=parseInt(E["number-columns-repeated"],10)),_.t){case"boolean":_.t="b",_.v=Pe(E["boolean-value"]);break;case"float":_.t="n",_.v=parseFloat(E.value);break;case"percentage":_.t="n",_.v=parseFloat(E.value);break;case"currency":_.t="n",_.v=parseFloat(E.value);break;case"date":_.t="d",_.v=rr(E["date-value"]),r.cellDates||(_.t="n",_.v=_r(_.v)),_.z="m/d/yy";break;case"time":_.t="n",_.v=Of(E["time-value"])/86400,r.cellDates&&(_.t="d",_.v=Kt(_.v)),_.z="HH:MM:SS";break;case"number":_.t="n",_.v=parseFloat(E.数据数值);break;default:if(_.t==="string"||_.t==="text"||!_.t)_.t="s",E["string-value"]!=null&&(R=Ce(E["string-value"]),b=[]);else throw new Error("Unsupported value type "+_.t)}}else{if(L=!1,_.t==="s"&&(_.v=R||"",b.length&&(_.R=b),L=g==0),j.Target&&(_.l=j),ne.length>0&&(_.c=ne,ne=[]),R&&r.cellText!==!1&&(_.w=R),L&&(_.t="z",delete _.v),(!L||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=H))for(var Q=0;Q<J;++Q){if(se=parseInt(E["number-columns-repeated"]||"1",10),r.dense)for(p[H+Q]||(p[H+Q]=[]),p[H+Q][U]=Q==0?_:tr(_);--se>0;)p[H+Q][U+se]=tr(_);else for(p[Ee({r:H+Q,c:U})]=_;--se>0;)p[Ee({r:H+Q,c:U+se})]=tr(_);S.e.c<=U&&(S.e.c=U)}se=parseInt(E["number-columns-repeated"]||"1",10),U+=se-1,se=0,_={},R="",b=[]}j={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(u[1]==="/"){if((s=t.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&t.push([u[3],!0]);break;case"annotation":if(u[1]==="/"){if((s=t.pop())[0]!==u[3])throw"Bad state: "+s;F.t=R,b.length&&(F.R=b),F.a=B,ne.push(F)}else u[0].charAt(u[0].length-2)!=="/"&&t.push([u[3],!1]);B="",M=0,R="",g=0,b=[];break;case"creator":u[1]==="/"?B=n.slice(M,u.index):M=u.index+u[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(u[1]==="/"){if((s=t.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&t.push([u[3],!1]);R="",g=0,b=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(u[1]==="/"){if(v[c.name]=o,(s=t.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&(o="",c=_e(u[0],!1),t.push([u[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(t[t.length-1][0]){case"time-style":case"date-style":i=_e(u[0],!1),o+=ss[u[3]][i.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(t[t.length-1][0]){case"time-style":case"date-style":i=_e(u[0],!1),o+=ss[u[3]][i.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(u[0].slice(-2)==="/>")break;if(u[1]==="/")switch(t[t.length-1][0]){case"number-style":case"date-style":case"time-style":o+=n.slice(f,u.index);break}else f=u.index+u[0].length;break;case"named-range":i=_e(u[0],!1),he=o0(i["cell-range-address"]);var Y={Name:i.name,Ref:he[0]+"!"+he[1]};z&&(Y.Sheet=d.length),ve.Names.push(Y);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(t[t.length-1][0])>-1)break;if(u[1]==="/"&&(!E||!E["string-value"])){var ie=_p(n.slice(g,u.index));R=(R.length>0?R+`
`:"")+ie[0]}else _e(u[0],!1),g=u.index+u[0].length;break;case"s":break;case"database-range":if(u[1]==="/")break;try{he=o0(_e(u[0])["target-range-address"]),x[he[0]]["!autofilter"]={ref:he[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(u[1]!=="/"){if(j=_e(u[0],!1),!j.href)break;j.Target=Ce(j.href),delete j.href,j.Target.charAt(0)=="#"&&j.Target.indexOf(".")>-1?(he=o0(j.Target.slice(1)),j.Target="#"+he[0]+"!"+he[1]):j.Target.match(/^\.\.[\\\/]/)&&(j.Target=j.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(u[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(u)}}var y={Sheets:x,SheetNames:d,Workbook:ve};return r.bookSheets&&delete y.Sheets,y}function is(e,a){a=a||{},Rr(e,"META-INF/manifest.xml")&&Po(Ke(e,"META-INF/manifest.xml"),a);var r=Fr(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var n=Ji(Ne(r),a);return Rr(e,"meta.xml")&&(n.Props=li(Ke(e,"meta.xml"))),n}function cs(e,a){return Ji(e,a)}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function K0(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function E0(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):Ne(Aa(e))}function k0(e){var a=e.reduce(function(t,s){return t+s.length},0),r=new Uint8Array(a),n=0;return e.forEach(function(t){r.set(t,n),n+=t.length}),r}function fs(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Ep(e,a){for(var r=(e[a+15]&127)<<7|e[a+14]>>1,n=e[a+14]&1,t=a+13;t>=a;--t)n=n*256+e[t];return(e[a+15]&128?-n:n)*Math.pow(10,r-6176)}function xt(e,a){var r=a?a[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return a&&(a[0]=r),n}function Qe(e){var a=0,r=e[a]&127;e:if(e[a++]>=128){if(r|=(e[a]&127)<<7,e[a++]<128||(r|=(e[a]&127)<<14,e[a++]<128)||(r|=(e[a]&127)<<21,e[a++]<128))break e;r|=(e[a]&127)<<28}return r}function hr(e){for(var a=[],r=[0];r[0]<e.length;){var n=r[0],t=xt(e,r),s=t&7;t=Math.floor(t/8);var i=0,c;if(t==0)break;switch(s){case 0:{for(var o=r[0];e[r[0]++]>=128;);c=e.slice(o,r[0])}break;case 5:i=4,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 1:i=8,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 2:i=xt(e,r),c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(t," at offset ").concat(n))}var f={data:c,type:s};a[t]==null?a[t]=[f]:a[t].push(f)}return a}function Y0(e,a){return(e==null?void 0:e.map(function(r){return a(r.data)}))||[]}function kp(e){for(var a,r=[],n=[0];n[0]<e.length;){var t=xt(e,n),s=hr(e.slice(n[0],n[0]+t));n[0]+=t;var i={id:Qe(s[1][0].data),messages:[]};s[2].forEach(function(c){var o=hr(c.data),f=Qe(o[3][0].data);i.messages.push({meta:o,data:e.slice(n[0],n[0]+f)}),n[0]+=f}),(a=s[3])!=null&&a[0]&&(i.merge=Qe(s[3][0].data)>>>0>0),r.push(i)}return r}function Tp(e,a){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=xt(a,r),t=[];r[0]<a.length;){var s=a[r[0]]&3;if(s==0){var i=a[r[0]++]>>2;if(i<60)++i;else{var c=i-59;i=a[r[0]],c>1&&(i|=a[r[0]+1]<<8),c>2&&(i|=a[r[0]+2]<<16),c>3&&(i|=a[r[0]+3]<<24),i>>>=0,i++,r[0]+=c}t.push(a.slice(r[0],r[0]+i)),r[0]+=i;continue}else{var o=0,f=0;if(s==1?(f=(a[r[0]]>>2&7)+4,o=(a[r[0]++]&224)<<3,o|=a[r[0]++]):(f=(a[r[0]++]>>2)+1,s==2?(o=a[r[0]]|a[r[0]+1]<<8,r[0]+=2):(o=(a[r[0]]|a[r[0]+1]<<8|a[r[0]+2]<<16|a[r[0]+3]<<24)>>>0,r[0]+=4)),t=[k0(t)],o==0)throw new Error("Invalid offset 0");if(o>t[0].length)throw new Error("Invalid offset beyond length");if(f>=o)for(t.push(t[0].slice(-o)),f-=o;f>=t[t.length-1].length;)t.push(t[t.length-1]),f-=t[t.length-1].length;t.push(t[0].slice(-o,-o+f))}}var l=k0(t);if(l.length!=n)throw new Error("Unexpected length: ".concat(l.length," != ").concat(n));return l}function wp(e){for(var a=[],r=0;r<e.length;){var n=e[r++],t=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,a.push(Tp(n,e.slice(r,r+t))),r+=t}if(r!==e.length)throw new Error("data is not a valid framed stream!");return k0(a)}function Ap(e,a,r,n){var t=K0(e),s=t.getUint32(4,!0),i=(n>1?12:8)+fs(s&(n>1?3470:398))*4,c=-1,o=-1,f=NaN,l=new Date(2001,0,1);s&512&&(c=t.getUint32(i,!0),i+=4),i+=fs(s&(n>1?12288:4096))*4,s&16&&(o=t.getUint32(i,!0),i+=4),s&32&&(f=t.getFloat64(i,!0),i+=8),s&64&&(l.setTime(l.getTime()+t.getFloat64(i,!0)*1e3),i+=8);var h;switch(e[2]){case 0:break;case 2:h={t:"n",v:f};break;case 3:h={t:"s",v:a[o]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:f>0};break;case 7:h={t:"n",v:f/86400};break;case 8:h={t:"e",v:0};break;case 9:if(c>-1)h={t:"s",v:r[c]};else if(o>-1)h={t:"s",v:a[o]};else if(!isNaN(f))h={t:"n",v:f};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return h}function Fp(e,a,r){var n=K0(e),t=n.getUint32(8,!0),s=12,i=-1,c=-1,o=NaN,f=NaN,l=new Date(2001,0,1);t&1&&(o=Ep(e,s),s+=16),t&2&&(f=n.getFloat64(s,!0),s+=8),t&4&&(l.setTime(l.getTime()+n.getFloat64(s,!0)*1e3),s+=8),t&8&&(c=n.getUint32(s,!0),s+=4),t&16&&(i=n.getUint32(s,!0),s+=4);var h;switch(e[1]){case 0:break;case 2:h={t:"n",v:o};break;case 3:h={t:"s",v:a[c]};break;case 5:h={t:"d",v:l};break;case 6:h={t:"b",v:f>0};break;case 7:h={t:"n",v:f/86400};break;case 8:h={t:"e",v:0};break;case 9:if(i>-1)h={t:"s",v:r[i]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(t&31," : ").concat(e.slice(0,4)));break;case 10:h={t:"n",v:o};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(t&31," : ").concat(e.slice(0,4)))}return h}function Sp(e,a,r){switch(e[0]){case 0:case 1:case 2:case 3:return Ap(e,a,r,e[0]);case 5:return Fp(e,a,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function la(e){var a=hr(e);return xt(a[1][0].data)}function os(e,a){var r=hr(a.data),n=Qe(r[1][0].data),t=r[3],s=[];return(t||[]).forEach(function(i){var c=hr(i.data),o=Qe(c[1][0].data)>>>0;switch(n){case 1:s[o]=E0(c[3][0].data);break;case 8:{var f=e[la(c[9][0].data)][0],l=hr(f.data),h=e[la(l[1][0].data)][0],x=Qe(h.meta[1][0].data);if(x!=2001)throw new Error("2000 unexpected reference to ".concat(x));var d=hr(h.data);s[o]=d[3].map(function(p){return E0(p.data)}).join("")}break}}),s}function yp(e,a){var r,n,t,s,i,c,o,f,l,h,x,d,p,u,_=hr(e),E=Qe(_[1][0].data)>>>0,R=Qe(_[2][0].data)>>>0,g=((n=(r=_[8])==null?void 0:r[0])==null?void 0:n.data)&&Qe(_[8][0].data)>0||!1,b,H;if((s=(t=_[7])==null?void 0:t[0])!=null&&s.data&&a!=0)b=(c=(i=_[7])==null?void 0:i[0])==null?void 0:c.data,H=(f=(o=_[6])==null?void 0:o[0])==null?void 0:f.data;else if((h=(l=_[4])==null?void 0:l[0])!=null&&h.data&&a!=1)b=(d=(x=_[4])==null?void 0:x[0])==null?void 0:d.data,H=(u=(p=_[3])==null?void 0:p[0])==null?void 0:u.data;else throw"NUMBERS Tile missing ".concat(a," cell storage");for(var U=g?4:1,S=K0(b),k=[],v=0;v<b.length/2;++v){var I=S.getUint16(v*2,!0);I<65535&&k.push([v,I])}if(k.length!=R)throw"Expected ".concat(R," cells, found ").concat(k.length);var P=[];for(v=0;v<k.length-1;++v)P[k[v][0]]=H.subarray(k[v][1]*U,k[v+1][1]*U);return k.length>=1&&(P[k[k.length-1][0]]=H.subarray(k[k.length-1][1]*U)),{R:E,cells:P}}function Cp(e,a){var r,n=hr(a.data),t=(r=n==null?void 0:n[7])!=null&&r[0]?Qe(n[7][0].data)>>>0>0?1:0:-1,s=Y0(n[5],function(i){return yp(i,t)});return{nrows:Qe(n[4][0].data)>>>0,data:s.reduce(function(i,c){return i[c.R]||(i[c.R]=[]),c.cells.forEach(function(o,f){if(i[c.R][f])throw new Error("Duplicate cell r=".concat(c.R," c=").concat(f));i[c.R][f]=o}),i},[])}}function Rp(e,a,r){var n,t=hr(a.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Qe(t[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(t[6][0].data));if(s.e.c=(Qe(t[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(t[7][0].data));r["!ref"]=ye(s);var i=hr(t[4][0].data),c=os(e,e[la(i[4][0].data)][0]),o=(n=i[17])!=null&&n[0]?os(e,e[la(i[17][0].data)][0]):[],f=hr(i[3][0].data),l=0;f[1].forEach(function(h){var x=hr(h.data),d=e[la(x[2][0].data)][0],p=Qe(d.meta[1][0].data);if(p!=6002)throw new Error("6001 unexpected reference to ".concat(p));var u=Cp(e,d);u.data.forEach(function(_,E){_.forEach(function(R,g){var b=Ee({r:l+E,c:g}),H=Sp(R,c,o);H&&(r[b]=H)})}),l+=u.nrows})}function Dp(e,a){var r=hr(a.data),n={"!ref":"A1"},t=e[la(r[2][0].data)],s=Qe(t[0].meta[1][0].data);if(s!=6001)throw new Error("6000 unexpected reference to ".concat(s));return Rp(e,t[0],n),n}function Op(e,a){var r,n=hr(a.data),t={name:(r=n[1])!=null&&r[0]?E0(n[1][0].data):"",sheets:[]},s=Y0(n[2],la);return s.forEach(function(i){e[i].forEach(function(c){var o=Qe(c.meta[1][0].data);o==6e3&&t.sheets.push(Dp(e,c))})}),t}function Np(e,a){var r=Z0(),n=hr(a.data),t=Y0(n[1],la);if(t.forEach(function(s){e[s].forEach(function(i){var c=Qe(i.meta[1][0].data);if(c==2){var o=Op(e,i);o.sheets.forEach(function(f,l){Q0(r,f,l==0?o.name:o.name+"_"+l,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function h0(e){var a,r,n,t,s={},i=[];if(e.FullPaths.forEach(function(o){if(o.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(o){if(o.name.match(/\.iwa$/)){var f;try{f=wp(o.content)}catch(h){return console.log("?? "+o.content.length+" "+(h.message||h))}var l;try{l=kp(f)}catch(h){return console.log("## "+(h.message||h))}l.forEach(function(h){s[h.id]=h.messages,i.push(h.id)})}}),!i.length)throw new Error("File has no messages");var c=((t=(n=(r=(a=s==null?void 0:s[1])==null?void 0:a[0])==null?void 0:r.meta)==null?void 0:n[1])==null?void 0:t[0].data)&&Qe(s[1][0].meta[1][0].data)==1&&s[1][0];if(c||i.forEach(function(o){s[o].forEach(function(f){var l=Qe(f.meta[1][0].data)>>>0;if(l==1)if(!c)c=f;else throw new Error("Document has multiple roots")})}),!c)throw new Error("Cannot find Document root");return Np(s,c)}function Ip(e){return function(r){for(var n=0;n!=e.length;++n){var t=e[n];r[t[0]]===void 0&&(r[t[0]]=t[1]),t[2]==="n"&&(r[t[0]]=Number(r[t[0]]))}}}function q0(e){Ip([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function bp(e){return Na.WS.indexOf(e)>-1?"sheet":e==Na.CS?"chart":e==Na.DS?"dialog":e==Na.MS?"macro":e&&e.length?e:"sheet"}function Pp(e,a){if(!e)return 0;try{e=a.map(function(n){return n.id||(n.id=n.strRelID),[n.name,e["!id"][n.id].Target,bp(e["!id"][n.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function Lp(e,a,r,n,t,s,i,c,o,f,l,h){try{s[n]=et(Fr(e,r,!0),a);var x=Ke(e,a),d;switch(c){case"sheet":d=Vd(x,a,t,o,s[n],f,l,h);break;case"chart":if(d=Wd(x,a,t,o,s[n],f,l,h),!d||!d["!drawel"])break;var p=$a(d["!drawel"].Target,a),u=p0(p),_=Hu(Fr(e,p,!0),et(Fr(e,u,!0),p)),E=$a(_,p),R=p0(E);d=Fd(Fr(e,E,!0),E,o,et(Fr(e,R,!0),E),f,d);break;case"macro":d=Gd(x,a,t,o,s[n],f,l,h);break;case"dialog":d=Xd(x,a,t,o,s[n],f,l,h);break;default:throw new Error("Unrecognized sheet type "+c)}i[n]=d;var g=[];s&&s[n]&&zr(s[n]).forEach(function(b){var H="";if(s[n][b].Type==Na.CMNT){H=$a(s[n][b].Target,a);var U=Yd(Ke(e,H,!0),H,o);if(!U||!U.length)return;Kn(d,U,!1)}s[n][b].Type==Na.TCMNT&&(H=$a(s[n][b].Target,a),g=g.concat(Wu(Ke(e,H,!0),o)))}),g&&g.length&&Kn(d,g,!0,o.people||[])}catch(b){if(o.WTF)throw b}}function yr(e){return e.charAt(0)=="/"?e.slice(1):e}function Mp(e,a){if(Ps(),a=a||{},q0(a),Rr(e,"META-INF/manifest.xml")||Rr(e,"objectdata.xml"))return is(e,a);if(Rr(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof h0<"u"){if(e.FileIndex)return h0(e);var r=Se.utils.cfb_new();return kn(e).forEach(function(Z){Mf(r,Z,Lf(e,Z))}),h0(r)}throw new Error("Unsupported NUMBERS file")}if(!Rr(e,"[Content_Types].xml"))throw Rr(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):Rr(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var n=kn(e),t=Io(Fr(e,"[Content_Types].xml")),s=!1,i,c;if(t.workbooks.length===0&&(c="xl/workbook.xml",Ke(e,c,!0)&&t.workbooks.push(c)),t.workbooks.length===0){if(c="xl/workbook.bin",!Ke(e,c,!0))throw new Error("Could not find workbook");t.workbooks.push(c),s=!0}t.workbooks[0].slice(-3)=="bin"&&(s=!0);var o={},f={};if(!a.bookSheets&&!a.bookProps){if(tt=[],t.sst)try{tt=Kd(Ke(e,yr(t.sst)),t.sst,a)}catch(Z){if(a.WTF)throw Z}a.cellStyles&&t.themes.length&&(o=zd(Fr(e,t.themes[0].replace(/^\//,""),!0)||"",t.themes[0],a)),t.style&&(f=$d(Ke(e,yr(t.style)),t.style,o,a))}t.links.map(function(Z){try{var J=et(Fr(e,p0(yr(Z))),Z);return Jd(Ke(e,yr(Z)),J,Z,a)}catch{}});var l=Hd(Ke(e,yr(t.workbooks[0])),t.workbooks[0],a),h={},x="";t.coreprops.length&&(x=Ke(e,yr(t.coreprops[0]),!0),x&&(h=li(x)),t.extprops.length!==0&&(x=Ke(e,yr(t.extprops[0]),!0),x&&Bo(x,h,a)));var d={};(!a.bookSheets||a.bookProps)&&t.custprops.length!==0&&(x=Fr(e,yr(t.custprops[0]),!0),x&&(d=jo(x,a)));var p={};if((a.bookSheets||a.bookProps)&&(l.Sheets?i=l.Sheets.map(function(J){return J.name}):h.Worksheets&&h.SheetNames.length>0&&(i=h.SheetNames),a.bookProps&&(p.Props=h,p.Custprops=d),a.bookSheets&&typeof i<"u"&&(p.SheetNames=i),a.bookSheets?p.SheetNames:a.bookProps))return p;i={};var u={};a.bookDeps&&t.calcchain&&(u=qd(Ke(e,yr(t.calcchain)),t.calcchain));var _=0,E={},R,g;{var b=l.Sheets;h.Worksheets=b.length,h.SheetNames=[];for(var H=0;H!=b.length;++H)h.SheetNames[H]=b[H].name}var U=s?"bin":"xml",S=t.workbooks[0].lastIndexOf("/"),k=(t.workbooks[0].slice(0,S+1)+"_rels/"+t.workbooks[0].slice(S+1)+".rels").replace(/^\//,"");Rr(e,k)||(k="xl/_rels/workbook."+U+".rels");var v=et(Fr(e,k,!0),k.replace(/_rels.*/,"s5s"));(t.metadata||[]).length>=1&&(a.xlmeta=Zd(Ke(e,yr(t.metadata[0])),t.metadata[0],a)),(t.people||[]).length>=1&&(a.people=Gu(Ke(e,yr(t.people[0])),a)),v&&(v=Pp(v,l.Sheets));var I=Ke(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(_=0;_!=h.Worksheets;++_){var P="sheet";if(v&&v[_]?(R="xl/"+v[_][1].replace(/[\/]?xl\//,""),Rr(e,R)||(R=v[_][1]),Rr(e,R)||(R=k.replace(/_rels\/.*$/,"")+v[_][1]),P=v[_][2]):(R="xl/worksheets/sheet"+(_+1-I)+"."+U,R=R.replace(/sheet0\./,"sheet.")),g=R.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),a&&a.sheets!=null)switch(typeof a.sheets){case"number":if(_!=a.sheets)continue e;break;case"string":if(h.SheetNames[_].toLowerCase()!=a.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(a.sheets)){for(var N=!1,K=0;K!=a.sheets.length;++K)typeof a.sheets[K]=="number"&&a.sheets[K]==_&&(N=1),typeof a.sheets[K]=="string"&&a.sheets[K].toLowerCase()==h.SheetNames[_].toLowerCase()&&(N=1);if(!N)continue e}}Lp(e,R,g,h.SheetNames[_],_,E,i,P,a,l,o,f)}return p={Directory:t,Workbook:l,Props:h,Custprops:d,Deps:u,Sheets:i,SheetNames:h.SheetNames,Strings:tt,Styles:f,Themes:o,SSF:tr(Te)},a&&a.bookFiles&&(e.files?(p.keys=n,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach(function(Z,J){Z=Z.replace(/^Root Entry[\/]/,""),p.keys.push(Z),p.files[Z]=e.FileIndex[J]}))),a&&a.bookVBA&&(t.vba.length>0?p.vbaraw=Ke(e,yr(t.vba[0]),!0):t.defaults&&t.defaults.bin===Ku&&(p.vbaraw=Ke(e,"xl/vbaProject.bin",!0))),p}function Bp(e,a){var r=a||{},n="Workbook",t=Se.find(e,n);try{if(n="/!DataSpaces/Version",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(Dh(t.content),n="/!DataSpaces/DataSpaceMap",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var s=Nh(t.content);if(s.length!==1||s[0].comps.length!==1||s[0].comps[0].t!==0||s[0].name!=="StrongEncryptionDataSpace"||s[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var i=Ih(t.content);if(i.length!=1||i[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);Ph(t.content)}catch{}if(n="/EncryptionInfo",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var c=Lh(t.content);if(n="/EncryptedPackage",t=Se.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(c[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(c[1],t.content,r.password||"",r);if(c[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(c[1],t.content,r.password||"",r);throw new Error("File is password-protected")}function J0(e,a){var r="";switch((a||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Sr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(a&&a.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Up(e,a){return Se.find(e,"EncryptedPackage")?Bp(e,a):Ki(e,a)}function jp(e,a){var r,n=e,t=a||{};return t.type||(t.type=Fe&&Buffer.isBuffer(e)?"buffer":"base64"),r=Hs(n,t),Mp(r,t)}function Zi(e,a){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return m0(e.slice(r),a);default:break e}return lt.to_workbook(e,a)}function Hp(e,a){var r="",n=J0(e,a);switch(a.type){case"base64":r=Sr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=ka(e);break;default:throw new Error("Unrecognized type "+a.type)}return n[0]==239&&n[1]==187&&n[2]==191&&(r=Ne(r)),a.type="binary",Zi(r,a)}function Vp(e,a){var r=e;return a.type=="base64"&&(r=Sr(r)),r=it.utils.decode(1200,r.slice(2),"str"),a.type="binary",Zi(r,a)}function Wp(e){return e.match(/[^\x00-\x7F]/)?za(e):e}function u0(e,a,r,n){return n?(r.type="string",lt.to_workbook(e,r)):lt.to_workbook(a,r)}function T0(e,a){ws();var r=a||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return T0(new Uint8Array(e),(r=tr(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var n=e,t=[0,0,0,0],s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),Pa={},r.dateNF&&(Pa.dateNF=r.dateNF),r.type||(r.type=Fe&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=Fe?"buffer":"binary",n=Rf(e),typeof Uint8Array<"u"&&!Fe&&(r.type="array")),r.type=="string"&&(s=!0,r.type="binary",r.codepage=65001,n=Wp(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return r=tr(r),r.type="array",T0(R0(n),r)}switch((t=J0(n,r))[0]){case 208:if(t[1]===207&&t[2]===17&&t[3]===224&&t[4]===161&&t[5]===177&&t[6]===26&&t[7]===225)return Up(Se.read(n,r),r);break;case 9:if(t[1]<=8)return Ki(n,r);break;case 60:return m0(n,r);case 73:if(t[1]===73&&t[2]===42&&t[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(t[1]===68)return gh(n,r);break;case 84:if(t[1]===65&&t[2]===66&&t[3]===76)return ph.to_workbook(n,r);break;case 80:return t[1]===75&&t[2]<9&&t[3]<9?jp(n,r):u0(e,n,r,s);case 239:return t[3]===60?m0(n,r):u0(e,n,r,s);case 255:if(t[1]===254)return Vp(n,r);if(t[1]===0&&t[2]===2&&t[3]===0)return at.to_workbook(n,r);break;case 0:if(t[1]===0&&(t[2]>=2&&t[3]===0||t[2]===0&&(t[3]===8||t[3]===9)))return at.to_workbook(n,r);break;case 3:case 131:case 139:case 140:return zn.to_workbook(n,r);case 123:if(t[1]===92&&t[2]===114&&t[3]===116)return Kh.to_workbook(n,r);break;case 10:case 13:case 32:return Hp(n,r);case 137:if(t[1]===80&&t[2]===78&&t[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return xh.indexOf(t[0])>-1&&t[2]<=12&&t[3]<=31?zn.to_workbook(n,r):u0(e,n,r,s)}function Gp(e,a,r,n,t,s,i,c){var o=nr(r),f=c.defval,l=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),h=!0,x=t===1?[]:{};if(t!==1)if(Object.defineProperty)try{Object.defineProperty(x,"__rowNum__",{value:r,enumerable:!1})}catch{x.__rowNum__=r}else x.__rowNum__=r;if(!i||e[r])for(var d=a.s.c;d<=a.e.c;++d){var p=i?e[r][d]:e[n[d]+o];if(p===void 0||p.t===void 0){if(f===void 0)continue;s[d]!=null&&(x[s[d]]=f);continue}var u=p.v;switch(p.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(s[d]!=null){if(u==null)if(p.t=="e"&&u===null)x[s[d]]=null;else if(f!==void 0)x[s[d]]=f;else if(l&&u===null)x[s[d]]=null;else continue;else x[s[d]]=l&&(p.t!=="n"||p.t==="n"&&c.rawNumbers!==!1)?u:aa(p,u,c);u!=null&&(h=!1)}}return{row:x,isempty:h}}function w0(e,a){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,t=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},f=a||{},l=f.range!=null?f.range:e["!ref"];switch(f.header===1?n=1:f.header==="A"?n=2:Array.isArray(f.header)?n=3:f.header==null&&(n=0),typeof l){case"string":o=He(l);break;case"number":o=He(e["!ref"]),o.s.r=l;break;default:o=l}n>0&&(t=0);var h=nr(o.s.r),x=[],d=[],p=0,u=0,_=Array.isArray(e),E=o.s.r,R=0,g={};_&&!e[E]&&(e[E]=[]);var b=f.skipHidden&&e["!cols"]||[],H=f.skipHidden&&e["!rows"]||[];for(R=o.s.c;R<=o.e.c;++R)if(!(b[R]||{}).hidden)switch(x[R]=Ze(R),r=_?e[E][R]:e[x[R]+h],n){case 1:s[R]=R-o.s.c;break;case 2:s[R]=x[R];break;case 3:s[R]=f.header[R-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),c=i=aa(r,null,f),u=g[i]||0,!u)g[i]=1;else{do c=i+"_"+u++;while(g[c]);g[i]=u,g[c]=1}s[R]=c}for(E=o.s.r+t;E<=o.e.r;++E)if(!(H[E]||{}).hidden){var U=Gp(e,o,E,x,n,s,_,f);(U.isempty===!1||(n===1?f.blankrows!==!1:f.blankrows))&&(d[p++]=U.row)}return d.length=p,d}var ls=/"/g;function Xp(e,a,r,n,t,s,i,c){for(var o=!0,f=[],l="",h=nr(r),x=a.s.c;x<=a.e.c;++x)if(n[x]){var d=c.dense?(e[r]||[])[x]:e[n[x]+h];if(d==null)l="";else if(d.v!=null){o=!1,l=""+(c.rawNumbers&&d.t=="n"?d.v:aa(d,null,c));for(var p=0,u=0;p!==l.length;++p)if((u=l.charCodeAt(p))===t||u===s||u===34||c.forceQuotes){l='"'+l.replace(ls,'""')+'"';break}l=="ID"&&(l='"ID"')}else d.f!=null&&!d.F?(o=!1,l="="+d.f,l.indexOf(",")>=0&&(l='"'+l.replace(ls,'""')+'"')):l="";f.push(l)}return c.blankrows===!1&&o?null:f.join(i)}function Qi(e,a){var r=[],n=a??{};if(e==null||e["!ref"]==null)return"";var t=He(e["!ref"]),s=n.FS!==void 0?n.FS:",",i=s.charCodeAt(0),c=n.RS!==void 0?n.RS:`
`,o=c.charCodeAt(0),f=new RegExp((s=="|"?"\\|":s)+"+$"),l="",h=[];n.dense=Array.isArray(e);for(var x=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=t.s.c;p<=t.e.c;++p)(x[p]||{}).hidden||(h[p]=Ze(p));for(var u=0,_=t.s.r;_<=t.e.r;++_)(d[_]||{}).hidden||(l=Xp(e,t,_,h,i,o,s,n),l!=null&&(n.strip&&(l=l.replace(f,"")),(l||n.blankrows!==!1)&&r.push((u++?c:"")+l)));return delete n.dense,r.join("")}function $p(e,a){a||(a={}),a.FS="	",a.RS=`
`;var r=Qi(e,a);return r}function zp(e){var a="",r,n="";if(e==null||e["!ref"]==null)return[];var t=He(e["!ref"]),s="",i=[],c,o=[],f=Array.isArray(e);for(c=t.s.c;c<=t.e.c;++c)i[c]=Ze(c);for(var l=t.s.r;l<=t.e.r;++l)for(s=nr(l),c=t.s.c;c<=t.e.c;++c)if(a=i[c]+s,r=f?(e[l]||[])[c]:e[a],n="",r!==void 0){if(r.F!=null){if(a=r.F,!r.f)continue;n=r.f,a.indexOf(":")==-1&&(a=a+":"+a)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=a+"="+n}return o}function ec(e,a,r){var n=r||{},t=+!n.skipHeader,s=e||{},i=0,c=0;if(s&&n.origin!=null)if(typeof n.origin=="number")i=n.origin;else{var o=typeof n.origin=="string"?gr(n.origin):n.origin;i=o.r,c=o.c}var f,l={s:{c:0,r:0},e:{c,r:i+a.length-1+t}};if(s["!ref"]){var h=He(s["!ref"]);l.e.c=Math.max(l.e.c,h.e.c),l.e.r=Math.max(l.e.r,h.e.r),i==-1&&(i=h.e.r+1,l.e.r=i+a.length-1+t)}else i==-1&&(i=0,l.e.r=a.length-1+t);var x=n.header||[],d=0;a.forEach(function(u,_){zr(u).forEach(function(E){(d=x.indexOf(E))==-1&&(x[d=x.length]=E);var R=u[E],g="z",b="",H=Ee({c:c+d,r:i+_+t});f=dt(s,H),R&&typeof R=="object"&&!(R instanceof Date)?s[H]=R:(typeof R=="number"?g="n":typeof R=="boolean"?g="b":typeof R=="string"?g="s":R instanceof Date?(g="d",n.cellDates||(g="n",R=_r(R)),b=n.dateNF||Te[14]):R===null&&n.nullError&&(g="e",R=0),f?(f.t=g,f.v=R,delete f.w,delete f.R,b&&(f.z=b)):s[H]=f={t:g,v:R},b&&(f.z=b))})}),l.e.c=Math.max(l.e.c,c+x.length-1);var p=nr(i);if(t)for(d=0;d<x.length;++d)s[Ze(d+c)+p]={t:"s",v:x[d]};return s["!ref"]=ye(l),s}function Kp(e,a){return ec(null,e,a)}function dt(e,a,r){if(typeof a=="string"){if(Array.isArray(e)){var n=gr(a);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[a]||(e[a]={t:"z"})}return typeof a!="number"?dt(e,Ee(a)):dt(e,Ee({r:a,c:r||0}))}function Yp(e,a){if(typeof a=="number"){if(a>=0&&e.SheetNames.length>a)return a;throw new Error("Cannot find sheet # "+a)}else if(typeof a=="string"){var r=e.SheetNames.indexOf(a);if(r>-1)return r;throw new Error("Cannot find sheet name |"+a+"|")}else throw new Error("Cannot find sheet |"+a+"|")}function Z0(){return{SheetNames:[],Sheets:{}}}function Q0(e,a,r,n){var t=1;if(!r)for(;t<=65535&&e.SheetNames.indexOf(r="Sheet"+t)!=-1;++t,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);t=s&&+s[2]||0;var i=s&&s[1]||r;for(++t;t<=65535&&e.SheetNames.indexOf(r=i+t)!=-1;++t);}if(Id(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=a,r}function qp(e,a,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=Yp(e,a);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function Jp(e,a){return e.z=a,e}function rc(e,a,r){return a?(e.l={Target:a},r&&(e.l.Tooltip=r)):delete e.l,e}function Zp(e,a,r){return rc(e,"#"+a,r)}function Qp(e,a,r){e.c||(e.c=[]),e.c.push({t:a,a:r||"SheetJS"})}function e2(e,a,r,n){for(var t=typeof a!="string"?a:He(a),s=typeof a=="string"?a:ye(a),i=t.s.r;i<=t.e.r;++i)for(var c=t.s.c;c<=t.e.c;++c){var o=dt(e,i,c);o.t="n",o.F=s,delete o.v,i==t.s.r&&c==t.s.c&&(o.f=r,n&&(o.D=!0))}return e}var r2={encode_col:Ze,encode_row:nr,encode_cell:Ee,encode_range:ye,decode_col:B0,decode_row:M0,split_cell:ho,decode_cell:gr,decode_range:Ua,format_cell:aa,sheet_add_aoa:si,sheet_add_json:ec,sheet_add_dom:Yi,aoa_to_sheet:ja,json_to_sheet:Kp,table_to_sheet:qi,table_to_book:gp,sheet_to_csv:Qi,sheet_to_txt:$p,sheet_to_json:w0,sheet_to_html:vp,sheet_to_formulae:zp,sheet_to_row_object_array:w0,sheet_get_cell:dt,book_new:Z0,book_append_sheet:Q0,book_set_sheet_visibility:qp,cell_set_number_format:Jp,cell_set_hyperlink:rc,cell_set_internal_link:Zp,cell_add_comment:Qp,sheet_set_array_formula:e2,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},Ot={exports:{}};/* @license
Papa Parse
v5.5.3
https://github.com/mholt/PapaParse
License: MIT
*/var a2=Ot.exports,hs;function t2(){return hs||(hs=1,function(e,a){((r,n)=>{e.exports=n()})(a2,function r(){var n=typeof self<"u"?self:typeof window<"u"?window:n!==void 0?n:{},t,s=!n.document&&!!n.postMessage,i=n.IS_PAPA_WORKER||!1,c={},o=0,f={};function l(k){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(v){var I=H(v);I.chunkSize=parseInt(I.chunkSize),v.step||v.chunk||(I.chunkSize=null),this._handle=new u(I),(this._handle.streamer=this)._config=I}).call(this,k),this.parseChunk=function(v,I){var P=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<P){let K=this._config.newline;K||(N=this._config.quoteChar||'"',K=this._handle.guessLineEndings(v,N)),v=[...v.split(K).slice(P)].join(K)}this.isFirstChunk&&S(this._config.beforeFirstChunk)&&(N=this._config.beforeFirstChunk(v))!==void 0&&(v=N),this.isFirstChunk=!1,this._halted=!1;var P=this._partialLine+v,N=(this._partialLine="",this._handle.parse(P,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(v=N.meta.cursor,P=(this._finished||(this._partialLine=P.substring(v-this._baseIndex),this._baseIndex=v),N&&N.data&&(this._rowCount+=N.data.length),this._finished||this._config.preview&&this._rowCount>=this._config.preview),i)n.postMessage({results:N,workerId:f.WORKER_ID,finished:P});else if(S(this._config.chunk)&&!I){if(this._config.chunk(N,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=N=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(N.data),this._completeResults.errors=this._completeResults.errors.concat(N.errors),this._completeResults.meta=N.meta),this._completed||!P||!S(this._config.complete)||N&&N.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),P||N&&N.meta.paused||this._nextChunk(),N}this._halted=!0},this._sendError=function(v){S(this._config.error)?this._config.error(v):i&&this._config.error&&n.postMessage({workerId:f.WORKER_ID,error:v,finished:!1})}}function h(k){var v;(k=k||{}).chunkSize||(k.chunkSize=f.RemoteChunkSize),l.call(this,k),this._nextChunk=s?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(I){this._input=I,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(v=new XMLHttpRequest,this._config.withCredentials&&(v.withCredentials=this._config.withCredentials),s||(v.onload=U(this._chunkLoaded,this),v.onerror=U(this._chunkError,this)),v.open(this._config.downloadRequestBody?"POST":"GET",this._input,!s),this._config.downloadRequestHeaders){var I,P=this._config.downloadRequestHeaders;for(I in P)v.setRequestHeader(I,P[I])}var N;this._config.chunkSize&&(N=this._start+this._config.chunkSize-1,v.setRequestHeader("Range","bytes="+this._start+"-"+N));try{v.send(this._config.downloadRequestBody)}catch(K){this._chunkError(K.message)}s&&v.status===0&&this._chunkError()}},this._chunkLoaded=function(){v.readyState===4&&(v.status<200||400<=v.status?this._chunkError():(this._start+=this._config.chunkSize||v.responseText.length,this._finished=!this._config.chunkSize||this._start>=(I=>(I=I.getResponseHeader("Content-Range"))!==null?parseInt(I.substring(I.lastIndexOf("/")+1)):-1)(v),this.parseChunk(v.responseText)))},this._chunkError=function(I){I=v.statusText||I,this._sendError(new Error(I))}}function x(k){(k=k||{}).chunkSize||(k.chunkSize=f.LocalChunkSize),l.call(this,k);var v,I,P=typeof FileReader<"u";this.stream=function(N){this._input=N,I=N.slice||N.webkitSlice||N.mozSlice,P?((v=new FileReader).onload=U(this._chunkLoaded,this),v.onerror=U(this._chunkError,this)):v=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var N=this._input,K=(this._config.chunkSize&&(K=Math.min(this._start+this._config.chunkSize,this._input.size),N=I.call(N,this._start,K)),v.readAsText(N,this._config.encoding));P||this._chunkLoaded({target:{result:K}})},this._chunkLoaded=function(N){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(N.target.result)},this._chunkError=function(){this._sendError(v.error)}}function d(k){var v;l.call(this,k=k||{}),this.stream=function(I){return v=I,this._nextChunk()},this._nextChunk=function(){var I,P;if(!this._finished)return I=this._config.chunkSize,v=I?(P=v.substring(0,I),v.substring(I)):(P=v,""),this._finished=!v,this.parseChunk(P)}}function p(k){l.call(this,k=k||{});var v=[],I=!0,P=!1;this.pause=function(){l.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){l.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(N){this._input=N,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){P&&v.length===1&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),v.length?this.parseChunk(v.shift()):I=!0},this._streamData=U(function(N){try{v.push(typeof N=="string"?N:N.toString(this._config.encoding)),I&&(I=!1,this._checkIsFinished(),this.parseChunk(v.shift()))}catch(K){this._streamError(K)}},this),this._streamError=U(function(N){this._streamCleanUp(),this._sendError(N)},this),this._streamEnd=U(function(){this._streamCleanUp(),P=!0,this._streamData("")},this),this._streamCleanUp=U(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function u(k){var v,I,P,N,K=Math.pow(2,53),Z=-K,J=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,se=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,ee=this,ve=0,j=0,he=!1,ne=!1,F=[],B={data:[],errors:[],meta:{}};function M(re){return k.skipEmptyLines==="greedy"?re.join("").trim()==="":re.length===1&&re[0].length===0}function L(){if(B&&P&&(ae("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+f.DefaultDelimiter+"'"),P=!1),k.skipEmptyLines&&(B.data=B.data.filter(function(ie){return!M(ie)})),z()){let ie=function(y,me){S(k.transformHeader)&&(y=k.transformHeader(y,me)),F.push(y)};if(B)if(Array.isArray(B.data[0])){for(var re=0;z()&&re<B.data.length;re++)B.data[re].forEach(ie);B.data.splice(0,1)}else B.data.forEach(ie)}function Q(ie,y){for(var me=k.header?{}:[],pe=0;pe<ie.length;pe++){var ge=pe,le=ie[pe],le=((fe,de)=>(Ae=>(k.dynamicTypingFunction&&k.dynamicTyping[Ae]===void 0&&(k.dynamicTyping[Ae]=k.dynamicTypingFunction(Ae)),(k.dynamicTyping[Ae]||k.dynamicTyping)===!0))(fe)?de==="true"||de==="TRUE"||de!=="false"&&de!=="FALSE"&&((Ae=>{if(J.test(Ae)&&(Ae=parseFloat(Ae),Z<Ae&&Ae<K))return 1})(de)?parseFloat(de):se.test(de)?new Date(de):de===""?null:de):de)(ge=k.header?pe>=F.length?"__parsed_extra":F[pe]:ge,le=k.transform?k.transform(le,ge):le);ge==="__parsed_extra"?(me[ge]=me[ge]||[],me[ge].push(le)):me[ge]=le}return k.header&&(pe>F.length?ae("FieldMismatch","TooManyFields","Too many fields: expected "+F.length+" fields but parsed "+pe,j+y):pe<F.length&&ae("FieldMismatch","TooFewFields","Too few fields: expected "+F.length+" fields but parsed "+pe,j+y)),me}var Y;B&&(k.header||k.dynamicTyping||k.transform)&&(Y=1,!B.data.length||Array.isArray(B.data[0])?(B.data=B.data.map(Q),Y=B.data.length):B.data=Q(B.data,0),k.header&&B.meta&&(B.meta.fields=F),j+=Y)}function z(){return k.header&&F.length===0}function ae(re,Q,Y,ie){re={type:re,code:Q,message:Y},ie!==void 0&&(re.row=ie),B.errors.push(re)}S(k.step)&&(N=k.step,k.step=function(re){B=re,z()?L():(L(),B.data.length!==0&&(ve+=re.data.length,k.preview&&ve>k.preview?I.abort():(B.data=B.data[0],N(B,ee))))}),this.parse=function(re,Q,Y){var ie=k.quoteChar||'"',ie=(k.newline||(k.newline=this.guessLineEndings(re,ie)),P=!1,k.delimiter?S(k.delimiter)&&(k.delimiter=k.delimiter(re),B.meta.delimiter=k.delimiter):((ie=((y,me,pe,ge,le)=>{var fe,de,Ae,qe;le=le||[",","	","|",";",f.RECORD_SEP,f.UNIT_SEP];for(var sr=0;sr<le.length;sr++){for(var ir,Er=le[sr],Re=0,Xe=0,Oe=0,$e=(Ae=void 0,new E({comments:ge,delimiter:Er,newline:me,preview:10}).parse(y)),Ie=0;Ie<$e.data.length;Ie++)pe&&M($e.data[Ie])?Oe++:(ir=$e.data[Ie].length,Xe+=ir,Ae===void 0?Ae=ir:0<ir&&(Re+=Math.abs(ir-Ae),Ae=ir));0<$e.data.length&&(Xe/=$e.data.length-Oe),(de===void 0||Re<=de)&&(qe===void 0||qe<Xe)&&1.99<Xe&&(de=Re,fe=Er,qe=Xe)}return{successful:!!(k.delimiter=fe),bestDelimiter:fe}})(re,k.newline,k.skipEmptyLines,k.comments,k.delimitersToGuess)).successful?k.delimiter=ie.bestDelimiter:(P=!0,k.delimiter=f.DefaultDelimiter),B.meta.delimiter=k.delimiter),H(k));return k.preview&&k.header&&ie.preview++,v=re,I=new E(ie),B=I.parse(v,Q,Y),L(),he?{meta:{paused:!0}}:B||{meta:{paused:!1}}},this.paused=function(){return he},this.pause=function(){he=!0,I.abort(),v=S(k.chunk)?"":v.substring(I.getCharIndex())},this.resume=function(){ee.streamer._halted?(he=!1,ee.streamer.parseChunk(v,!0)):setTimeout(ee.resume,3)},this.aborted=function(){return ne},this.abort=function(){ne=!0,I.abort(),B.meta.aborted=!0,S(k.complete)&&k.complete(B),v=""},this.guessLineEndings=function(y,ie){y=y.substring(0,1048576);var ie=new RegExp(_(ie)+"([^]*?)"+_(ie),"gm"),Y=(y=y.replace(ie,"")).split("\r"),ie=y.split(`
`),y=1<ie.length&&ie[0].length<Y[0].length;if(Y.length===1||y)return`
`;for(var me=0,pe=0;pe<Y.length;pe++)Y[pe][0]===`
`&&me++;return me>=Y.length/2?`\r
`:"\r"}}function _(k){return k.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function E(k){var v=(k=k||{}).delimiter,I=k.newline,P=k.comments,N=k.step,K=k.preview,Z=k.fastMode,J=null,se=!1,ee=k.quoteChar==null?'"':k.quoteChar,ve=ee;if(k.escapeChar!==void 0&&(ve=k.escapeChar),(typeof v!="string"||-1<f.BAD_DELIMITERS.indexOf(v))&&(v=","),P===v)throw new Error("Comment character same as delimiter");P===!0?P="#":(typeof P!="string"||-1<f.BAD_DELIMITERS.indexOf(P))&&(P=!1),I!==`
`&&I!=="\r"&&I!==`\r
`&&(I=`
`);var j=0,he=!1;this.parse=function(ne,F,B){if(typeof ne!="string")throw new Error("Input must be a string");var M=ne.length,L=v.length,z=I.length,ae=P.length,re=S(N),Q=[],Y=[],ie=[],y=j=0;if(!ne)return Re();if(Z||Z!==!1&&ne.indexOf(ee)===-1){for(var me=ne.split(I),pe=0;pe<me.length;pe++){if(ie=me[pe],j+=ie.length,pe!==me.length-1)j+=I.length;else if(B)return Re();if(!P||ie.substring(0,ae)!==P){if(re){if(Q=[],qe(ie.split(v)),Xe(),he)return Re()}else qe(ie.split(v));if(K&&K<=pe)return Q=Q.slice(0,K),Re(!0)}}return Re()}for(var ge=ne.indexOf(v,j),le=ne.indexOf(I,j),fe=new RegExp(_(ve)+_(ee),"g"),de=ne.indexOf(ee,j);;)if(ne[j]===ee)for(de=j,j++;;){if((de=ne.indexOf(ee,de+1))===-1)return B||Y.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:Q.length,index:j}),ir();if(de===M-1)return ir(ne.substring(j,de).replace(fe,ee));if(ee===ve&&ne[de+1]===ve)de++;else if(ee===ve||de===0||ne[de-1]!==ve){ge!==-1&&ge<de+1&&(ge=ne.indexOf(v,de+1));var Ae=sr((le=le!==-1&&le<de+1?ne.indexOf(I,de+1):le)===-1?ge:Math.min(ge,le));if(ne.substr(de+1+Ae,L)===v){ie.push(ne.substring(j,de).replace(fe,ee)),ne[j=de+1+Ae+L]!==ee&&(de=ne.indexOf(ee,j)),ge=ne.indexOf(v,j),le=ne.indexOf(I,j);break}if(Ae=sr(le),ne.substring(de+1+Ae,de+1+Ae+z)===I){if(ie.push(ne.substring(j,de).replace(fe,ee)),Er(de+1+Ae+z),ge=ne.indexOf(v,j),de=ne.indexOf(ee,j),re&&(Xe(),he))return Re();if(K&&Q.length>=K)return Re(!0);break}Y.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:Q.length,index:j}),de++}}else if(P&&ie.length===0&&ne.substring(j,j+ae)===P){if(le===-1)return Re();j=le+z,le=ne.indexOf(I,j),ge=ne.indexOf(v,j)}else if(ge!==-1&&(ge<le||le===-1))ie.push(ne.substring(j,ge)),j=ge+L,ge=ne.indexOf(v,j);else{if(le===-1)break;if(ie.push(ne.substring(j,le)),Er(le+z),re&&(Xe(),he))return Re();if(K&&Q.length>=K)return Re(!0)}return ir();function qe(Oe){Q.push(Oe),y=j}function sr(Oe){var $e=0;return $e=Oe!==-1&&(Oe=ne.substring(de+1,Oe))&&Oe.trim()===""?Oe.length:$e}function ir(Oe){return B||(Oe===void 0&&(Oe=ne.substring(j)),ie.push(Oe),j=M,qe(ie),re&&Xe()),Re()}function Er(Oe){j=Oe,qe(ie),ie=[],le=ne.indexOf(I,j)}function Re(Oe){if(k.header&&!F&&Q.length&&!se){var $e=Q[0],Ie=Object.create(null),kr=new Set($e);let we=!1;for(let De=0;De<$e.length;De++){let Le=$e[De];if(Ie[Le=S(k.transformHeader)?k.transformHeader(Le,De):Le]){let be,Wr=Ie[Le];for(;be=Le+"_"+Wr,Wr++,kr.has(be););kr.add(be),$e[De]=be,Ie[Le]++,we=!0,(J=J===null?{}:J)[be]=Le}else Ie[Le]=1,$e[De]=Le;kr.add(Le)}we&&console.warn("Duplicate headers found and renamed."),se=!0}return{data:Q,errors:Y,meta:{delimiter:v,linebreak:I,aborted:he,truncated:!!Oe,cursor:y+(F||0),renamedHeaders:J}}}function Xe(){N(Re()),Q=[],Y=[]}},this.abort=function(){he=!0},this.getCharIndex=function(){return j}}function R(k){var v=k.data,I=c[v.workerId],P=!1;if(v.error)I.userError(v.error,v.file);else if(v.results&&v.results.data){var N={abort:function(){P=!0,g(v.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:b,resume:b};if(S(I.userStep)){for(var K=0;K<v.results.data.length&&(I.userStep({data:v.results.data[K],errors:v.results.errors,meta:v.results.meta},N),!P);K++);delete v.results}else S(I.userChunk)&&(I.userChunk(v.results,N,v.file),delete v.results)}v.finished&&!P&&g(v.workerId,v.results)}function g(k,v){var I=c[k];S(I.userComplete)&&I.userComplete(v),I.terminate(),delete c[k]}function b(){throw new Error("Not implemented.")}function H(k){if(typeof k!="object"||k===null)return k;var v,I=Array.isArray(k)?[]:{};for(v in k)I[v]=H(k[v]);return I}function U(k,v){return function(){k.apply(v,arguments)}}function S(k){return typeof k=="function"}return f.parse=function(k,v){var I=(v=v||{}).dynamicTyping||!1;if(S(I)&&(v.dynamicTypingFunction=I,I={}),v.dynamicTyping=I,v.transform=!!S(v.transform)&&v.transform,!v.worker||!f.WORKERS_SUPPORTED)return I=null,f.NODE_STREAM_INPUT,typeof k=="string"?(k=(P=>P.charCodeAt(0)!==65279?P:P.slice(1))(k),I=new(v.download?h:d)(v)):k.readable===!0&&S(k.read)&&S(k.on)?I=new p(v):(n.File&&k instanceof File||k instanceof Object)&&(I=new x(v)),I.stream(k);(I=(()=>{var P;return!!f.WORKERS_SUPPORTED&&(P=(()=>{var N=n.URL||n.webkitURL||null,K=r.toString();return f.BLOB_URL||(f.BLOB_URL=N.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",K,")();"],{type:"text/javascript"})))})(),(P=new n.Worker(P)).onmessage=R,P.id=o++,c[P.id]=P)})()).userStep=v.step,I.userChunk=v.chunk,I.userComplete=v.complete,I.userError=v.error,v.step=S(v.step),v.chunk=S(v.chunk),v.complete=S(v.complete),v.error=S(v.error),delete v.worker,I.postMessage({input:k,config:v,workerId:I.id})},f.unparse=function(k,v){var I=!1,P=!0,N=",",K=`\r
`,Z='"',J=Z+Z,se=!1,ee=null,ve=!1,j=((()=>{if(typeof v=="object"){if(typeof v.delimiter!="string"||f.BAD_DELIMITERS.filter(function(F){return v.delimiter.indexOf(F)!==-1}).length||(N=v.delimiter),typeof v.quotes!="boolean"&&typeof v.quotes!="function"&&!Array.isArray(v.quotes)||(I=v.quotes),typeof v.skipEmptyLines!="boolean"&&typeof v.skipEmptyLines!="string"||(se=v.skipEmptyLines),typeof v.newline=="string"&&(K=v.newline),typeof v.quoteChar=="string"&&(Z=v.quoteChar),typeof v.header=="boolean"&&(P=v.header),Array.isArray(v.columns)){if(v.columns.length===0)throw new Error("Option columns is empty");ee=v.columns}v.escapeChar!==void 0&&(J=v.escapeChar+Z),v.escapeFormulae instanceof RegExp?ve=v.escapeFormulae:typeof v.escapeFormulae=="boolean"&&v.escapeFormulae&&(ve=/^[=+\-@\t\r].*$/)}})(),new RegExp(_(Z),"g"));if(typeof k=="string"&&(k=JSON.parse(k)),Array.isArray(k)){if(!k.length||Array.isArray(k[0]))return he(null,k,se);if(typeof k[0]=="object")return he(ee||Object.keys(k[0]),k,se)}else if(typeof k=="object")return typeof k.data=="string"&&(k.data=JSON.parse(k.data)),Array.isArray(k.data)&&(k.fields||(k.fields=k.meta&&k.meta.fields||ee),k.fields||(k.fields=Array.isArray(k.data[0])?k.fields:typeof k.data[0]=="object"?Object.keys(k.data[0]):[]),Array.isArray(k.data[0])||typeof k.data[0]=="object"||(k.data=[k.data])),he(k.fields||[],k.data||[],se);throw new Error("Unable to serialize unrecognized input");function he(F,B,M){var L="",z=(typeof F=="string"&&(F=JSON.parse(F)),typeof B=="string"&&(B=JSON.parse(B)),Array.isArray(F)&&0<F.length),ae=!Array.isArray(B[0]);if(z&&P){for(var re=0;re<F.length;re++)0<re&&(L+=N),L+=ne(F[re],re);0<B.length&&(L+=K)}for(var Q=0;Q<B.length;Q++){var Y=(z?F:B[Q]).length,ie=!1,y=z?Object.keys(B[Q]).length===0:B[Q].length===0;if(M&&!z&&(ie=M==="greedy"?B[Q].join("").trim()==="":B[Q].length===1&&B[Q][0].length===0),M==="greedy"&&z){for(var me=[],pe=0;pe<Y;pe++){var ge=ae?F[pe]:pe;me.push(B[Q][ge])}ie=me.join("").trim()===""}if(!ie){for(var le=0;le<Y;le++){0<le&&!y&&(L+=N);var fe=z&&ae?F[le]:le;L+=ne(B[Q][fe],le)}Q<B.length-1&&(!M||0<Y&&!y)&&(L+=K)}}return L}function ne(F,B){var M,L;return F==null?"":F.constructor===Date?JSON.stringify(F).slice(1,25):(L=!1,ve&&typeof F=="string"&&ve.test(F)&&(F="'"+F,L=!0),M=F.toString().replace(j,J),(L=L||I===!0||typeof I=="function"&&I(F,B)||Array.isArray(I)&&I[B]||((z,ae)=>{for(var re=0;re<ae.length;re++)if(-1<z.indexOf(ae[re]))return!0;return!1})(M,f.BAD_DELIMITERS)||-1<M.indexOf(N)||M.charAt(0)===" "||M.charAt(M.length-1)===" ")?Z+M+Z:M)}},f.RECORD_SEP="",f.UNIT_SEP="",f.BYTE_ORDER_MARK="\uFEFF",f.BAD_DELIMITERS=["\r",`
`,'"',f.BYTE_ORDER_MARK],f.WORKERS_SUPPORTED=!s&&!!n.Worker,f.NODE_STREAM_INPUT=1,f.LocalChunkSize=10485760,f.RemoteChunkSize=5242880,f.DefaultDelimiter=",",f.Parser=E,f.ParserHandle=u,f.NetworkStreamer=h,f.FileStreamer=x,f.StringStreamer=d,f.ReadableStreamStreamer=p,n.jQuery&&((t=n.jQuery).fn.parse=function(k){var v=k.config||{},I=[];return this.each(function(K){if(!(t(this).prop("tagName").toUpperCase()==="INPUT"&&t(this).attr("type").toLowerCase()==="file"&&n.FileReader)||!this.files||this.files.length===0)return!0;for(var Z=0;Z<this.files.length;Z++)I.push({file:this.files[Z],inputElem:this,instanceConfig:t.extend({},v)})}),P(),this;function P(){if(I.length===0)S(k.complete)&&k.complete();else{var K,Z,J,se,ee=I[0];if(S(k.before)){var ve=k.before(ee.file,ee.inputElem);if(typeof ve=="object"){if(ve.action==="abort")return K="AbortError",Z=ee.file,J=ee.inputElem,se=ve.reason,void(S(k.error)&&k.error({name:K},Z,J,se));if(ve.action==="skip")return void N();typeof ve.config=="object"&&(ee.instanceConfig=t.extend(ee.instanceConfig,ve.config))}else if(ve==="skip")return void N()}var j=ee.instanceConfig.complete;ee.instanceConfig.complete=function(he){S(j)&&j(he,ee.file,ee.inputElem),N()},f.parse(ee.file,ee.instanceConfig)}}function N(){I.splice(0,1),P()}}),i&&(n.onmessage=function(k){k=k.data,f.WORKER_ID===void 0&&k&&(f.WORKER_ID=k.workerId),typeof k.input=="string"?n.postMessage({workerId:f.WORKER_ID,results:f.parse(k.input,k.config),finished:!0}):(n.File&&k.input instanceof File||k.input instanceof Object)&&(k=f.parse(k.input,k.config))&&n.postMessage({workerId:f.WORKER_ID,results:k,finished:!0})}),(h.prototype=Object.create(l.prototype)).constructor=h,(x.prototype=Object.create(l.prototype)).constructor=x,(d.prototype=Object.create(d.prototype)).constructor=d,(p.prototype=Object.create(l.prototype)).constructor=p,f})}(Ot)),Ot.exports}var n2=t2();const s2=mc(n2),_2=function(){const a=_c(),[r,n]=Ue.useState("upload"),[t,s]=Ue.useState(null),[i,c]=Ue.useState(null),[o,f]=Ue.useState({}),[l,h]=Ue.useState([]),[x,d]=Ue.useState(!1),p=Z=>new Promise((J,se)=>{const ee=new FileReader;ee.onload=ve=>{var j;try{const he=new Uint8Array((j=ve.target)==null?void 0:j.result),ne=T0(he,{type:"array"}),F=ne.SheetNames[0],B=ne.Sheets[F],M=r2.sheet_to_json(B,{header:1});if(M.length<2){se(new Error("الملف يجب أن يحتوي على صفين على الأقل"));return}let L=-1,z=[];for(let Y=1;Y<M.length;Y++){const ie=M[Y];if(ie&&ie.some(y=>y!=null&&y!=="")&&(z=ie.map(me=>me?String(me).trim():""),z.filter(me=>me).length>0)){L=Y;break}}if(L===-1){se(new Error("لم يتم العثور على صف يحتوي على أعمدة صالحة"));return}const ae=M.slice(L+1),re=z.filter(Y=>Y),Q=ae.filter(Y=>Y&&Y.some(ie=>ie!=null&&ie!=="")).map(Y=>{const ie={};return z.forEach((y,me)=>{y&&(ie[y]=Y[me]||"")}),ie});J({columns:re,data:Q})}catch{se(new Error("فشل في قراءة ملف Excel"))}},ee.onerror=()=>se(new Error("فشل في قراءة الملف")),ee.readAsArrayBuffer(Z)}),u=Z=>new Promise((J,se)=>{s2.parse(Z,{header:!1,skipEmptyLines:!0,complete:ee=>{if(ee.errors.length>0){se(new Error("فشل في قراءة ملف CSV"));return}const ve=ee.data;if(ve.length<2){se(new Error("الملف يجب أن يحتوي على صفين على الأقل"));return}let j=-1,he=[];for(let M=1;M<ve.length;M++){const L=ve[M];if(L&&L.some(z=>z!=null&&z!=="")&&(he=L.map(ae=>ae?String(ae).trim():""),he.filter(ae=>ae).length>0)){j=M;break}}if(j===-1){se(new Error("لم يتم العثور على صف يحتوي على أعمدة صالحة"));return}const ne=ve.slice(j+1),F=he.filter(M=>M),B=ne.filter(M=>M&&M.some(L=>L!=null&&L!=="")).map(M=>{const L={};return he.forEach((z,ae)=>{z&&(L[z]=M[ae]||"")}),L});J({columns:F,data:B})},error:()=>se(new Error("فشل في قراءة ملف CSV"))})}),_=async Z=>{s(Z),d(!0);try{let J;if(Z.name.endsWith(".xlsx"))J=await p(Z);else if(Z.name.endsWith(".csv"))J=await u(Z);else throw new Error("نوع الملف غير مدعوم");if(J.columns.length===0)throw new Error("لم يتم العثور على أعمدة في الملف");if(J.data.length===0)throw new Error("لم يتم العثور على بيانات في الملف");c(J),ia.success(`تم تحليل الملف بنجاح. تم العثور على ${J.data.length} صف و ${J.columns.length} عمود`)}catch(J){ia.error(J.message||"فشل في تحليل الملف"),s(null),c(null)}finally{d(!1)}},E=()=>{s(null),c(null),n("upload")},R=()=>{i&&n("mapping")},g=()=>{n("upload"),f({})},b=()=>{n("processing")},H=()=>{n("mapping")},U=Z=>{h(Z),n("results")},S=()=>{n("processing")},k=()=>{s(null),c(null),f({}),h([]),n("upload")},v=()=>{a({to:"/orders"})},I=Z=>{switch(Z){case"upload":return T.jsx(vs,{className:"h-4 w-4"});case"mapping":return T.jsx(Oc,{className:"h-4 w-4"});case"processing":return T.jsx(Lc,{className:"h-4 w-4"});case"results":return T.jsx(Qr,{className:"h-4 w-4"})}},P=Z=>{switch(Z){case"upload":return"رفع الملف";case"mapping":return"ربط الأعمدة";case"processing":return"معالجة البيانات";case"results":return"النتائج"}},N=Z=>{switch(Z){case"upload":return!!i;case"mapping":return r==="processing"||r==="results";case"processing":return r==="results";case"results":return!1}},K=Z=>r===Z;return T.jsxs(T.Fragment,{children:[T.jsx(Ec,{title:"إدارة الطلبات"}),T.jsxs("div",{className:"flex flex-col m-4",children:[T.jsx("h1",{className:"text-2xl font-bold",children:"استيراد الطلبات بالجملة"}),T.jsx("p",{className:"text-sm text-muted-foreground",children:"استيراد عدة طلبات من ملف Excel أو CSV"})]}),T.jsx(kc,{className:"mb-6"}),T.jsx("div",{className:"m-4 mb-6",children:T.jsxs(Br,{children:[T.jsxs(Ur,{children:[T.jsx(jr,{children:"خطوات الاستيراد"}),T.jsx(Zr,{children:"اتبع الخطوات التالية لاستيراد الطلبات"})]}),T.jsx(Hr,{children:T.jsx("div",{className:"flex items-center justify-between",children:["upload","mapping","processing","results"].map((Z,J)=>T.jsxs("div",{className:"flex items-center",children:[T.jsxs("div",{className:`flex items-center gap-2 px-3 py-2 rounded-lg ${K(Z)?"bg-primary text-primary-foreground":N(Z)?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:[I(Z),T.jsx("span",{className:"text-sm font-medium",children:P(Z)}),N(Z)&&T.jsx(Dr,{variant:"secondary",className:"ml-1",children:T.jsx(Qr,{className:"h-3 w-3"})})]}),J<3&&T.jsx("div",{className:`w-8 h-0.5 mx-2 ${N(Z)?"bg-green-300":"bg-gray-300"}`})]},Z))})})]})}),T.jsxs("div",{className:"m-4",children:[r==="upload"&&T.jsxs("div",{className:"space-y-6",children:[T.jsx(Kc,{onFileSelect:_,onFileRemove:E,selectedFile:t,isProcessing:x}),i&&T.jsxs(Br,{children:[T.jsx(Ur,{children:T.jsx(jr,{children:"معلومات الملف"})}),T.jsxs(Hr,{children:[T.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[T.jsxs("div",{children:[T.jsx("span",{className:"text-sm font-medium",children:"عدد الصفوف:"}),T.jsx("span",{className:"ml-2",children:i.data.length})]}),T.jsxs("div",{children:[T.jsx("span",{className:"text-sm font-medium",children:"عدد الأعمدة:"}),T.jsx("span",{className:"ml-2",children:i.columns.length})]})]}),T.jsxs("div",{className:"mt-4",children:[T.jsx("span",{className:"text-sm font-medium",children:"الأعمدة المتاحة:"}),T.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:i.columns.map((Z,J)=>T.jsx(Dr,{variant:"outline",children:Z},J))})]}),T.jsx("div",{className:"flex justify-end mt-6",children:T.jsx("button",{onClick:R,className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"التالي - ربط الأعمدة"})})]})]})]}),r==="mapping"&&i&&T.jsx(Yc,{fileColumns:i.columns,sampleData:i.data.slice(0,5),onMappingChange:f,onNext:b,onBack:g}),r==="processing"&&i&&T.jsx(qc,{data:i.data,mapping:o,onComplete:U,onBack:H}),r==="results"&&T.jsx(Jc,{results:l,mapping:o,onRetryAll:S,onNewImport:k,onViewOrders:v})]})]})};export{_2 as component};
