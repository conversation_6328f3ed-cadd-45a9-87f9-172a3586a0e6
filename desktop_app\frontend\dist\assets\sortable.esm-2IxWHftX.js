import{c as Se,r as p,R as H,aj as Ge}from"./index-DM6GIfB4.js";/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=[["path",{d:"M6 9l6 6l6 -6",key:"svg-0"}]],ki=Se("outline","chevron-down","ChevronDown",lr);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=[["path",{d:"M15 6l-6 6l6 6",key:"svg-0"}]],Bi=Se("outline","chevron-left","ChevronLeft",ur);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]],qi=Se("outline","chevron-right","ChevronRight",ar);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=[["path",{d:"M11 7l-5 5l5 5",key:"svg-0"}],["path",{d:"M17 7l-5 5l5 5",key:"svg-1"}]],ji=Se("outline","chevrons-left","ChevronsLeft",cr);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=[["path",{d:"M7 7l5 5l-5 5",key:"svg-0"}],["path",{d:"M13 7l5 5l-5 5",key:"svg-1"}]],Ui=Se("outline","chevrons-right","ChevronsRight",dr);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=[["path",{d:"M9 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M9 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M9 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}],["path",{d:"M15 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-3"}],["path",{d:"M15 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-4"}],["path",{d:"M15 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-5"}]],Xi=Se("outline","grip-vertical","GripVertical",gr);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=[["path",{d:"M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M12 4l0 16",key:"svg-1"}]],Yi=Se("outline","layout-columns","LayoutColumns",fr);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]],Ki=Se("outline","plus","Plus",pr);/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function me(e,n){return typeof e=="function"?e(n):e}function j(e,n){return t=>{n.setState(r=>({...r,[e]:me(t,r[e])}))}}function ft(e){return e instanceof Function}function hr(e){return Array.isArray(e)&&e.every(n=>typeof n=="number")}function vr(e,n){const t=[],r=o=>{o.forEach(i=>{t.push(i);const s=n(i);s!=null&&s.length&&r(s)})};return r(e),t}function R(e,n,t){let r=[],o;return i=>{let s;t.key&&t.debug&&(s=Date.now());const l=e(i);if(!(l.length!==r.length||l.some((d,f)=>r[f]!==d)))return o;r=l;let c;if(t.key&&t.debug&&(c=Date.now()),o=n(...l),t==null||t.onChange==null||t.onChange(o),t.key&&t.debug&&t!=null&&t.debug()){const d=Math.round((Date.now()-s)*100)/100,f=Math.round((Date.now()-c)*100)/100,g=f/16,a=(v,h)=>{for(v=String(v);v.length<h;)v=" "+v;return v};console.info(`%c⏱ ${a(f,5)} /${a(d,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,t==null?void 0:t.key)}return o}}function y(e,n,t,r){return{debug:()=>{var o;return(o=e==null?void 0:e.debugAll)!=null?o:e[n]},key:!1,onChange:r}}function mr(e,n,t,r){const o=()=>{var s;return(s=i.getValue())!=null?s:e.options.renderFallbackValue},i={id:`${n.id}_${t.id}`,row:n,column:t,getValue:()=>n.getValue(r),renderValue:o,getContext:R(()=>[e,t,n,i],(s,l,u,c)=>({table:s,column:l,row:u,cell:c,getValue:c.getValue,renderValue:c.renderValue}),y(e.options,"debugCells"))};return e._features.forEach(s=>{s.createCell==null||s.createCell(i,t,n,e)},{}),i}function wr(e,n,t,r){var o,i;const l={...e._getDefaultColumnDef(),...n},u=l.accessorKey;let c=(o=(i=l.id)!=null?i:u?typeof String.prototype.replaceAll=="function"?u.replaceAll(".","_"):u.replace(/\./g,"_"):void 0)!=null?o:typeof l.header=="string"?l.header:void 0,d;if(l.accessorFn?d=l.accessorFn:u&&(u.includes(".")?d=g=>{let a=g;for(const h of u.split(".")){var v;a=(v=a)==null?void 0:v[h]}return a}:d=g=>g[l.accessorKey]),!c)throw new Error;let f={id:`${String(c)}`,accessorFn:d,parent:r,depth:t,columnDef:l,columns:[],getFlatColumns:R(()=>[!0],()=>{var g;return[f,...(g=f.columns)==null?void 0:g.flatMap(a=>a.getFlatColumns())]},y(e.options,"debugColumns")),getLeafColumns:R(()=>[e._getOrderColumnsFn()],g=>{var a;if((a=f.columns)!=null&&a.length){let v=f.columns.flatMap(h=>h.getLeafColumns());return g(v)}return[f]},y(e.options,"debugColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(f,e);return f}const z="debugHeaders";function ln(e,n,t){var r;let i={id:(r=t.id)!=null?r:n.id,column:n,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const s=[],l=u=>{u.subHeaders&&u.subHeaders.length&&u.subHeaders.map(l),s.push(u)};return l(i),s},getContext:()=>({table:e,header:i,column:n})};return e._features.forEach(s=>{s.createHeader==null||s.createHeader(i,e)}),i}const Sr={createTable:e=>{e.getHeaderGroups=R(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>{var i,s;const l=(i=r==null?void 0:r.map(f=>t.find(g=>g.id===f)).filter(Boolean))!=null?i:[],u=(s=o==null?void 0:o.map(f=>t.find(g=>g.id===f)).filter(Boolean))!=null?s:[],c=t.filter(f=>!(r!=null&&r.includes(f.id))&&!(o!=null&&o.includes(f.id)));return nt(n,[...l,...c,...u],e)},y(e.options,z)),e.getCenterHeaderGroups=R(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>(t=t.filter(i=>!(r!=null&&r.includes(i.id))&&!(o!=null&&o.includes(i.id))),nt(n,t,e,"center")),y(e.options,z)),e.getLeftHeaderGroups=R(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(n,t,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>t.find(l=>l.id===s)).filter(Boolean))!=null?o:[];return nt(n,i,e,"left")},y(e.options,z)),e.getRightHeaderGroups=R(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(n,t,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>t.find(l=>l.id===s)).filter(Boolean))!=null?o:[];return nt(n,i,e,"right")},y(e.options,z)),e.getFooterGroups=R(()=>[e.getHeaderGroups()],n=>[...n].reverse(),y(e.options,z)),e.getLeftFooterGroups=R(()=>[e.getLeftHeaderGroups()],n=>[...n].reverse(),y(e.options,z)),e.getCenterFooterGroups=R(()=>[e.getCenterHeaderGroups()],n=>[...n].reverse(),y(e.options,z)),e.getRightFooterGroups=R(()=>[e.getRightHeaderGroups()],n=>[...n].reverse(),y(e.options,z)),e.getFlatHeaders=R(()=>[e.getHeaderGroups()],n=>n.map(t=>t.headers).flat(),y(e.options,z)),e.getLeftFlatHeaders=R(()=>[e.getLeftHeaderGroups()],n=>n.map(t=>t.headers).flat(),y(e.options,z)),e.getCenterFlatHeaders=R(()=>[e.getCenterHeaderGroups()],n=>n.map(t=>t.headers).flat(),y(e.options,z)),e.getRightFlatHeaders=R(()=>[e.getRightHeaderGroups()],n=>n.map(t=>t.headers).flat(),y(e.options,z)),e.getCenterLeafHeaders=R(()=>[e.getCenterFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),y(e.options,z)),e.getLeftLeafHeaders=R(()=>[e.getLeftFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),y(e.options,z)),e.getRightLeafHeaders=R(()=>[e.getRightFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),y(e.options,z)),e.getLeafHeaders=R(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(n,t,r)=>{var o,i,s,l,u,c;return[...(o=(i=n[0])==null?void 0:i.headers)!=null?o:[],...(s=(l=t[0])==null?void 0:l.headers)!=null?s:[],...(u=(c=r[0])==null?void 0:c.headers)!=null?u:[]].map(d=>d.getLeafHeaders()).flat()},y(e.options,z))}};function nt(e,n,t,r){var o,i;let s=0;const l=function(g,a){a===void 0&&(a=1),s=Math.max(s,a),g.filter(v=>v.getIsVisible()).forEach(v=>{var h;(h=v.columns)!=null&&h.length&&l(v.columns,a+1)},0)};l(e);let u=[];const c=(g,a)=>{const v={depth:a,id:[r,`${a}`].filter(Boolean).join("_"),headers:[]},h=[];g.forEach(m=>{const w=[...h].reverse()[0],C=m.column.depth===v.depth;let S,_=!1;if(C&&m.column.parent?S=m.column.parent:(S=m.column,_=!0),w&&(w==null?void 0:w.column)===S)w.subHeaders.push(m);else{const x=ln(t,S,{id:[r,a,S.id,m==null?void 0:m.id].filter(Boolean).join("_"),isPlaceholder:_,placeholderId:_?`${h.filter(D=>D.column===S).length}`:void 0,depth:a,index:h.length});x.subHeaders.push(m),h.push(x)}v.headers.push(m),m.headerGroup=v}),u.push(v),a>0&&c(h,a-1)},d=n.map((g,a)=>ln(t,g,{depth:s,index:a}));c(d,s-1),u.reverse();const f=g=>g.filter(v=>v.column.getIsVisible()).map(v=>{let h=0,m=0,w=[0];v.subHeaders&&v.subHeaders.length?(w=[],f(v.subHeaders).forEach(S=>{let{colSpan:_,rowSpan:x}=S;h+=_,w.push(x)})):h=1;const C=Math.min(...w);return m=m+C,v.colSpan=h,v.rowSpan=m,{colSpan:h,rowSpan:m}});return f((o=(i=u[0])==null?void 0:i.headers)!=null?o:[]),u}const Nt=(e,n,t,r,o,i,s)=>{let l={id:n,index:r,original:t,depth:o,parentId:s,_valuesCache:{},_uniqueValuesCache:{},getValue:u=>{if(l._valuesCache.hasOwnProperty(u))return l._valuesCache[u];const c=e.getColumn(u);if(c!=null&&c.accessorFn)return l._valuesCache[u]=c.accessorFn(l.original,r),l._valuesCache[u]},getUniqueValues:u=>{if(l._uniqueValuesCache.hasOwnProperty(u))return l._uniqueValuesCache[u];const c=e.getColumn(u);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(l._uniqueValuesCache[u]=c.columnDef.getUniqueValues(l.original,r),l._uniqueValuesCache[u]):(l._uniqueValuesCache[u]=[l.getValue(u)],l._uniqueValuesCache[u])},renderValue:u=>{var c;return(c=l.getValue(u))!=null?c:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>vr(l.subRows,u=>u.subRows),getParentRow:()=>l.parentId?e.getRow(l.parentId,!0):void 0,getParentRows:()=>{let u=[],c=l;for(;;){const d=c.getParentRow();if(!d)break;u.push(d),c=d}return u.reverse()},getAllCells:R(()=>[e.getAllLeafColumns()],u=>u.map(c=>mr(e,l,c,c.id)),y(e.options,"debugRows")),_getAllCellsByColumnId:R(()=>[l.getAllCells()],u=>u.reduce((c,d)=>(c[d.column.id]=d,c),{}),y(e.options,"debugRows"))};for(let u=0;u<e._features.length;u++){const c=e._features[u];c==null||c.createRow==null||c.createRow(l,e)}return l},Cr={createColumn:(e,n)=>{e._getFacetedRowModel=n.options.getFacetedRowModel&&n.options.getFacetedRowModel(n,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():n.getPreFilteredRowModel(),e._getFacetedUniqueValues=n.options.getFacetedUniqueValues&&n.options.getFacetedUniqueValues(n,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=n.options.getFacetedMinMaxValues&&n.options.getFacetedMinMaxValues(n,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Rn=(e,n,t)=>{var r,o;const i=t==null||(r=t.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(n))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(i))};Rn.autoRemove=e=>J(e);const yn=(e,n,t)=>{var r;return!!(!((r=e.getValue(n))==null||(r=r.toString())==null)&&r.includes(t))};yn.autoRemove=e=>J(e);const xn=(e,n,t)=>{var r;return((r=e.getValue(n))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(t==null?void 0:t.toLowerCase())};xn.autoRemove=e=>J(e);const _n=(e,n,t)=>{var r;return(r=e.getValue(n))==null?void 0:r.includes(t)};_n.autoRemove=e=>J(e);const bn=(e,n,t)=>!t.some(r=>{var o;return!((o=e.getValue(n))!=null&&o.includes(r))});bn.autoRemove=e=>J(e)||!(e!=null&&e.length);const Dn=(e,n,t)=>t.some(r=>{var o;return(o=e.getValue(n))==null?void 0:o.includes(r)});Dn.autoRemove=e=>J(e)||!(e!=null&&e.length);const Mn=(e,n,t)=>e.getValue(n)===t;Mn.autoRemove=e=>J(e);const Fn=(e,n,t)=>e.getValue(n)==t;Fn.autoRemove=e=>J(e);const kt=(e,n,t)=>{let[r,o]=t;const i=e.getValue(n);return i>=r&&i<=o};kt.resolveFilterValue=e=>{let[n,t]=e,r=typeof n!="number"?parseFloat(n):n,o=typeof t!="number"?parseFloat(t):t,i=n===null||Number.isNaN(r)?-1/0:r,s=t===null||Number.isNaN(o)?1/0:o;if(i>s){const l=i;i=s,s=l}return[i,s]};kt.autoRemove=e=>J(e)||J(e[0])&&J(e[1]);const ae={includesString:Rn,includesStringSensitive:yn,equalsString:xn,arrIncludes:_n,arrIncludesAll:bn,arrIncludesSome:Dn,equals:Mn,weakEquals:Fn,inNumberRange:kt};function J(e){return e==null||e===""}const Rr={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:j("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,n)=>{e.getAutoFilterFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t==null?void 0:t.getValue(e.id);return typeof r=="string"?ae.includesString:typeof r=="number"?ae.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?ae.equals:Array.isArray(r)?ae.arrIncludes:ae.weakEquals},e.getFilterFn=()=>{var t,r;return ft(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(r=n.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?t:ae[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,r,o;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((r=n.options.enableColumnFilters)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=n.getState().columnFilters)==null||(t=t.find(r=>r.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,r;return(t=(r=n.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.setFilterValue=t=>{n.setColumnFilters(r=>{const o=e.getFilterFn(),i=r==null?void 0:r.find(d=>d.id===e.id),s=me(t,i?i.value:void 0);if(un(o,s,e)){var l;return(l=r==null?void 0:r.filter(d=>d.id!==e.id))!=null?l:[]}const u={id:e.id,value:s};if(i){var c;return(c=r==null?void 0:r.map(d=>d.id===e.id?u:d))!=null?c:[]}return r!=null&&r.length?[...r,u]:[u]})}},createRow:(e,n)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=n=>{const t=e.getAllLeafColumns(),r=o=>{var i;return(i=me(n,o))==null?void 0:i.filter(s=>{const l=t.find(u=>u.id===s.id);if(l){const u=l.getFilterFn();if(un(u,s.value,l))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=n=>{var t,r;e.setColumnFilters(n?[]:(t=(r=e.initialState)==null?void 0:r.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function un(e,n,t){return(e&&e.autoRemove?e.autoRemove(n,t):!1)||typeof n>"u"||typeof n=="string"&&!n}const yr=(e,n,t)=>t.reduce((r,o)=>{const i=o.getValue(e);return r+(typeof i=="number"?i:0)},0),xr=(e,n,t)=>{let r;return t.forEach(o=>{const i=o.getValue(e);i!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}),r},_r=(e,n,t)=>{let r;return t.forEach(o=>{const i=o.getValue(e);i!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}),r},br=(e,n,t)=>{let r,o;return t.forEach(i=>{const s=i.getValue(e);s!=null&&(r===void 0?s>=s&&(r=o=s):(r>s&&(r=s),o<s&&(o=s)))}),[r,o]},Dr=(e,n)=>{let t=0,r=0;if(n.forEach(o=>{let i=o.getValue(e);i!=null&&(i=+i)>=i&&(++t,r+=i)}),t)return r/t},Mr=(e,n)=>{if(!n.length)return;const t=n.map(i=>i.getValue(e));if(!hr(t))return;if(t.length===1)return t[0];const r=Math.floor(t.length/2),o=t.sort((i,s)=>i-s);return t.length%2!==0?o[r]:(o[r-1]+o[r])/2},Fr=(e,n)=>Array.from(new Set(n.map(t=>t.getValue(e))).values()),Ir=(e,n)=>new Set(n.map(t=>t.getValue(e))).size,Er=(e,n)=>n.length,Ct={sum:yr,min:xr,max:_r,extent:br,mean:Dr,median:Mr,unique:Fr,uniqueCount:Ir,count:Er},$r={getDefaultColumnDef:()=>({aggregatedCell:e=>{var n,t;return(n=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?n:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:j("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,n)=>{e.toggleGrouping=()=>{n.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(r=>r!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,r;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((r=n.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t==null?void 0:t.getValue(e.id);if(typeof r=="number")return Ct.sum;if(Object.prototype.toString.call(r)==="[object Date]")return Ct.extent},e.getAggregationFn=()=>{var t,r;if(!e)throw new Error;return ft(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(r=n.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?t:Ct[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=n=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(n),e.resetGrouping=n=>{var t,r;e.setGrouping(n?[]:(t=(r=e.initialState)==null?void 0:r.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,n)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const r=n.getColumn(t);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[t]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,n,t,r)=>{e.getIsGrouped=()=>n.getIsGrouped()&&n.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&n.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=t.subRows)!=null&&o.length)}}};function Pr(e,n,t){if(!(n!=null&&n.length)||!t)return e;const r=e.filter(i=>!n.includes(i.id));return t==="remove"?r:[...n.map(i=>e.find(s=>s.id===i)).filter(Boolean),...r]}const Vr={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:j("columnOrder",e)}),createColumn:(e,n)=>{e.getIndex=R(t=>[Ne(n,t)],t=>t.findIndex(r=>r.id===e.id),y(n.options,"debugColumns")),e.getIsFirstColumn=t=>{var r;return((r=Ne(n,t)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=t=>{var r;const o=Ne(n,t);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=n=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(n),e.resetColumnOrder=n=>{var t;e.setColumnOrder(n?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=R(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(n,t,r)=>o=>{let i=[];if(!(n!=null&&n.length))i=o;else{const s=[...n],l=[...o];for(;l.length&&s.length;){const u=s.shift(),c=l.findIndex(d=>d.id===u);c>-1&&i.push(l.splice(c,1)[0])}i=[...i,...l]}return Pr(i,t,r)},y(e.options,"debugTable"))}},Rt=()=>({left:[],right:[]}),Ar={getInitialState:e=>({columnPinning:Rt(),...e}),getDefaultOptions:e=>({onColumnPinningChange:j("columnPinning",e)}),createColumn:(e,n)=>{e.pin=t=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);n.setColumnPinning(o=>{var i,s;if(t==="right"){var l,u;return{left:((l=o==null?void 0:o.left)!=null?l:[]).filter(f=>!(r!=null&&r.includes(f))),right:[...((u=o==null?void 0:o.right)!=null?u:[]).filter(f=>!(r!=null&&r.includes(f))),...r]}}if(t==="left"){var c,d;return{left:[...((c=o==null?void 0:o.left)!=null?c:[]).filter(f=>!(r!=null&&r.includes(f))),...r],right:((d=o==null?void 0:o.right)!=null?d:[]).filter(f=>!(r!=null&&r.includes(f)))}}return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(f=>!(r!=null&&r.includes(f))),right:((s=o==null?void 0:o.right)!=null?s:[]).filter(f=>!(r!=null&&r.includes(f)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,i,s;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((i=(s=n.options.enableColumnPinning)!=null?s:n.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(l=>l.id),{left:r,right:o}=n.getState().columnPinning,i=t.some(l=>r==null?void 0:r.includes(l)),s=t.some(l=>o==null?void 0:o.includes(l));return i?"left":s?"right":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();return o?(t=(r=n.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?t:-1:0}},createRow:(e,n)=>{e.getCenterVisibleCells=R(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left,n.getState().columnPinning.right],(t,r,o)=>{const i=[...r??[],...o??[]];return t.filter(s=>!i.includes(s.column.id))},y(n.options,"debugRows")),e.getLeftVisibleCells=R(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left],(t,r)=>(r??[]).map(i=>t.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),y(n.options,"debugRows")),e.getRightVisibleCells=R(()=>[e._getAllVisibleCells(),n.getState().columnPinning.right],(t,r)=>(r??[]).map(i=>t.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),y(n.options,"debugRows"))},createTable:e=>{e.setColumnPinning=n=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(n),e.resetColumnPinning=n=>{var t,r;return e.setColumnPinning(n?Rt():(t=(r=e.initialState)==null?void 0:r.columnPinning)!=null?t:Rt())},e.getIsSomeColumnsPinned=n=>{var t;const r=e.getState().columnPinning;if(!n){var o,i;return!!((o=r.left)!=null&&o.length||(i=r.right)!=null&&i.length)}return!!((t=r[n])!=null&&t.length)},e.getLeftLeafColumns=R(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),y(e.options,"debugColumns")),e.getRightLeafColumns=R(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),y(e.options,"debugColumns")),e.getCenterLeafColumns=R(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r)=>{const o=[...t??[],...r??[]];return n.filter(i=>!o.includes(i.id))},y(e.options,"debugColumns"))}};function Lr(e){return e||(typeof document<"u"?document:null)}const rt={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},yt=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Or={getDefaultColumnDef:()=>rt,getInitialState:e=>({columnSizing:{},columnSizingInfo:yt(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:j("columnSizing",e),onColumnSizingInfoChange:j("columnSizingInfo",e)}),createColumn:(e,n)=>{e.getSize=()=>{var t,r,o;const i=n.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:rt.minSize,(r=i??e.columnDef.size)!=null?r:rt.size),(o=e.columnDef.maxSize)!=null?o:rt.maxSize)},e.getStart=R(t=>[t,Ne(n,t),n.getState().columnSizing],(t,r)=>r.slice(0,e.getIndex(t)).reduce((o,i)=>o+i.getSize(),0),y(n.options,"debugColumns")),e.getAfter=R(t=>[t,Ne(n,t),n.getState().columnSizing],(t,r)=>r.slice(e.getIndex(t)+1).reduce((o,i)=>o+i.getSize(),0),y(n.options,"debugColumns")),e.resetSize=()=>{n.setColumnSizing(t=>{let{[e.id]:r,...o}=t;return o})},e.getCanResize=()=>{var t,r;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((r=n.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>n.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,n)=>{e.getSize=()=>{let t=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var i;t+=(i=o.column.getSize())!=null?i:0}};return r(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const r=n.getColumn(e.column.id),o=r==null?void 0:r.getCanResize();return i=>{if(!r||!o||(i.persist==null||i.persist(),xt(i)&&i.touches&&i.touches.length>1))return;const s=e.getSize(),l=e?e.getLeafHeaders().map(w=>[w.column.id,w.column.getSize()]):[[r.id,r.getSize()]],u=xt(i)?Math.round(i.touches[0].clientX):i.clientX,c={},d=(w,C)=>{typeof C=="number"&&(n.setColumnSizingInfo(S=>{var _,x;const D=n.options.columnResizeDirection==="rtl"?-1:1,M=(C-((_=S==null?void 0:S.startOffset)!=null?_:0))*D,b=Math.max(M/((x=S==null?void 0:S.startSize)!=null?x:0),-.999999);return S.columnSizingStart.forEach($=>{let[P,E]=$;c[P]=Math.round(Math.max(E+E*b,0)*100)/100}),{...S,deltaOffset:M,deltaPercentage:b}}),(n.options.columnResizeMode==="onChange"||w==="end")&&n.setColumnSizing(S=>({...S,...c})))},f=w=>d("move",w),g=w=>{d("end",w),n.setColumnSizingInfo(C=>({...C,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},a=Lr(t),v={moveHandler:w=>f(w.clientX),upHandler:w=>{a==null||a.removeEventListener("mousemove",v.moveHandler),a==null||a.removeEventListener("mouseup",v.upHandler),g(w.clientX)}},h={moveHandler:w=>(w.cancelable&&(w.preventDefault(),w.stopPropagation()),f(w.touches[0].clientX),!1),upHandler:w=>{var C;a==null||a.removeEventListener("touchmove",h.moveHandler),a==null||a.removeEventListener("touchend",h.upHandler),w.cancelable&&(w.preventDefault(),w.stopPropagation()),g((C=w.touches[0])==null?void 0:C.clientX)}},m=zr()?{passive:!1}:!1;xt(i)?(a==null||a.addEventListener("touchmove",h.moveHandler,m),a==null||a.addEventListener("touchend",h.upHandler,m)):(a==null||a.addEventListener("mousemove",v.moveHandler,m),a==null||a.addEventListener("mouseup",v.upHandler,m)),n.setColumnSizingInfo(w=>({...w,startOffset:u,startSize:s,deltaOffset:0,deltaPercentage:0,columnSizingStart:l,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=n=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(n),e.setColumnSizingInfo=n=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(n),e.resetColumnSizing=n=>{var t;e.setColumnSizing(n?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=n=>{var t;e.setColumnSizingInfo(n?yt():(t=e.initialState.columnSizingInfo)!=null?t:yt())},e.getTotalSize=()=>{var n,t;return(n=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getLeftTotalSize=()=>{var n,t;return(n=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getCenterTotalSize=()=>{var n,t;return(n=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getRightTotalSize=()=>{var n,t;return(n=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0}}};let ot=null;function zr(){if(typeof ot=="boolean")return ot;let e=!1;try{const n={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,n),window.removeEventListener("test",t)}catch{e=!1}return ot=e,ot}function xt(e){return e.type==="touchstart"}const Tr={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:j("columnVisibility",e)}),createColumn:(e,n)=>{e.toggleVisibility=t=>{e.getCanHide()&&n.setColumnVisibility(r=>({...r,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,r;const o=e.columns;return(t=o.length?o.some(i=>i.getIsVisible()):(r=n.getState().columnVisibility)==null?void 0:r[e.id])!=null?t:!0},e.getCanHide=()=>{var t,r;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((r=n.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,n)=>{e._getAllVisibleCells=R(()=>[e.getAllCells(),n.getState().columnVisibility],t=>t.filter(r=>r.column.getIsVisible()),y(n.options,"debugRows")),e.getVisibleCells=R(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,r,o)=>[...t,...r,...o],y(n.options,"debugRows"))},createTable:e=>{const n=(t,r)=>R(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),y(e.options,"debugColumns"));e.getVisibleFlatColumns=n("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=n("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=n("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=n("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=n("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var r;e.setColumnVisibility(t?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=t=>{var r;t=(r=t)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,i)=>({...o,[i.id]:t||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var r;e.toggleAllColumnsVisible((r=t.target)==null?void 0:r.checked)}}};function Ne(e,n){return n?n==="center"?e.getCenterVisibleLeafColumns():n==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Hr={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Gr={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:j("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:n=>{var t;const r=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[n.id])==null?void 0:t.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,n)=>{e.getCanGlobalFilter=()=>{var t,r,o,i;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((r=n.options.enableGlobalFilter)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&((i=n.options.getColumnCanGlobalFilter==null?void 0:n.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>ae.includesString,e.getGlobalFilterFn=()=>{var n,t;const{globalFilterFn:r}=e.options;return ft(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(n=(t=e.options.filterFns)==null?void 0:t[r])!=null?n:ae[r]},e.setGlobalFilter=n=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(n)},e.resetGlobalFilter=n=>{e.setGlobalFilter(n?void 0:e.initialState.globalFilter)}}},Nr={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:j("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let n=!1,t=!1;e._autoResetExpanded=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,i;e.setExpanded(r?{}:(o=(i=e.initialState)==null?void 0:i.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const s=i.split(".");r=Math.max(r,s.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,n)=>{e.toggleExpanded=t=>{n.setExpanded(r=>{var o;const i=r===!0?!0:!!(r!=null&&r[e.id]);let s={};if(r===!0?Object.keys(n.getRowModel().rowsById).forEach(l=>{s[l]=!0}):s=r,t=(o=t)!=null?o:!i,!i&&t)return{...s,[e.id]:!0};if(i&&!t){const{[e.id]:l,...u}=s;return u}return r})},e.getIsExpanded=()=>{var t;const r=n.getState().expanded;return!!((t=n.options.getIsRowExpanded==null?void 0:n.options.getIsRowExpanded(e))!=null?t:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var t,r,o;return(t=n.options.getRowCanExpand==null?void 0:n.options.getRowCanExpand(e))!=null?t:((r=n.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let t=!0,r=e;for(;t&&r.parentId;)r=n.getRow(r.parentId,!0),t=r.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},$t=0,Pt=10,_t=()=>({pageIndex:$t,pageSize:Pt}),kr={getInitialState:e=>({...e,pagination:{..._t(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:j("pagination",e)}),createTable:e=>{let n=!1,t=!1;e._autoResetPageIndex=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=r=>{const o=i=>me(r,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?_t():(o=e.initialState.pagination)!=null?o:_t())},e.setPageIndex=r=>{e.setPagination(o=>{let i=me(r,o.pageIndex);const s=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,s)),{...o,pageIndex:i}})},e.resetPageIndex=r=>{var o,i;e.setPageIndex(r?$t:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?o:$t)},e.resetPageSize=r=>{var o,i;e.setPageSize(r?Pt:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?o:Pt)},e.setPageSize=r=>{e.setPagination(o=>{const i=Math.max(1,me(r,o.pageSize)),s=o.pageSize*o.pageIndex,l=Math.floor(s/i);return{...o,pageIndex:l,pageSize:i}})},e.setPageCount=r=>e.setPagination(o=>{var i;let s=me(r,(i=e.options.pageCount)!=null?i:-1);return typeof s=="number"&&(s=Math.max(-1,s)),{...o,pageCount:s}}),e.getPageOptions=R(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((i,s)=>s)),o},y(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},bt=()=>({top:[],bottom:[]}),Br={getInitialState:e=>({rowPinning:bt(),...e}),getDefaultOptions:e=>({onRowPinningChange:j("rowPinning",e)}),createRow:(e,n)=>{e.pin=(t,r,o)=>{const i=r?e.getLeafRows().map(u=>{let{id:c}=u;return c}):[],s=o?e.getParentRows().map(u=>{let{id:c}=u;return c}):[],l=new Set([...s,e.id,...i]);n.setRowPinning(u=>{var c,d;if(t==="bottom"){var f,g;return{top:((f=u==null?void 0:u.top)!=null?f:[]).filter(h=>!(l!=null&&l.has(h))),bottom:[...((g=u==null?void 0:u.bottom)!=null?g:[]).filter(h=>!(l!=null&&l.has(h))),...Array.from(l)]}}if(t==="top"){var a,v;return{top:[...((a=u==null?void 0:u.top)!=null?a:[]).filter(h=>!(l!=null&&l.has(h))),...Array.from(l)],bottom:((v=u==null?void 0:u.bottom)!=null?v:[]).filter(h=>!(l!=null&&l.has(h)))}}return{top:((c=u==null?void 0:u.top)!=null?c:[]).filter(h=>!(l!=null&&l.has(h))),bottom:((d=u==null?void 0:u.bottom)!=null?d:[]).filter(h=>!(l!=null&&l.has(h)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:r,enablePinning:o}=n.options;return typeof r=="function"?r(e):(t=r??o)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:r,bottom:o}=n.getState().rowPinning,i=t.some(l=>r==null?void 0:r.includes(l)),s=t.some(l=>o==null?void 0:o.includes(l));return i?"top":s?"bottom":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();if(!o)return-1;const i=(t=o==="top"?n.getTopRows():n.getBottomRows())==null?void 0:t.map(s=>{let{id:l}=s;return l});return(r=i==null?void 0:i.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=n=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(n),e.resetRowPinning=n=>{var t,r;return e.setRowPinning(n?bt():(t=(r=e.initialState)==null?void 0:r.rowPinning)!=null?t:bt())},e.getIsSomeRowsPinned=n=>{var t;const r=e.getState().rowPinning;if(!n){var o,i;return!!((o=r.top)!=null&&o.length||(i=r.bottom)!=null&&i.length)}return!!((t=r[n])!=null&&t.length)},e._getPinnedRows=(n,t,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(t??[]).map(s=>{const l=e.getRow(s,!0);return l.getIsAllParentsExpanded()?l:null}):(t??[]).map(s=>n.find(l=>l.id===s))).filter(Boolean).map(s=>({...s,position:r}))},e.getTopRows=R(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(n,t)=>e._getPinnedRows(n,t,"top"),y(e.options,"debugRows")),e.getBottomRows=R(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(n,t)=>e._getPinnedRows(n,t,"bottom"),y(e.options,"debugRows")),e.getCenterRows=R(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(n,t,r)=>{const o=new Set([...t??[],...r??[]]);return n.filter(i=>!o.has(i.id))},y(e.options,"debugRows"))}},qr={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:j("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=n=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(n),e.resetRowSelection=n=>{var t;return e.setRowSelection(n?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=n=>{e.setRowSelection(t=>{n=typeof n<"u"?n:!e.getIsAllRowsSelected();const r={...t},o=e.getPreGroupedRowModel().flatRows;return n?o.forEach(i=>{i.getCanSelect()&&(r[i.id]=!0)}):o.forEach(i=>{delete r[i.id]}),r})},e.toggleAllPageRowsSelected=n=>e.setRowSelection(t=>{const r=typeof n<"u"?n:!e.getIsAllPageRowsSelected(),o={...t};return e.getRowModel().rows.forEach(i=>{Vt(o,i.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=R(()=>[e.getState().rowSelection,e.getCoreRowModel()],(n,t)=>Object.keys(n).length?Dt(e,t):{rows:[],flatRows:[],rowsById:{}},y(e.options,"debugTable")),e.getFilteredSelectedRowModel=R(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(n,t)=>Object.keys(n).length?Dt(e,t):{rows:[],flatRows:[],rowsById:{}},y(e.options,"debugTable")),e.getGroupedSelectedRowModel=R(()=>[e.getState().rowSelection,e.getSortedRowModel()],(n,t)=>Object.keys(n).length?Dt(e,t):{rows:[],flatRows:[],rowsById:{}},y(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const n=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let r=!!(n.length&&Object.keys(t).length);return r&&n.some(o=>o.getCanSelect()&&!t[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:t}=e.getState();let r=!!n.length;return r&&n.some(o=>!t[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var n;const t=Object.keys((n=e.getState().rowSelection)!=null?n:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:n.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>n=>{e.toggleAllRowsSelected(n.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>n=>{e.toggleAllPageRowsSelected(n.target.checked)}},createRow:(e,n)=>{e.toggleSelected=(t,r)=>{const o=e.getIsSelected();n.setRowSelection(i=>{var s;if(t=typeof t<"u"?t:!o,e.getCanSelect()&&o===t)return i;const l={...i};return Vt(l,e.id,t,(s=r==null?void 0:r.selectChildren)!=null?s:!0,n),l})},e.getIsSelected=()=>{const{rowSelection:t}=n.getState();return Bt(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=n.getState();return At(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=n.getState();return At(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof n.options.enableRowSelection=="function"?n.options.enableRowSelection(e):(t=n.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof n.options.enableSubRowSelection=="function"?n.options.enableSubRowSelection(e):(t=n.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof n.options.enableMultiRowSelection=="function"?n.options.enableMultiRowSelection(e):(t=n.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return r=>{var o;t&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},Vt=(e,n,t,r,o)=>{var i;const s=o.getRow(n,!0);t?(s.getCanMultiSelect()||Object.keys(e).forEach(l=>delete e[l]),s.getCanSelect()&&(e[n]=!0)):delete e[n],r&&(i=s.subRows)!=null&&i.length&&s.getCanSelectSubRows()&&s.subRows.forEach(l=>Vt(e,l.id,t,r,o))};function Dt(e,n){const t=e.getState().rowSelection,r=[],o={},i=function(s,l){return s.map(u=>{var c;const d=Bt(u,t);if(d&&(r.push(u),o[u.id]=u),(c=u.subRows)!=null&&c.length&&(u={...u,subRows:i(u.subRows)}),d)return u}).filter(Boolean)};return{rows:i(n.rows),flatRows:r,rowsById:o}}function Bt(e,n){var t;return(t=n[e.id])!=null?t:!1}function At(e,n,t){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,i=!1;return e.subRows.forEach(s=>{if(!(i&&!o)&&(s.getCanSelect()&&(Bt(s,n)?i=!0:o=!1),s.subRows&&s.subRows.length)){const l=At(s,n);l==="all"?i=!0:(l==="some"&&(i=!0),o=!1)}}),o?"all":i?"some":!1}const Lt=/([0-9]+)/gm,jr=(e,n,t)=>In(we(e.getValue(t)).toLowerCase(),we(n.getValue(t)).toLowerCase()),Ur=(e,n,t)=>In(we(e.getValue(t)),we(n.getValue(t))),Xr=(e,n,t)=>qt(we(e.getValue(t)).toLowerCase(),we(n.getValue(t)).toLowerCase()),Yr=(e,n,t)=>qt(we(e.getValue(t)),we(n.getValue(t))),Kr=(e,n,t)=>{const r=e.getValue(t),o=n.getValue(t);return r>o?1:r<o?-1:0},Wr=(e,n,t)=>qt(e.getValue(t),n.getValue(t));function qt(e,n){return e===n?0:e>n?1:-1}function we(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function In(e,n){const t=e.split(Lt).filter(Boolean),r=n.split(Lt).filter(Boolean);for(;t.length&&r.length;){const o=t.shift(),i=r.shift(),s=parseInt(o,10),l=parseInt(i,10),u=[s,l].sort();if(isNaN(u[0])){if(o>i)return 1;if(i>o)return-1;continue}if(isNaN(u[1]))return isNaN(s)?-1:1;if(s>l)return 1;if(l>s)return-1}return t.length-r.length}const He={alphanumeric:jr,alphanumericCaseSensitive:Ur,text:Xr,textCaseSensitive:Yr,datetime:Kr,basic:Wr},Jr={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:j("sorting",e),isMultiSortEvent:n=>n.shiftKey}),createColumn:(e,n)=>{e.getAutoSortingFn=()=>{const t=n.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of t){const i=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return He.datetime;if(typeof i=="string"&&(r=!0,i.split(Lt).length>1))return He.alphanumeric}return r?He.text:He.basic},e.getAutoSortDir=()=>{const t=n.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,r;if(!e)throw new Error;return ft(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(r=n.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?t:He[e.columnDef.sortingFn]},e.toggleSorting=(t,r)=>{const o=e.getNextSortingOrder(),i=typeof t<"u"&&t!==null;n.setSorting(s=>{const l=s==null?void 0:s.find(a=>a.id===e.id),u=s==null?void 0:s.findIndex(a=>a.id===e.id);let c=[],d,f=i?t:o==="desc";if(s!=null&&s.length&&e.getCanMultiSort()&&r?l?d="toggle":d="add":s!=null&&s.length&&u!==s.length-1?d="replace":l?d="toggle":d="replace",d==="toggle"&&(i||o||(d="remove")),d==="add"){var g;c=[...s,{id:e.id,desc:f}],c.splice(0,c.length-((g=n.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else d==="toggle"?c=s.map(a=>a.id===e.id?{...a,desc:f}:a):d==="remove"?c=s.filter(a=>a.id!==e.id):c=[{id:e.id,desc:f}];return c})},e.getFirstSortDir=()=>{var t,r;return((t=(r=e.columnDef.sortDescFirst)!=null?r:n.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var r,o;const i=e.getFirstSortDir(),s=e.getIsSorted();return s?s!==i&&((r=n.options.enableSortingRemoval)==null||r)&&(!(t&&(o=n.options.enableMultiRemove)!=null)||o)?!1:s==="desc"?"asc":"desc":i},e.getCanSort=()=>{var t,r;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((r=n.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,r;return(t=(r=e.columnDef.enableMultiSort)!=null?r:n.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const r=(t=n.getState().sorting)==null?void 0:t.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,r;return(t=(r=n.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.clearSorting=()=>{n.setSorting(t=>t!=null&&t.length?t.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return r=>{t&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?n.options.isMultiSortEvent==null?void 0:n.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=n=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(n),e.resetSorting=n=>{var t,r;e.setSorting(n?[]:(t=(r=e.initialState)==null?void 0:r.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},Qr=[Sr,Tr,Vr,Ar,Cr,Rr,Hr,Gr,Jr,$r,Nr,kr,Br,qr,Or];function Zr(e){var n,t;const r=[...Qr,...(n=e._features)!=null?n:[]];let o={_features:r};const i=o._features.reduce((g,a)=>Object.assign(g,a.getDefaultOptions==null?void 0:a.getDefaultOptions(o)),{}),s=g=>o.options.mergeOptions?o.options.mergeOptions(i,g):{...i,...g};let u={...{},...(t=e.initialState)!=null?t:{}};o._features.forEach(g=>{var a;u=(a=g.getInitialState==null?void 0:g.getInitialState(u))!=null?a:u});const c=[];let d=!1;const f={_features:r,options:{...i,...e},initialState:u,_queue:g=>{c.push(g),d||(d=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();d=!1}).catch(a=>setTimeout(()=>{throw a})))},reset:()=>{o.setState(o.initialState)},setOptions:g=>{const a=me(g,o.options);o.options=s(a)},getState:()=>o.options.state,setState:g=>{o.options.onStateChange==null||o.options.onStateChange(g)},_getRowId:(g,a,v)=>{var h;return(h=o.options.getRowId==null?void 0:o.options.getRowId(g,a,v))!=null?h:`${v?[v.id,a].join("."):a}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(g,a)=>{let v=(a?o.getPrePaginationRowModel():o.getRowModel()).rowsById[g];if(!v&&(v=o.getCoreRowModel().rowsById[g],!v))throw new Error;return v},_getDefaultColumnDef:R(()=>[o.options.defaultColumn],g=>{var a;return g=(a=g)!=null?a:{},{header:v=>{const h=v.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:v=>{var h,m;return(h=(m=v.renderValue())==null||m.toString==null?void 0:m.toString())!=null?h:null},...o._features.reduce((v,h)=>Object.assign(v,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...g}},y(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:R(()=>[o._getColumnDefs()],g=>{const a=function(v,h,m){return m===void 0&&(m=0),v.map(w=>{const C=wr(o,w,m,h),S=w;return C.columns=S.columns?a(S.columns,C,m+1):[],C})};return a(g)},y(e,"debugColumns")),getAllFlatColumns:R(()=>[o.getAllColumns()],g=>g.flatMap(a=>a.getFlatColumns()),y(e,"debugColumns")),_getAllFlatColumnsById:R(()=>[o.getAllFlatColumns()],g=>g.reduce((a,v)=>(a[v.id]=v,a),{}),y(e,"debugColumns")),getAllLeafColumns:R(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(g,a)=>{let v=g.flatMap(h=>h.getLeafColumns());return a(v)},y(e,"debugColumns")),getColumn:g=>o._getAllFlatColumnsById()[g]};Object.assign(o,f);for(let g=0;g<o._features.length;g++){const a=o._features[g];a==null||a.createTable==null||a.createTable(o)}return o}function Wi(){return e=>R(()=>[e.options.data],n=>{const t={rows:[],flatRows:[],rowsById:{}},r=function(o,i,s){i===void 0&&(i=0);const l=[];for(let c=0;c<o.length;c++){const d=Nt(e,e._getRowId(o[c],c,s),o[c],c,i,void 0,s==null?void 0:s.id);if(t.flatRows.push(d),t.rowsById[d.id]=d,l.push(d),e.options.getSubRows){var u;d.originalSubRows=e.options.getSubRows(o[c],c),(u=d.originalSubRows)!=null&&u.length&&(d.subRows=r(d.originalSubRows,i+1,d))}}return l};return t.rows=r(n),t},y(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function eo(e){const n=[],t=r=>{var o;n.push(r),(o=r.subRows)!=null&&o.length&&r.getIsExpanded()&&r.subRows.forEach(t)};return e.rows.forEach(t),{rows:n,flatRows:e.flatRows,rowsById:e.rowsById}}function En(e,n,t){return t.options.filterFromLeafRows?to(e,n,t):no(e,n,t)}function to(e,n,t){var r;const o=[],i={},s=(r=t.options.maxLeafRowFilterDepth)!=null?r:100,l=function(u,c){c===void 0&&(c=0);const d=[];for(let g=0;g<u.length;g++){var f;let a=u[g];const v=Nt(t,a.id,a.original,a.index,a.depth,void 0,a.parentId);if(v.columnFilters=a.columnFilters,(f=a.subRows)!=null&&f.length&&c<s){if(v.subRows=l(a.subRows,c+1),a=v,n(a)&&!v.subRows.length){d.push(a),i[a.id]=a,o.push(a);continue}if(n(a)||v.subRows.length){d.push(a),i[a.id]=a,o.push(a);continue}}else a=v,n(a)&&(d.push(a),i[a.id]=a,o.push(a))}return d};return{rows:l(e),flatRows:o,rowsById:i}}function no(e,n,t){var r;const o=[],i={},s=(r=t.options.maxLeafRowFilterDepth)!=null?r:100,l=function(u,c){c===void 0&&(c=0);const d=[];for(let g=0;g<u.length;g++){let a=u[g];if(n(a)){var f;if((f=a.subRows)!=null&&f.length&&c<s){const h=Nt(t,a.id,a.original,a.index,a.depth,void 0,a.parentId);h.subRows=l(a.subRows,c+1),a=h}d.push(a),o.push(a),i[a.id]=a}}return d};return{rows:l(e),flatRows:o,rowsById:i}}function Ji(){return(e,n)=>R(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter,e.getFilteredRowModel()],(t,r,o)=>{if(!t.rows.length||!(r!=null&&r.length)&&!o)return t;const i=[...r.map(l=>l.id).filter(l=>l!==n),o?"__global__":void 0].filter(Boolean),s=l=>{for(let u=0;u<i.length;u++)if(l.columnFilters[i[u]]===!1)return!1;return!0};return En(t.rows,s,e)},y(e.options,"debugTable"))}function Qi(){return(e,n)=>R(()=>{var t;return[(t=e.getColumn(n))==null?void 0:t.getFacetedRowModel()]},t=>{if(!t)return new Map;let r=new Map;for(let i=0;i<t.flatRows.length;i++){const s=t.flatRows[i].getUniqueValues(n);for(let l=0;l<s.length;l++){const u=s[l];if(r.has(u)){var o;r.set(u,((o=r.get(u))!=null?o:0)+1)}else r.set(u,1)}}return r},y(e.options,"debugTable"))}function Zi(){return e=>R(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(n,t,r)=>{if(!n.rows.length||!(t!=null&&t.length)&&!r){for(let g=0;g<n.flatRows.length;g++)n.flatRows[g].columnFilters={},n.flatRows[g].columnFiltersMeta={};return n}const o=[],i=[];(t??[]).forEach(g=>{var a;const v=e.getColumn(g.id);if(!v)return;const h=v.getFilterFn();h&&o.push({id:g.id,filterFn:h,resolvedValue:(a=h.resolveFilterValue==null?void 0:h.resolveFilterValue(g.value))!=null?a:g.value})});const s=(t??[]).map(g=>g.id),l=e.getGlobalFilterFn(),u=e.getAllLeafColumns().filter(g=>g.getCanGlobalFilter());r&&l&&u.length&&(s.push("__global__"),u.forEach(g=>{var a;i.push({id:g.id,filterFn:l,resolvedValue:(a=l.resolveFilterValue==null?void 0:l.resolveFilterValue(r))!=null?a:r})}));let c,d;for(let g=0;g<n.flatRows.length;g++){const a=n.flatRows[g];if(a.columnFilters={},o.length)for(let v=0;v<o.length;v++){c=o[v];const h=c.id;a.columnFilters[h]=c.filterFn(a,h,c.resolvedValue,m=>{a.columnFiltersMeta[h]=m})}if(i.length){for(let v=0;v<i.length;v++){d=i[v];const h=d.id;if(d.filterFn(a,h,d.resolvedValue,m=>{a.columnFiltersMeta[h]=m})){a.columnFilters.__global__=!0;break}}a.columnFilters.__global__!==!0&&(a.columnFilters.__global__=!1)}}const f=g=>{for(let a=0;a<s.length;a++)if(g.columnFilters[s[a]]===!1)return!1;return!0};return En(n.rows,f,e)},y(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function es(e){return n=>R(()=>[n.getState().pagination,n.getPrePaginationRowModel(),n.options.paginateExpandedRows?void 0:n.getState().expanded],(t,r)=>{if(!r.rows.length)return r;const{pageSize:o,pageIndex:i}=t;let{rows:s,flatRows:l,rowsById:u}=r;const c=o*i,d=c+o;s=s.slice(c,d);let f;n.options.paginateExpandedRows?f={rows:s,flatRows:l,rowsById:u}:f=eo({rows:s,flatRows:l,rowsById:u}),f.flatRows=[];const g=a=>{f.flatRows.push(a),a.subRows.length&&a.subRows.forEach(g)};return f.rows.forEach(g),f},y(n.options,"debugTable"))}function ts(){return e=>R(()=>[e.getState().sorting,e.getPreSortedRowModel()],(n,t)=>{if(!t.rows.length||!(n!=null&&n.length))return t;const r=e.getState().sorting,o=[],i=r.filter(u=>{var c;return(c=e.getColumn(u.id))==null?void 0:c.getCanSort()}),s={};i.forEach(u=>{const c=e.getColumn(u.id);c&&(s[u.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const l=u=>{const c=u.map(d=>({...d}));return c.sort((d,f)=>{for(let a=0;a<i.length;a+=1){var g;const v=i[a],h=s[v.id],m=h.sortUndefined,w=(g=v==null?void 0:v.desc)!=null?g:!1;let C=0;if(m){const S=d.getValue(v.id),_=f.getValue(v.id),x=S===void 0,D=_===void 0;if(x||D){if(m==="first")return x?-1:1;if(m==="last")return x?1:-1;C=x&&D?0:x?m:-m}}if(C===0&&(C=h.sortingFn(d,f,v.id)),C!==0)return w&&(C*=-1),h.invertSorting&&(C*=-1),C}return d.index-f.index}),c.forEach(d=>{var f;o.push(d),(f=d.subRows)!=null&&f.length&&(d.subRows=l(d.subRows))}),c};return{rows:l(t.rows),flatRows:o,rowsById:t.rowsById}},y(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ns(e,n){return e?ro(e)?p.createElement(e,n):e:null}function ro(e){return oo(e)||typeof e=="function"||io(e)}function oo(e){return typeof e=="function"&&(()=>{const n=Object.getPrototypeOf(e);return n.prototype&&n.prototype.isReactComponent})()}function io(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function rs(e){const n={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=p.useState(()=>({current:Zr(n)})),[r,o]=p.useState(()=>t.current.initialState);return t.current.setOptions(i=>({...i,...e,state:{...r,...e.state},onStateChange:s=>{o(s),e.onStateChange==null||e.onStateChange(s)}})),t.current}function so(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];return p.useMemo(()=>r=>{n.forEach(o=>o(r))},n)}const pt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function Fe(e){const n=Object.prototype.toString.call(e);return n==="[object Window]"||n==="[object global]"}function jt(e){return"nodeType"in e}function G(e){var n,t;return e?Fe(e)?e:jt(e)&&(n=(t=e.ownerDocument)==null?void 0:t.defaultView)!=null?n:window:window}function Ut(e){const{Document:n}=G(e);return e instanceof n}function Xe(e){return Fe(e)?!1:e instanceof G(e).HTMLElement}function $n(e){return e instanceof G(e).SVGElement}function Ie(e){return e?Fe(e)?e.document:jt(e)?Ut(e)?e:Xe(e)||$n(e)?e.ownerDocument:document:document:document}const re=pt?p.useLayoutEffect:p.useEffect;function Xt(e){const n=p.useRef(e);return re(()=>{n.current=e}),p.useCallback(function(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return n.current==null?void 0:n.current(...r)},[])}function lo(){const e=p.useRef(null),n=p.useCallback((r,o)=>{e.current=setInterval(r,o)},[]),t=p.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[n,t]}function je(e,n){n===void 0&&(n=[e]);const t=p.useRef(e);return re(()=>{t.current!==e&&(t.current=e)},n),t}function Ye(e,n){const t=p.useRef();return p.useMemo(()=>{const r=e(t.current);return t.current=r,r},[...n])}function ut(e){const n=Xt(e),t=p.useRef(null),r=p.useCallback(o=>{o!==t.current&&(n==null||n(o,t.current)),t.current=o},[]);return[t,r]}function Ot(e){const n=p.useRef();return p.useEffect(()=>{n.current=e},[e]),n.current}let Mt={};function Ke(e,n){return p.useMemo(()=>{if(n)return n;const t=Mt[e]==null?0:Mt[e]+1;return Mt[e]=t,e+"-"+t},[e,n])}function Pn(e){return function(n){for(var t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];return r.reduce((i,s)=>{const l=Object.entries(s);for(const[u,c]of l){const d=i[u];d!=null&&(i[u]=d+e*c)}return i},{...n})}}const Me=Pn(1),at=Pn(-1);function uo(e){return"clientX"in e&&"clientY"in e}function Yt(e){if(!e)return!1;const{KeyboardEvent:n}=G(e.target);return n&&e instanceof n}function ao(e){if(!e)return!1;const{TouchEvent:n}=G(e.target);return n&&e instanceof n}function zt(e){if(ao(e)){if(e.touches&&e.touches.length){const{clientX:n,clientY:t}=e.touches[0];return{x:n,y:t}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:n,clientY:t}=e.changedTouches[0];return{x:n,y:t}}}return uo(e)?{x:e.clientX,y:e.clientY}:null}const ct=Object.freeze({Translate:{toString(e){if(!e)return;const{x:n,y:t}=e;return"translate3d("+(n?Math.round(n):0)+"px, "+(t?Math.round(t):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:n,scaleY:t}=e;return"scaleX("+n+") scaleY("+t+")"}},Transform:{toString(e){if(e)return[ct.Translate.toString(e),ct.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:n,duration:t,easing:r}=e;return n+" "+t+"ms "+r}}}),an="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function co(e){return e.matches(an)?e:e.querySelector(an)}const go={display:"none"};function fo(e){let{id:n,value:t}=e;return H.createElement("div",{id:n,style:go},t)}function po(e){let{id:n,announcement:t,ariaLiveType:r="assertive"}=e;const o={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return H.createElement("div",{id:n,style:o,role:"status","aria-live":r,"aria-atomic":!0},t)}function ho(){const[e,n]=p.useState("");return{announce:p.useCallback(r=>{r!=null&&n(r)},[]),announcement:e}}const Vn=p.createContext(null);function vo(e){const n=p.useContext(Vn);p.useEffect(()=>{if(!n)throw new Error("useDndMonitor must be used within a children of <DndContext>");return n(e)},[e,n])}function mo(){const[e]=p.useState(()=>new Set),n=p.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[p.useCallback(r=>{let{type:o,event:i}=r;e.forEach(s=>{var l;return(l=s[o])==null?void 0:l.call(s,i)})},[e]),n]}const wo={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},So={onDragStart(e){let{active:n}=e;return"Picked up draggable item "+n.id+"."},onDragOver(e){let{active:n,over:t}=e;return t?"Draggable item "+n.id+" was moved over droppable area "+t.id+".":"Draggable item "+n.id+" is no longer over a droppable area."},onDragEnd(e){let{active:n,over:t}=e;return t?"Draggable item "+n.id+" was dropped over droppable area "+t.id:"Draggable item "+n.id+" was dropped."},onDragCancel(e){let{active:n}=e;return"Dragging was cancelled. Draggable item "+n.id+" was dropped."}};function Co(e){let{announcements:n=So,container:t,hiddenTextDescribedById:r,screenReaderInstructions:o=wo}=e;const{announce:i,announcement:s}=ho(),l=Ke("DndLiveRegion"),[u,c]=p.useState(!1);if(p.useEffect(()=>{c(!0)},[]),vo(p.useMemo(()=>({onDragStart(f){let{active:g}=f;i(n.onDragStart({active:g}))},onDragMove(f){let{active:g,over:a}=f;n.onDragMove&&i(n.onDragMove({active:g,over:a}))},onDragOver(f){let{active:g,over:a}=f;i(n.onDragOver({active:g,over:a}))},onDragEnd(f){let{active:g,over:a}=f;i(n.onDragEnd({active:g,over:a}))},onDragCancel(f){let{active:g,over:a}=f;i(n.onDragCancel({active:g,over:a}))}}),[i,n])),!u)return null;const d=H.createElement(H.Fragment,null,H.createElement(fo,{id:r,value:o.draggable}),H.createElement(po,{id:l,announcement:s}));return t?Ge.createPortal(d,t):d}var V;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(V||(V={}));function dt(){}function os(e,n){return p.useMemo(()=>({sensor:e,options:n??{}}),[e,n])}function is(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];return p.useMemo(()=>[...n].filter(r=>r!=null),[...n])}const Q=Object.freeze({x:0,y:0});function Ro(e,n){return Math.sqrt(Math.pow(e.x-n.x,2)+Math.pow(e.y-n.y,2))}function yo(e,n){let{data:{value:t}}=e,{data:{value:r}}=n;return t-r}function xo(e,n){let{data:{value:t}}=e,{data:{value:r}}=n;return r-t}function _o(e,n){if(!e||e.length===0)return null;const[t]=e;return t[n]}function cn(e,n,t){return n===void 0&&(n=e.left),t===void 0&&(t=e.top),{x:n+e.width*.5,y:t+e.height*.5}}const ss=e=>{let{collisionRect:n,droppableRects:t,droppableContainers:r}=e;const o=cn(n,n.left,n.top),i=[];for(const s of r){const{id:l}=s,u=t.get(l);if(u){const c=Ro(cn(u),o);i.push({id:l,data:{droppableContainer:s,value:c}})}}return i.sort(yo)};function bo(e,n){const t=Math.max(n.top,e.top),r=Math.max(n.left,e.left),o=Math.min(n.left+n.width,e.left+e.width),i=Math.min(n.top+n.height,e.top+e.height),s=o-r,l=i-t;if(r<o&&t<i){const u=n.width*n.height,c=e.width*e.height,d=s*l,f=d/(u+c-d);return Number(f.toFixed(4))}return 0}const Do=e=>{let{collisionRect:n,droppableRects:t,droppableContainers:r}=e;const o=[];for(const i of r){const{id:s}=i,l=t.get(s);if(l){const u=bo(l,n);u>0&&o.push({id:s,data:{droppableContainer:i,value:u}})}}return o.sort(xo)};function Mo(e,n,t){return{...e,scaleX:n&&t?n.width/t.width:1,scaleY:n&&t?n.height/t.height:1}}function An(e,n){return e&&n?{x:e.left-n.left,y:e.top-n.top}:Q}function Fo(e){return function(t){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return o.reduce((s,l)=>({...s,top:s.top+e*l.y,bottom:s.bottom+e*l.y,left:s.left+e*l.x,right:s.right+e*l.x}),{...t})}}const Io=Fo(1);function Eo(e){if(e.startsWith("matrix3d(")){const n=e.slice(9,-1).split(/, /);return{x:+n[12],y:+n[13],scaleX:+n[0],scaleY:+n[5]}}else if(e.startsWith("matrix(")){const n=e.slice(7,-1).split(/, /);return{x:+n[4],y:+n[5],scaleX:+n[0],scaleY:+n[3]}}return null}function $o(e,n,t){const r=Eo(n);if(!r)return e;const{scaleX:o,scaleY:i,x:s,y:l}=r,u=e.left-s-(1-o)*parseFloat(t),c=e.top-l-(1-i)*parseFloat(t.slice(t.indexOf(" ")+1)),d=o?e.width/o:e.width,f=i?e.height/i:e.height;return{width:d,height:f,top:c,right:u+d,bottom:c+f,left:u}}const Po={ignoreTransform:!1};function Ee(e,n){n===void 0&&(n=Po);let t=e.getBoundingClientRect();if(n.ignoreTransform){const{transform:c,transformOrigin:d}=G(e).getComputedStyle(e);c&&(t=$o(t,c,d))}const{top:r,left:o,width:i,height:s,bottom:l,right:u}=t;return{top:r,left:o,width:i,height:s,bottom:l,right:u}}function dn(e){return Ee(e,{ignoreTransform:!0})}function Vo(e){const n=e.innerWidth,t=e.innerHeight;return{top:0,left:0,right:n,bottom:t,width:n,height:t}}function Ao(e,n){return n===void 0&&(n=G(e).getComputedStyle(e)),n.position==="fixed"}function Lo(e,n){n===void 0&&(n=G(e).getComputedStyle(e));const t=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(o=>{const i=n[o];return typeof i=="string"?t.test(i):!1})}function Kt(e,n){const t=[];function r(o){if(n!=null&&t.length>=n||!o)return t;if(Ut(o)&&o.scrollingElement!=null&&!t.includes(o.scrollingElement))return t.push(o.scrollingElement),t;if(!Xe(o)||$n(o)||t.includes(o))return t;const i=G(e).getComputedStyle(o);return o!==e&&Lo(o,i)&&t.push(o),Ao(o,i)?t:r(o.parentNode)}return e?r(e):t}function Ln(e){const[n]=Kt(e,1);return n??null}function Ft(e){return!pt||!e?null:Fe(e)?e:jt(e)?Ut(e)||e===Ie(e).scrollingElement?window:Xe(e)?e:null:null}function On(e){return Fe(e)?e.scrollX:e.scrollLeft}function zn(e){return Fe(e)?e.scrollY:e.scrollTop}function Tt(e){return{x:On(e),y:zn(e)}}var A;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(A||(A={}));function Tn(e){return!pt||!e?!1:e===document.scrollingElement}function Hn(e){const n={x:0,y:0},t=Tn(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-t.width,y:e.scrollHeight-t.height},o=e.scrollTop<=n.y,i=e.scrollLeft<=n.x,s=e.scrollTop>=r.y,l=e.scrollLeft>=r.x;return{isTop:o,isLeft:i,isBottom:s,isRight:l,maxScroll:r,minScroll:n}}const Oo={x:.2,y:.2};function zo(e,n,t,r,o){let{top:i,left:s,right:l,bottom:u}=t;r===void 0&&(r=10),o===void 0&&(o=Oo);const{isTop:c,isBottom:d,isLeft:f,isRight:g}=Hn(e),a={x:0,y:0},v={x:0,y:0},h={height:n.height*o.y,width:n.width*o.x};return!c&&i<=n.top+h.height?(a.y=A.Backward,v.y=r*Math.abs((n.top+h.height-i)/h.height)):!d&&u>=n.bottom-h.height&&(a.y=A.Forward,v.y=r*Math.abs((n.bottom-h.height-u)/h.height)),!g&&l>=n.right-h.width?(a.x=A.Forward,v.x=r*Math.abs((n.right-h.width-l)/h.width)):!f&&s<=n.left+h.width&&(a.x=A.Backward,v.x=r*Math.abs((n.left+h.width-s)/h.width)),{direction:a,speed:v}}function To(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:s}=window;return{top:0,left:0,right:i,bottom:s,width:i,height:s}}const{top:n,left:t,right:r,bottom:o}=e.getBoundingClientRect();return{top:n,left:t,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function Gn(e){return e.reduce((n,t)=>Me(n,Tt(t)),Q)}function Ho(e){return e.reduce((n,t)=>n+On(t),0)}function Go(e){return e.reduce((n,t)=>n+zn(t),0)}function No(e,n){if(n===void 0&&(n=Ee),!e)return;const{top:t,left:r,bottom:o,right:i}=n(e);Ln(e)&&(o<=0||i<=0||t>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const ko=[["x",["left","right"],Ho],["y",["top","bottom"],Go]];class Wt{constructor(n,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=Kt(t),o=Gn(r);this.rect={...n},this.width=n.width,this.height=n.height;for(const[i,s,l]of ko)for(const u of s)Object.defineProperty(this,u,{get:()=>{const c=l(r),d=o[i]-c;return this.rect[u]+d},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ke{constructor(n){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(t=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...t)})},this.target=n}add(n,t,r){var o;(o=this.target)==null||o.addEventListener(n,t,r),this.listeners.push([n,t,r])}}function Bo(e){const{EventTarget:n}=G(e);return e instanceof n?e:Ie(e)}function It(e,n){const t=Math.abs(e.x),r=Math.abs(e.y);return typeof n=="number"?Math.sqrt(t**2+r**2)>n:"x"in n&&"y"in n?t>n.x&&r>n.y:"x"in n?t>n.x:"y"in n?r>n.y:!1}var Y;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(Y||(Y={}));function gn(e){e.preventDefault()}function qo(e){e.stopPropagation()}var F;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(F||(F={}));const Nn={start:[F.Space,F.Enter],cancel:[F.Esc],end:[F.Space,F.Enter,F.Tab]},jo=(e,n)=>{let{currentCoordinates:t}=n;switch(e.code){case F.Right:return{...t,x:t.x+25};case F.Left:return{...t,x:t.x-25};case F.Down:return{...t,y:t.y+25};case F.Up:return{...t,y:t.y-25}}};class kn{constructor(n){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=n;const{event:{target:t}}=n;this.props=n,this.listeners=new ke(Ie(t)),this.windowListeners=new ke(G(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Y.Resize,this.handleCancel),this.windowListeners.add(Y.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(Y.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:n,onStart:t}=this.props,r=n.node.current;r&&No(r),t(Q)}handleKeyDown(n){if(Yt(n)){const{active:t,context:r,options:o}=this.props,{keyboardCodes:i=Nn,coordinateGetter:s=jo,scrollBehavior:l="smooth"}=o,{code:u}=n;if(i.end.includes(u)){this.handleEnd(n);return}if(i.cancel.includes(u)){this.handleCancel(n);return}const{collisionRect:c}=r.current,d=c?{x:c.left,y:c.top}:Q;this.referenceCoordinates||(this.referenceCoordinates=d);const f=s(n,{active:t,context:r.current,currentCoordinates:d});if(f){const g=at(f,d),a={x:0,y:0},{scrollableAncestors:v}=r.current;for(const h of v){const m=n.code,{isTop:w,isRight:C,isLeft:S,isBottom:_,maxScroll:x,minScroll:D}=Hn(h),M=To(h),b={x:Math.min(m===F.Right?M.right-M.width/2:M.right,Math.max(m===F.Right?M.left:M.left+M.width/2,f.x)),y:Math.min(m===F.Down?M.bottom-M.height/2:M.bottom,Math.max(m===F.Down?M.top:M.top+M.height/2,f.y))},$=m===F.Right&&!C||m===F.Left&&!S,P=m===F.Down&&!_||m===F.Up&&!w;if($&&b.x!==f.x){const E=h.scrollLeft+g.x,K=m===F.Right&&E<=x.x||m===F.Left&&E>=D.x;if(K&&!g.y){h.scrollTo({left:E,behavior:l});return}K?a.x=h.scrollLeft-E:a.x=m===F.Right?h.scrollLeft-x.x:h.scrollLeft-D.x,a.x&&h.scrollBy({left:-a.x,behavior:l});break}else if(P&&b.y!==f.y){const E=h.scrollTop+g.y,K=m===F.Down&&E<=x.y||m===F.Up&&E>=D.y;if(K&&!g.x){h.scrollTo({top:E,behavior:l});return}K?a.y=h.scrollTop-E:a.y=m===F.Down?h.scrollTop-x.y:h.scrollTop-D.y,a.y&&h.scrollBy({top:-a.y,behavior:l});break}}this.handleMove(n,Me(at(f,this.referenceCoordinates),a))}}}handleMove(n,t){const{onMove:r}=this.props;n.preventDefault(),r(t)}handleEnd(n){const{onEnd:t}=this.props;n.preventDefault(),this.detach(),t()}handleCancel(n){const{onCancel:t}=this.props;n.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}kn.activators=[{eventName:"onKeyDown",handler:(e,n,t)=>{let{keyboardCodes:r=Nn,onActivation:o}=n,{active:i}=t;const{code:s}=e.nativeEvent;if(r.start.includes(s)){const l=i.activatorNode.current;return l&&e.target!==l?!1:(e.preventDefault(),o==null||o({event:e.nativeEvent}),!0)}return!1}}];function fn(e){return!!(e&&"distance"in e)}function pn(e){return!!(e&&"delay"in e)}class Jt{constructor(n,t,r){var o;r===void 0&&(r=Bo(n.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=n,this.events=t;const{event:i}=n,{target:s}=i;this.props=n,this.events=t,this.document=Ie(s),this.documentListeners=new ke(this.document),this.listeners=new ke(r),this.windowListeners=new ke(G(s)),this.initialCoordinates=(o=zt(i))!=null?o:Q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:n,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(n.move.name,this.handleMove,{passive:!1}),this.listeners.add(n.end.name,this.handleEnd),n.cancel&&this.listeners.add(n.cancel.name,this.handleCancel),this.windowListeners.add(Y.Resize,this.handleCancel),this.windowListeners.add(Y.DragStart,gn),this.windowListeners.add(Y.VisibilityChange,this.handleCancel),this.windowListeners.add(Y.ContextMenu,gn),this.documentListeners.add(Y.Keydown,this.handleKeydown),t){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(pn(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(fn(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(n,t){const{active:r,onPending:o}=this.props;o(r,n,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:n}=this,{onStart:t}=this.props;n&&(this.activated=!0,this.documentListeners.add(Y.Click,qo,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Y.SelectionChange,this.removeTextSelection),t(n))}handleMove(n){var t;const{activated:r,initialCoordinates:o,props:i}=this,{onMove:s,options:{activationConstraint:l}}=i;if(!o)return;const u=(t=zt(n))!=null?t:Q,c=at(o,u);if(!r&&l){if(fn(l)){if(l.tolerance!=null&&It(c,l.tolerance))return this.handleCancel();if(It(c,l.distance))return this.handleStart()}if(pn(l)&&It(c,l.tolerance))return this.handleCancel();this.handlePending(l,c);return}n.cancelable&&n.preventDefault(),s(u)}handleEnd(){const{onAbort:n,onEnd:t}=this.props;this.detach(),this.activated||n(this.props.active),t()}handleCancel(){const{onAbort:n,onCancel:t}=this.props;this.detach(),this.activated||n(this.props.active),t()}handleKeydown(n){n.code===F.Esc&&this.handleCancel()}removeTextSelection(){var n;(n=this.document.getSelection())==null||n.removeAllRanges()}}const Uo={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Bn extends Jt{constructor(n){const{event:t}=n,r=Ie(t.target);super(n,Uo,r)}}Bn.activators=[{eventName:"onPointerDown",handler:(e,n)=>{let{nativeEvent:t}=e,{onActivation:r}=n;return!t.isPrimary||t.button!==0?!1:(r==null||r({event:t}),!0)}}];const Xo={move:{name:"mousemove"},end:{name:"mouseup"}};var Ht;(function(e){e[e.RightClick=2]="RightClick"})(Ht||(Ht={}));class Yo extends Jt{constructor(n){super(n,Xo,Ie(n.event.target))}}Yo.activators=[{eventName:"onMouseDown",handler:(e,n)=>{let{nativeEvent:t}=e,{onActivation:r}=n;return t.button===Ht.RightClick?!1:(r==null||r({event:t}),!0)}}];const Et={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Ko extends Jt{constructor(n){super(n,Et)}static setup(){return window.addEventListener(Et.move.name,n,{capture:!1,passive:!1}),function(){window.removeEventListener(Et.move.name,n)};function n(){}}}Ko.activators=[{eventName:"onTouchStart",handler:(e,n)=>{let{nativeEvent:t}=e,{onActivation:r}=n;const{touches:o}=t;return o.length>1?!1:(r==null||r({event:t}),!0)}}];var Be;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(Be||(Be={}));var gt;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(gt||(gt={}));function Wo(e){let{acceleration:n,activator:t=Be.Pointer,canScroll:r,draggingRect:o,enabled:i,interval:s=5,order:l=gt.TreeOrder,pointerCoordinates:u,scrollableAncestors:c,scrollableAncestorRects:d,delta:f,threshold:g}=e;const a=Qo({delta:f,disabled:!i}),[v,h]=lo(),m=p.useRef({x:0,y:0}),w=p.useRef({x:0,y:0}),C=p.useMemo(()=>{switch(t){case Be.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case Be.DraggableRect:return o}},[t,o,u]),S=p.useRef(null),_=p.useCallback(()=>{const D=S.current;if(!D)return;const M=m.current.x*w.current.x,b=m.current.y*w.current.y;D.scrollBy(M,b)},[]),x=p.useMemo(()=>l===gt.TreeOrder?[...c].reverse():c,[l,c]);p.useEffect(()=>{if(!i||!c.length||!C){h();return}for(const D of x){if((r==null?void 0:r(D))===!1)continue;const M=c.indexOf(D),b=d[M];if(!b)continue;const{direction:$,speed:P}=zo(D,b,C,n,g);for(const E of["x","y"])a[E][$[E]]||(P[E]=0,$[E]=0);if(P.x>0||P.y>0){h(),S.current=D,v(_,s),m.current=P,w.current=$;return}}m.current={x:0,y:0},w.current={x:0,y:0},h()},[n,_,r,h,i,s,JSON.stringify(C),JSON.stringify(a),v,c,x,d,JSON.stringify(g)])}const Jo={x:{[A.Backward]:!1,[A.Forward]:!1},y:{[A.Backward]:!1,[A.Forward]:!1}};function Qo(e){let{delta:n,disabled:t}=e;const r=Ot(n);return Ye(o=>{if(t||!r||!o)return Jo;const i={x:Math.sign(n.x-r.x),y:Math.sign(n.y-r.y)};return{x:{[A.Backward]:o.x[A.Backward]||i.x===-1,[A.Forward]:o.x[A.Forward]||i.x===1},y:{[A.Backward]:o.y[A.Backward]||i.y===-1,[A.Forward]:o.y[A.Forward]||i.y===1}}},[t,n,r])}function Zo(e,n){const t=n!=null?e.get(n):void 0,r=t?t.node.current:null;return Ye(o=>{var i;return n==null?null:(i=r??o)!=null?i:null},[r,n])}function ei(e,n){return p.useMemo(()=>e.reduce((t,r)=>{const{sensor:o}=r,i=o.activators.map(s=>({eventName:s.eventName,handler:n(s.handler,r)}));return[...t,...i]},[]),[e,n])}var Ue;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(Ue||(Ue={}));var Gt;(function(e){e.Optimized="optimized"})(Gt||(Gt={}));const hn=new Map;function ti(e,n){let{dragging:t,dependencies:r,config:o}=n;const[i,s]=p.useState(null),{frequency:l,measure:u,strategy:c}=o,d=p.useRef(e),f=m(),g=je(f),a=p.useCallback(function(w){w===void 0&&(w=[]),!g.current&&s(C=>C===null?w:C.concat(w.filter(S=>!C.includes(S))))},[g]),v=p.useRef(null),h=Ye(w=>{if(f&&!t)return hn;if(!w||w===hn||d.current!==e||i!=null){const C=new Map;for(let S of e){if(!S)continue;if(i&&i.length>0&&!i.includes(S.id)&&S.rect.current){C.set(S.id,S.rect.current);continue}const _=S.node.current,x=_?new Wt(u(_),_):null;S.rect.current=x,x&&C.set(S.id,x)}return C}return w},[e,i,t,f,u]);return p.useEffect(()=>{d.current=e},[e]),p.useEffect(()=>{f||a()},[t,f]),p.useEffect(()=>{i&&i.length>0&&s(null)},[JSON.stringify(i)]),p.useEffect(()=>{f||typeof l!="number"||v.current!==null||(v.current=setTimeout(()=>{a(),v.current=null},l))},[l,f,a,...r]),{droppableRects:h,measureDroppableContainers:a,measuringScheduled:i!=null};function m(){switch(c){case Ue.Always:return!1;case Ue.BeforeDragging:return t;default:return!t}}}function qn(e,n){return Ye(t=>e?t||(typeof n=="function"?n(e):e):null,[n,e])}function ni(e,n){return qn(e,n)}function ri(e){let{callback:n,disabled:t}=e;const r=Xt(n),o=p.useMemo(()=>{if(t||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,t]);return p.useEffect(()=>()=>o==null?void 0:o.disconnect(),[o]),o}function ht(e){let{callback:n,disabled:t}=e;const r=Xt(n),o=p.useMemo(()=>{if(t||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[t]);return p.useEffect(()=>()=>o==null?void 0:o.disconnect(),[o]),o}function oi(e){return new Wt(Ee(e),e)}function vn(e,n,t){n===void 0&&(n=oi);const[r,o]=p.useState(null);function i(){o(u=>{if(!e)return null;if(e.isConnected===!1){var c;return(c=u??t)!=null?c:null}const d=n(e);return JSON.stringify(u)===JSON.stringify(d)?u:d})}const s=ri({callback(u){if(e)for(const c of u){const{type:d,target:f}=c;if(d==="childList"&&f instanceof HTMLElement&&f.contains(e)){i();break}}}}),l=ht({callback:i});return re(()=>{i(),e?(l==null||l.observe(e),s==null||s.observe(document.body,{childList:!0,subtree:!0})):(l==null||l.disconnect(),s==null||s.disconnect())},[e]),r}function ii(e){const n=qn(e);return An(e,n)}const mn=[];function si(e){const n=p.useRef(e),t=Ye(r=>e?r&&r!==mn&&e&&n.current&&e.parentNode===n.current.parentNode?r:Kt(e):mn,[e]);return p.useEffect(()=>{n.current=e},[e]),t}function li(e){const[n,t]=p.useState(null),r=p.useRef(e),o=p.useCallback(i=>{const s=Ft(i.target);s&&t(l=>l?(l.set(s,Tt(s)),new Map(l)):null)},[]);return p.useEffect(()=>{const i=r.current;if(e!==i){s(i);const l=e.map(u=>{const c=Ft(u);return c?(c.addEventListener("scroll",o,{passive:!0}),[c,Tt(c)]):null}).filter(u=>u!=null);t(l.length?new Map(l):null),r.current=e}return()=>{s(e),s(i)};function s(l){l.forEach(u=>{const c=Ft(u);c==null||c.removeEventListener("scroll",o)})}},[o,e]),p.useMemo(()=>e.length?n?Array.from(n.values()).reduce((i,s)=>Me(i,s),Q):Gn(e):Q,[e,n])}function wn(e,n){n===void 0&&(n=[]);const t=p.useRef(null);return p.useEffect(()=>{t.current=null},n),p.useEffect(()=>{const r=e!==Q;r&&!t.current&&(t.current=e),!r&&t.current&&(t.current=null)},[e]),t.current?at(e,t.current):Q}function ui(e){p.useEffect(()=>{if(!pt)return;const n=e.map(t=>{let{sensor:r}=t;return r.setup==null?void 0:r.setup()});return()=>{for(const t of n)t==null||t()}},e.map(n=>{let{sensor:t}=n;return t}))}function ai(e,n){return p.useMemo(()=>e.reduce((t,r)=>{let{eventName:o,handler:i}=r;return t[o]=s=>{i(s,n)},t},{}),[e,n])}function jn(e){return p.useMemo(()=>e?Vo(e):null,[e])}const Sn=[];function ci(e,n){n===void 0&&(n=Ee);const[t]=e,r=jn(t?G(t):null),[o,i]=p.useState(Sn);function s(){i(()=>e.length?e.map(u=>Tn(u)?r:new Wt(n(u),u)):Sn)}const l=ht({callback:s});return re(()=>{l==null||l.disconnect(),s(),e.forEach(u=>l==null?void 0:l.observe(u))},[e]),o}function di(e){if(!e)return null;if(e.children.length>1)return e;const n=e.children[0];return Xe(n)?n:e}function gi(e){let{measure:n}=e;const[t,r]=p.useState(null),o=p.useCallback(c=>{for(const{target:d}of c)if(Xe(d)){r(f=>{const g=n(d);return f?{...f,width:g.width,height:g.height}:g});break}},[n]),i=ht({callback:o}),s=p.useCallback(c=>{const d=di(c);i==null||i.disconnect(),d&&(i==null||i.observe(d)),r(d?n(d):null)},[n,i]),[l,u]=ut(s);return p.useMemo(()=>({nodeRef:l,rect:t,setRef:u}),[t,l,u])}const fi=[{sensor:Bn,options:{}},{sensor:kn,options:{}}],pi={current:{}},lt={draggable:{measure:dn},droppable:{measure:dn,strategy:Ue.WhileDragging,frequency:Gt.Optimized},dragOverlay:{measure:Ee}};class qe extends Map{get(n){var t;return n!=null&&(t=super.get(n))!=null?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(n=>{let{disabled:t}=n;return!t})}getNodeFor(n){var t,r;return(t=(r=this.get(n))==null?void 0:r.node.current)!=null?t:void 0}}const hi={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new qe,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:dt},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:lt,measureDroppableContainers:dt,windowRect:null,measuringScheduled:!1},vi={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:dt,draggableNodes:new Map,over:null,measureDroppableContainers:dt},vt=p.createContext(vi),Un=p.createContext(hi);function mi(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new qe}}}function wi(e,n){switch(n.type){case V.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:n.initialCoordinates,active:n.active}};case V.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:n.coordinates.x-e.draggable.initialCoordinates.x,y:n.coordinates.y-e.draggable.initialCoordinates.y}}};case V.DragEnd:case V.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case V.RegisterDroppable:{const{element:t}=n,{id:r}=t,o=new qe(e.droppable.containers);return o.set(r,t),{...e,droppable:{...e.droppable,containers:o}}}case V.SetDroppableDisabled:{const{id:t,key:r,disabled:o}=n,i=e.droppable.containers.get(t);if(!i||r!==i.key)return e;const s=new qe(e.droppable.containers);return s.set(t,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:s}}}case V.UnregisterDroppable:{const{id:t,key:r}=n,o=e.droppable.containers.get(t);if(!o||r!==o.key)return e;const i=new qe(e.droppable.containers);return i.delete(t),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Si(e){let{disabled:n}=e;const{active:t,activatorEvent:r,draggableNodes:o}=p.useContext(vt),i=Ot(r),s=Ot(t==null?void 0:t.id);return p.useEffect(()=>{if(!n&&!r&&i&&s!=null){if(!Yt(i)||document.activeElement===i.target)return;const l=o.get(s);if(!l)return;const{activatorNode:u,node:c}=l;if(!u.current&&!c.current)return;requestAnimationFrame(()=>{for(const d of[u.current,c.current]){if(!d)continue;const f=co(d);if(f){f.focus();break}}})}},[r,n,o,s,i]),null}function Ci(e,n){let{transform:t,...r}=n;return e!=null&&e.length?e.reduce((o,i)=>i({transform:o,...r}),t):t}function Ri(e){return p.useMemo(()=>({draggable:{...lt.draggable,...e==null?void 0:e.draggable},droppable:{...lt.droppable,...e==null?void 0:e.droppable},dragOverlay:{...lt.dragOverlay,...e==null?void 0:e.dragOverlay}}),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function yi(e){let{activeNode:n,measure:t,initialRect:r,config:o=!0}=e;const i=p.useRef(!1),{x:s,y:l}=typeof o=="boolean"?{x:o,y:o}:o;re(()=>{if(!s&&!l||!n){i.current=!1;return}if(i.current||!r)return;const c=n==null?void 0:n.node.current;if(!c||c.isConnected===!1)return;const d=t(c),f=An(d,r);if(s||(f.x=0),l||(f.y=0),i.current=!0,Math.abs(f.x)>0||Math.abs(f.y)>0){const g=Ln(c);g&&g.scrollBy({top:f.y,left:f.x})}},[n,s,l,r,t])}const Xn=p.createContext({...Q,scaleX:1,scaleY:1});var ve;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(ve||(ve={}));const ls=p.memo(function(n){var t,r,o,i;let{id:s,accessibility:l,autoScroll:u=!0,children:c,sensors:d=fi,collisionDetection:f=Do,measuring:g,modifiers:a,...v}=n;const h=p.useReducer(wi,void 0,mi),[m,w]=h,[C,S]=mo(),[_,x]=p.useState(ve.Uninitialized),D=_===ve.Initialized,{draggable:{active:M,nodes:b,translate:$},droppable:{containers:P}}=m,E=M!=null?b.get(M):null,K=p.useRef({initial:null,translated:null}),W=p.useMemo(()=>{var O;return M!=null?{id:M,data:(O=E==null?void 0:E.data)!=null?O:pi,rect:K}:null},[M,E]),Z=p.useRef(null),[$e,We]=p.useState(null),[N,Je]=p.useState(null),oe=je(v,Object.values(v)),Pe=Ke("DndDescribedBy",s),Qe=p.useMemo(()=>P.getEnabled(),[P]),T=Ri(g),{droppableRects:ie,measureDroppableContainers:Ce,measuringScheduled:Ve}=ti(Qe,{dragging:D,dependencies:[$.x,$.y],config:T.droppable}),U=Zo(b,M),Ze=p.useMemo(()=>N?zt(N):null,[N]),ce=sr(),se=ni(U,T.draggable.measure);yi({activeNode:M!=null?b.get(M):null,config:ce.layoutShiftCompensation,initialRect:se,measure:T.draggable.measure});const I=vn(U,T.draggable.measure,se),Ae=vn(U?U.parentElement:null),ee=p.useRef({activatorEvent:null,active:null,activeNode:U,collisionRect:null,collisions:null,droppableRects:ie,draggableNodes:b,draggingNode:null,draggingNodeRect:null,droppableContainers:P,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Re=P.getNodeFor((t=ee.current.over)==null?void 0:t.id),le=gi({measure:T.dragOverlay.measure}),ye=(r=le.nodeRef.current)!=null?r:U,xe=D?(o=le.rect)!=null?o:I:null,Qt=!!(le.nodeRef.current&&le.rect),Zt=ii(Qt?null:I),mt=jn(ye?G(ye):null),de=si(D?Re??U:null),et=ci(de),tt=Ci(a,{transform:{x:$.x-Zt.x,y:$.y-Zt.y,scaleX:1,scaleY:1},activatorEvent:N,active:W,activeNodeRect:I,containerNodeRect:Ae,draggingNodeRect:xe,over:ee.current.over,overlayNodeRect:le.rect,scrollableAncestors:de,scrollableAncestorRects:et,windowRect:mt}),en=Ze?Me(Ze,$):null,tn=li(de),Zn=wn(tn),er=wn(tn,[I]),_e=Me(tt,Zn),be=xe?Io(xe,tt):null,Le=W&&be?f({active:W,collisionRect:be,droppableRects:ie,droppableContainers:Qe,pointerCoordinates:en}):null,nn=_o(Le,"id"),[ge,rn]=p.useState(null),tr=Qt?tt:Me(tt,er),nr=Mo(tr,(i=ge==null?void 0:ge.rect)!=null?i:null,I),wt=p.useRef(null),on=p.useCallback((O,k)=>{let{sensor:B,options:fe}=k;if(Z.current==null)return;const X=b.get(Z.current);if(!X)return;const q=O.nativeEvent,te=new B({active:Z.current,activeNode:X,event:q,options:fe,context:ee,onAbort(L){if(!b.get(L))return;const{onDragAbort:ne}=oe.current,ue={id:L};ne==null||ne(ue),C({type:"onDragAbort",event:ue})},onPending(L,pe,ne,ue){if(!b.get(L))return;const{onDragPending:ze}=oe.current,he={id:L,constraint:pe,initialCoordinates:ne,offset:ue};ze==null||ze(he),C({type:"onDragPending",event:he})},onStart(L){const pe=Z.current;if(pe==null)return;const ne=b.get(pe);if(!ne)return;const{onDragStart:ue}=oe.current,Oe={activatorEvent:q,active:{id:pe,data:ne.data,rect:K}};Ge.unstable_batchedUpdates(()=>{ue==null||ue(Oe),x(ve.Initializing),w({type:V.DragStart,initialCoordinates:L,active:pe}),C({type:"onDragStart",event:Oe}),We(wt.current),Je(q)})},onMove(L){w({type:V.DragMove,coordinates:L})},onEnd:De(V.DragEnd),onCancel:De(V.DragCancel)});wt.current=te;function De(L){return async function(){const{active:ne,collisions:ue,over:Oe,scrollAdjustedTranslate:ze}=ee.current;let he=null;if(ne&&ze){const{cancelDrop:Te}=oe.current;he={activatorEvent:q,active:ne,collisions:ue,delta:ze,over:Oe},L===V.DragEnd&&typeof Te=="function"&&await Promise.resolve(Te(he))&&(L=V.DragCancel)}Z.current=null,Ge.unstable_batchedUpdates(()=>{w({type:L}),x(ve.Uninitialized),rn(null),We(null),Je(null),wt.current=null;const Te=L===V.DragEnd?"onDragEnd":"onDragCancel";if(he){const St=oe.current[Te];St==null||St(he),C({type:Te,event:he})}})}}},[b]),rr=p.useCallback((O,k)=>(B,fe)=>{const X=B.nativeEvent,q=b.get(fe);if(Z.current!==null||!q||X.dndKit||X.defaultPrevented)return;const te={active:q};O(B,k.options,te)===!0&&(X.dndKit={capturedBy:k.sensor},Z.current=fe,on(B,k))},[b,on]),sn=ei(d,rr);ui(d),re(()=>{I&&_===ve.Initializing&&x(ve.Initialized)},[I,_]),p.useEffect(()=>{const{onDragMove:O}=oe.current,{active:k,activatorEvent:B,collisions:fe,over:X}=ee.current;if(!k||!B)return;const q={active:k,activatorEvent:B,collisions:fe,delta:{x:_e.x,y:_e.y},over:X};Ge.unstable_batchedUpdates(()=>{O==null||O(q),C({type:"onDragMove",event:q})})},[_e.x,_e.y]),p.useEffect(()=>{const{active:O,activatorEvent:k,collisions:B,droppableContainers:fe,scrollAdjustedTranslate:X}=ee.current;if(!O||Z.current==null||!k||!X)return;const{onDragOver:q}=oe.current,te=fe.get(nn),De=te&&te.rect.current?{id:te.id,rect:te.rect.current,data:te.data,disabled:te.disabled}:null,L={active:O,activatorEvent:k,collisions:B,delta:{x:X.x,y:X.y},over:De};Ge.unstable_batchedUpdates(()=>{rn(De),q==null||q(L),C({type:"onDragOver",event:L})})},[nn]),re(()=>{ee.current={activatorEvent:N,active:W,activeNode:U,collisionRect:be,collisions:Le,droppableRects:ie,draggableNodes:b,draggingNode:ye,draggingNodeRect:xe,droppableContainers:P,over:ge,scrollableAncestors:de,scrollAdjustedTranslate:_e},K.current={initial:xe,translated:be}},[W,U,Le,be,b,ye,xe,ie,P,ge,de,_e]),Wo({...ce,delta:$,draggingRect:be,pointerCoordinates:en,scrollableAncestors:de,scrollableAncestorRects:et});const or=p.useMemo(()=>({active:W,activeNode:U,activeNodeRect:I,activatorEvent:N,collisions:Le,containerNodeRect:Ae,dragOverlay:le,draggableNodes:b,droppableContainers:P,droppableRects:ie,over:ge,measureDroppableContainers:Ce,scrollableAncestors:de,scrollableAncestorRects:et,measuringConfiguration:T,measuringScheduled:Ve,windowRect:mt}),[W,U,I,N,Le,Ae,le,b,P,ie,ge,Ce,de,et,T,Ve,mt]),ir=p.useMemo(()=>({activatorEvent:N,activators:sn,active:W,activeNodeRect:I,ariaDescribedById:{draggable:Pe},dispatch:w,draggableNodes:b,over:ge,measureDroppableContainers:Ce}),[N,sn,W,I,w,Pe,b,ge,Ce]);return H.createElement(Vn.Provider,{value:S},H.createElement(vt.Provider,{value:ir},H.createElement(Un.Provider,{value:or},H.createElement(Xn.Provider,{value:nr},c)),H.createElement(Si,{disabled:(l==null?void 0:l.restoreFocus)===!1})),H.createElement(Co,{...l,hiddenTextDescribedById:Pe}));function sr(){const O=($e==null?void 0:$e.autoScrollEnabled)===!1,k=typeof u=="object"?u.enabled===!1:u===!1,B=D&&!O&&!k;return typeof u=="object"?{...u,enabled:B}:{enabled:B}}}),xi=p.createContext(null),Cn="button",_i="Draggable";function bi(e){let{id:n,data:t,disabled:r=!1,attributes:o}=e;const i=Ke(_i),{activators:s,activatorEvent:l,active:u,activeNodeRect:c,ariaDescribedById:d,draggableNodes:f,over:g}=p.useContext(vt),{role:a=Cn,roleDescription:v="draggable",tabIndex:h=0}=o??{},m=(u==null?void 0:u.id)===n,w=p.useContext(m?Xn:xi),[C,S]=ut(),[_,x]=ut(),D=ai(s,n),M=je(t);re(()=>(f.set(n,{id:n,key:i,node:C,activatorNode:_,data:M}),()=>{const $=f.get(n);$&&$.key===i&&f.delete(n)}),[f,n]);const b=p.useMemo(()=>({role:a,tabIndex:h,"aria-disabled":r,"aria-pressed":m&&a===Cn?!0:void 0,"aria-roledescription":v,"aria-describedby":d.draggable}),[r,a,h,m,v,d.draggable]);return{active:u,activatorEvent:l,activeNodeRect:c,attributes:b,isDragging:m,listeners:r?void 0:D,node:C,over:g,setNodeRef:S,setActivatorNodeRef:x,transform:w}}function Di(){return p.useContext(Un)}const Mi="Droppable",Fi={timeout:25};function Ii(e){let{data:n,disabled:t=!1,id:r,resizeObserverConfig:o}=e;const i=Ke(Mi),{active:s,dispatch:l,over:u,measureDroppableContainers:c}=p.useContext(vt),d=p.useRef({disabled:t}),f=p.useRef(!1),g=p.useRef(null),a=p.useRef(null),{disabled:v,updateMeasurementsFor:h,timeout:m}={...Fi,...o},w=je(h??r),C=p.useCallback(()=>{if(!f.current){f.current=!0;return}a.current!=null&&clearTimeout(a.current),a.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),a.current=null},m)},[m]),S=ht({callback:C,disabled:v||!s}),_=p.useCallback((b,$)=>{S&&($&&(S.unobserve($),f.current=!1),b&&S.observe(b))},[S]),[x,D]=ut(_),M=je(n);return p.useEffect(()=>{!S||!x.current||(S.disconnect(),f.current=!1,S.observe(x.current))},[x,S]),p.useEffect(()=>(l({type:V.RegisterDroppable,element:{id:r,key:i,disabled:t,node:x,rect:g,data:M}}),()=>l({type:V.UnregisterDroppable,key:i,id:r})),[r]),p.useEffect(()=>{t!==d.current.disabled&&(l({type:V.SetDroppableDisabled,id:r,key:i,disabled:t}),d.current.disabled=t)},[r,i,t,l]),{active:s,rect:g,isOver:(u==null?void 0:u.id)===r,node:x,over:u,setNodeRef:D}}const us=e=>{let{transform:n}=e;return{...n,x:0}};function Yn(e,n,t){const r=e.slice();return r.splice(t<0?r.length+t:t,0,r.splice(n,1)[0]),r}function Ei(e,n){return e.reduce((t,r,o)=>{const i=n.get(r);return i&&(t[o]=i),t},Array(e.length))}function it(e){return e!==null&&e>=0}function $i(e,n){if(e===n)return!0;if(e.length!==n.length)return!1;for(let t=0;t<e.length;t++)if(e[t]!==n[t])return!1;return!0}function Pi(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const Kn=e=>{let{rects:n,activeIndex:t,overIndex:r,index:o}=e;const i=Yn(n,r,t),s=n[o],l=i[o];return!l||!s?null:{x:l.left-s.left,y:l.top-s.top,scaleX:l.width/s.width,scaleY:l.height/s.height}},st={scaleX:1,scaleY:1},as=e=>{var n;let{activeIndex:t,activeNodeRect:r,index:o,rects:i,overIndex:s}=e;const l=(n=i[t])!=null?n:r;if(!l)return null;if(o===t){const c=i[s];return c?{x:0,y:t<s?c.top+c.height-(l.top+l.height):c.top-l.top,...st}:null}const u=Vi(i,o,t);return o>t&&o<=s?{x:0,y:-l.height-u,...st}:o<t&&o>=s?{x:0,y:l.height+u,...st}:{x:0,y:0,...st}};function Vi(e,n,t){const r=e[n],o=e[n-1],i=e[n+1];return r?t<n?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}const Wn="Sortable",Jn=H.createContext({activeIndex:-1,containerId:Wn,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:Kn,disabled:{draggable:!1,droppable:!1}});function cs(e){let{children:n,id:t,items:r,strategy:o=Kn,disabled:i=!1}=e;const{active:s,dragOverlay:l,droppableRects:u,over:c,measureDroppableContainers:d}=Di(),f=Ke(Wn,t),g=l.rect!==null,a=p.useMemo(()=>r.map(D=>typeof D=="object"&&"id"in D?D.id:D),[r]),v=s!=null,h=s?a.indexOf(s.id):-1,m=c?a.indexOf(c.id):-1,w=p.useRef(a),C=!$i(a,w.current),S=m!==-1&&h===-1||C,_=Pi(i);re(()=>{C&&v&&d(a)},[C,a,v,d]),p.useEffect(()=>{w.current=a},[a]);const x=p.useMemo(()=>({activeIndex:h,containerId:f,disabled:_,disableTransforms:S,items:a,overIndex:m,useDragOverlay:g,sortedRects:Ei(a,u),strategy:o}),[h,f,_.draggable,_.droppable,S,a,m,u,g,o]);return H.createElement(Jn.Provider,{value:x},n)}const Ai=e=>{let{id:n,items:t,activeIndex:r,overIndex:o}=e;return Yn(t,r,o).indexOf(n)},Li=e=>{let{containerId:n,isSorting:t,wasDragging:r,index:o,items:i,newIndex:s,previousItems:l,previousContainerId:u,transition:c}=e;return!c||!r||l!==i&&o===s?!1:t?!0:s!==o&&n===u},Oi={duration:200,easing:"ease"},Qn="transform",zi=ct.Transition.toString({property:Qn,duration:0,easing:"linear"}),Ti={roleDescription:"sortable"};function Hi(e){let{disabled:n,index:t,node:r,rect:o}=e;const[i,s]=p.useState(null),l=p.useRef(t);return re(()=>{if(!n&&t!==l.current&&r.current){const u=o.current;if(u){const c=Ee(r.current,{ignoreTransform:!0}),d={x:u.left-c.left,y:u.top-c.top,scaleX:u.width/c.width,scaleY:u.height/c.height};(d.x||d.y)&&s(d)}}t!==l.current&&(l.current=t)},[n,t,r,o]),p.useEffect(()=>{i&&s(null)},[i]),i}function ds(e){let{animateLayoutChanges:n=Li,attributes:t,disabled:r,data:o,getNewIndex:i=Ai,id:s,strategy:l,resizeObserverConfig:u,transition:c=Oi}=e;const{items:d,containerId:f,activeIndex:g,disabled:a,disableTransforms:v,sortedRects:h,overIndex:m,useDragOverlay:w,strategy:C}=p.useContext(Jn),S=Gi(r,a),_=d.indexOf(s),x=p.useMemo(()=>({sortable:{containerId:f,index:_,items:d},...o}),[f,o,_,d]),D=p.useMemo(()=>d.slice(d.indexOf(s)),[d,s]),{rect:M,node:b,isOver:$,setNodeRef:P}=Ii({id:s,data:x,disabled:S.droppable,resizeObserverConfig:{updateMeasurementsFor:D,...u}}),{active:E,activatorEvent:K,activeNodeRect:W,attributes:Z,setNodeRef:$e,listeners:We,isDragging:N,over:Je,setActivatorNodeRef:oe,transform:Pe}=bi({id:s,data:x,attributes:{...Ti,...t},disabled:S.draggable}),Qe=so(P,$e),T=!!E,ie=T&&!v&&it(g)&&it(m),Ce=!w&&N,Ve=Ce&&ie?Pe:null,Ze=ie?Ve??(l??C)({rects:h,activeNodeRect:W,activeIndex:g,overIndex:m,index:_}):null,ce=it(g)&&it(m)?i({id:s,items:d,activeIndex:g,overIndex:m}):_,se=E==null?void 0:E.id,I=p.useRef({activeId:se,items:d,newIndex:ce,containerId:f}),Ae=d!==I.current.items,ee=n({active:E,containerId:f,isDragging:N,isSorting:T,id:s,index:_,items:d,newIndex:I.current.newIndex,previousItems:I.current.items,previousContainerId:I.current.containerId,transition:c,wasDragging:I.current.activeId!=null}),Re=Hi({disabled:!ee,index:_,node:b,rect:M});return p.useEffect(()=>{T&&I.current.newIndex!==ce&&(I.current.newIndex=ce),f!==I.current.containerId&&(I.current.containerId=f),d!==I.current.items&&(I.current.items=d)},[T,ce,f,d]),p.useEffect(()=>{if(se===I.current.activeId)return;if(se!=null&&I.current.activeId==null){I.current.activeId=se;return}const ye=setTimeout(()=>{I.current.activeId=se},50);return()=>clearTimeout(ye)},[se]),{active:E,activeIndex:g,attributes:Z,data:x,rect:M,index:_,newIndex:ce,items:d,isOver:$,isSorting:T,isDragging:N,listeners:We,node:b,overIndex:m,over:Je,setNodeRef:Qe,setActivatorNodeRef:oe,setDroppableNodeRef:P,setDraggableNodeRef:$e,transform:Re??Ze,transition:le()};function le(){if(Re||Ae&&I.current.newIndex===_)return zi;if(!(Ce&&!Yt(K)||!c)&&(T||ee))return ct.Transition.toString({...c,property:Qn})}}function Gi(e,n){var t,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(t=e==null?void 0:e.draggable)!=null?t:n.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:n.droppable}}F.Down,F.Right,F.Up,F.Left;export{ct as C,ls as D,Yi as I,kn as K,Yo as M,cs as S,Ko as T,os as a,rs as b,ki as c,Ki as d,ss as e,ns as f,ji as g,Bi as h,qi as i,Ui as j,Qi as k,Ji as l,ts as m,es as n,Zi as o,Wi as p,Yn as q,us as r,ds as s,Xi as t,is as u,as as v};
