import{c as K,u as ne,a as re,r as o,j as e,B as y,N as ie,S as ce,a0 as le}from"./index-DM6GIfB4.js";import{C as N,f as v,a as W,c as $}from"./card-D0yfVNGp.js";import{B as q}from"./badge-frfbuLAi.js";import{C as z}from"./checkbox-B00BLXKz.js";import{S as de,a as oe,b as me,c as xe,d as he}from"./select-BCFM5Vl0.js";import{D as pe,b as ue,c as je,d as fe,f as ge}from"./dialog-BrlEMtli.js";import{I as ye}from"./input-BhcMMszI.js";import{L as C}from"./label-D7aoP4f7.js";import{S as H,t as g}from"./index-B_SQ5DcE.js";import{I as Ne}from"./IconUser-7feVy3q9.js";import{I as ve}from"./IconCheck-DQhb2mdB.js";import{I as M}from"./IconLoader2-CgJzOld6.js";/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=[["path",{d:"M9 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-0"}],["path",{d:"M17.657 16.657l-4.243 4.243a2 2 0 0 1 -2.827 0l-4.244 -4.243a8 8 0 1 1 11.314 0z",key:"svg-1"}]],Ce=K("outline","map-pin","MapPin",_e);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]],we=K("outline","package","Package",Se);function be(c,r){const n=Array(r.length+1).fill(null).map(()=>Array(c.length+1).fill(null));for(let a=0;a<=c.length;a++)n[0][a]=a;for(let a=0;a<=r.length;a++)n[a][0]=a;for(let a=1;a<=r.length;a++)for(let l=1;l<=c.length;l++){const h=c[l-1]===r[a-1]?0:1;n[a][l]=Math.min(n[a][l-1]+1,n[a-1][l]+1,n[a-1][l-1]+h)}return n[r.length][c.length]}function ke(c,r){const n=Math.max(c.length,r.length);return n===0?1:1-be(c,r)/n}function J(c,r={}){let n=c;return r.ignoreCase!==!1&&(n=n.toLowerCase()),r.normalizeWhitespace!==!1&&(n=n.replace(/\s+/g," ").trim()),n=n.replace(/[.,;:!?]/g,""),n}function Ee(c,r={}){const{threshold:n=.8,ignoreCase:a=!0,normalizeWhitespace:l=!0}=r,h=[],f=new Set;for(const m of c){if(f.has(m.id))continue;const E=J(m.address,{ignoreCase:a,normalizeWhitespace:l}),p={representativeAddress:m.address,orders:[m.id],similarity:1};f.add(m.id);for(const u of c){if(f.has(u.id))continue;const S=J(u.address,{ignoreCase:a,normalizeWhitespace:l});ke(E,S)>=n&&(p.orders.push(u.id),f.add(u.id),u.address.length>p.representativeAddress.length&&(p.representativeAddress=u.address))}h.push(p)}return h}const Pe=function(){var P,T,V;const{selectedOffice:r}=ne(),n=re(),[a,l]=o.useState(!1),[h,f]=o.useState(!1),[m,E]=o.useState([]),[p,u]=o.useState([]),[S,R]=o.useState([]),[w,D]=o.useState(""),[j,O]=o.useState(new Set),[A,G]=o.useState(new Set),[b,L]=o.useState("grouped"),[d,k]=o.useState({isOpen:!1,selectedOrders:[],selectedEmployee:null,customCommissionRate:""}),B=async()=>{if(r){l(!0);try{const s=await ie({query:{office_id:r.id,assigned_to_isnull:!0,page_size:1e3}});s.data&&(E(s.data.orders),X(s.data.orders))}catch(s){console.error("Error fetching unassigned orders:",s),g.error("حدث خطأ أثناء تحميل الطلبات غير المخصصة")}finally{l(!1)}}},Q=async()=>{if(r)try{const s=await ce({query:{office_id:r.id,page_size:1e3}});if(s.data){const t=s.data.users.filter(i=>i.role==="employee");u(t)}}catch(s){console.error("Error fetching employees:",s),g.error("حدث خطأ أثناء تحميل الموظفين")}},X=s=>{const t=s.map(x=>({id:x.id,address:x.customer_address||"عنوان غير محدد"})),_=Ee(t,{threshold:.7}).map(x=>({group:x,orders:s.filter(ae=>x.orders.includes(ae.id))}));R(_)};o.useEffect(()=>{B(),Q()},[r]);const F=(s,t)=>{const i=new Set(j);t?i.add(s):i.delete(s),O(i)},Y=(s,t)=>{const i=new Set(A),_=new Set(j);t?(i.add(s.representativeAddress),s.orders.forEach(x=>_.add(x))):(i.delete(s.representativeAddress),s.orders.forEach(x=>_.delete(x))),G(i),O(_)},Z=s=>{D(s)},ee=()=>{if(!w){g.error("يرجى اختيار موظف");return}if(j.size===0){g.error("يرجى اختيار طلبات للتخصيص");return}const s=p.find(i=>i.id===w);if(!s){g.error("الموظف المحدد غير موجود");return}const t=m.filter(i=>j.has(i.id));k({isOpen:!0,selectedOrders:t,selectedEmployee:s,customCommissionRate:""})},se=async()=>{if(d.selectedEmployee){f(!0);try{const s=d.customCommissionRate?parseFloat(d.customCommissionRate):null;for(const t of d.selectedOrders)await le({path:{order_id:t.id},body:{code:t.code,notes:t.notes,total_price:t.total_price,customer_name:t.customer_name,customer_phone:t.customer_phone,customer_address:t.customer_address,customer_company_id:t.customer_company_id,breakable:t.breakable,deadline_date:t.deadline_date,commission_fixed_rate:s||t.commission_fixed_rate,assigned_to_id:d.selectedEmployee.id,final_customer_payment:t.final_customer_payment,handling_status:t.handling_status,cancellation_reason_template_id:t.cancellation_reason_template_id,cancellation_reason:t.cancellation_reason}});g.success(`تم تخصيص ${d.selectedOrders.length} طلب بنجاح`),O(new Set),G(new Set),D(""),k({isOpen:!1,selectedOrders:[],selectedEmployee:null,customCommissionRate:""}),B()}catch(s){console.error("Error assigning orders:",s),g.error("حدث خطأ أثناء تخصيص الطلبات")}finally{f(!1)}}},U=()=>{k({isOpen:!1,selectedOrders:[],selectedEmployee:null,customCommissionRate:""})},I=()=>j.size,te=()=>A.size;return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"تخصيص سريع للطلبات"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"تخصيص الطلبات غير المخصصة للموظفين"})]}),e.jsx(y,{variant:"outline",onClick:()=>n({to:"/orders"}),children:"العودة للطلبات"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(N,{children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(we,{className:"h-5 w-5 text-blue-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الطلبات"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:m.length})]})]})})}),e.jsx(N,{children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ce,{className:"h-5 w-5 text-green-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"مجموعات العناوين"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:S.length})]})]})})}),e.jsx(N,{children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ne,{className:"h-5 w-5 text-purple-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"الموظفين المتاحين"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:p.length})]})]})})}),e.jsx(N,{children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ve,{className:"h-5 w-5 text-orange-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"الطلبات المحددة"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:I()})]})]})})})]}),e.jsxs(N,{children:[e.jsx(W,{children:e.jsx($,{children:"إعدادات التخصيص"})}),e.jsx(v,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(C,{children:"وضع العرض"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(y,{variant:b==="grouped"?"default":"outline",size:"sm",onClick:()=>L("grouped"),children:["مجمعة بالعناوين (",te(),")"]}),e.jsxs(y,{variant:b==="individual"?"default":"outline",size:"sm",onClick:()=>L("individual"),children:["فردية (",I(),")"]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(C,{children:"اختيار الموظف"}),e.jsxs(de,{value:w,onValueChange:Z,children:[e.jsx(oe,{children:e.jsx(me,{placeholder:"اختر موظف..."})}),e.jsx(xe,{children:p.map(s=>e.jsx(he,{value:s.id,children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("span",{children:[s.first_name," ",s.last_name]}),e.jsxs(q,{variant:"secondary",className:"ml-2",children:[s.commission_fixed_rate," ج.م"]})]})},s.id))})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsx(y,{onClick:ee,disabled:!w||j.size===0,className:"w-full",children:h?e.jsxs(e.Fragment,{children:[e.jsx(M,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري التخصيص..."]}):`تخصيص ${I()} طلب`})})]})})]}),e.jsxs(N,{children:[e.jsx(W,{children:e.jsx($,{children:b==="grouped"?"الطلبات مجمعة بالعناوين":"الطلبات الفردية"})}),e.jsx(v,{children:a?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(M,{className:"h-8 w-8 animate-spin text-blue-500"}),e.jsx("span",{className:"ml-2",children:"جاري التحميل..."})]}):b==="grouped"?e.jsx("div",{className:"space-y-4",children:S.map(s=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(z,{checked:A.has(s.group.representativeAddress),onCheckedChange:t=>Y(s.group,t)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:s.group.representativeAddress}),e.jsxs("p",{className:"text-sm text-gray-500",children:[s.orders.length," طلب في هذا العنوان"]})]}),e.jsxs(q,{variant:"secondary",children:[s.orders.length," طلب"]})]}),e.jsx("div",{className:"ml-6 space-y-2",children:s.orders.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{checked:j.has(t.id),onCheckedChange:i=>F(t.id,i)}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:t.customer_name}),e.jsx("p",{className:"text-sm text-gray-500",children:t.code})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-medium",children:[t.total_price," ج.م"]}),e.jsx("p",{className:"text-sm text-gray-500",children:t.customer_phone})]})]},t.id))})]},s.group.representativeAddress))}):e.jsx("div",{className:"space-y-2",children:m.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(z,{checked:j.has(s.id),onCheckedChange:t=>F(s.id,t)}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.customer_name}),e.jsx("p",{className:"text-sm text-gray-500",children:s.code})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-medium",children:[s.total_price," ج.م"]}),e.jsx("p",{className:"text-sm text-gray-500",children:s.customer_phone})]}),e.jsx("div",{className:"text-sm text-gray-500 max-w-xs truncate",children:s.customer_address})]})]},s.id))})})]}),e.jsx(pe,{open:d.isOpen,onOpenChange:U,children:e.jsxs(ue,{className:"sm:max-w-md",children:[e.jsx(je,{children:e.jsx(fe,{children:"تخصيص نسبة العمولة"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(C,{children:"الموظف المحدد"}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[(P=d.selectedEmployee)==null?void 0:P.first_name," ",(T=d.selectedEmployee)==null?void 0:T.last_name]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["نسبة العمولة الافتراضية: ",(V=d.selectedEmployee)==null?void 0:V.commission_fixed_rate," ج.م"]})]}),e.jsx(H,{}),e.jsxs("div",{children:[e.jsxs(C,{children:["الطلبات المحددة (",d.selectedOrders.length,")"]}),e.jsx("div",{className:"mt-2 max-h-32 overflow-y-auto space-y-1",children:d.selectedOrders.map(s=>e.jsxs("div",{className:"text-sm text-gray-600",children:["• ",s.customer_name," - ",s.code]},s.id))})]}),e.jsx(H,{}),e.jsxs("div",{children:[e.jsx(C,{htmlFor:"commission-rate",children:"نسبة العمولة المخصصة (اختياري)"}),e.jsx(ye,{id:"commission-rate",type:"number",placeholder:"اترك فارغاً لاستخدام النسبة الافتراضية",value:d.customCommissionRate,onChange:s=>k(t=>({...t,customCommissionRate:s.target.value}))}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"إذا تركت هذا الحقل فارغاً، سيتم استخدام نسبة العمولة الافتراضية للموظف"})]})]}),e.jsxs(ge,{children:[e.jsx(y,{variant:"outline",onClick:U,children:"إلغاء"}),e.jsx(y,{onClick:se,disabled:h,children:h?e.jsxs(e.Fragment,{children:[e.jsx(M,{className:"mr-2 h-4 w-4 animate-spin"}),"جاري التخصيص..."]}):"تأكيد التخصيص"})]})]})})]})};export{Pe as component};
