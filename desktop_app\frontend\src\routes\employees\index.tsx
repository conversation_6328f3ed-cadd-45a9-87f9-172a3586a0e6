import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { DataTableComponent } from '@/components/DataTableComponent'
import { Badge } from '@/components/ui/badge'
import { useEffect, useState } from 'react'
import { merchantsApisListOfficeEmployees, accountsApisDeleteUser, merchantsApisRemoveOfficeEmployee } from '@/client'
import { type OfficeEmployeeSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'

export const Route = createFileRoute('/employees/')({
    component: RouteComponent,
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const [data, setData] = useState<OfficeEmployeeSchema[]>([])
    const navigate = useNavigate()

    const fetchData = async () => {
        try {
            const response = await merchantsApisListOfficeEmployees({
                path: {
                    office_id: selectedOffice!.id,
                },
            })
            if (response.data) {
                setData(response.data.employees)
            }
        } catch (error) {
            console.error('Error fetching employees:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الموظفين')
        }
    }

    useEffect(() => {
        fetchData()
    }, [selectedOffice])

    const handleEditEmployee = (employee: OfficeEmployeeSchema) => {
        navigate({
            to: "/employees/form",
            search: { id: employee.user.id }
        })
    }

    const handleViewEmployee = (employee: OfficeEmployeeSchema) => {
        // For now, we'll just show a toast with employee details
        // In the future, this could navigate to a detailed view page
        toast.info(`عرض تفاصيل الموظف: ${employee.user.first_name} ${employee.user.last_name}`)
    }

    const handleDeleteEmployee = async (employee: OfficeEmployeeSchema) => {
        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            try {
                // First remove from office
                await merchantsApisRemoveOfficeEmployee({
                    path: {
                        office_id: selectedOffice!.id,
                        employee_id: employee.id,
                    },
                })

                // Then delete the user account
                await accountsApisDeleteUser({
                    path: {
                        user_id: employee.user.id,
                    },
                })

                toast.success('تم حذف الموظف بنجاح')
                fetchData() // Refresh the data
            } catch (error) {
                console.error('Error deleting employee:', error)
                toast.error('حدث خطأ أثناء حذف الموظف')
            }
        }
    }

    return <>
        <SiteHeader title="الموظفين" />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">الموظفين</h1>
            <p className="text-sm text-muted-foreground">هنا يمكنك إدارة الموظفين</p>
        </div>
        <Separator className="mb-4" />
        <div className="flex flex-col m-4">
            <DataTableComponent
                idField='id'
                pageSize={10}
                enablePagination={false}
                showAddButton={true}
                addButtonText="إضافة موظف"
                onAddClick={() => {
                    navigate({
                        to: "/employees/form",
                        search: { id: undefined }
                    })
                }}
                data={data}
                columns={
                    [
                        {
                            header: "الاسم",
                            accessorKey: "user.first_name",
                            cell: ({ row }) => (
                                <div
                                    className="flex flex-col cursor-pointer hover:bg-gray-50 p-2 rounded transition"
                                    onClick={() => {
                                        navigate({
                                            to: "/employees/form",
                                            search: { id: row.original.user.id }
                                        })
                                    }}
                                    title="تعديل الموظف"
                                >
                                    <span className="font-medium">
                                        {row.original.user.first_name} {row.original.user.last_name}
                                    </span>
                                    <span className="text-sm text-muted-foreground">
                                        {row.original.user.email}
                                    </span>
                                </div>
                            ),
                        },
                        {
                            header: "رقم الهاتف",
                            accessorKey: "user.phone_number",
                        },
                        {
                            header: "المنصب",
                            accessorKey: "user.role",
                            cell: ({ row }) => {
                                const role = row.original.user.role
                                const roleLabels = {
                                    admin: "ادمن",
                                    manager: "مدير",
                                    employee: "موظف"
                                }
                                const roleColors = {
                                    admin: "bg-red-100 text-red-800",
                                    manager: "bg-blue-100 text-blue-800",
                                    employee: "bg-green-100 text-green-800"
                                }
                                return (
                                    <Badge className={roleColors[role as keyof typeof roleColors]}>
                                        {roleLabels[role as keyof typeof roleLabels]}
                                    </Badge>
                                )
                            },
                        },
                        {
                            header: "نسبة العمولة",
                            accessorKey: "user.commission_fixed_rate",
                            cell: ({ row }) => (
                                <span className="text-sm">
                                    {row.original.user.commission_fixed_rate}  ج.م
                                </span>
                            ),
                        },
                    ]
                }
                actionsColumn={{
                    enableEdit: false, // We'll use custom actions instead
                    enableDelete: false, // We'll use custom actions instead
                    customActions: [
                        {
                            label: "عرض",
                            onClick: handleViewEmployee,
                        },
                        {
                            label: "تعديل",
                            onClick: handleEditEmployee,
                        },
                        {
                            label: "حذف",
                            onClick: handleDeleteEmployee,
                            variant: "destructive",
                        },
                    ],
                }}
            />
        </div>
    </>
}

