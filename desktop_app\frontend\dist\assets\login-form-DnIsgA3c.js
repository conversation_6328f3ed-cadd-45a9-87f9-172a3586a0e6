import{G as u,u as _,a as A,r as i,H as q,j as e,e as d,B as p}from"./index-BhUrl90v.js";import{I as j}from"./input-Bg4pLWIo.js";import{L as v}from"./label-DaXzHiyZ.js";import{D as F,a as B,b as H,c as I,d as O,e as $}from"./dialog-D2ymaVEk.js";import{A as z,a as P}from"./alert-61bUGbMG.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],T=u("arrow-left",R);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],y=u("building-2",V);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Z=u("circle-alert",G);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],K=u("loader-circle",J),Q=n=>{if(!n.trim())return"رقم الهاتف مطلوب";if(!/^01[0-2,5]{1}[0-9]{8}$/.test(n.trim()))return"رقم الهاتف غير صحيح"},U=n=>{if(!n.trim())return"كلمة المرور مطلوبة";if(n.length<6)return"كلمة المرور يجب أن تكون 6 أحرف على الأقل"};function re({className:n,...w}){const{login:f,isAuthenticated:k,isLoading:C}=_(),h=A(),[r,D]=i.useState({phoneNumber:"",password:""}),[g,c]=i.useState("idle"),[t,l]=i.useState({}),[L,M]=i.useState([]);if(k)return h({to:"/dashboard"}),null;const m=i.useMemo(()=>{const s={},a=Q(r.phoneNumber);a&&(s.phoneNumber=a);const o=U(r.password);return o&&(s.password=o),s},[r.phoneNumber,r.password]),x=i.useMemo(()=>Object.keys(m).length===0&&r.phoneNumber.trim()&&r.password.trim(),[m,r]),N=i.useCallback((s,a)=>{D(o=>({...o,[s]:a})),t[s]&&l(o=>({...o,[s]:void 0})),t.general&&l(o=>({...o,general:void 0}))},[t]),S=i.useCallback(async s=>{var a;if(s.preventDefault(),!x){l(m);return}try{c("loading"),l({});const b=((a=(await q({query:{employee_phone_number:r.phoneNumber.trim()}})).data)==null?void 0:a.offices)??[];b.length>0?(M(b),c("officeSelecting")):(l({general:"لا يوجد مكاتب مرتبطة بهذا الرقم، يرجى التأكد من صحة البيانات"}),c("error"))}catch(o){console.error("Error fetching offices:",o),l({general:"حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى"}),c("error")}},[r,x,m]),E=i.useCallback(async s=>{try{c("loading"),await f(r.phoneNumber.trim(),r.password,s),h({to:"/dashboard"})}catch(a){console.error("Login error:",a),l({general:a instanceof Error?a.message:"حدث خطأ في تسجيل الدخول"}),c("error")}},[f,r,h]);return g==="loading"||C?e.jsxs("div",{className:d("flex flex-col items-center justify-center gap-4 p-8",n),children:[e.jsx(K,{className:"h-8 w-8 animate-spin text-primary"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"جاري التحميل..."})]}):g==="officeSelecting"?e.jsxs("div",{className:d("flex flex-col gap-6",n),children:[e.jsxs("div",{className:"flex flex-col items-center gap-2 text-center",children:[e.jsx(y,{className:"h-8 w-8 text-primary"}),e.jsx("h2",{className:"text-xl font-semibold",children:"اختر المكتب"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"اختر المكتب الذي تريد تسجيل الدخول إليه"})]}),e.jsx("div",{className:"space-y-3",children:L.map(s=>e.jsxs(p,{className:"w-full justify-start gap-3 text-lg h-14",variant:"outline",type:"button",onClick:()=>E(s.id),disabled:!1,children:[e.jsx(y,{className:"h-10 w-10"}),e.jsxs("div",{className:"flex flex-row items-center justify-between w-full",children:[e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("span",{className:"font-medium",children:s.name}),e.jsx("span",{className:"text-xs text-muted-foreground",children:s.address})]}),e.jsx("div",{className:"flex flex-col items-end",children:e.jsx(T,{className:"h-4 w-4"})})]})]},s.id))}),e.jsx(p,{variant:"ghost",onClick:()=>{c("idle"),l({})},className:"w-full",children:"العودة"})]}):e.jsxs("form",{className:d("flex flex-col gap-6",n),onSubmit:S,...w,children:[e.jsxs("div",{className:"flex flex-col items-center gap-2 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"سجل دخولك"}),e.jsx("p",{className:"text-muted-foreground text-sm text-balance",children:"أدخل رقم هاتفك وكلمة المرور لتسجيل الدخول"})]}),t.general&&e.jsxs(z,{variant:"destructive",children:[e.jsx(Z,{className:"h-4 w-4"}),e.jsx(P,{children:t.general})]}),e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-3",children:[e.jsx(v,{htmlFor:"phoneNumber",className:"text-sm font-medium",children:"رقم الهاتف"}),e.jsx(j,{id:"phoneNumber",name:"phoneNumber",type:"tel",placeholder:"01000000000",value:r.phoneNumber,onChange:s=>N("phoneNumber",s.target.value),className:d(t.phoneNumber&&"border-destructive focus-visible:ring-destructive"),"aria-describedby":t.phoneNumber?"phoneNumber-error":void 0,autoComplete:"tel",required:!0}),t.phoneNumber&&e.jsx("p",{id:"phoneNumber-error",className:"text-sm text-destructive",children:t.phoneNumber})]}),e.jsxs("div",{className:"grid gap-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(v,{htmlFor:"password",className:"text-sm font-medium",children:"كلمة المرور"}),e.jsxs(F,{children:[e.jsx(B,{asChild:!0,children:e.jsx("button",{type:"button",className:"text-sm text-muted-foreground underline-offset-4 hover:underline hover:text-foreground transition-colors",children:"نسيت كلمة المرور؟"})}),e.jsx(H,{className:"sm:max-w-md",children:e.jsxs(I,{children:[e.jsx(O,{children:"نسيت كلمة المرور؟"}),e.jsx($,{children:"تواصل مع المطور عشان يساعدك في استعادة كلمة المرور"})]})})]})]}),e.jsx(j,{id:"password",name:"password",type:"password",value:r.password,onChange:s=>N("password",s.target.value),className:d(t.password&&"border-destructive focus-visible:ring-destructive"),"aria-describedby":t.password?"password-error":void 0,autoComplete:"current-password",required:!0}),t.password&&e.jsx("p",{id:"password-error",className:"text-sm text-destructive",children:t.password})]}),e.jsx(p,{type:"submit",className:"w-full",disabled:!x,children:"سجل دخول"})]}),e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["معندكش حساب؟"," ",e.jsx("button",{type:"button",className:"text-primary underline-offset-4 hover:underline font-medium",onClick:()=>{console.log("Registration not implemented yet")},children:"اعمل حساب جديد"})]})]})}export{re as L};
