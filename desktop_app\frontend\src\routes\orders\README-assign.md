# Fast Order Assignment Mechanism

## Overview

The fast assigning mechanism allows managers to quickly assign multiple unassigned orders to employees with intelligent address grouping and flexible commission rate management.

## Features

### 1. Address Grouping with Fuzzy Matching
- **Automatic Grouping**: Orders with similar addresses are automatically grouped together using fuzzy string matching
- **Configurable Threshold**: Similarity threshold can be adjusted (default: 0.7)
- **Smart Normalization**: Addresses are normalized by removing punctuation and extra whitespace
- **Representative Address**: Each group shows the most detailed address as the representative

### 2. Dual View Modes
- **Grouped View**: Orders are displayed grouped by similar addresses
- **Individual View**: Orders are displayed individually for precise selection

### 3. Employee Selection
- **Filtered List**: Only employees (not admins/managers) are shown for assignment
- **Commission Display**: Each employee shows their default commission rate
- **Real-time Validation**: Ensures a valid employee is selected before assignment

### 4. Commission Rate Management
- **Default Rate**: Uses employee's default commission rate if no custom rate is specified
- **Custom Rate**: Allows setting a different commission rate for specific orders
- **Batch Assignment**: All selected orders can be assigned with the same commission rate

### 5. Batch Operations
- **Group Selection**: Select entire address groups with one click
- **Individual Selection**: Select specific orders within groups
- **Mixed Selection**: Combine group and individual selections
- **Real-time Counters**: Shows selected orders and groups count

## Technical Implementation

### Fuzzy Matching Algorithm
- **Levenshtein Distance**: Calculates string similarity using edit distance
- **Normalization**: Removes case sensitivity, extra whitespace, and punctuation
- **Configurable Options**: Threshold, case sensitivity, and whitespace normalization

### API Integration
- **Unassigned Orders**: Fetches orders with `assigned_to_isnull=true`
- **Employee List**: Fetches users with role='employee'
- **Order Updates**: Updates each order individually with new assignment and commission rate

### State Management
- **Selection State**: Tracks selected orders and groups separately
- **Dialog State**: Manages commission rate dialog with selected data
- **Loading States**: Shows loading indicators during API calls

## Usage Flow

1. **Access**: Navigate to `/orders/assign` or click "تخصيص سريع" from orders page
2. **View Orders**: See unassigned orders grouped by address or individually
3. **Select Orders**: Choose orders by group or individually
4. **Choose Employee**: Select an employee from the dropdown
5. **Set Commission**: Optionally set a custom commission rate
6. **Confirm Assignment**: Review and confirm the assignment

## File Structure

```
src/
├── lib/
│   ├── fuzzy-match.ts          # Fuzzy matching utility
│   └── fuzzy-match.test.ts     # Test file for fuzzy matching
├── routes/orders/
│   ├── assign.tsx              # Main assignment page
│   └── README-assign.md        # This documentation
```

## Configuration

### Fuzzy Matching Options
```typescript
interface FuzzyMatchOptions {
  threshold?: number;           // Similarity threshold (0-1), default 0.8
  ignoreCase?: boolean;         // Whether to ignore case, default true
  normalizeWhitespace?: boolean; // Whether to normalize whitespace, default true
}
```

### Address Grouping
```typescript
interface AddressGroup {
  representativeAddress: string; // Most detailed address in the group
  orders: string[];             // Array of order IDs
  similarity: number;           // Group similarity score
}
```

## Error Handling

- **Network Errors**: Shows toast notifications for API failures
- **Validation**: Ensures employee and orders are selected before assignment
- **Loading States**: Prevents multiple submissions during processing
- **State Reset**: Clears selections after successful assignment

## Future Enhancements

1. **Bulk Commission Rates**: Set different rates for different orders in the same batch
2. **Assignment History**: Track assignment changes over time
3. **Smart Suggestions**: Suggest employees based on location or workload
4. **Advanced Filtering**: Filter orders by date, company, or other criteria
5. **Export/Import**: Export assignment data or import from external sources 