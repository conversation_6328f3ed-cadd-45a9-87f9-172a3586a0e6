
import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { ordersApisListCompanies, ordersApisDeleteCompany, type CompanySchema } from '@/client'
import { DataTableComponent } from '@/components/DataTableComponent'
import { toast } from 'sonner'

export const Route = createFileRoute('/companies/')({
    component: RouteComponent,
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const [data, setData] = useState<CompanySchema[]>([])
    const navigate = useNavigate()

    const fetchData = async () => {
        try {
            const response = await ordersApisListCompanies({
                query: {
                    office_id: selectedOffice!.id,
                },
            })
            setData(response.data?.companies || [])
        } catch (error) {
            console.error('Error fetching companies:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الشركات')
        }
    }

    useEffect(() => {
        fetchData()
    }, [selectedOffice])

    const handleDeleteCompany = async (company: CompanySchema) => {
        if (confirm('هل أنت متأكد من حذف هذه الشركة؟')) {
            try {
                await ordersApisDeleteCompany({
                    path: {
                        company_id: company.id,
                    },
                })

                toast.success('تم حذف الشركة بنجاح')
                fetchData() // Refresh the data
            } catch (error) {
                console.error('Error deleting company:', error)
                toast.error('حدث خطأ أثناء حذف الشركة')
            }
        }
    }

    return <>
        <SiteHeader title="الشركات" />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">الشركات</h1>
            <p className="text-sm text-muted-foreground">هنا يمكنك إدارة الشركات</p>
        </div>
        <Separator className="mb-4" />
        <DataTableComponent
            idField='id'
            pageSize={10}
            enablePagination={false}
            showAddButton={true}
            addButtonText="إضافة شركة"
            onAddClick={() => {
                navigate({
                    to: "/companies/form",
                    search: { id: undefined }
                })
            }}
            data={data}
            columns={
                [

                    {
                        header: "رمز الشركة",
                        accessorKey: "code",
                        cell: ({ row }) =>
                            <div onClick={() => {
                                navigate({ to: "/companies/channels", search: { company_id: row.id } })
                            }} className="flex items-center gap-2">
                                <div className="w-4 h-4 rounded-full" style={{ backgroundColor: row.original.color_code }}></div>
                                <span>{row.original.code}</span>
                            </div>
                    },
                    {
                        header: "الاسم",
                        accessorKey: "name",
                        cell: ({ row }) =>
                            <div onClick={() => {
                                navigate({ to: "/companies/channels", search: { company_id: row.id } })
                            }} className="flex items-center gap-2">
                                <span>{row.original.name}</span>
                            </div>
                    },
                    {
                        header: "العنوان",
                        accessorKey: "address",
                    },
                    {
                        header: "الهاتف",
                        accessorKey: "phone",
                    },

                ]
            }
            actionsColumn={{
                enableEdit: false,
                enableDelete: false,
                customActions: [
                    {
                        label: "تعديل",
                        onClick: (row) => {
                            navigate({ to: "/companies/form", search: { id: row.id } })
                        }
                    },
                    {
                        label: "قنوات",
                        onClick: (row) => {
                            navigate({ to: "/companies/channels", search: { company_id: row.id } })
                        }
                    },
                    {
                        label: "حذف",
                        onClick: handleDeleteCompany,
                        variant: "destructive",
                    },
                ]
            }}
        />
    </>
}
