import * as React from "react"
import { type ColumnDef } from "@tanstack/react-table"
import { z } from "zod"
import { toast } from "sonner"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { DataTableComponent, type DataTableProps } from "./DataTableComponent"

// Example 1: Original data structure
export const originalSchema = z.object({
    id: z.number(),
    header: z.string(),
    type: z.string(),
    status: z.string(),
    target: z.string(),
    limit: z.string(),
    reviewer: z.string(),
})

type OriginalData = z.infer<typeof originalSchema>

// Example 2: User management data structure
export const userSchema = z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    role: z.string(),
    status: z.enum(["active", "inactive", "pending"]),
    lastLogin: z.string(),
})

type UserData = z.infer<typeof userSchema>

// Example 3: Product inventory data structure
export const productSchema = z.object({
    sku: z.string(),
    name: z.string(),
    category: z.string(),
    price: z.number(),
    stock: z.number(),
    supplier: z.string(),
})

type ProductData = z.infer<typeof productSchema>

// Example usage of the original data table
export function OriginalDataTableExample() {
    const [data, setData] = React.useState<OriginalData[]>([
        {
            id: 1,
            header: "Executive Summary",
            type: "Executive Summary",
            status: "Done",
            target: "5",
            limit: "10",
            reviewer: "Eddie Lake",
        },
        {
            id: 2,
            header: "Technical Approach",
            type: "Technical Approach",
            status: "In Progress",
            target: "15",
            limit: "20",
            reviewer: "Assign reviewer",
        },
        {
            id: 3,
            header: "Past Performance",
            type: "Past Performance",
            status: "Not Started",
            target: "8",
            limit: "12",
            reviewer: "Jamik Tashpulatov",
        },
    ])

    const columns: ColumnDef<OriginalData>[] = [
        {
            accessorKey: "header",
            header: "Header",
            cell: ({ row }) => (
                <Button variant="link" className="text-foreground w-fit px-0 text-start">
                    {row.original.header}
                </Button>
            ),
            enableHiding: false,
        },
        {
            accessorKey: "type",
            header: "Section Type",
            cell: ({ row }) => (
                <div className="w-32">
                    <Badge variant="outline" className="text-muted-foreground px-1.5">
                        {row.original.type}
                    </Badge>
                </div>
            ),
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => (
                <Badge
                    variant={row.original.status === "Done" ? "default" : "secondary"}
                    className="px-1.5"
                >
                    {row.original.status}
                </Badge>
            ),
        },
        {
            accessorKey: "target",
            header: () => <div className="w-full text-end">Target</div>,
            cell: ({ row }) => (
                <form
                    onSubmit={(e) => {
                        e.preventDefault()
                        toast.success(`Target updated for ${row.original.header}`)
                    }}
                >
                    <Label htmlFor={`${row.original.id}-target`} className="sr-only">
                        Target
                    </Label>
                    <Input
                        className="hover:bg-input/30 focus-visible:bg-background h-8 w-16 border-transparent bg-transparent text-end shadow-none focus-visible:border"
                        defaultValue={row.original.target}
                        id={`${row.original.id}-target`}
                    />
                </form>
            ),
        },
        {
            accessorKey: "limit",
            header: () => <div className="w-full text-end">Limit</div>,
            cell: ({ row }) => (
                <form
                    onSubmit={(e) => {
                        e.preventDefault()
                        toast.success(`Limit updated for ${row.original.header}`)
                    }}
                >
                    <Label htmlFor={`${row.original.id}-limit`} className="sr-only">
                        Limit
                    </Label>
                    <Input
                        className="hover:bg-input/30 focus-visible:bg-background h-8 w-16 border-transparent bg-transparent text-end shadow-none focus-visible:border"
                        defaultValue={row.original.limit}
                        id={`${row.original.id}-limit`}
                    />
                </form>
            ),
        },
        {
            accessorKey: "reviewer",
            header: "Reviewer",
            cell: ({ row }) => {
                const isAssigned = row.original.reviewer !== "Assign reviewer"

                if (isAssigned) {
                    return row.original.reviewer
                }

                return (
                    <Select>
                        <SelectTrigger className="w-38" size="sm">
                            <SelectValue placeholder="Assign reviewer" />
                        </SelectTrigger>
                        <SelectContent align="end">
                            <SelectItem value="Eddie Lake">Eddie Lake</SelectItem>
                            <SelectItem value="Jamik Tashpulatov">Jamik Tashpulatov</SelectItem>
                        </SelectContent>
                    </Select>
                )
            },
        },
    ]

    const handleRowUpdate = (rowId: string, updatedData: Partial<OriginalData>) => {
        setData(prev => prev.map(item =>
            item.id.toString() === rowId ? { ...item, ...updatedData } : item
        ))
        toast.success("Row updated successfully")
    }

    const handleRowDelete = (rowId: string) => {
        setData(prev => prev.filter(item => item.id.toString() !== rowId))
        toast.success("Row deleted successfully")
    }

    const handleRowReorder = (newData: OriginalData[]) => {
        setData(newData)
        toast.success("Rows reordered successfully")
    }

    const handleAddClick = () => {
        const newItem: OriginalData = {
            id: Math.max(...data.map(d => d.id)) + 1,
            header: "New Section",
            type: "Narrative",
            status: "Not Started",
            target: "0",
            limit: "0",
            reviewer: "Assign reviewer",
        }
        setData(prev => [...prev, newItem])
        toast.success("New section added")
    }

    return (
        <DataTableComponent
            data={data}
            columns={columns}
            enableDragAndDrop={true}
            enableRowSelection={true}
            enableColumnVisibility={true}
            enablePagination={true}
            pageSize={10}
            showAddButton={true}
            addButtonText="Add Section"
            onAddClick={handleAddClick}
            onRowUpdate={handleRowUpdate}
            onRowDelete={handleRowDelete}
            onRowReorder={handleRowReorder}
            idField="id"
            emptyMessage="No sections found."
            actionsColumn={{
                enableEdit: true,
                enableDelete: true,
                customActions: [
                    {
                        label: "Duplicate",
                        onClick: (row) => {
                            const newItem = { ...row, id: Math.max(...data.map(d => d.id)) + 1 }
                            setData(prev => [...prev, newItem])
                            toast.success("Section duplicated")
                        }
                    }
                ]
            }}
        />
    )
}

// Example usage for user management
export function UserManagementTableExample() {
    const [users, setUsers] = React.useState<UserData[]>([
        {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            role: "Admin",
            status: "active",
            lastLogin: "2024-01-15",
        },
        {
            id: "2",
            name: "Jane Smith",
            email: "<EMAIL>",
            role: "User",
            status: "active",
            lastLogin: "2024-01-14",
        },
        {
            id: "3",
            name: "Bob Johnson",
            email: "<EMAIL>",
            role: "Manager",
            status: "inactive",
            lastLogin: "2024-01-10",
        },
    ])

    const userColumns: ColumnDef<UserData>[] = [
        {
            accessorKey: "name",
            header: "Name",
        },
        {
            accessorKey: "email",
            header: "Email",
        },
        {
            accessorKey: "role",
            header: "Role",
            cell: ({ row }) => (
                <Badge variant="outline">{row.original.role}</Badge>
            ),
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => (
                <Badge
                    variant={row.original.status === "active" ? "default" : "secondary"}
                >
                    {row.original.status}
                </Badge>
            ),
        },
        {
            accessorKey: "lastLogin",
            header: "Last Login",
        },
    ]

    const handleUserDelete = (userId: string) => {
        setUsers(prev => prev.filter(user => user.id !== userId))
        toast.success("User deleted successfully")
    }

    const handleAddUser = () => {
        const newUser: UserData = {
            id: (users.length + 1).toString(),
            name: "New User",
            email: "<EMAIL>",
            role: "User",
            status: "pending",
            lastLogin: "Never",
        }
        setUsers(prev => [...prev, newUser])
        toast.success("New user added")
    }

    return (
        <DataTableComponent
            data={users}
            columns={userColumns}
            enableRowSelection={true}
            enableColumnVisibility={true}
            enablePagination={true}
            pageSize={5}
            showAddButton={true}
            addButtonText="Add User"
            onAddClick={handleAddUser}
            onRowDelete={handleUserDelete}
            idField="id"
            emptyMessage="No users found."
            actionsColumn={{
                enableEdit: true,
                enableDelete: true,
                customActions: [
                    {
                        label: "Activate",
                        onClick: (row) => {
                            setUsers(prev => prev.map(user =>
                                user.id === row.id ? { ...user, status: "active" as const } : user
                            ))
                            toast.success("User activated")
                        }
                    },
                    {
                        label: "Deactivate",
                        onClick: (row) => {
                            setUsers(prev => prev.map(user =>
                                user.id === row.id ? { ...user, status: "inactive" as const } : user
                            ))
                            toast.success("User deactivated")
                        }
                    }
                ]
            }}
        />
    )
}

// Example usage for product inventory
export function ProductInventoryTableExample() {
    const [products, setProducts] = React.useState<ProductData[]>([
        {
            sku: "PROD-001",
            name: "Laptop",
            category: "Electronics",
            price: 999.99,
            stock: 50,
            supplier: "TechCorp",
        },
        {
            sku: "PROD-002",
            name: "Mouse",
            category: "Accessories",
            price: 29.99,
            stock: 200,
            supplier: "AccessCorp",
        },
        {
            sku: "PROD-003",
            name: "Keyboard",
            category: "Accessories",
            price: 79.99,
            stock: 75,
            supplier: "AccessCorp",
        },
    ])

    const productColumns: ColumnDef<ProductData>[] = [
        {
            accessorKey: "sku",
            header: "SKU",
        },
        {
            accessorKey: "name",
            header: "Product Name",
        },
        {
            accessorKey: "category",
            header: "Category",
            cell: ({ row }) => (
                <Badge variant="outline">{row.original.category}</Badge>
            ),
        },
        {
            accessorKey: "price",
            header: "Price",
            cell: ({ row }) => (
                <span>${row.original.price.toFixed(2)}</span>
            ),
        },
        {
            accessorKey: "stock",
            header: "Stock",
            cell: ({ row }) => (
                <Badge
                    variant={row.original.stock > 0 ? "default" : "destructive"}
                >
                    {row.original.stock}
                </Badge>
            ),
        },
        {
            accessorKey: "supplier",
            header: "Supplier",
        },
    ]

    const handleProductDelete = (sku: string) => {
        setProducts(prev => prev.filter(product => product.sku !== sku))
        toast.success("Product deleted successfully")
    }

    const handleAddProduct = () => {
        const newProduct: ProductData = {
            sku: `PROD-${String(products.length + 1).padStart(3, '0')}`,
            name: "New Product",
            category: "General",
            price: 0,
            stock: 0,
            supplier: "Unknown",
        }
        setProducts(prev => [...prev, newProduct])
        toast.success("New product added")
    }

    return (
        <DataTableComponent
            data={products}
            columns={productColumns}
            enableRowSelection={true}
            enableColumnVisibility={true}
            enablePagination={true}
            pageSize={5}
            showAddButton={true}
            addButtonText="Add Product"
            onAddClick={handleAddProduct}
            onRowDelete={handleProductDelete}
            idField="sku"
            emptyMessage="No products found."
            actionsColumn={{
                enableEdit: true,
                enableDelete: true,
                customActions: [
                    {
                        label: "Restock",
                        onClick: (row) => {
                            setProducts(prev => prev.map(product =>
                                product.sku === row.sku ? { ...product, stock: product.stock + 10 } : product
                            ))
                            toast.success("Product restocked")
                        }
                    }
                ]
            }}
        />
    )
} 