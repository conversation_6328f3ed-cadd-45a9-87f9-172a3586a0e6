import{r as i,j as e,w as R,x as E,B as c,A as re,y as F,E as S,F as oe,z as ce}from"./index-DwPFwVGs.js";import{u as de,a as z,K as ge,T as xe,M as he,b as me,k as ue,l as je,m as pe,n as fe,o as Se,p as Ce,I as ve,c as be,d as Ne,D as we,r as ye,e as Ie,f as b,S as Me,v as ke,g as ze,h as Pe,i as De,j as Te,s as B,t as Ve,q as Re,C as Ee}from"./sortable.esm-TGC3xZfG.js";import"./index-DVysEDS8.js";import{C as H}from"./checkbox-BLNazWDm.js";import{S as Fe,a as He,b as Ae,c as Ge,d as <PERSON>}from"./select-S-eFdQZZ.js";import{T as A,a as G,b as m,c as L,d as q,e as N}from"./table-D7wvSByk.js";function qe({id:r,idField:u}){const{attributes:d,listeners:g}=B({id:r});return e.jsxs(c,{...d,...g,variant:"ghost",size:"icon",className:"text-muted-foreground size-7 hover:bg-transparent",children:[e.jsx(Ve,{className:"text-muted-foreground size-3"}),e.jsx("span",{className:"sr-only",children:"Drag to reorder"})]})}function Be({row:r,idField:u}){const{transform:d,transition:g,setNodeRef:w,isDragging:x}=B({id:r.original[u]});return e.jsx(m,{"data-state":r.getIsSelected()&&"selected","data-dragging":x,ref:w,className:"relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80",style:{transform:Ee.Transform.toString(d),transition:g},children:r.getVisibleCells().map(j=>e.jsx(N,{children:b(j.column.columnDef.cell,j.getContext())},j.id))})}function Xe({data:r,columns:u,enableDragAndDrop:d=!1,enableRowSelection:g=!0,enableColumnVisibility:w=!0,enablePagination:x=!0,pageSize:j=10,pageSizeOptions:O=[10,20,30,40,50],showAddButton:$=!1,addButtonText:K="Add Item",onAddClick:J,onRowUpdate:C,onRowDelete:v,onRowReorder:y,idField:o="id",className:Q="",emptyMessage:P="No results.",actionsColumn:n}){var T,V;const[h,D]=i.useState(()=>r),[W,X]=i.useState({}),[Y,Z]=i.useState({}),[_,U]=i.useState([]),[ee,se]=i.useState([]),[te,ae]=i.useState({pageIndex:0,pageSize:j}),ne=i.useId(),le=de(z(he,{}),z(xe,{}),z(ge,{}));i.useEffect(()=>{D(r)},[r]);const I=i.useMemo(()=>(h==null?void 0:h.map(t=>t[o]))||[],[h,o]),M=i.useMemo(()=>{const t=[];return d&&t.push({id:"drag",header:()=>null,cell:({row:s})=>e.jsx(qe,{id:s.original[o],idField:o}),enableSorting:!1,enableHiding:!1}),g&&t.push({id:"select",header:({table:s})=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(H,{checked:s.getIsAllPageRowsSelected()||s.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:l=>s.toggleAllPageRowsSelected(!!l),"aria-label":"Select all"})}),cell:({row:s})=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(H,{checked:s.getIsSelected(),onCheckedChange:l=>s.toggleSelected(!!l),"aria-label":"Select row"})}),enableSorting:!1,enableHiding:!1}),t.push(...u),n&&t.push({id:"actions",cell:({row:s})=>{var l,p;return e.jsxs(R,{children:[e.jsx(E,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"data-[state=open]:bg-muted text-muted-foreground flex size-8",size:"icon",children:[e.jsx(re,{}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(F,{align:"end",className:"w-32",children:[n.enableEdit&&e.jsx(S,{onClick:()=>C==null?void 0:C(s.original[o],s.original),children:"Edit"}),n.enableCopy&&e.jsx(S,{children:"Make a copy"}),n.enableFavorite&&e.jsx(S,{children:"Favorite"}),(l=n.customActions)==null?void 0:l.map((f,k)=>e.jsx(S,{onClick:()=>f.onClick(s.original),variant:f.variant,children:f.label},k)),(n.enableEdit||n.enableCopy||n.enableFavorite||((p=n.customActions)==null?void 0:p.length))&&n.enableDelete&&e.jsx(oe,{}),n.enableDelete&&e.jsx(S,{variant:"destructive",onClick:()=>v==null?void 0:v(s.original[o]),children:"Delete"})]})]})}}),t},[u,d,g,n,o,C,v]),a=me({data:h,columns:M,state:{sorting:ee,columnVisibility:Y,rowSelection:W,columnFilters:_,pagination:x?te:void 0},getRowId:t=>{var s;return((s=t[o])==null?void 0:s.toString())||""},enableRowSelection:g,onRowSelectionChange:X,onSortingChange:se,onColumnFiltersChange:U,onColumnVisibilityChange:Z,onPaginationChange:x?ae:void 0,getCoreRowModel:Ce(),getFilteredRowModel:Se(),getPaginationRowModel:x?fe():void 0,getSortedRowModel:pe(),getFacetedRowModel:je(),getFacetedUniqueValues:ue()});function ie(t){const{active:s,over:l}=t;if(s&&l&&s.id!==l.id){const p=(()=>{const f=I.indexOf(s.id),k=I.indexOf(l.id);return Re(h,f,k)})();D(p),y==null||y(p)}}return e.jsxs("div",{className:`w-full ${Q}`,children:[e.jsx("div",{className:"flex justify-end px-4 lg:px-6 mb-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[w&&e.jsxs(R,{children:[e.jsx(E,{asChild:!0,children:e.jsxs(c,{variant:"outline",size:"sm",children:[e.jsx(ve,{}),e.jsx("span",{className:"hidden lg:inline",children:"تخصيص الأعمدة"}),e.jsx("span",{className:"lg:hidden",children:"الأعمدة"}),e.jsx(be,{})]})}),e.jsx(F,{align:"start",className:"w-56",children:a.getAllColumns().filter(t=>typeof t.accessorFn<"u"&&t.getCanHide()).map(t=>{var s;return e.jsx(ce,{className:"capitalize",checked:t.getIsVisible(),onCheckedChange:l=>t.toggleVisibility(!!l),children:e.jsx("span",{className:"flex flex-row-reverse justify-between w-full",children:(s=t.columnDef.header)==null?void 0:s.toString()})},t.id)})})]}),$&&e.jsxs(c,{variant:"outline",size:"sm",onClick:J,children:[e.jsx(Ne,{}),e.jsx("span",{className:"hidden lg:inline",children:K})]})]})}),e.jsx("div",{className:"overflow-hidden rounded-lg border",children:d?e.jsx(we,{collisionDetection:Ie,modifiers:[ye],onDragEnd:ie,sensors:le,id:ne,children:e.jsxs(A,{children:[e.jsx(G,{className:"bg-muted sticky top-0 z-10",children:a.getHeaderGroups().map(t=>e.jsx(m,{children:t.headers.map(s=>e.jsx(L,{colSpan:s.colSpan,children:s.isPlaceholder?null:b(s.column.columnDef.header,s.getContext())},s.id))},t.id))}),e.jsx(q,{children:(T=a.getRowModel().rows)!=null&&T.length?e.jsx(Me,{items:I,strategy:ke,children:a.getRowModel().rows.map(t=>e.jsx(Be,{row:t,idField:o},t.id))}):e.jsx(m,{children:e.jsx(N,{colSpan:M.length,className:"h-24 text-center",children:P})})})]})}):e.jsxs(A,{children:[e.jsx(G,{className:"bg-muted sticky top-0 z-10",children:a.getHeaderGroups().map(t=>e.jsx(m,{children:t.headers.map(s=>e.jsx(L,{colSpan:s.colSpan,children:s.isPlaceholder?null:b(s.column.columnDef.header,s.getContext())},s.id))},t.id))}),e.jsx(q,{children:(V=a.getRowModel().rows)!=null&&V.length?a.getRowModel().rows.map(t=>e.jsx(m,{"data-state":t.getIsSelected()&&"selected",children:t.getVisibleCells().map(s=>e.jsx(N,{children:b(s.column.columnDef.cell,s.getContext())},s.id))},t.id)):e.jsx(m,{children:e.jsx(N,{colSpan:M.length,className:"h-24 text-center",children:P})})})]})}),x&&e.jsxs("div",{className:"flex items-center justify-between px-4 mt-4",children:[e.jsxs("div",{className:"text-muted-foreground hidden flex-1 text-sm lg:flex",children:[a.getFilteredSelectedRowModel().rows.length," of"," ",a.getFilteredRowModel().rows.length," row(s) selected."]}),e.jsxs("div",{className:"flex w-full items-center gap-8 lg:w-fit",children:[e.jsxs("div",{className:"hidden items-center gap-2 lg:flex",children:[e.jsx("span",{className:"text-sm font-medium",children:"Rows per page"}),e.jsxs(Fe,{value:`${a.getState().pagination.pageSize}`,onValueChange:t=>{a.setPageSize(Number(t))},children:[e.jsx(He,{size:"sm",className:"w-20",children:e.jsx(Ae,{placeholder:a.getState().pagination.pageSize})}),e.jsx(Ge,{side:"top",children:O.map(t=>e.jsx(Le,{value:`${t}`,children:t},t))})]})]}),e.jsxs("div",{className:"flex w-fit items-center justify-center text-sm font-medium",children:["Page ",a.getState().pagination.pageIndex+1," of"," ",a.getPageCount()]}),e.jsxs("div",{className:"ms-auto flex items-center gap-2 lg:ms-0",children:[e.jsxs(c,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>a.setPageIndex(0),disabled:!a.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to first page"}),e.jsx(ze,{})]}),e.jsxs(c,{variant:"outline",className:"size-8",size:"icon",onClick:()=>a.previousPage(),disabled:!a.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to previous page"}),e.jsx(Pe,{})]}),e.jsxs(c,{variant:"outline",className:"size-8",size:"icon",onClick:()=>a.nextPage(),disabled:!a.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to next page"}),e.jsx(De,{})]}),e.jsxs(c,{variant:"outline",className:"hidden size-8 lg:flex",size:"icon",onClick:()=>a.setPageIndex(a.getPageCount()-1),disabled:!a.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:"Go to last page"}),e.jsx(Te,{})]})]})]})]})]})}export{Xe as D};
