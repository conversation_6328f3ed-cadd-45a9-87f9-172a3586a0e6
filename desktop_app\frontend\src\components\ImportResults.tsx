import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { IconCheck, IconX, IconRefresh, IconDownload, IconEye, IconAlertCircle, IconTable } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { type ProcessingResult } from './BulkImportProcessor'
import { ColumnMapping } from './ColumnMapping'
import { ordersApisCreateOrder } from '@/client'
import { type CreateOrderSchema, type OrderHandlingStatusSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'

interface ImportResultsProps {
  results: ProcessingResult[]
  mapping: ColumnMapping
  onRetryAll: () => void
  onNewImport: () => void
  onViewOrders: () => void
  className?: string
}

export function ImportResults({
  results,
  mapping,
  onRetryAll,
  onNewImport,
  onViewOrders,
  className
}: ImportResultsProps) {
  const { selectedOffice } = useAuth()
  const [retryingRows, setRetryingRows] = useState<Set<string>>(new Set())

  const successResults = results.filter(r => r.success)
  const errorResults = results.filter(r => !r.success)
  const totalResults = results.length

  const mapRowToOrder = (row: Record<string, any>): CreateOrderSchema => {
    // Initialize with all required fields and proper defaults
    const order: CreateOrderSchema = {
      office_id: selectedOffice?.id || '',
      code: '',
      notes: '',
      total_price: null,
      customer_name: '',
      customer_phone: '',
      customer_address: '',
      customer_company_id: null,
      breakable: false,
      deadline_date: null,
      commission_fixed_rate: null,
      assigned_to_id: null,
      final_customer_payment: null,
      handling_status: 'PENDING' as OrderHandlingStatusSchema,
      cancellation_reason_template_id: null,
      cancellation_reason: null,
    }

    Object.entries(mapping).forEach(([fileColumn, orderField]) => {
      if (orderField && row[fileColumn] !== undefined && row[fileColumn] !== null) {
        const value = row[fileColumn]

        switch (orderField) {
          case 'code':
          case 'customer_name':
          case 'customer_phone':
          case 'customer_address':
          case 'notes':
            order[orderField] = String(value).trim()
            break
          case 'total_price':
          case 'commission_fixed_rate':
          case 'final_customer_payment':
            const numValue = parseFloat(String(value))
            order[orderField] = isNaN(numValue) ? null : numValue
            break
          case 'breakable':
            order[orderField] = Boolean(value) || String(value).toLowerCase() === 'true' || String(value) === '1'
            break
          case 'deadline_date':
            try {
              const date = new Date(value)
              order[orderField] = isNaN(date.getTime()) ? null : date.toISOString()
            } catch {
              order[orderField] = null
            }
            break
        }
      }
    })

    return order
  }

  const getResultKey = (result: ProcessingResult) => `${result.sheetName}-${result.rowIndex}`

  const retryFailedRow = async (result: ProcessingResult) => {
    const resultKey = getResultKey(result)
    setRetryingRows(prev => new Set(prev).add(resultKey))

    try {
      const orderData = mapRowToOrder(result.data)
      await ordersApisCreateOrder({
        body: orderData
      })

      // Update the result in the parent component would require a callback
      toast.success(`تم إنشاء الطلب بنجاح للصف ${result.rowIndex + 1} في ورقة ${result.sheetName}`)
    } catch (error: any) {
      let errorMessage = 'خطأ غير معروف'
      if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail
      } else if (error?.message) {
        errorMessage = error.message
      }
      toast.error(`فشل في إنشاء الطلب للصف ${result.rowIndex + 1} في ورقة ${result.sheetName}: ${errorMessage}`)
    } finally {
      setRetryingRows(prev => {
        const newSet = new Set(prev)
        newSet.delete(resultKey)
        return newSet
      })
    }
  }

  const getPhoneNumber = (row: Record<string, any>): string => {
    const phoneColumn = Object.entries(mapping).find(([_, field]) => field === 'customer_phone')?.[0]
    return phoneColumn ? String(row[phoneColumn] || '') : ''
  }

  const getCustomerName = (row: Record<string, any>): string => {
    const nameColumn = Object.entries(mapping).find(([_, field]) => field === 'customer_name')?.[0]
    return nameColumn ? String(row[nameColumn] || '') : ''
  }

  const exportFailedRows = () => {
    const failedData = errorResults.map(result => ({
      'رقم الصف': result.rowIndex + 1,
      'سبب الفشل': result.error,
      ...result.data
    }))

    const csvContent = [
      Object.keys(failedData[0] || {}).join(','),
      ...failedData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `failed_orders_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconCheck className="h-5 w-5 text-green-600" />
            ملخص نتائج الاستيراد
          </CardTitle>
          <CardDescription>
            تم معالجة {totalResults} طلب بإجمالي
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="text-2xl font-bold text-green-600">{successResults.length}</div>
              <div className="text-sm text-green-600">طلب تم إنشاؤه بنجاح</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="text-2xl font-bold text-red-600">{errorResults.length}</div>
              <div className="text-sm text-red-600">طلب فشل في الإنشاء</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">
                {totalResults > 0 ? Math.round((successResults.length / totalResults) * 100) : 0}%
              </div>
              <div className="text-sm text-blue-600">معدل النجاح</div>
            </div>
          </div>

          {errorResults.length > 0 && (
            <Alert variant="destructive" className="mt-4">
              <IconAlertCircle className="h-4 w-4" />
              <AlertDescription>
                يوجد {errorResults.length} طلب فشل في الإنشاء. يمكنك مراجعة الأخطاء وإعادة المحاولة.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-wrap gap-2 mt-6">
            <Button onClick={onViewOrders} className="flex items-center gap-2">
              <IconEye className="h-4 w-4" />
              عرض الطلبات
            </Button>
            <Button variant="outline" onClick={onNewImport}>
              استيراد جديد
            </Button>
            {errorResults.length > 0 && (
              <>
                <Button variant="outline" onClick={onRetryAll} className="flex items-center gap-2">
                  <IconRefresh className="h-4 w-4" />
                  إعادة محاولة الفاشل
                </Button>
                <Button variant="outline" onClick={exportFailedRows} className="flex items-center gap-2">
                  <IconDownload className="h-4 w-4" />
                  تصدير الفاشل
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل النتائج</CardTitle>
          <CardDescription>
            عرض تفصيلي لجميع الطلبات المعالجة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">الكل ({totalResults})</TabsTrigger>
              <TabsTrigger value="success">نجح ({successResults.length})</TabsTrigger>
              <TabsTrigger value="failed">فشل ({errorResults.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ورقة العمل</TableHead>
                      <TableHead>الصف</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>رقم الهاتف</TableHead>
                      <TableHead>التفاصيل</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((result) => (
                      <TableRow key={getResultKey(result)}>
                        <TableCell>
                          <Badge variant="outline" className="flex items-center gap-1 w-fit">
                            <IconTable className="h-3 w-3" />
                            {result.sheetName}
                          </Badge>
                        </TableCell>
                        <TableCell>{result.rowIndex + 1}</TableCell>
                        <TableCell>
                          {result.success ? (
                            <Badge variant="default" className="flex items-center gap-1 w-fit">
                              <IconCheck className="h-3 w-3" />
                              نجح
                            </Badge>
                          ) : (
                            <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                              <IconX className="h-3 w-3" />
                              فشل
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{getCustomerName(result.data)}</TableCell>
                        <TableCell>{getPhoneNumber(result.data)}</TableCell>
                        <TableCell className="max-w-xs">
                          {result.success ? (
                            <span className="text-sm text-green-600">
                              تم إنشاء الطلب بنجاح
                            </span>
                          ) : (
                            <span className="text-sm text-red-600">
                              {result.error}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          {!result.success && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => retryFailedRow(result)}
                              disabled={retryingRows.has(getResultKey(result))}
                              className="flex items-center gap-1"
                            >
                              {retryingRows.has(getResultKey(result)) ? (
                                <IconRefresh className="h-3 w-3 animate-spin" />
                              ) : (
                                <IconRefresh className="h-3 w-3" />
                              )}
                              إعادة المحاولة
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="success" className="mt-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الصف</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>رقم الهاتف</TableHead>
                      <TableHead>معرف الطلب</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {successResults.map((result) => (
                      <TableRow key={result.rowIndex}>
                        <TableCell>{result.rowIndex + 1}</TableCell>
                        <TableCell>{getCustomerName(result.data)}</TableCell>
                        <TableCell>{getPhoneNumber(result.data)}</TableCell>
                        <TableCell className="font-mono text-sm">{result.orderId}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="failed" className="mt-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ورقة العمل</TableHead>
                      <TableHead>الصف</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>رقم الهاتف</TableHead>
                      <TableHead>سبب الفشل</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {errorResults.map((result) => (
                      <TableRow key={getResultKey(result)}>
                        <TableCell>
                          <Badge variant="outline" className="flex items-center gap-1 w-fit">
                            <IconTable className="h-3 w-3" />
                            {result.sheetName}
                          </Badge>
                        </TableCell>
                        <TableCell>{result.rowIndex + 1}</TableCell>
                        <TableCell>{getCustomerName(result.data)}</TableCell>
                        <TableCell>{getPhoneNumber(result.data)}</TableCell>
                        <TableCell className="max-w-xs">
                          <span className="text-sm text-red-600">
                            {result.error}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => retryFailedRow(result)}
                            disabled={retryingRows.has(getResultKey(result))}
                            className="flex items-center gap-1"
                          >
                            {retryingRows.has(getResultKey(result)) ? (
                              <IconRefresh className="h-3 w-3 animate-spin" />
                            ) : (
                              <IconRefresh className="h-3 w-3" />
                            )}
                            إعادة المحاولة
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
