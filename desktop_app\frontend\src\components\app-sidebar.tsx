import * as React from "react"
import {
  IconDashboard,
  IconHelp,
  IconInnerShadowTop,
  IconSearch,
  IconSettings,
  IconShoppingCart,
  IconBuilding,
  IconUsers,
} from "@tabler/icons-react"
import { Link, useNavigate } from "@tanstack/react-router"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import { useAuth } from "@/contexts/AuthContext"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "الرئيسية",
      url: "/",
      icon: IconDashboard,
    },
    {
      title: "الموظفين",
      url: "/employees",
      icon: IconUsers,
    },
    {
      title: "الطلبات",
      url: "/orders",
      icon: IconShoppingCart,
    },

    {
      title: "الشركات",
      url: "/companies",
      icon: IconBuilding,
    },
  ],
  navSecondary: [
    {
      title: "الإعدادات",
      url: "/settings",
      icon: IconSettings,
    },
    {
      title: "المساعدة",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "البحث",
      url: "#",
      icon: IconSearch,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  // Create user data object for NavUser component
  const userData = {
    name: user ? `${user.first_name} ${user.last_name}` : "Guest",
    email: user?.email || "<EMAIL>",
    avatar: "/avatars/default.jpg", // You can add a default avatar or use user avatar if available
  }

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link to="/">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">MyRunway</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* <NavDocuments items={data.documents} /> */}
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} onLogout={() => {
          logout()
          navigate({ to: '/login' })
        }} />
      </SidebarFooter>
    </Sidebar>
  )
}
