import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { IconAlertCircle, IconCheck, IconEye } from '@tabler/icons-react'
import { cn } from '@/lib/utils'

export interface ColumnMapping {
  [fileColumn: string]: string | null // Maps file column to order field
}

export interface OrderField {
  key: string
  label: string
  required: boolean
  type: 'text' | 'number' | 'boolean' | 'date'
}

interface ColumnMappingProps {
  fileColumns: string[]
  sampleData: Record<string, any>[]
  onMappingChange: (mapping: ColumnMapping) => void
  onNext: () => void
  onBack: () => void
  className?: string
}

const ORDER_FIELDS: OrderField[] = [
  { key: 'code', label: 'رمز الطلب', required: true, type: 'text' },
  { key: 'customer_name', label: 'اسم العميل', required: true, type: 'text' },
  { key: 'customer_phone', label: 'رقم الهاتف', required: true, type: 'text' },
  { key: 'customer_address', label: 'العنوان', required: false, type: 'text' },
  { key: 'total_price', label: 'السعر الإجمالي', required: false, type: 'number' },
  { key: 'notes', label: 'ملاحظات', required: false, type: 'text' },
  { key: 'deadline_date', label: 'تاريخ الاستحقاق', required: false, type: 'date' },
  { key: 'commission_fixed_rate', label: 'نسبة العمولة', required: false, type: 'number' },
  { key: 'breakable', label: 'قابل للكسر', required: false, type: 'boolean' },
]

export function ColumnMapping({
  fileColumns,
  sampleData,
  onMappingChange,
  onNext,
  onBack,
  className
}: ColumnMappingProps) {
  const [mapping, setMapping] = useState<ColumnMapping>({})
  const [showPreview, setShowPreview] = useState(false)
  const [errors, setErrors] = useState<string[]>([])

  // Auto-suggest mappings based on column names
  useEffect(() => {
    const autoMapping: ColumnMapping = {}
    
    fileColumns.forEach(column => {
      const lowerColumn = column.toLowerCase()
      
      // Try to match common column names
      if (lowerColumn.includes('code') || lowerColumn.includes('رمز') || lowerColumn.includes('كود')) {
        autoMapping[column] = 'code'
      } else if (lowerColumn.includes('name') || lowerColumn.includes('اسم') || lowerColumn.includes('عميل')) {
        autoMapping[column] = 'customer_name'
      } else if (lowerColumn.includes('phone') || lowerColumn.includes('هاتف') || lowerColumn.includes('تليفون')) {
        autoMapping[column] = 'customer_phone'
      } else if (lowerColumn.includes('address') || lowerColumn.includes('عنوان')) {
        autoMapping[column] = 'customer_address'
      } else if (lowerColumn.includes('price') || lowerColumn.includes('سعر') || lowerColumn.includes('مبلغ')) {
        autoMapping[column] = 'total_price'
      } else if (lowerColumn.includes('note') || lowerColumn.includes('ملاحظ') || lowerColumn.includes('تعليق')) {
        autoMapping[column] = 'notes'
      } else if (lowerColumn.includes('deadline') || lowerColumn.includes('استحقاق') || lowerColumn.includes('موعد')) {
        autoMapping[column] = 'deadline_date'
      } else if (lowerColumn.includes('commission') || lowerColumn.includes('عمولة')) {
        autoMapping[column] = 'commission_fixed_rate'
      } else if (lowerColumn.includes('breakable') || lowerColumn.includes('كسر') || lowerColumn.includes('قابل')) {
        autoMapping[column] = 'breakable'
      }
    })
    
    setMapping(autoMapping)
  }, [fileColumns])

  useEffect(() => {
    onMappingChange(mapping)
    validateMapping()
  }, [mapping, onMappingChange])

  const validateMapping = () => {
    const newErrors: string[] = []
    const requiredFields = ORDER_FIELDS.filter(field => field.required)
    const mappedFields = Object.values(mapping).filter(Boolean)

    requiredFields.forEach(field => {
      if (!mappedFields.includes(field.key)) {
        newErrors.push(`الحقل المطلوب "${field.label}" غير مربوط`)
      }
    })

    // Check for duplicate mappings
    const duplicates = mappedFields.filter((field, index) => 
      mappedFields.indexOf(field) !== index
    )
    if (duplicates.length > 0) {
      newErrors.push('يوجد حقول مكررة في الربط')
    }

    setErrors(newErrors)
  }

  const handleMappingChange = (fileColumn: string, orderField: string | null) => {
    setMapping(prev => ({
      ...prev,
      [fileColumn]: orderField
    }))
  }

  const getUsedFields = () => {
    return Object.values(mapping).filter(Boolean) as string[]
  }

  const isFieldUsed = (fieldKey: string) => {
    return getUsedFields().includes(fieldKey)
  }

  const canProceed = errors.length === 0

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle>ربط الأعمدة</CardTitle>
          <CardDescription>
            اربط أعمدة الملف بحقول الطلب المطلوبة. الحقول المطلوبة مميزة بعلامة *
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {fileColumns.map((column, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="flex-1">
                  <label className="text-sm font-medium">{column}</label>
                  <div className="text-xs text-muted-foreground">
                    عمود من الملف
                  </div>
                </div>
                <div className="flex-1">
                  <Select
                    value={mapping[column] || 'none'}
                    onValueChange={(value) => 
                      handleMappingChange(column, value === 'none' ? null : value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحقل المقابل" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">لا يوجد ربط</SelectItem>
                      {ORDER_FIELDS.map((field) => (
                        <SelectItem 
                          key={field.key} 
                          value={field.key}
                          disabled={isFieldUsed(field.key) && mapping[column] !== field.key}
                        >
                          <div className="flex items-center gap-2">
                            <span>{field.label}</span>
                            {field.required && <Badge variant="destructive" className="text-xs">مطلوب</Badge>}
                            {isFieldUsed(field.key) && mapping[column] !== field.key && (
                              <Badge variant="secondary" className="text-xs">مستخدم</Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}
          </div>

          {errors.length > 0 && (
            <Alert variant="destructive" className="mt-4">
              <IconAlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex items-center gap-2 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2"
            >
              <IconEye className="h-4 w-4" />
              {showPreview ? 'إخفاء المعاينة' : 'معاينة البيانات'}
            </Button>
            {canProceed && (
              <Badge variant="default" className="flex items-center gap-1">
                <IconCheck className="h-3 w-3" />
                جاهز للمتابعة
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {showPreview && sampleData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>معاينة البيانات</CardTitle>
            <CardDescription>
              عرض أول {Math.min(5, sampleData.length)} صفوف من الملف مع الربط المحدد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {Object.entries(mapping)
                      .filter(([_, orderField]) => orderField)
                      .map(([fileColumn, orderField]) => (
                        <TableHead key={fileColumn}>
                          <div>
                            <div className="font-medium">{fileColumn}</div>
                            <div className="text-xs text-muted-foreground">
                              → {ORDER_FIELDS.find(f => f.key === orderField)?.label}
                            </div>
                          </div>
                        </TableHead>
                      ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sampleData.slice(0, 5).map((row, index) => (
                    <TableRow key={index}>
                      {Object.entries(mapping)
                        .filter(([_, orderField]) => orderField)
                        .map(([fileColumn]) => (
                          <TableCell key={fileColumn}>
                            {row[fileColumn] || '-'}
                          </TableCell>
                        ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          السابق
        </Button>
        <Button onClick={onNext} disabled={!canProceed}>
          التالي - بدء الاستيراد
        </Button>
      </div>
    </div>
  )
}
