import{c as I,u as M,r as c,a as T,j as s,B as d,M as $,N as H,Q,S as X,U as J}from"./index-DM6GIfB4.js";import{S as W}from"./site-header-BZV9tMVa.js";import{D as Y}from"./DataTableComponent-BhuAndc5.js";import{B as D}from"./badge-frfbuLAi.js";import{I as u}from"./input-BhcMMszI.js";import{S as x,a as f,b as p,c as g,d as r}from"./select-BCFM5Vl0.js";import{C as Z,a as ee,c as se,f as ae}from"./card-D0yfVNGp.js";import{S as te,t as h}from"./index-B_SQ5DcE.js";import{I as re}from"./IconUser-7feVy3q9.js";import{I as le}from"./IconX-q8FUGPcT.js";import{I as ie}from"./IconLoader2-CgJzOld6.js";import"./sortable.esm-2IxWHftX.js";import"./checkbox-B00BLXKz.js";import"./table-CLheJEBl.js";/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M5 13v-8a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2h-5.5m-9.5 -2h7m-3 -3l3 3l-3 3",key:"svg-1"}]],ne=I("outline","file-import","FileImport",ce);/**
 * @license @tabler/icons-react v3.34.1 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-8.5l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.227z",key:"svg-0"}]],de=I("outline","filter","Filter",oe),Se=function(){const{selectedOffice:i}=M(),[L,k]=c.useState([]),[_,j]=c.useState(!1),[N,A]=c.useState(!0),[F,z]=c.useState([]),[v,w]=c.useState([]),o=T(),[l,y]=c.useState({status:"",search:"",created_at_from:"",created_at_to:"",customer_company_id:"",assigned_to_id:""}),m=async e=>{if(i){j(!0);try{const a={office_id:i.id,page_size:100},t=e||l;t.status&&(a.status=t.status),t.search&&(a.search=t.search),t.created_at_from&&(a.created_at_from=t.created_at_from),t.created_at_to&&(a.created_at_to=t.created_at_to),t.customer_company_id&&(a.customer_company_id=t.customer_company_id),t.assigned_to_id&&(a.assigned_to_id=t.assigned_to_id);const S=await H({query:a});S.data&&k(S.data.orders)}catch(a){console.error("Error fetching orders:",a),h.error("حدث خطأ أثناء تحميل بيانات الطلبات")}finally{j(!1)}}},O=async()=>{if(i)try{const e=await Q({query:{office_id:i.id,page_size:100}});e.data&&z(e.data.companies)}catch(e){console.error("Error fetching companies:",e)}},G=async()=>{if(i)try{const e=await X({query:{office_id:i.id,page_size:100}});e.data&&w(e.data.users)}catch(e){console.error("Error fetching users:",e)}};c.useEffect(()=>{m(),O(),G()},[i]);const n=(e,a)=>{y(t=>({...t,[e]:a}))},C=()=>{m(l)},P=()=>{const e={status:"",search:"",created_at_from:"",created_at_to:"",customer_company_id:"",assigned_to_id:""};y(e),m(e)},R=e=>{o({to:"/orders/form",search:{id:e.id}})},K=e=>{h.info(`عرض تفاصيل الطلب: ${e.code} - ${e.customer_name}`)},V=async e=>{if(confirm("هل أنت متأكد من حذف هذا الطلب؟"))try{await J({path:{order_id:e.id}}),h.success("تم حذف الطلب بنجاح"),m()}catch(a){console.error("Error deleting order:",a),h.error("حدث خطأ أثناء حذف الطلب")}},B=e=>({PENDING:"في الانتظار",ASSIGNED:"تم التعيين",PROCESSING:"قيد المعالجة",CANCELLED:"ملغي",DELIVERED:"تم التسليم"})[e]||e,U=e=>({PENDING:"bg-yellow-100 text-yellow-800",ASSIGNED:"bg-blue-100 text-blue-800",PROCESSING:"bg-orange-100 text-orange-800",CANCELLED:"bg-red-100 text-red-800",DELIVERED:"bg-green-100 text-green-800"})[e]||"bg-gray-100 text-gray-800",b=e=>e?new Date(e).toLocaleDateString("ar-EG",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"-",E=e=>e===null?"-":`${e.toFixed(2)} ج.م`,q=Object.values(l).some(e=>e!=="");return s.jsxs(s.Fragment,{children:[s.jsx(W,{title:"الطلبات"}),s.jsx("div",{className:"flex flex-col m-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"الطلبات"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"هنا يمكنك إدارة الطلبات"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs(d,{variant:"outline",size:"sm",onClick:()=>o({to:"/orders/bulk-import"}),className:"flex items-center gap-2",children:[s.jsx(ne,{size:16}),"استيراد بالجملة"]}),s.jsxs(d,{variant:"outline",size:"sm",onClick:()=>o({to:"/orders/assign"}),className:"flex items-center gap-2",children:[s.jsx(re,{size:16}),"تخصيص سريع"]}),s.jsxs(d,{variant:"outline",size:"sm",onClick:()=>A(!N),className:"flex items-center gap-2",children:[s.jsx(de,{size:16}),"فلاتر",q&&s.jsx(D,{variant:"secondary",className:"ml-1",children:Object.values(l).filter(e=>e!=="").length})]})]})]})}),s.jsx(te,{className:"mb-4"}),N&&s.jsxs(Z,{className:"m-4 mb-6",children:[s.jsx(ee,{children:s.jsx(se,{className:"text-lg",children:"فلاتر البحث"})}),s.jsxs(ae,{children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"البحث"}),s.jsxs("div",{className:"relative",children:[s.jsx($,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:16}),s.jsx(u,{placeholder:"البحث في رمز الطلب، اسم العميل، الهاتف، العنوان...",value:l.search,onChange:e=>n("search",e.target.value),className:"pl-10",onKeyDown:e=>{e.key==="Enter"&&C()}})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"حالة الطلب"}),s.jsxs(x,{value:l.status||"all",onValueChange:e=>n("status",e==="all"?"":e),children:[s.jsx(f,{children:s.jsx(p,{placeholder:"جميع الحالات"})}),s.jsxs(g,{children:[s.jsx(r,{value:"all",children:"جميع الحالات"}),s.jsx(r,{value:"PENDING",children:"في الانتظار"}),s.jsx(r,{value:"ASSIGNED",children:"تم التعيين"}),s.jsx(r,{value:"PROCESSING",children:"قيد المعالجة"}),s.jsx(r,{value:"CANCELLED",children:"ملغي"}),s.jsx(r,{value:"DELIVERED",children:"تم التسليم"})]})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"من تاريخ"}),s.jsx(u,{type:"date",value:l.created_at_from,onChange:e=>n("created_at_from",e.target.value)})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"إلى تاريخ"}),s.jsx(u,{type:"date",value:l.created_at_to,onChange:e=>n("created_at_to",e.target.value)})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"الشركة"}),s.jsxs(x,{value:l.customer_company_id||"all",onValueChange:e=>n("customer_company_id",e==="all"?"":e),children:[s.jsx(f,{children:s.jsx(p,{placeholder:"جميع الشركات"})}),s.jsxs(g,{children:[s.jsx(r,{value:"all",children:"جميع الشركات"}),F.map(e=>s.jsx(r,{value:e.id,children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color_code||"#ccc"}}),e.name]})},e.id))]})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"المسؤول"}),s.jsxs(x,{value:l.assigned_to_id||"all",onValueChange:e=>n("assigned_to_id",e==="all"?"":e),children:[s.jsx(f,{children:s.jsx(p,{placeholder:"جميع المسؤولين"})}),s.jsxs(g,{children:[s.jsx(r,{value:"all",children:"جميع المسؤولين"}),v.length>0?v.map(e=>s.jsxs(r,{value:e.id,children:[e.first_name," ",e.last_name]},e.id)):s.jsx(r,{value:"no-data",disabled:!0,children:"لا توجد بيانات متاحة"})]})]})]})]}),s.jsxs("div",{className:"flex items-center justify-end gap-2 mt-6",children:[s.jsxs(d,{variant:"outline",onClick:P,className:"flex items-center gap-2",children:[s.jsx(le,{size:16}),"مسح الفلاتر"]}),s.jsxs(d,{onClick:C,disabled:_,className:"flex items-center gap-2",children:[_&&s.jsx(ie,{className:"animate-spin",size:16}),"تطبيق الفلاتر"]})]})]})]}),s.jsx("div",{className:"flex flex-col m-4",children:s.jsx(Y,{idField:"id",pageSize:10,enablePagination:!0,showAddButton:!0,addButtonText:"إضافة طلب",onAddClick:()=>{o({to:"/orders/form",search:{id:void 0}})},data:L,columns:[{header:"رمز الطلب",accessorKey:"code",cell:({row:e})=>s.jsxs("div",{className:"flex flex-col cursor-pointer hover:bg-gray-50 p-2 rounded transition",onClick:()=>{o({to:"/orders/form",search:{id:e.original.id}})},title:"تعديل الطلب",children:[s.jsx("span",{className:"font-medium",children:e.original.code}),s.jsx("span",{className:"text-sm text-muted-foreground",children:b(e.original.created_at)})]})},{header:"العميل",accessorKey:"customer_name",cell:({row:e})=>s.jsxs("div",{className:"flex flex-col",children:[s.jsx("span",{className:"font-medium",children:e.original.customer_name}),s.jsx("span",{className:"text-sm text-muted-foreground",children:e.original.customer_phone})]})},{header:"العنوان",accessorKey:"customer_address",cell:({row:e})=>s.jsx("span",{className:"text-sm max-w-xs truncate",children:e.original.customer_address||"-"})},{header:"السعر الإجمالي",accessorKey:"total_price",cell:({row:e})=>s.jsx("span",{className:"font-medium",children:E(e.original.total_price)})},{header:"الدفع النهائي",accessorKey:"final_customer_payment",cell:({row:e})=>s.jsx("span",{className:"font-medium",children:E(e.original.final_customer_payment)})},{header:"الحالة",accessorKey:"handling_status",cell:({row:e})=>s.jsx(D,{className:U(e.original.handling_status),children:B(e.original.handling_status)})},{header:"تاريخ الاستحقاق",accessorKey:"deadline_date",cell:({row:e})=>s.jsx("span",{className:"text-sm",children:b(e.original.deadline_date)})}],actionsColumn:{enableEdit:!1,enableDelete:!1,customActions:[{label:"عرض",onClick:K},{label:"تعديل",onClick:R},{label:"حذف",onClick:V,variant:"destructive"}]}})})]})};export{Se as component};
