# Generated by Django 5.2.4 on 2025-07-19 22:53

import core.utils
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('merchants', '0002_alter_merchant_options_alter_office_options_and_more'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderstatushistory',
            name='status_reason',
        ),
        migrations.RemoveField(
            model_name='order',
            name='status_reason',
        ),
        migrations.RenameField(
            model_name='order',
            old_name='delivery_deadline_date',
            new_name='deadline_date',
        ),
        migrations.RenameField(
            model_name='order',
            old_name='delivery_customer_payment',
            new_name='final_customer_payment',
        ),
        migrations.RemoveField(
            model_name='order',
            name='special_commission_rate',
        ),
        migrations.AddField(
            model_name='order',
            name='cancellation_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='commission_fixed_rate',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Fixed commission rate for the order', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000)]),
        ),
        migrations.AddField(
            model_name='orderstatushistory',
            name='notes',
            field=models.TextField(blank=True, help_text='Notes for the status change, will be the cancellation reason if the status is cancelled'),
        ),
        migrations.AlterField(
            model_name='companychannel',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='channels', to='orders.company'),
        ),
        migrations.AlterField(
            model_name='companychannel',
            name='merchant',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_channels', to='merchants.merchant'),
        ),
        migrations.AlterField(
            model_name='companychannel',
            name='office',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_channels', to='merchants.office'),
        ),
        migrations.AlterField(
            model_name='order',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='assigned_orders', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='order',
            name='code',
            field=models.CharField(help_text='The shipment to which the order belongs', max_length=255),
        ),
        migrations.AlterField(
            model_name='order',
            name='customer_company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='orders.company'),
        ),
        migrations.AlterField(
            model_name='order',
            name='customer_phone',
            field=models.CharField(help_text='List of customer phone numbers separated by `-`', max_length=500),
        ),
        migrations.AlterField(
            model_name='order',
            name='handling_status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('DELIVERED', 'Delivered')], default='PENDING', max_length=20),
        ),
        migrations.AlterField(
            model_name='order',
            name='office',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='merchants.office'),
        ),
        migrations.AlterField(
            model_name='orderassigneehistory',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignee_history', to='orders.order'),
        ),
        migrations.AlterField(
            model_name='orderstatushistory',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_status_changes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='orderstatushistory',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='orders.order'),
        ),
        migrations.AlterField(
            model_name='orderstatushistory',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('DELIVERED', 'Delivered')], max_length=20),
        ),
        migrations.CreateModel(
            name='OrderCancellationReasonTemplate',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('order_default_handling_status', models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('DELIVERED', 'Delivered')], max_length=20, null=True)),
                ('just_delivery_commission_rate', models.BooleanField(default=True)),
                ('commission_fixed_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('percentage_of_order_total_price', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('office', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cancellation_templates', to='merchants.office')),
            ],
            options={
                'verbose_name': 'Order Cancellation Reason Template',
                'verbose_name_plural': 'Order Cancellation Reason Templates',
                'db_table': 'order_cancellation_reason_templates',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='cancellation_reason_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='orders.ordercancellationreasontemplate'),
        ),
        migrations.DeleteModel(
            name='OrderDeliveryStatus',
        ),
    ]
