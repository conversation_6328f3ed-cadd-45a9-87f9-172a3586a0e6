import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useEffect, useState } from 'react'
import { ordersApisGetOrder, ordersApisCreateOrder, ordersApisUpdateOrder, ordersApisListCancellationTemplates, ordersApisListCompanies, ordersApisCreateCompany } from '@/client'
import { type OrderSchema, type OrderHandlingStatusSchema, type OrderCancellationReasonTemplateSchema, type CompanySchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'

export const Route = createFileRoute('/orders/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
    }),
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const { id } = useSearch({ from: '/orders/form' })
    const [loading, setLoading] = useState(false)
    const [cancellationTemplates, setCancellationTemplates] = useState<OrderCancellationReasonTemplateSchema[]>([])
    const [companies, setCompanies] = useState<CompanySchema[]>([])
    const [showCompanyDialog, setShowCompanyDialog] = useState(false)
    const [newCompany, setNewCompany] = useState({
        code: '',
        name: '',
        address: '',
        phone: '',
        color_code: '#3B82F6',
    })
    const [creatingCompany, setCreatingCompany] = useState(false)
    const [order, setOrder] = useState<Partial<OrderSchema>>({
        code: '',
        notes: '',
        total_price: null,
        customer_name: '',
        customer_phone: '',
        customer_address: '',
        customer_company_id: null,
        breakable: false,
        deadline_date: null,
        commission_fixed_rate: null,
        assigned_to_id: null,
        final_customer_payment: null,
        handling_status: 'PENDING' as OrderHandlingStatusSchema,
        cancellation_reason_template_id: null,
        cancellation_reason: null,
    })

    const isEditing = !!id

    useEffect(() => {
        fetchCancellationTemplates()
        fetchCompanies()
        if (isEditing && id) {
            fetchOrder()
        }
    }, [id])

    const fetchCancellationTemplates = async () => {
        try {
            const response = await ordersApisListCancellationTemplates({
                query: {
                    office_id: selectedOffice?.id,
                    page_size: 100,
                },
            })
            if (response.data) {
                setCancellationTemplates(response.data.templates)
            }
        } catch (error) {
            console.error('Error fetching cancellation templates:', error)
            toast.error('حدث خطأ أثناء تحميل قوالب الإلغاء')
        }
    }

    const fetchCompanies = async () => {
        try {
            const response = await ordersApisListCompanies({
                query: {
                    office_id: selectedOffice?.id,
                    page_size: 100,
                },
            })
            if (response.data) {
                setCompanies(response.data.companies)
            }
        } catch (error) {
            console.error('Error fetching companies:', error)
            toast.error('حدث خطأ أثناء تحميل الشركات')
        }
    }

    const fetchOrder = async () => {
        try {
            setLoading(true)
            const response = await ordersApisGetOrder({
                path: {
                    order_id: id!,
                },
            })
            if (response.data) {
                setOrder(response.data)
            }
        } catch (error) {
            console.error('Error fetching order:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الطلب')
        } finally {
            setLoading(false)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!selectedOffice) {
            toast.error('يرجى اختيار مكتب')
            return
        }

        try {
            setLoading(true)

            if (isEditing) {
                await ordersApisUpdateOrder({
                    path: { order_id: id! },
                    body: {
                        code: order.code!,
                        notes: order.notes!,
                        total_price: order.total_price ?? null,
                        customer_name: order.customer_name!,
                        customer_phone: order.customer_phone!,
                        customer_address: order.customer_address ?? '',
                        customer_company_id: order.customer_company_id ?? null,
                        breakable: order.breakable!,
                        deadline_date: order.deadline_date ?? null,
                        commission_fixed_rate: order.commission_fixed_rate ?? null,
                        assigned_to_id: order.assigned_to_id ?? null,
                        final_customer_payment: order.final_customer_payment ?? null,
                        handling_status: order.handling_status!,
                        cancellation_reason_template_id: order.cancellation_reason_template_id ?? null,
                        cancellation_reason: order.cancellation_reason ?? null,
                    },
                })
                toast.success('تم تحديث الطلب بنجاح')
            } else {
                await ordersApisCreateOrder({
                    body: {
                        office_id: selectedOffice.id,
                        code: order.code!,
                        notes: order.notes!,
                        total_price: order.total_price ?? null,
                        customer_name: order.customer_name!,
                        customer_phone: order.customer_phone!,
                        customer_address: order.customer_address ?? '',
                        customer_company_id: order.customer_company_id ?? null,
                        breakable: order.breakable!,
                        deadline_date: order.deadline_date ?? null,
                        commission_fixed_rate: order.commission_fixed_rate ?? null,
                        assigned_to_id: order.assigned_to_id ?? null,
                        final_customer_payment: null, // Always null when creating
                        handling_status: order.handling_status!,
                        cancellation_reason_template_id: order.cancellation_reason_template_id ?? null,
                        cancellation_reason: order.cancellation_reason ?? null,
                    },
                })
                toast.success('تم إنشاء الطلب بنجاح')
            }

            navigate({ to: '/orders' })
        } catch (error) {
            console.error('Error saving order:', error)
            toast.error('حدث خطأ أثناء حفظ الطلب')
        } finally {
            setLoading(false)
        }
    }

    const handleInputChange = (field: keyof OrderSchema, value: any) => {
        setOrder(prev => ({ ...prev, [field]: value }))
    }

    const handleCancellationTemplateChange = async (templateId: string) => {
        if (!isEditing || !id) {
            toast.error('لا يمكن تطبيق قالب الإلغاء إلا عند تعديل طلب موجود')
            return
        }

        const template = cancellationTemplates.find(t => t.id === templateId)
        if (!template) return

        try {
            setLoading(true)

            await ordersApisUpdateOrder({
                path: { order_id: id },
                body: {
                    code: order.code!,
                    notes: order.notes!,
                    total_price: order.total_price ?? null,
                    customer_name: order.customer_name!,
                    customer_phone: order.customer_phone!,
                    customer_address: order.customer_address ?? '',
                    customer_company_id: order.customer_company_id ?? null,
                    breakable: order.breakable!,
                    deadline_date: order.deadline_date ?? null,
                    commission_fixed_rate: order.commission_fixed_rate ?? null,
                    assigned_to_id: order.assigned_to_id ?? null,
                    final_customer_payment: order.final_customer_payment ?? null,
                    handling_status: template.order_default_handling_status || 'CANCELLED',
                    cancellation_reason_template_id: template.id,
                    cancellation_reason: template.description,
                },
            })

            toast.success('تم تطبيق قالب الإلغاء بنجاح')
            navigate({ to: '/orders' })
        } catch (error) {
            console.error('Error applying cancellation template:', error)
            toast.error('حدث خطأ أثناء تطبيق قالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }

    const handleCreateCompany = async () => {
        if (!selectedOffice) {
            toast.error('يرجى اختيار مكتب')
            return
        }

        if (!newCompany.code.trim() || !newCompany.name.trim() || !newCompany.address.trim() || !newCompany.phone.trim()) {
            toast.error('يرجى ملء جميع الحقول المطلوبة')
            return
        }

        try {
            setCreatingCompany(true)
            const response = await ordersApisCreateCompany({
                body: {
                    office_id: selectedOffice.id,
                    code: newCompany.code,
                    name: newCompany.name,
                    address: newCompany.address,
                    phone: newCompany.phone,
                    color_code: newCompany.color_code,
                },
            })

            if (response.data) {
                toast.success('تم إنشاء الشركة بنجاح')
                setOrder(prev => ({ ...prev, customer_company_id: response.data.id }))
                setShowCompanyDialog(false)
                setNewCompany({
                    code: '',
                    name: '',
                    address: '',
                    phone: '',
                    color_code: '#3B82F6',
                })
                fetchCompanies() // Refresh the companies list
            }
        } catch (error) {
            console.error('Error creating company:', error)
            toast.error('حدث خطأ أثناء إنشاء الشركة')
        } finally {
            setCreatingCompany(false)
        }
    }

    const formatDateForInput = (dateString: string | null | undefined) => {
        if (!dateString) return ''
        const date = new Date(dateString)
        return date.toISOString().slice(0, 16) // Format for datetime-local input
    }

    const parseDateFromInput = (dateString: string) => {
        if (!dateString) return null
        return new Date(dateString).toISOString()
    }

    return <>
        <SiteHeader title={"إدارة الطلبات"} />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">{isEditing ? "تعديل الطلب" : "إضافة طلب جديد"}</h1>
            <p className="text-sm text-muted-foreground">
                {isEditing ? "قم بتعديل بيانات الطلب" : "أدخل بيانات الطلب الجديد"}
            </p>
        </div>
        <Separator className="mb-4" />

        <form onSubmit={handleSubmit} className="flex flex-col m-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">المعلومات الأساسية</h3>

                    <div className="space-y-2">
                        <Label htmlFor="code">رمز الطلب *</Label>
                        <Input
                            id="code"
                            value={order.code || ''}
                            onChange={(e) => handleInputChange('code', e.target.value)}
                            required
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_name">اسم العميل *</Label>
                        <Input
                            id="customer_name"
                            value={order.customer_name || ''}
                            onChange={(e) => handleInputChange('customer_name', e.target.value)}
                            required
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_phone">رقم الهاتف *</Label>
                        <Input
                            id="customer_phone"
                            value={order.customer_phone || ''}
                            onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                            required
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_address">العنوان</Label>
                        <Textarea
                            id="customer_address"
                            value={order.customer_address || ''}
                            onChange={(e) => handleInputChange('customer_address', e.target.value)}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_company">الشركة</Label>
                        <div className="flex gap-2">
                            <Select
                                value={order.customer_company_id || 'none'}
                                onValueChange={(value) => handleInputChange('customer_company_id', value === 'none' ? null : value)}
                                disabled={loading}
                            >
                                <SelectTrigger className="flex-1">
                                    <SelectValue placeholder="اختر الشركة (اختياري)" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">بدون شركة</SelectItem>
                                    {companies.map((company) => (
                                        <SelectItem key={company.id} value={company.id}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: company.color_code }}
                                                />
                                                <span>{company.name}</span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Dialog open={showCompanyDialog} onOpenChange={setShowCompanyDialog}>
                                <DialogTrigger asChild>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        disabled={loading}
                                        className="whitespace-nowrap"
                                    >
                                        إضافة شركة
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-[425px]">
                                    <DialogHeader>
                                        <DialogTitle>إضافة شركة جديدة</DialogTitle>
                                        <DialogDescription>
                                            أضف شركة جديدة لربطها بالطلب
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="grid gap-4 py-4">
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="company_code" className="text-right">
                                                الرمز
                                            </Label>
                                            <Input
                                                id="company_code"
                                                value={newCompany.code}
                                                onChange={(e) => setNewCompany(prev => ({ ...prev, code: e.target.value }))}
                                                className="col-span-3"
                                                placeholder="مثال: COMP001"
                                            />
                                        </div>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="company_name" className="text-right">
                                                الاسم
                                            </Label>
                                            <Input
                                                id="company_name"
                                                value={newCompany.name}
                                                onChange={(e) => setNewCompany(prev => ({ ...prev, name: e.target.value }))}
                                                className="col-span-3"
                                                placeholder="اسم الشركة"
                                            />
                                        </div>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="company_address" className="text-right">
                                                العنوان
                                            </Label>
                                            <Input
                                                id="company_address"
                                                value={newCompany.address}
                                                onChange={(e) => setNewCompany(prev => ({ ...prev, address: e.target.value }))}
                                                className="col-span-3"
                                                placeholder="عنوان الشركة"
                                            />
                                        </div>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="company_phone" className="text-right">
                                                الهاتف
                                            </Label>
                                            <Input
                                                id="company_phone"
                                                value={newCompany.phone}
                                                onChange={(e) => setNewCompany(prev => ({ ...prev, phone: e.target.value }))}
                                                className="col-span-3"
                                                placeholder="رقم الهاتف"
                                            />
                                        </div>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="company_color" className="text-right">
                                                اللون
                                            </Label>
                                            <div className="col-span-3 flex gap-2">
                                                <input
                                                    type="color"
                                                    value={newCompany.color_code}
                                                    onChange={(e) => setNewCompany(prev => ({ ...prev, color_code: e.target.value }))}
                                                    className="w-12 h-10 rounded border"
                                                />
                                                <Input
                                                    value={newCompany.color_code}
                                                    onChange={(e) => setNewCompany(prev => ({ ...prev, color_code: e.target.value }))}
                                                    className="flex-1"
                                                    placeholder="#3B82F6"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <DialogFooter>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => setShowCompanyDialog(false)}
                                            disabled={creatingCompany}
                                        >
                                            إلغاء
                                        </Button>
                                        <Button
                                            type="button"
                                            onClick={handleCreateCompany}
                                            disabled={creatingCompany}
                                        >
                                            {creatingCompany ? 'جاري الإنشاء...' : 'إنشاء الشركة'}
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>
                        </div>
                    </div>
                </div>

                {/* Financial Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">المعلومات المالية</h3>

                    <div className="space-y-2">
                        <Label htmlFor="total_price">السعر الإجمالي</Label>
                        <Input
                            id="total_price"
                            type="number"
                            step="0.01"
                            value={order.total_price || ''}
                            onChange={(e) => handleInputChange('total_price', e.target.value ? parseFloat(e.target.value) : null)}
                            disabled={loading}
                        />
                    </div>

                    {isEditing && (
                        <div className="space-y-2">
                            <Label htmlFor="final_customer_payment">الدفع النهائي</Label>
                            <Input
                                id="final_customer_payment"
                                type="number"
                                step="0.01"
                                value={order.final_customer_payment || ''}
                                onChange={(e) => handleInputChange('final_customer_payment', e.target.value ? parseFloat(e.target.value) : null)}
                                disabled={loading}
                            />
                        </div>
                    )}

                    <div className="space-y-2">
                        <Label htmlFor="commission_fixed_rate">نسبة العمولة</Label>
                        <Input
                            id="commission_fixed_rate"
                            type="number"
                            step="0.01"
                            value={order.commission_fixed_rate || ''}
                            onChange={(e) => handleInputChange('commission_fixed_rate', e.target.value ? parseFloat(e.target.value) : null)}
                            disabled={loading}
                        />
                    </div>
                </div>
            </div>

            {/* Order Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">تفاصيل الطلب</h3>

                    <div className="space-y-2">
                        <Label htmlFor="notes">ملاحظات</Label>
                        <Textarea
                            id="notes"
                            value={order.notes || ''}
                            onChange={(e) => handleInputChange('notes', e.target.value)}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="deadline_date">تاريخ الاستحقاق</Label>
                        <Input
                            id="deadline_date"
                            type="datetime-local"
                            value={formatDateForInput(order.deadline_date)}
                            onChange={(e) => handleInputChange('deadline_date', parseDateFromInput(e.target.value))}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="handling_status">حالة الطلب *</Label>
                        {isEditing ? (
                            <Select
                                value={order.handling_status || 'PENDING'}
                                onValueChange={(value) => handleInputChange('handling_status', value as OrderHandlingStatusSchema)}
                                disabled={loading}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="PENDING">في الانتظار</SelectItem>
                                    <SelectItem value="ASSIGNED">تم التعيين</SelectItem>
                                    <SelectItem value="PROCESSING">قيد المعالجة</SelectItem>
                                    <SelectItem value="CANCELLED">ملغي</SelectItem>
                                    <SelectItem value="DELIVERED">تم التسليم</SelectItem>
                                </SelectContent>
                            </Select>
                        ) : (
                            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md border">
                                <span className="text-sm font-medium text-gray-700">في الانتظار</span>
                                <span className="text-xs text-gray-500">(سيتم تعيينها تلقائياً)</span>
                            </div>
                        )}
                    </div>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">خيارات إضافية</h3>

                    <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="scale-110">
                            <Checkbox
                                id="breakable"
                                checked={order.breakable || false}
                                onCheckedChange={(checked) => handleInputChange('breakable', checked)}
                                disabled={loading}
                            />
                        </div>
                        <Label htmlFor="breakable" className="mr-2">قابل للكسر</Label>
                    </div>

                    {isEditing && (
                        <>
                            <div className="space-y-2">
                                <Label htmlFor="cancellation_template">قالب سبب الإلغاء</Label>
                                <Select
                                    onValueChange={handleCancellationTemplateChange}
                                    disabled={loading}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="اختر قالب سبب الإلغاء" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {cancellationTemplates.map((template) => (
                                            <SelectItem key={template.id} value={template.id}>
                                                {template.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-sm text-muted-foreground">
                                    اختيار قالب سيؤدي إلى تحديث حالة الطلب وإلغائه تلقائياً
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="cancellation_reason">سبب الإلغاء المخصص</Label>
                                <Textarea
                                    id="cancellation_reason"
                                    value={order.cancellation_reason || ''}
                                    onChange={(e) => handleInputChange('cancellation_reason', e.target.value)}
                                    disabled={loading}
                                    placeholder="اكتب سبب الإلغاء يدوياً (اختياري)"
                                />
                            </div>
                        </>
                    )}
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 space-x-reverse">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate({ to: '/orders' })}
                    disabled={loading}
                >
                    إلغاء
                </Button>
                <Button type="submit" disabled={loading}>
                    {loading ? 'جاري الحفظ...' : (isEditing ? 'تحديث الطلب' : 'إنشاء الطلب')}
                </Button>
            </div>
        </form>
    </>
} 