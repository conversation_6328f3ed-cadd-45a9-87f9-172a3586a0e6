import{a9 as I,u as L,a as M,r as l,aa as R,j as e,B as g,a2 as k,ab as H}from"./index-DM6GIfB4.js";import{S as G}from"./site-header-BZV9tMVa.js";import{I as m}from"./input-BhcMMszI.js";import{L as x}from"./label-D7aoP4f7.js";import{C as N,a as y,c as v,b as C,f as _}from"./card-D0yfVNGp.js";import{t,S as O}from"./index-B_SQ5DcE.js";import{B as w}from"./badge-frfbuLAi.js";const V=function(){const{id:n}=I.useSearch(),{selectedOffice:d}=L(),h=M(),[p,j]=l.useState(!1),[o,S]=l.useState(!1),[r,u]=l.useState({office_id:"",code:"",name:"",address:"",phone:"",color_code:"#3B82F6"}),[a,b]=l.useState({});l.useEffect(()=>{d!=null&&d.id&&u(s=>({...s,office_id:d.id}))},[d]),l.useEffect(()=>{n&&(S(!0),$())},[n]);const c=s=>{const{name:i,value:A}=s.target;u(f=>({...f,[i]:A})),a[i]&&b(f=>({...f,[i]:""}))},E=()=>{const s={};return r.code.trim()||(s.code="رمز الشركة مطلوب"),r.name.trim()||(s.name="اسم الشركة مطلوب"),r.address.trim()||(s.address="العنوان مطلوب"),r.phone.trim()||(s.phone="رقم الهاتف مطلوب"),r.color_code.trim()||(s.color_code="لون الشركة مطلوب"),b(s),Object.keys(s).length===0},X=async s=>{if(s.preventDefault(),!E()){t.error("يرجى تصحيح الأخطاء في النموذج");return}j(!0);try{o?await D():await B()}catch(i){console.error("Error saving company:",i),t.error("حدث خطأ أثناء حفظ الشركة")}finally{j(!1)}},B=async()=>{(await k({body:{...r}})).response.status===200?(t.success("تم إنشاء الشركة بنجاح"),h({to:"/companies"})):t.error("حدث خطأ أثناء إنشاء الشركة")},D=async()=>{(await H({path:{company_id:n},body:{...r}})).response.status===200?(t.success("تم تحديث الشركة بنجاح"),h({to:"/companies"})):t.error("حدث خطأ أثناء تحديث الشركة")},F=()=>{h({to:"/companies"})},$=async()=>{if(n)try{const s=await R({path:{company_id:n}});if(!s.data)return;u({office_id:s.data.office_id,code:s.data.code||"",name:s.data.name,address:s.data.address,phone:s.data.phone,color_code:s.data.color_code})}catch(s){console.error("Error fetching company data:",s),t.error("حدث خطأ أثناء تحميل بيانات الشركة")}};return e.jsxs(e.Fragment,{children:[e.jsx(G,{title:"الشركات"}),e.jsxs("div",{className:"flex min-h-screen pt-16",children:[e.jsx("div",{className:"hidden lg:flex lg:w-80",children:e.jsx("div",{className:"w-full p-8 pl-0",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:o?"تعديل شركة":"إضافة شركة جديدة"}),e.jsx("p",{className:"text-gray-600 text-sm",children:o?"قم بتعديل معلومات الشركة أدناه":"املأ النموذج أدناه لإضافة شركة جديدة للنظام"})]}),e.jsx(O,{}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"💡 نصائح"}),e.jsxs("ul",{className:"text-xs text-blue-800 space-y-1",children:[e.jsx("li",{children:"• تأكد من صحة رمز الشركة"}),e.jsx("li",{children:"• أدخل رقم هاتف صحيح"}),e.jsx("li",{children:"• اختر لون مميز للشركة"})]})]})]})})}),e.jsx("div",{className:"flex-1 p-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"lg:hidden mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:o?"تعديل شركة":"إضافة شركة جديدة"}),e.jsx("p",{className:"text-gray-600",children:o?"قم بتعديل معلومات الشركة أدناه":"املأ النموذج أدناه لإضافة شركة جديدة للنظام"})]}),e.jsxs("form",{className:"space-y-5",onSubmit:X,children:[e.jsxs(N,{children:[e.jsxs(y,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(w,{variant:"secondary",className:"bg-blue-100 text-blue-700",children:"المعلومات الأساسية"}),e.jsx(v,{className:"text-lg",children:"معلومات الشركة"})]}),e.jsx(C,{children:"أدخل المعلومات الأساسية للشركة"})]}),e.jsxs(_,{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(x,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"رمز الشركة"]}),e.jsx(m,{name:"code",value:r.code,onChange:c,placeholder:"مثال: COMP001",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.code?"border-red-500":""}`}),a.code&&e.jsx("p",{className:"text-red-500 text-xs",children:a.code})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(x,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"اسم الشركة"]}),e.jsx(m,{name:"name",value:r.name,onChange:c,placeholder:"أدخل اسم الشركة",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.name?"border-red-500":""}`}),a.name&&e.jsx("p",{className:"text-red-500 text-xs",children:a.name})]})]})]}),e.jsxs(N,{children:[e.jsxs(y,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(w,{variant:"secondary",className:"bg-green-100 text-green-700",children:"معلومات الاتصال"}),e.jsx(v,{className:"text-lg",children:"معلومات الاتصال والعنوان"})]}),e.jsx(C,{children:"أدخل معلومات الاتصال والعنوان"})]}),e.jsxs(_,{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(x,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"رقم الهاتف"]}),e.jsx(m,{name:"phone",value:r.phone,onChange:c,placeholder:"+966 5X XXX XXXX",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.phone?"border-red-500":""}`}),a.phone&&e.jsx("p",{className:"text-red-500 text-xs",children:a.phone})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(x,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"لون الشركة"]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"color",name:"color_code",value:r.color_code,onChange:c,className:"w-16 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"}),e.jsx(m,{name:"color_code",value:r.color_code,onChange:c,placeholder:"#3B82F6",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.color_code?"border-red-500":""}`})]}),a.color_code&&e.jsx("p",{className:"text-red-500 text-xs",children:a.color_code})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(x,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"العنوان"]}),e.jsx(m,{name:"address",value:r.address,onChange:c,placeholder:"أدخل عنوان الشركة",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.address?"border-red-500":""}`}),a.address&&e.jsx("p",{className:"text-red-500 text-xs",children:a.address})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6",children:[e.jsx(g,{type:"button",variant:"outline",onClick:F,disabled:p,children:"إلغاء"}),e.jsx(g,{type:"submit",disabled:p,children:p?"جاري المعالجة...":o?"تحديث الشركة":"إضافة الشركة"})]})]})]})})]})]})};export{V as component};
