import{u as h,ag as p,r as i,a as f,j as e,ah as u,ai as x}from"./index-DM6GIfB4.js";import{S as C}from"./site-header-BZV9tMVa.js";import{S as y,t as o}from"./index-B_SQ5DcE.js";import{D as b}from"./DataTableComponent-BhuAndc5.js";import"./sortable.esm-2IxWHftX.js";import"./checkbox-B00BLXKz.js";import"./select-BCFM5Vl0.js";import"./table-CLheJEBl.js";const w=function(){const{selectedOffice:n}=h(),{company_id:s}=p.useSearch(),[l,d]=i.useState([]),r=f(),c=async()=>{var a;try{const t=await u({query:{office_id:n.id,company_id:s}});d(((a=t.data)==null?void 0:a.channels)||[])}catch(t){console.error("Error fetching channels:",t),o.error("حدث خطأ أثناء تحميل بيانات القنوات")}};i.useEffect(()=>{c()},[n,s]);const m=async a=>{if(confirm("هل أنت متأكد من حذف هذه القناة؟"))try{await x({path:{channel_id:a.id}}),o.success("تم حذف القناة بنجاح"),c()}catch(t){console.error("Error deleting channel:",t),o.error("حدث خطأ أثناء حذف القناة")}};return e.jsxs(e.Fragment,{children:[e.jsx(C,{title:"قنوات الشركة"}),e.jsxs("div",{className:"flex flex-col m-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"قنوات الشركة"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"هنا يمكنك إدارة قنوات الشركة"})]}),e.jsx(y,{className:"mb-4"}),e.jsx(b,{idField:"id",pageSize:10,enablePagination:!1,showAddButton:!0,addButtonText:"إضافة قناة",onAddClick:()=>{r({to:"/companies/channels/form",search:{id:void 0,company_id:s}})},data:l,columns:[{header:"اسم القناة",accessorKey:"name"},{header:"رقم الواتساب",accessorKey:"channel_whatsapp_number"},{header:"الملاحظات",accessorKey:"notes"}],actionsColumn:{enableEdit:!1,enableDelete:!1,customActions:[{label:"تعديل",onClick:a=>{r({to:"/companies/channels/form",search:{id:a.id,company_id:s}})}},{label:"حذف",onClick:m,variant:"destructive"}]}})]})};export{w as component};
