import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useAuth } from "@/contexts/AuthContext"
import { useState, useCallback, useMemo } from "react"
import { merchantsApisListEmployeesOfficesByPhoneNumber, type MerchantsApisListEmployeesOfficesByPhoneNumberResponse } from "@/client"
import { useNavigate } from "@tanstack/react-router"
import { AlertCircle, Loader2, Building2, ArrowLeftRight, ArrowLeft } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

// Types for better type safety
interface LoginFormData {
  phoneNumber: string
  password: string
}

interface FormErrors {
  phoneNumber?: string
  password?: string
  general?: string
}

type FormState = "idle" | "loading" | "error" | "officeSelecting"

// Validation functions
const validatePhoneNumber = (phoneNumber: string): string | undefined => {
  if (!phoneNumber.trim()) {
    return "رقم الهاتف مطلوب"
  }
  if (!/^01[0-2,5]{1}[0-9]{8}$/.test(phoneNumber.trim())) {
    return "رقم الهاتف غير صحيح"
  }
  return undefined
}

const validatePassword = (password: string): string | undefined => {
  if (!password.trim()) {
    return "كلمة المرور مطلوبة"
  }
  if (password.length < 6) {
    return "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
  }
  return undefined
}

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  const { login, isAuthenticated, isLoading: authLoading } = useAuth()
  const navigate = useNavigate()

  // Form state management
  const [formData, setFormData] = useState<LoginFormData>({
    phoneNumber: "",
    password: "",
  })
  const [formState, setFormState] = useState<FormState>("idle")
  const [errors, setErrors] = useState<FormErrors>({})
  const [offices, setOffices] = useState<MerchantsApisListEmployeesOfficesByPhoneNumberResponse["offices"]>([])

  // Redirect if already authenticated
  if (isAuthenticated) {
    navigate({ to: "/dashboard" })
    return null
  }

  // Memoized validation
  const formErrors = useMemo((): FormErrors => {
    const newErrors: FormErrors = {}

    const phoneError = validatePhoneNumber(formData.phoneNumber)
    if (phoneError) newErrors.phoneNumber = phoneError

    const passwordError = validatePassword(formData.password)
    if (passwordError) newErrors.password = passwordError

    return newErrors
  }, [formData.phoneNumber, formData.password])

  // Check if form is valid
  const isFormValid = useMemo(() => {
    return Object.keys(formErrors).length === 0 &&
      formData.phoneNumber.trim() &&
      formData.password.trim()
  }, [formErrors, formData])

  // Handle input changes
  const handleInputChange = useCallback((field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // Clear general error when user makes changes
    if (errors.general) {
      setErrors(prev => ({ ...prev, general: undefined }))
    }
  }, [errors])

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // Validate form
    if (!isFormValid) {
      setErrors(formErrors)
      return
    }

    try {
      setFormState("loading")
      setErrors({})

      const response = await merchantsApisListEmployeesOfficesByPhoneNumber({
        query: {
          employee_phone_number: formData.phoneNumber.trim(),
        },
      })

      const officeList = response.data?.offices ?? []

      if (officeList.length > 0) {
        setOffices(officeList)
        setFormState("officeSelecting")
      } else {
        setErrors({
          general: "لا يوجد مكاتب مرتبطة بهذا الرقم، يرجى التأكد من صحة البيانات"
        })
        setFormState("error")
      }
    } catch (error) {
      console.error("Error fetching offices:", error)
      setErrors({
        general: "حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى"
      })
      setFormState("error")
    }
  }, [formData, isFormValid, formErrors])

  // Handle office selection
  const handleOfficeSelect = useCallback(async (officeId: string) => {
    try {
      setFormState("loading")
      await login(formData.phoneNumber.trim(), formData.password, officeId)
      navigate({ to: "/dashboard" })
    } catch (error) {
      console.error("Login error:", error)
      setErrors({
        general: error instanceof Error ? error.message : "حدث خطأ في تسجيل الدخول"
      })
      setFormState("error")
    }
  }, [login, formData, navigate])

  // Loading state
  if (formState === "loading" || authLoading) {
    return (
      <div className={cn("flex flex-col items-center justify-center gap-4 p-8", className)}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">جاري التحميل...</p>
      </div>
    )
  }

  // Office selection state
  if (formState === "officeSelecting") {
    return (
      <div className={cn("flex flex-col gap-6", className)}>
        <div className="flex flex-col items-center gap-2 text-center">
          <Building2 className="h-8 w-8 text-primary" />
          <h2 className="text-xl font-semibold">اختر المكتب</h2>
          <p className="text-sm text-muted-foreground">
            اختر المكتب الذي تريد تسجيل الدخول إليه
          </p>
        </div>

        <div className="space-y-3">
          {offices.map((office) => (
            <Button
              key={office.id}
              className="w-full justify-start gap-3 text-lg h-14"
              variant="outline"
              type="button"
              onClick={() => handleOfficeSelect(office.id)}
              disabled={false}
            >
              <Building2 className="h-10 w-10" />
              <div className="flex flex-row items-center justify-between w-full">
                <div className="flex flex-col items-start">
                  <span className="font-medium">{office.name}</span>
                  <span className="text-xs text-muted-foreground">{office.address}</span>
                </div>
                <div className="flex flex-col items-end">
                  <ArrowLeft className="h-4 w-4" />
                </div>
              </div>
            </Button>
          ))}
        </div>

        <Button
          variant="ghost"
          onClick={() => {
            setFormState("idle")
            setErrors({})
          }}
          className="w-full"
        >
          العودة
        </Button>
      </div>
    )
  }

  return (
    <form className={cn("flex flex-col gap-6", className)} onSubmit={handleSubmit} {...props}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">سجل دخولك</h1>
        <p className="text-muted-foreground text-sm text-balance">
          أدخل رقم هاتفك وكلمة المرور لتسجيل الدخول
        </p>
      </div>

      {/* Error Alert */}
      {errors.general && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errors.general}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6">
        <div className="grid gap-3">
          <Label htmlFor="phoneNumber" className="text-sm font-medium">
            رقم الهاتف
          </Label>
          <Input
            id="phoneNumber"
            name="phoneNumber"
            type="tel"
            placeholder="01000000000"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
            className={cn(errors.phoneNumber && "border-destructive focus-visible:ring-destructive")}
            aria-describedby={errors.phoneNumber ? "phoneNumber-error" : undefined}
            autoComplete="tel"
            required
          />
          {errors.phoneNumber && (
            <p id="phoneNumber-error" className="text-sm text-destructive">
              {errors.phoneNumber}
            </p>
          )}
        </div>

        <div className="grid gap-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="text-sm font-medium">
              كلمة المرور
            </Label>
            <Dialog>
              <DialogTrigger asChild>
                <button
                  type="button"
                  className="text-sm text-muted-foreground underline-offset-4 hover:underline hover:text-foreground transition-colors"
                >
                  نسيت كلمة المرور؟
                </button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>نسيت كلمة المرور؟</DialogTitle>
                  <DialogDescription>
                    تواصل مع المطور عشان يساعدك في استعادة كلمة المرور
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={(e) => handleInputChange("password", e.target.value)}
            className={cn(errors.password && "border-destructive focus-visible:ring-destructive")}
            aria-describedby={errors.password ? "password-error" : undefined}
            autoComplete="current-password"
            required
          />
          {errors.password && (
            <p id="password-error" className="text-sm text-destructive">
              {errors.password}
            </p>
          )}
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={!isFormValid}
        >
          سجل دخول
        </Button>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        معندكش حساب؟{" "}
        <button
          type="button"
          className="text-primary underline-offset-4 hover:underline font-medium"
          onClick={() => {
            // TODO: Implement registration flow
            console.log("Registration not implemented yet")
          }}
        >
          اعمل حساب جديد
        </button>
      </div>
    </form>
  )
}
