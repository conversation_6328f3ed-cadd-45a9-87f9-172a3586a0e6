import { SiteHeader } from '@/components/site-header'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { useAuth } from '@/contexts/AuthContext'
import { ordersApisCreateCompany, ordersApisGetCompany, ordersApisUpdateCompany } from '@/client'

export const Route = createFileRoute('/companies/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
    }),
})

interface FormData {
    office_id: string
    code: string
    name: string
    address: string
    phone: string
    color_code: string
}

interface FormErrors {
    [key: string]: string
}

function RouteComponent() {
    // get query params
    const { id } = Route.useSearch()
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [loading, setLoading] = useState(false)
    const [isEditMode, setIsEditMode] = useState(false)
    const [formData, setFormData] = useState<FormData>({
        office_id: '',
        code: '',
        name: '',
        address: '',
        phone: '',
        color_code: '#3B82F6', // Default blue color
    })
    const [errors, setErrors] = useState<FormErrors>({})

    // Set default office_id if available
    useEffect(() => {
        if (selectedOffice?.id) {
            setFormData(prev => ({ ...prev, office_id: selectedOffice.id }))
        }
    }, [selectedOffice])

    // Load company data if in edit mode
    useEffect(() => {
        if (id) {
            setIsEditMode(true)
            getCompanyData()
        }
    }, [id])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }))
        }
    }

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {}

        if (!formData.code.trim()) {
            newErrors.code = 'رمز الشركة مطلوب'
        }
        if (!formData.name.trim()) {
            newErrors.name = 'اسم الشركة مطلوب'
        }
        if (!formData.address.trim()) {
            newErrors.address = 'العنوان مطلوب'
        }
        if (!formData.phone.trim()) {
            newErrors.phone = 'رقم الهاتف مطلوب'
        }
        if (!formData.color_code.trim()) {
            newErrors.color_code = 'لون الشركة مطلوب'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            toast.error('يرجى تصحيح الأخطاء في النموذج')
            return
        }

        setLoading(true)
        try {
            if (isEditMode) {
                await updateCompanyData()
            } else {
                await createCompany()
            }
        } catch (error) {
            console.error('Error saving company:', error)
            toast.error('حدث خطأ أثناء حفظ الشركة')
        } finally {
            setLoading(false)
        }
    }

    const createCompany = async () => {
        const response = await ordersApisCreateCompany({
            body: {
                ...formData,
            },
        })
        if (response.response.status === 200) {
            toast.success('تم إنشاء الشركة بنجاح')
            navigate({ to: '/companies' })
        } else {
            toast.error('حدث خطأ أثناء إنشاء الشركة')
        }
    }

    const updateCompanyData = async () => {
        const response = await ordersApisUpdateCompany({
            path: {
                company_id: id!,
            },
            body: {
                ...formData,
            },
        })
        if (response.response.status === 200) {
            toast.success('تم تحديث الشركة بنجاح')
            navigate({ to: '/companies' })
        } else {
            toast.error('حدث خطأ أثناء تحديث الشركة')
        }
    }

    const handleCancel = () => {
        navigate({ to: '/companies' })
    }

    const getCompanyData = async () => {
        if (!id) return
        try {
            // TODO: Implement API call to get company data
            // For now, just simulate loading data
            const res = await ordersApisGetCompany({
                path: {
                    company_id: id!,
                },
            })
            if (!res.data) return
            setFormData({
                office_id: res.data.office_id,
                code: res.data.code || '',
                name: res.data.name,
                address: res.data.address,
                phone: res.data.phone,
                color_code: res.data.color_code,
            })
        } catch (error) {
            console.error('Error fetching company data:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الشركة')
        }
    }

    return (
        <>
            <SiteHeader title={"الشركات"} />
            <div className="flex min-h-screen pt-16">
                {/* Left Sidebar */}
                <div className="hidden lg:flex lg:w-80">
                    <div className="w-full p-8 pl-0">
                        <div className="space-y-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                                    {isEditMode ? "تعديل شركة" : "إضافة شركة جديدة"}
                                </h2>
                                <p className="text-gray-600 text-sm">
                                    {isEditMode
                                        ? "قم بتعديل معلومات الشركة أدناه"
                                        : "املأ النموذج أدناه لإضافة شركة جديدة للنظام"
                                    }
                                </p>
                            </div>

                            <Separator />

                            <div className="bg-blue-50 rounded-lg p-4">
                                <h4 className="text-sm font-medium text-blue-900 mb-2">💡 نصائح</h4>
                                <ul className="text-xs text-blue-800 space-y-1">
                                    <li>• تأكد من صحة رمز الشركة</li>
                                    <li>• أدخل رقم هاتف صحيح</li>
                                    <li>• اختر لون مميز للشركة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 p-4">
                    <div className="max-w-3xl mx-auto">
                        {/* Mobile Header */}
                        <div className="lg:hidden mb-8">
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                {isEditMode ? "تعديل شركة" : "إضافة شركة جديدة"}
                            </h1>
                            <p className="text-gray-600">
                                {isEditMode
                                    ? "قم بتعديل معلومات الشركة أدناه"
                                    : "املأ النموذج أدناه لإضافة شركة جديدة للنظام"
                                }
                            </p>
                        </div>

                        <form className="space-y-5" onSubmit={handleSubmit}>
                            {/* Basic Information */}
                            <Card>
                                <CardHeader className="pb-4">
                                    <div className="flex items-center space-x-3">
                                        <Badge variant="secondary" className="bg-blue-100 text-blue-700">المعلومات الأساسية</Badge>
                                        <CardTitle className="text-lg">معلومات الشركة</CardTitle>
                                    </div>
                                    <CardDescription>أدخل المعلومات الأساسية للشركة</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            رمز الشركة
                                        </Label>
                                        <Input
                                            name="code"
                                            value={formData.code}
                                            onChange={handleInputChange}
                                            placeholder="مثال: COMP001"
                                            className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.code ? 'border-red-500' : ''
                                                }`}
                                        />
                                        {errors.code && (
                                            <p className="text-red-500 text-xs">{errors.code}</p>
                                        )}
                                    </div>
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            اسم الشركة
                                        </Label>
                                        <Input
                                            name="name"
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            placeholder="أدخل اسم الشركة"
                                            className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.name ? 'border-red-500' : ''
                                                }`}
                                        />
                                        {errors.name && (
                                            <p className="text-red-500 text-xs">{errors.name}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Contact Information Section */}
                            <Card>
                                <CardHeader className="pb-4">
                                    <div className="flex items-center space-x-3">
                                        <Badge variant="secondary" className="bg-green-100 text-green-700">معلومات الاتصال</Badge>
                                        <CardTitle className="text-lg">معلومات الاتصال والعنوان</CardTitle>
                                    </div>
                                    <CardDescription>أدخل معلومات الاتصال والعنوان</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-3">
                                            <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                رقم الهاتف
                                            </Label>
                                            <Input
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleInputChange}
                                                placeholder="+966 5X XXX XXXX"
                                                className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.phone ? 'border-red-500' : ''
                                                    }`}
                                            />
                                            {errors.phone && (
                                                <p className="text-red-500 text-xs">{errors.phone}</p>
                                            )}
                                        </div>
                                        <div className="space-y-3">
                                            <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                لون الشركة
                                            </Label>
                                            <div className="flex items-center space-x-3">
                                                <input
                                                    type="color"
                                                    name="color_code"
                                                    value={formData.color_code}
                                                    onChange={handleInputChange}
                                                    className="w-16 h-12 rounded-lg border-2 border-gray-200 cursor-pointer"
                                                />
                                                <Input
                                                    name="color_code"
                                                    value={formData.color_code}
                                                    onChange={handleInputChange}
                                                    placeholder="#3B82F6"
                                                    className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.color_code ? 'border-red-500' : ''
                                                        }`}
                                                />
                                            </div>
                                            {errors.color_code && (
                                                <p className="text-red-500 text-xs">{errors.color_code}</p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            العنوان
                                        </Label>
                                        <Input
                                            name="address"
                                            value={formData.address}
                                            onChange={handleInputChange}
                                            placeholder="أدخل عنوان الشركة"
                                            className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.address ? 'border-red-500' : ''
                                                }`}
                                        />
                                        {errors.address && (
                                            <p className="text-red-500 text-xs">{errors.address}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={loading}
                                >
                                    إلغاء
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading}
                                >
                                    {loading
                                        ? 'جاري المعالجة...'
                                        : isEditMode
                                            ? 'تحديث الشركة'
                                            : 'إضافة الشركة'
                                    }
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    )
}