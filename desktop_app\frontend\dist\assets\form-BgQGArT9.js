import{aD as A,u as B,a as F,r as l,aE as I,j as e,B as _,aF as L,aG as R}from"./index-DM6GIfB4.js";import{S as T}from"./site-header-BZV9tMVa.js";import{I as g}from"./input-BhcMMszI.js";import{L as f}from"./label-D7aoP4f7.js";import{C as k,a as G,c as H,b as M,f as $}from"./card-D0yfVNGp.js";import{t,S as U}from"./index-B_SQ5DcE.js";import{B as q}from"./badge-frfbuLAi.js";import{T as z}from"./textarea-ChEeGdgE.js";const ee=function(){const b=A.useSearch(),o=b.id,d=b.company_id,{selectedOffice:r}=B(),m=F(),[h,j]=l.useState(!1),[c,N]=l.useState(!1),[s,p]=l.useState({merchant_id:"",office_id:"",company_id:d||null,name:"",notes:"",channel_whatsapp_number:""}),[n,y]=l.useState({});l.useEffect(()=>{r!=null&&r.id&&p(a=>({...a,office_id:r.id,merchant_id:r.merchant_id}))},[r]),l.useEffect(()=>{o&&(N(!0),X())},[o]);const x=a=>{const{name:i,value:D}=a.target;p(u=>({...u,[i]:D})),n[i]&&y(u=>({...u,[i]:""}))},v=()=>{const a={};return s.name.trim()||(a.name="اسم القناة مطلوب"),s.channel_whatsapp_number.trim()||(a.channel_whatsapp_number="رقم الواتساب مطلوب"),y(a),Object.keys(a).length===0},C=async a=>{if(a.preventDefault(),!v()){t.error("يرجى تصحيح الأخطاء في النموذج");return}j(!0);try{c?await S():await w()}catch(i){console.error("Error saving channel:",i),t.error("حدث خطأ أثناء حفظ القناة")}finally{j(!1)}},w=async()=>{(await L({body:{...s}})).response.status===200?(t.success("تم إنشاء القناة بنجاح"),m({to:"/companies/channels",search:{company_id:d}})):t.error("حدث خطأ أثناء إنشاء القناة")},S=async()=>{(await R({path:{channel_id:o},body:{company_id:s.company_id,name:s.name,notes:s.notes,channel_whatsapp_number:s.channel_whatsapp_number}})).response.status===200?(t.success("تم تحديث القناة بنجاح"),m({to:"/companies/channels",search:{company_id:d}})):t.error("حدث خطأ أثناء تحديث القناة")},E=()=>{m({to:"/companies/channels",search:{company_id:d}})},X=async()=>{if(o)try{const a=await I({path:{channel_id:o}});if(!a.data)return;p({merchant_id:a.data.merchant_id,office_id:a.data.office_id,company_id:a.data.company_id,name:a.data.name,notes:a.data.notes,channel_whatsapp_number:a.data.channel_whatsapp_number})}catch(a){console.error("Error fetching channel data:",a),t.error("حدث خطأ أثناء تحميل بيانات القناة")}};return e.jsxs(e.Fragment,{children:[e.jsx(T,{title:"القنوات"}),e.jsxs("div",{className:"flex min-h-screen pt-16",children:[e.jsx("div",{className:"hidden lg:flex lg:w-80",children:e.jsx("div",{className:"w-full p-8 pl-0",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:c?"تعديل قناة":"إضافة قناة جديدة"}),e.jsx("p",{className:"text-gray-600 text-sm",children:c?"قم بتعديل معلومات القناة أدناه":"املأ النموذج أدناه لإضافة قناة جديدة للشركة"})]}),e.jsx(U,{}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"💡 نصائح"}),e.jsxs("ul",{className:"text-xs text-blue-800 space-y-1",children:[e.jsx("li",{children:"• تأكد من صحة رقم الواتساب"}),e.jsx("li",{children:"• أدخل اسم واضح للقناة"}),e.jsx("li",{children:"• أضف ملاحظات مفيدة"})]})]})]})})}),e.jsx("div",{className:"flex-1 p-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"lg:hidden mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:c?"تعديل قناة":"إضافة قناة جديدة"}),e.jsx("p",{className:"text-gray-600",children:c?"قم بتعديل معلومات القناة أدناه":"املأ النموذج أدناه لإضافة قناة جديدة للشركة"})]}),e.jsxs("form",{className:"space-y-5",onSubmit:C,children:[e.jsxs(k,{children:[e.jsxs(G,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(q,{variant:"secondary",className:"bg-blue-100 text-blue-700",children:"المعلومات الأساسية"}),e.jsx(H,{className:"text-lg",children:"معلومات القناة"})]}),e.jsx(M,{children:"أدخل المعلومات الأساسية للقناة"})]}),e.jsxs($,{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(f,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"اسم القناة"]}),e.jsx(g,{name:"name",value:s.name,onChange:x,placeholder:"مثال: قناة المبيعات",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${n.name?"border-red-500":""}`}),n.name&&e.jsx("p",{className:"text-red-500 text-xs",children:n.name})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(f,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"رقم الواتساب"]}),e.jsx(g,{name:"channel_whatsapp_number",value:s.channel_whatsapp_number,onChange:x,placeholder:"+966 5X XXX XXXX",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${n.channel_whatsapp_number?"border-red-500":""}`}),n.channel_whatsapp_number&&e.jsx("p",{className:"text-red-500 text-xs",children:n.channel_whatsapp_number})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(f,{className:"text-sm font-semibold text-gray-700",children:"الملاحظات"}),e.jsx(z,{name:"notes",value:s.notes,onChange:x,placeholder:"أدخل ملاحظات حول القناة (اختياري)",className:"min-h-[100px] border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6",children:[e.jsx(_,{type:"button",variant:"outline",onClick:E,disabled:h,children:"إلغاء"}),e.jsx(_,{type:"submit",disabled:h,children:h?"جاري المعالجة...":c?"تحديث القناة":"إضافة القناة"})]})]})]})})]})]})};export{ee as component};
