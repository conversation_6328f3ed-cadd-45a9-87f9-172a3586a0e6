import{u as x,r as l,a as h,j as s,V as g,W as y,X as b}from"./index-DwPFwVGs.js";import{S as j}from"./site-header-NQ8Z0yp1.js";import{D as _}from"./DataTableComponent-DPC256ar.js";import{B as E}from"./badge-PzfUlgPe.js";import{S as N,t as r}from"./index-DVysEDS8.js";import"./sortable.esm-TGC3xZfG.js";import"./checkbox-BLNazWDm.js";import"./select-S-eFdQZZ.js";import"./table-D7wvSByk.js";const w=function(){const{selectedOffice:t}=x(),[n,c]=l.useState([]),o=h(),i=async()=>{try{const e=await g({path:{office_id:t.id}});e.data&&c(e.data.employees)}catch(e){console.error("Error fetching employees:",e),r.error("حدث خطأ أثناء تحميل بيانات الموظفين")}};l.useEffect(()=>{i()},[t]);const m=e=>{o({to:"/employees/form",search:{id:e.user.id}})},d=e=>{r.info(`عرض تفاصيل الموظف: ${e.user.first_name} ${e.user.last_name}`)},f=async e=>{if(confirm("هل أنت متأكد من حذف هذا الموظف؟"))try{await y({path:{office_id:t.id,employee_id:e.id}}),await b({path:{user_id:e.user.id}}),r.success("تم حذف الموظف بنجاح"),i()}catch(a){console.error("Error deleting employee:",a),r.error("حدث خطأ أثناء حذف الموظف")}};return s.jsxs(s.Fragment,{children:[s.jsx(j,{title:"الموظفين"}),s.jsxs("div",{className:"flex flex-col m-4",children:[s.jsx("h1",{className:"text-2xl font-bold",children:"الموظفين"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"هنا يمكنك إدارة الموظفين"})]}),s.jsx(N,{className:"mb-4"}),s.jsx("div",{className:"flex flex-col m-4",children:s.jsx(_,{idField:"id",pageSize:10,enablePagination:!1,showAddButton:!0,addButtonText:"إضافة موظف",onAddClick:()=>{o({to:"/employees/form",search:{id:void 0}})},data:n,columns:[{header:"الاسم",accessorKey:"user.first_name",cell:({row:e})=>s.jsxs("div",{className:"flex flex-col cursor-pointer hover:bg-gray-50 p-2 rounded transition",onClick:()=>{o({to:"/employees/form",search:{id:e.original.user.id}})},title:"تعديل الموظف",children:[s.jsxs("span",{className:"font-medium",children:[e.original.user.first_name," ",e.original.user.last_name]}),s.jsx("span",{className:"text-sm text-muted-foreground",children:e.original.user.email})]})},{header:"رقم الهاتف",accessorKey:"user.phone_number"},{header:"المنصب",accessorKey:"user.role",cell:({row:e})=>{const a=e.original.user.role,p={admin:"ادمن",manager:"مدير",employee:"موظف"},u={admin:"bg-red-100 text-red-800",manager:"bg-blue-100 text-blue-800",employee:"bg-green-100 text-green-800"};return s.jsx(E,{className:u[a],children:p[a]})}},{header:"نسبة العمولة",accessorKey:"user.commission_fixed_rate",cell:({row:e})=>s.jsxs("span",{className:"text-sm",children:[e.original.user.commission_fixed_rate,"  ج.م"]})}],actionsColumn:{enableEdit:!1,enableDelete:!1,customActions:[{label:"عرض",onClick:d},{label:"تعديل",onClick:m},{label:"حذف",onClick:f,variant:"destructive"}]}})})]})};export{w as component};
