# Generated by Django 5.2.4 on 2025-07-18 17:53

import core.utils
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('merchants', '0002_alter_merchant_options_alter_office_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('color_code', models.CharField(blank=True, help_text='Hex color code for the company', max_length=20)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='companies', to='merchants.office')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'db_table': 'companies',
            },
        ),
        migrations.CreateModel(
            name='CompanyChannel',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True)),
                ('channel_whatsapp_number', models.CharField(help_text='WhatsApp number for the channel', max_length=255)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.company')),
                ('merchant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='merchants.merchant')),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='merchants.office')),
            ],
            options={
                'verbose_name': 'Company Channel',
                'verbose_name_plural': 'Company Channels',
                'db_table': 'company_channels',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True)),
                ('total_price', models.DecimalField(blank=True, decimal_places=2, help_text='Amount customer should pay', max_digits=10, null=True)),
                ('customer_name', models.CharField(max_length=255)),
                ('customer_phone', models.TextField(help_text='List of customer phone numbers separated by `-`')),
                ('customer_address', models.TextField(blank=True)),
                ('special_commission_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Special commission rate for the order', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(500)])),
                ('delivery_deadline_date', models.DateTimeField(blank=True, null=True)),
                ('delivery_customer_payment', models.DecimalField(blank=True, decimal_places=2, help_text='Amount paid by the customer', max_digits=10, null=True)),
                ('handling_status', models.CharField(choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('COMPLETED', 'Completed')], default='PENDING', max_length=20)),
                ('assigned_at', models.DateTimeField(blank=True, null=True)),
                ('breakable', models.BooleanField(default=False, help_text='Whether the order contains breakable items like glass')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('customer_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.company')),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='merchants.office')),
            ],
            options={
                'verbose_name': 'Order',
                'verbose_name_plural': 'Orders',
                'db_table': 'orders',
            },
        ),
        migrations.CreateModel(
            name='OrderAssigneeHistory',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders_assigned_by_history', to=settings.AUTH_USER_MODEL)),
                ('assignee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_orders_history', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
            ],
            options={
                'verbose_name': 'Order Assignee History',
                'verbose_name_plural': 'Order Assignee Histories',
                'db_table': 'order_assignee_histories',
            },
        ),
        migrations.CreateModel(
            name='OrderDeliveryStatus',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('order_default_handling_status', models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('COMPLETED', 'Completed')], max_length=20, null=True)),
                ('just_delivery_commission_rate', models.BooleanField(default=True)),
                ('commission_fixed_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('percentage_of_order_total_price', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('office', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='merchants.office')),
            ],
            options={
                'verbose_name': 'Order Delivery Status',
                'verbose_name_plural': 'Order Delivery Statuses',
                'db_table': 'order_delivery_statuses',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='status_reason',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.orderdeliverystatus'),
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('CANCELLED', 'Cancelled'), ('COMPLETED', 'Completed')], max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('status_reason', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.orderdeliverystatus')),
            ],
            options={
                'verbose_name': 'Order Status History',
                'verbose_name_plural': 'Order Status Histories',
                'db_table': 'order_status_histories',
            },
        ),
    ]
