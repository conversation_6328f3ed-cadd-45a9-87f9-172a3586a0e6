import { groupAddresses, findBestMatch } from './fuzzy-match'

// Simple test to verify fuzzy matching works
const testAddresses = [
    { id: '1', address: 'شارع النيل، القاهرة' },
    { id: '2', address: 'شارع النيل، القاهرة' },
    { id: '3', address: 'شارع النيل، القاهرة، مصر' },
    { id: '4', address: 'شارع التحرير، القاهرة' },
    { id: '5', address: 'شارع التحرير، القاهرة، مصر' },
    { id: '6', address: 'شارع الهرم، الجيزة' },
    { id: '7', address: 'شارع الهرم، الجيزة، مصر' },
]

console.log('Testing fuzzy address grouping...')

const groups = groupAddresses(testAddresses, { threshold: 0.7 })

console.log('Groups found:', groups.length)
groups.forEach((group, index) => {
    console.log(`Group ${index + 1}:`)
    console.log(`  Representative: ${group.representativeAddress}`)
    console.log(`  Orders: ${group.orders.join(', ')}`)
    console.log(`  Count: ${group.orders.length}`)
})

// Test finding best match
const testAddress = 'شارع النيل، القاهرة، مصر'
const bestMatch = findBestMatch(testAddress, groups, { threshold: 0.7 })

console.log('\nTesting best match...')
console.log(`Test address: ${testAddress}`)
if (bestMatch) {
    console.log(`Best match: ${bestMatch.group.representativeAddress}`)
    console.log(`Similarity: ${bestMatch.similarity.toFixed(2)}`)
} else {
    console.log('No match found')
}

export { testAddresses, groups, bestMatch } 