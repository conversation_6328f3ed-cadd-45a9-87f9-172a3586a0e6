from django.db import transaction
from django.db.models import Q
from django.http import HttpRequest
from django.core.paginator import Paginator
from django.utils import timezone
from ninja import Router
from accounts.models import Role, User
from merchants.models import Office, Merchant
from .models import (
    Company,
    CompanyChannel,
    OrderCancellationReasonTemplate,
    Order,
    OrderStatusHistory,
    OrderAssigneeHistory,
    OrderHandlingStatus,
)
from .schemas import (
    CompanySchema,
    CreateCompanySchema,
    UpdateCompanySchema,
    CompanyChannelSchema,
    CreateCompanyChannelSchema,
    UpdateCompanyChannelSchema,
    OrderCancellationReasonTemplateSchema,
    CreateOrderCancellationReasonTemplateSchema,
    UpdateOrderCancellationReasonTemplateSchema,
    OrderSchema,
    CreateOrderSchema,
    UpdateOrderSchema,
    OrderStatusHistorySchema,
    OrderAssigneeHistorySchema,
    CompanyListResponseSchema,
    CompanyChannelListResponseSchema,
    OrderCancellationReasonTemplateListResponseSchema,
    OrderListResponseSchema,
    OrderStatusHistoryListResponseSchema,
    OrderAssigneeHistoryListResponseSchema,
)
from core.middleware import AuthKey
from core.utils import HttpException

router = Router(tags=["orders"])


# Company endpoints
# GET /api/orders/companies/
@router.get("/companies/", auth=AuthKey, response=CompanyListResponseSchema)
def list_companies(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str = None,
):
    # Check if user has permission to list companies
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list companies")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        companies = Company.objects.all()
    else:
        # Managers can only see companies in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        companies = Company.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        companies = companies.filter(office_id=office_id)

    companies = companies.order_by("name")

    paginator = Paginator(companies, page_size)
    page_obj = paginator.get_page(page)

    return CompanyListResponseSchema(
        companies=[CompanySchema.from_orm(company) for company in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/companies/
@router.post("/companies/", auth=AuthKey, response=CompanySchema)
def create_company(request: HttpRequest, data: CreateCompanySchema) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create companies")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        office = Office.objects.get(id=data.office_id)
        company = Company.objects.create(
            office=office,
            code=data.code,
            name=data.name,
            address=data.address,
            phone=data.phone,
            color_code=data.color_code,
        )
        return CompanySchema.from_orm(company)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/companies/{company_id}
@router.get("/companies/{company_id}", auth=AuthKey, response=CompanySchema)
def get_company(request: HttpRequest, company_id: str) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view companies")

    # Check if user has access to this company
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        company = Company.objects.get(id=company_id)
        if company.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this company")

    company = Company.objects.get(id=company_id)
    return CompanySchema.from_orm(company)


# PUT /api/orders/companies/{company_id}
@router.put("/companies/{company_id}", auth=AuthKey, response=CompanySchema)
def update_company(
    request: HttpRequest, company_id: str, data: UpdateCompanySchema
) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to update companies")

    # Check if user has access to this company
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        company = Company.objects.get(id=company_id)
        if company.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this company")

    company = Company.objects.get(id=company_id)
    company.code = data.code
    company.name = data.name
    company.address = data.address
    company.phone = data.phone
    company.color_code = data.color_code
    company.save()

    return CompanySchema.from_orm(company)


# DELETE /api/orders/companies/{company_id}
@router.delete("/companies/{company_id}", auth=AuthKey, response=str)
def delete_company(request: HttpRequest, company_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete companies")

    company = Company.objects.get(id=company_id)
    company.delete()
    return "OK"


# Company Channel endpoints
# GET /api/orders/company-channels/
@router.get(
    "/company-channels/", auth=AuthKey, response=CompanyChannelListResponseSchema
)
def list_company_channels(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    merchant_id: str = None,
    office_id: str = None,
    company_id: str = None,
):
    # Check if user has permission to list company channels
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list company channels")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        channels = CompanyChannel.objects.all()
    else:
        # Managers can only see channels in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channels = CompanyChannel.objects.filter(office_id__in=user_offices)

    # Apply filters
    if merchant_id:
        channels = channels.filter(merchant_id=merchant_id)
    if office_id:
        channels = channels.filter(office_id=office_id)
    if company_id:
        channels = channels.filter(company_id=company_id)

    channels = channels.order_by("name")

    paginator = Paginator(channels, page_size)
    page_obj = paginator.get_page(page)

    return CompanyChannelListResponseSchema(
        channels=[CompanyChannelSchema.from_orm(channel) for channel in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/company-channels/
@router.post("/company-channels/", auth=AuthKey, response=CompanyChannelSchema)
def create_company_channel(
    request: HttpRequest, data: CreateCompanyChannelSchema
) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create company channels")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        merchant = Merchant.objects.get(id=data.merchant_id)
        office = Office.objects.get(id=data.office_id)
        company = None
        if data.company_id:
            company = Company.objects.get(id=data.company_id)

        channel = CompanyChannel.objects.create(
            merchant=merchant,
            office=office,
            company=company,
            name=data.name,
            notes=data.notes,
            channel_whatsapp_number=data.channel_whatsapp_number,
        )
        return CompanyChannelSchema.from_orm(channel)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/company-channels/{channel_id}
@router.get(
    "/company-channels/{channel_id}", auth=AuthKey, response=CompanyChannelSchema
)
def get_company_channel(request: HttpRequest, channel_id: str) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view company channels")

    # Check if user has access to this channel
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channel = CompanyChannel.objects.get(id=channel_id)
        if channel.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this channel")

    channel = CompanyChannel.objects.get(id=channel_id)
    return CompanyChannelSchema.from_orm(channel)


# PUT /api/orders/company-channels/{channel_id}
@router.put(
    "/company-channels/{channel_id}", auth=AuthKey, response=CompanyChannelSchema
)
def update_company_channel(
    request: HttpRequest, channel_id: str, data: UpdateCompanyChannelSchema
) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to update company channels")

    # Check if user has access to this channel
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channel = CompanyChannel.objects.get(id=channel_id)
        if channel.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this channel")

    channel = CompanyChannel.objects.get(id=channel_id)
    if data.company_id:
        company = Company.objects.get(id=data.company_id)
        channel.company = company
    else:
        channel.company = None
    channel.name = data.name
    channel.notes = data.notes
    channel.channel_whatsapp_number = data.channel_whatsapp_number
    channel.save()

    return CompanyChannelSchema.from_orm(channel)


# DELETE /api/orders/company-channels/{channel_id}
@router.delete("/company-channels/{channel_id}", auth=AuthKey, response=str)
def delete_company_channel(request: HttpRequest, channel_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete company channels")

    channel = CompanyChannel.objects.get(id=channel_id)
    channel.delete()
    return "OK"


# Order Cancellation Reason Template endpoints
# GET /api/orders/cancellation-templates/
@router.get(
    "/cancellation-templates/",
    auth=AuthKey,
    response=OrderCancellationReasonTemplateListResponseSchema,
)
def list_cancellation_templates(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str = None,
):
    # Check if user has permission to list cancellation templates
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(
            403, "You don't have permission to list cancellation templates"
        )

    user_offices = request.user.offices.values_list("office_id", flat=True)
    if office_id:
        user_offices = [office_id]

    templates = OrderCancellationReasonTemplate.objects.filter(
        Q(office_id__in=user_offices) | Q(office_id__isnull=True)
    )

    templates = templates.order_by("name")

    paginator = Paginator(templates, page_size)
    page_obj = paginator.get_page(page)

    return OrderCancellationReasonTemplateListResponseSchema(
        templates=[
            OrderCancellationReasonTemplateSchema.from_orm(template)
            for template in page_obj
        ],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/cancellation-templates/
@router.post(
    "/cancellation-templates/",
    auth=AuthKey,
    response=OrderCancellationReasonTemplateSchema,
)
def create_cancellation_template(
    request: HttpRequest, data: CreateOrderCancellationReasonTemplateSchema
) -> OrderCancellationReasonTemplateSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(
            403, "You don't have permission to create cancellation templates"
        )

    # Check if user has access to this office
    if data.office_id and request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        office = None
        if data.office_id:
            office = Office.objects.get(id=data.office_id)

        template = OrderCancellationReasonTemplate.objects.create(
            office=office,
            name=data.name,
            description=data.description,
            order_default_handling_status=data.order_default_handling_status,
            just_delivery_commission_rate=data.just_delivery_commission_rate,
            commission_fixed_rate=data.commission_fixed_rate,
            percentage_of_order_total_price=data.percentage_of_order_total_price,
        )
        return OrderCancellationReasonTemplateSchema.from_orm(template)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/cancellation-templates/{template_id}
@router.get(
    "/cancellation-templates/{template_id}",
    auth=AuthKey,
    response=OrderCancellationReasonTemplateSchema,
)
def get_cancellation_template(
    request: HttpRequest, template_id: str
) -> OrderCancellationReasonTemplateSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {
            "message": "You don't have permission to view cancellation templates"
        }

    # Check if user has access to this template
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        template = OrderCancellationReasonTemplate.objects.get(id=template_id)
        if template.office_id and template.office_id not in user_offices:
            return 403, {"message": "You don't have access to this template"}

    template = OrderCancellationReasonTemplate.objects.get(id=template_id)
    return OrderCancellationReasonTemplateSchema.from_orm(template)


# PUT /api/orders/cancellation-templates/{template_id}
@router.put(
    "/cancellation-templates/{template_id}",
    auth=AuthKey,
    response=OrderCancellationReasonTemplateSchema,
)
def update_cancellation_template(
    request: HttpRequest,
    template_id: str,
    data: UpdateOrderCancellationReasonTemplateSchema,
) -> OrderCancellationReasonTemplateSchema:
    if request.user.role not in [Role.ADMIN]:
        raise HttpException(
            403, "You don't have permission to update cancellation templates"
        )

    # Check if user has access to this template
    user_offices = request.user.offices.values_list("office_id", flat=True)
    template = OrderCancellationReasonTemplate.objects.get(id=template_id)
    if template.office and template.office.id not in user_offices:
        raise HttpException(403, "You don't have access to this template")

    if not template.office:
        raise HttpException(403, "You don't have access to this template")

    template = OrderCancellationReasonTemplate.objects.get(id=template_id)
    template.name = data.name
    template.description = data.description
    template.order_default_handling_status = data.order_default_handling_status
    template.just_delivery_commission_rate = data.just_delivery_commission_rate
    template.commission_fixed_rate = data.commission_fixed_rate
    template.percentage_of_order_total_price = data.percentage_of_order_total_price
    template.save()

    return OrderCancellationReasonTemplateSchema.from_orm(template)


# DELETE /api/orders/cancellation-templates/{template_id}
@router.delete("/cancellation-templates/{template_id}", auth=AuthKey, response=str)
def delete_cancellation_template(request: HttpRequest, template_id: str):
    if request.user.role not in [Role.ADMIN]:
        raise HttpException(
            403, "You don't have permission to delete cancellation templates"
        )

        # Check if user has access to this template
    user_offices = request.user.offices.values_list("office_id", flat=True)
    template = OrderCancellationReasonTemplate.objects.get(id=template_id)
    if template.office_id and template.office_id not in user_offices:
        raise HttpException(403, "You don't have access to this template")

    if not template.office:
        raise HttpException(403, "You don't have access to this template")

    template = OrderCancellationReasonTemplate.objects.get(id=template_id)
    template.delete()
    return "OK"


# Order endpoints
# GET /api/orders/
@router.get("/", auth=AuthKey, response=OrderListResponseSchema)
def list_orders(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str = None,
    status: str = None,
    assigned_to_id: str = None,
    customer_company_id: str = None,
    search: str = None,
    created_at_from: str = None,
    created_at_to: str = None,
    assigned_to_isnull: bool = False,
):
    # Check if user has permission to list orders
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to list orders")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        orders = Order.objects.all()
    else:
        # Managers and delivery agents can only see orders in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        orders = Order.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        orders = orders.filter(office_id=office_id)
    if status:
        orders = orders.filter(handling_status=status)
    if assigned_to_id:
        orders = orders.filter(assigned_to_id=assigned_to_id)
    if customer_company_id:
        orders = orders.filter(customer_company_id=customer_company_id)
    if search:
        orders = orders.filter(
            Q(code__icontains=search)
            | Q(customer_name__icontains=search)
            | Q(customer_phone__icontains=search)
            | Q(customer_address__icontains=search)
        )
    if created_at_from:
        orders = orders.filter(created_at__gte=created_at_from)
    if created_at_to:
        orders = orders.filter(created_at__lte=created_at_to)
    if assigned_to_isnull:
        orders = orders.filter(assigned_to_id__isnull=True)
    orders = orders.order_by("-created_at")

    paginator = Paginator(orders, page_size)
    page_obj = paginator.get_page(page)

    return OrderListResponseSchema(
        orders=[OrderSchema.from_orm(order) for order in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/
@router.post("/", auth=AuthKey, response=OrderSchema)
def create_order(request: HttpRequest, data: CreateOrderSchema) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create orders")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        with transaction.atomic():
            office = Office.objects.get(id=data.office_id)
            assigned_to = None
            if data.assigned_to_id:
                assigned_to = User.objects.get(id=data.assigned_to_id)
            customer_company = None
            if data.customer_company_id:
                customer_company = Company.objects.get(id=data.customer_company_id)
            cancellation_template = None
            if data.cancellation_reason_template_id:
                cancellation_template = OrderCancellationReasonTemplate.objects.get(
                    id=data.cancellation_reason_template_id
                )

            order = Order.objects.create(
                office=office,
                code=data.code,
                notes=data.notes,
                total_price=data.total_price,
                customer_name=data.customer_name,
                customer_phone=data.customer_phone,
                customer_address=data.customer_address,
                customer_company=customer_company,
                breakable=data.breakable,
                deadline_date=data.deadline_date,
                commission_fixed_rate=data.commission_fixed_rate,
                assigned_to=assigned_to,
                assigned_at=timezone.now() if assigned_to else None,
                final_customer_payment=data.final_customer_payment,
                handling_status=data.handling_status,
                cancellation_reason_template=cancellation_template,
                cancellation_reason=data.cancellation_reason,
            )

            # Create initial status history
            OrderStatusHistory.objects.create(
                order=order,
                status=data.handling_status,
                notes="Order created",
                created_by=request.user,
            )

            # Create assignee history if assigned
            if assigned_to:
                OrderAssigneeHistory.objects.create(
                    order=order,
                    assignee=assigned_to,
                    assigned_by=request.user,
                )

            return OrderSchema.from_orm(order)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/{order_id}
@router.get("/{order_id}", auth=AuthKey)
def get_order(request: HttpRequest, order_id: str) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to view orders")

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this order")

    order = Order.objects.get(id=order_id)
    return OrderSchema.from_orm(order)


# PUT /api/orders/{order_id}
@router.put("/{order_id}", auth=AuthKey)
def update_order(
    request: HttpRequest, order_id: str, data: UpdateOrderSchema
) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to update orders")

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this order")

    try:
        with transaction.atomic():
            order = Order.objects.get(id=order_id)
            old_status = order.handling_status
            old_assigned_to = order.assigned_to

            # Update order fields
            order.code = data.code
            order.notes = data.notes
            order.total_price = data.total_price
            order.customer_name = data.customer_name
            order.customer_phone = data.customer_phone
            order.customer_address = data.customer_address
            order.breakable = data.breakable
            order.deadline_date = data.deadline_date
            order.commission_fixed_rate = data.commission_fixed_rate
            order.final_customer_payment = data.final_customer_payment
            order.handling_status = data.handling_status
            order.cancellation_reason = data.cancellation_reason

            # Handle customer company
            if data.customer_company_id:
                order.customer_company = Company.objects.get(
                    id=data.customer_company_id
                )
            else:
                order.customer_company = None

            # Handle cancellation template
            if data.cancellation_reason_template_id:
                order.cancellation_reason_template = (
                    OrderCancellationReasonTemplate.objects.get(
                        id=data.cancellation_reason_template_id
                    )
                )
                order.cancellation_reason = order.cancellation_reason_template.name
                data.handling_status = (
                    order.cancellation_reason_template.order_default_handling_status
                )
                if order.cancellation_reason_template.just_delivery_commission_rate:
                    order.final_customer_payment = float(order.commission_fixed_rate)
                else:
                    order.final_customer_payment = (
                        order.total_price
                        * (
                            float(
                                order.cancellation_reason_template.percentage_of_order_total_price
                            )
                            / 100
                        )
                    ) + order.cancellation_reason_template.commission_fixed_rate
            else:
                order.cancellation_reason_template = None

            # Handle assignment
            if data.assigned_to_id:
                if not order.assigned_to:
                    order.handling_status = OrderHandlingStatus.ASSIGNED
                new_assigned_to = User.objects.get(id=data.assigned_to_id)
                order.assigned_to = new_assigned_to
                order.assigned_at = timezone.now()
            else:
                if order.assigned_to:
                    order.handling_status = OrderHandlingStatus.PENDING
                order.assigned_to = None
                order.assigned_at = None

            order.save()

            # Create status history if status changed
            if old_status != data.handling_status:
                OrderStatusHistory.objects.create(
                    order=order,
                    status=data.handling_status,
                    notes=data.cancellation_reason
                    if data.handling_status == OrderHandlingStatus.CANCELLED
                    else "Status updated",
                    created_by=request.user,
                )

            # Create assignee history if assignment changed
            if old_assigned_to != order.assigned_to:
                if order.assigned_to:
                    OrderAssigneeHistory.objects.create(
                        order=order,
                        assignee=order.assigned_to,
                        assigned_by=request.user,
                    )

            return OrderSchema.from_orm(order)
    except Exception as e:
        raise HttpException(400, str(e))


# DELETE /api/orders/{order_id}
@router.delete("/{order_id}", auth=AuthKey)
def delete_order(request: HttpRequest, order_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete orders")

    order = Order.objects.get(id=order_id)
    order.delete()
    return "OK"


# Order Status History endpoints
# GET /api/orders/{order_id}/status-history/
@router.get(
    "/{order_id}/status-history/",
    auth=AuthKey,
    response=OrderStatusHistoryListResponseSchema,
)
def list_order_status_history(
    request: HttpRequest,
    order_id: str,
    page: int = 1,
    page_size: int = 10,
):
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        return 403, {
            "message": "You don't have permission to view order status history"
        }

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            return 403, {"message": "You don't have access to this order"}

    history = OrderStatusHistory.objects.filter(order_id=order_id).order_by(
        "-created_at"
    )

    paginator = Paginator(history, page_size)
    page_obj = paginator.get_page(page)

    return OrderStatusHistoryListResponseSchema(
        history=[OrderStatusHistorySchema.from_orm(item) for item in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# Order Assignee History endpoints
# GET /api/orders/{order_id}/assignee-history/
@router.get(
    "/{order_id}/assignee-history/",
    auth=AuthKey,
    response=OrderAssigneeHistoryListResponseSchema,
)
def list_order_assignee_history(
    request: HttpRequest,
    order_id: str,
    page: int = 1,
    page_size: int = 10,
):
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        return 403, {"message": "You don't have permission to view assignee history"}

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            return 403, {"message": "You don't have access to this order"}

    history = OrderAssigneeHistory.objects.filter(order_id=order_id).order_by(
        "-created_at"
    )

    paginator = Paginator(history, page_size)
    page_obj = paginator.get_page(page)

    return OrderAssigneeHistoryListResponseSchema(
        history=[OrderAssigneeHistorySchema.from_orm(item) for item in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )
