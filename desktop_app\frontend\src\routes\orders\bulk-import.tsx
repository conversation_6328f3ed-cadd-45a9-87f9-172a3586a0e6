import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { IconUpload, IconArrowRight, IconCheck, IconFileSpreadsheet } from '@tabler/icons-react'
import { FileUpload } from '@/components/FileUpload'
import { ColumnMapping, type ColumnMapping as ColumnMappingType } from '@/components/ColumnMapping'
import { BulkImportProcessor, type ProcessingResult } from '@/components/BulkImportProcessor'
import { ImportResults } from '@/components/ImportResults'
import * as XLSX from 'xlsx'
import Papa from 'papaparse'
import { toast } from 'sonner'

export const Route = createFileRoute('/orders/bulk-import')({
    component: RouteComponent,
})

type ImportStep = 'upload' | 'mapping' | 'processing' | 'results'

interface ParsedData {
  columns: string[]
  data: Record<string, any>[]
}

function RouteComponent() {
    const navigate = useNavigate()
    const [currentStep, setCurrentStep] = useState<ImportStep>('upload')
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [parsedData, setParsedData] = useState<ParsedData | null>(null)
    const [columnMapping, setColumnMapping] = useState<ColumnMappingType>({})
    const [processingResults, setProcessingResults] = useState<ProcessingResult[]>([])
    const [isProcessingFile, setIsProcessingFile] = useState(false)

    const parseExcelFile = (file: File): Promise<ParsedData> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target?.result as ArrayBuffer)
                    const workbook = XLSX.read(data, { type: 'array' })
                    const sheetName = workbook.SheetNames[0]
                    const worksheet = workbook.Sheets[sheetName]
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

                    if (jsonData.length < 2) {
                        reject(new Error('الملف يجب أن يحتوي على صفين على الأقل'))
                        return
                    }

                    // Skip the first row and use the second row as headers
                    const headers = jsonData[1] as string[]
                    const rows = jsonData.slice(2) as any[][]

                    // Filter out empty headers and rows
                    const validHeaders = headers.filter(h => h && h.trim())
                    if (validHeaders.length === 0) {
                        reject(new Error('لم يتم العثور على أعمدة صالحة في الصف الثاني'))
                        return
                    }

                    const rowData = rows
                        .filter(row => row.some(cell => cell !== undefined && cell !== null && cell !== ''))
                        .map(row => {
                            const obj: Record<string, any> = {}
                            headers.forEach((header, index) => {
                                if (header && header.trim()) {
                                    obj[header] = row[index] || ''
                                }
                            })
                            return obj
                        })

                    resolve({
                        columns: validHeaders,
                        data: rowData
                    })
                } catch (error) {
                    reject(new Error('فشل في قراءة ملف Excel'))
                }
            }
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'))
            reader.readAsArrayBuffer(file)
        })
    }

    const parseCSVFile = (file: File): Promise<ParsedData> => {
        return new Promise((resolve, reject) => {
            Papa.parse(file, {
                header: false, // Parse as array to handle custom header logic
                skipEmptyLines: true,
                complete: (results) => {
                    if (results.errors.length > 0) {
                        reject(new Error('فشل في قراءة ملف CSV'))
                        return
                    }

                    const rows = results.data as any[][]
                    if (rows.length < 2) {
                        reject(new Error('الملف يجب أن يحتوي على صفين على الأقل'))
                        return
                    }

                    // Skip the first row and use the second row as headers
                    const headers = rows[1] as string[]
                    const dataRows = rows.slice(2)

                    // Filter out empty headers
                    const validHeaders = headers.filter(h => h && h.trim())
                    if (validHeaders.length === 0) {
                        reject(new Error('لم يتم العثور على أعمدة صالحة في الصف الثاني'))
                        return
                    }

                    const csvData = dataRows
                        .filter(row => row.some(cell => cell !== undefined && cell !== null && cell !== ''))
                        .map(row => {
                            const obj: Record<string, any> = {}
                            headers.forEach((header, index) => {
                                if (header && header.trim()) {
                                    obj[header] = row[index] || ''
                                }
                            })
                            return obj
                        })

                    resolve({
                        columns: validHeaders,
                        data: csvData
                    })
                },
                error: () => reject(new Error('فشل في قراءة ملف CSV'))
            })
        })
    }

    const handleFileSelect = async (file: File) => {
        setSelectedFile(file)
        setIsProcessingFile(true)

        try {
            let parsed: ParsedData
            
            if (file.name.endsWith('.xlsx')) {
                parsed = await parseExcelFile(file)
            } else if (file.name.endsWith('.csv')) {
                parsed = await parseCSVFile(file)
            } else {
                throw new Error('نوع الملف غير مدعوم')
            }

            if (parsed.columns.length === 0) {
                throw new Error('لم يتم العثور على أعمدة في الملف')
            }

            if (parsed.data.length === 0) {
                throw new Error('لم يتم العثور على بيانات في الملف')
            }

            setParsedData(parsed)
            toast.success(`تم تحليل الملف بنجاح. تم العثور على ${parsed.data.length} صف و ${parsed.columns.length} عمود`)
        } catch (error: any) {
            toast.error(error.message || 'فشل في تحليل الملف')
            setSelectedFile(null)
            setParsedData(null)
        } finally {
            setIsProcessingFile(false)
        }
    }

    const handleFileRemove = () => {
        setSelectedFile(null)
        setParsedData(null)
        setCurrentStep('upload')
    }

    const handleNextFromUpload = () => {
        if (parsedData) {
            setCurrentStep('mapping')
        }
    }

    const handleBackToUpload = () => {
        setCurrentStep('upload')
        setColumnMapping({})
    }

    const handleNextFromMapping = () => {
        setCurrentStep('processing')
    }

    const handleBackToMapping = () => {
        setCurrentStep('mapping')
    }

    const handleProcessingComplete = (results: ProcessingResult[]) => {
        setProcessingResults(results)
        setCurrentStep('results')
    }

    const handleRetryAll = () => {
        setCurrentStep('processing')
    }

    const handleNewImport = () => {
        setSelectedFile(null)
        setParsedData(null)
        setColumnMapping({})
        setProcessingResults([])
        setCurrentStep('upload')
    }

    const handleViewOrders = () => {
        navigate({ to: '/orders' })
    }

    const getStepIcon = (step: ImportStep) => {
        switch (step) {
            case 'upload': return <IconUpload className="h-4 w-4" />
            case 'mapping': return <IconArrowRight className="h-4 w-4" />
            case 'processing': return <IconFileSpreadsheet className="h-4 w-4" />
            case 'results': return <IconCheck className="h-4 w-4" />
        }
    }

    const getStepTitle = (step: ImportStep) => {
        switch (step) {
            case 'upload': return 'رفع الملف'
            case 'mapping': return 'ربط الأعمدة'
            case 'processing': return 'معالجة البيانات'
            case 'results': return 'النتائج'
        }
    }

    const isStepCompleted = (step: ImportStep) => {
        switch (step) {
            case 'upload': return !!parsedData
            case 'mapping': return currentStep === 'processing' || currentStep === 'results'
            case 'processing': return currentStep === 'results'
            case 'results': return false
        }
    }

    const isStepActive = (step: ImportStep) => {
        return currentStep === step
    }

    return <>
        <SiteHeader title="إدارة الطلبات" />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">استيراد الطلبات بالجملة</h1>
            <p className="text-sm text-muted-foreground">
                استيراد عدة طلبات من ملف Excel أو CSV
            </p>
        </div>
        <Separator className="mb-6" />

        {/* Progress Steps */}
        <div className="m-4 mb-6">
            <Card>
                <CardHeader>
                    <CardTitle>خطوات الاستيراد</CardTitle>
                    <CardDescription>
                        اتبع الخطوات التالية لاستيراد الطلبات
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        {(['upload', 'mapping', 'processing', 'results'] as ImportStep[]).map((step, index) => (
                            <div key={step} className="flex items-center">
                                <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
                                    isStepActive(step) 
                                        ? 'bg-primary text-primary-foreground' 
                                        : isStepCompleted(step)
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-600'
                                }`}>
                                    {getStepIcon(step)}
                                    <span className="text-sm font-medium">{getStepTitle(step)}</span>
                                    {isStepCompleted(step) && (
                                        <Badge variant="secondary" className="ml-1">
                                            <IconCheck className="h-3 w-3" />
                                        </Badge>
                                    )}
                                </div>
                                {index < 3 && (
                                    <div className={`w-8 h-0.5 mx-2 ${
                                        isStepCompleted(step) ? 'bg-green-300' : 'bg-gray-300'
                                    }`} />
                                )}
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>

        {/* Step Content */}
        <div className="m-4">
            {currentStep === 'upload' && (
                <div className="space-y-6">
                    <FileUpload
                        onFileSelect={handleFileSelect}
                        onFileRemove={handleFileRemove}
                        selectedFile={selectedFile}
                        isProcessing={isProcessingFile}
                    />
                    {parsedData && (
                        <Card>
                            <CardHeader>
                                <CardTitle>معلومات الملف</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <span className="text-sm font-medium">عدد الصفوف:</span>
                                        <span className="ml-2">{parsedData.data.length}</span>
                                    </div>
                                    <div>
                                        <span className="text-sm font-medium">عدد الأعمدة:</span>
                                        <span className="ml-2">{parsedData.columns.length}</span>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <span className="text-sm font-medium">الأعمدة المتاحة:</span>
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        {parsedData.columns.map((column, index) => (
                                            <Badge key={index} variant="outline">{column}</Badge>
                                        ))}
                                    </div>
                                </div>
                                <div className="flex justify-end mt-6">
                                    <button
                                        onClick={handleNextFromUpload}
                                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                                    >
                                        التالي - ربط الأعمدة
                                    </button>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            )}

            {currentStep === 'mapping' && parsedData && (
                <ColumnMapping
                    fileColumns={parsedData.columns}
                    sampleData={parsedData.data.slice(0, 5)}
                    onMappingChange={setColumnMapping}
                    onNext={handleNextFromMapping}
                    onBack={handleBackToUpload}
                />
            )}

            {currentStep === 'processing' && parsedData && (
                <BulkImportProcessor
                    data={parsedData.data}
                    mapping={columnMapping}
                    onComplete={handleProcessingComplete}
                    onBack={handleBackToMapping}
                />
            )}

            {currentStep === 'results' && (
                <ImportResults
                    results={processingResults}
                    mapping={columnMapping}
                    onRetryAll={handleRetryAll}
                    onNewImport={handleNewImport}
                    onViewOrders={handleViewOrders}
                />
            )}
        </div>
    </>
}
