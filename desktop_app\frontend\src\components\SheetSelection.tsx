import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { IconChevronDown, IconChevronRight, IconTable, IconAlertCircle, IconCheck } from '@tabler/icons-react'
import { cn } from '@/lib/utils'

export interface SheetData {
  name: string
  columns: string[]
  data: Record<string, any>[]
  preview: Record<string, any>[]
}

interface SheetSelectionProps {
  sheets: SheetData[]
  onSheetsSelect: (selectedSheets: SheetData[]) => void
  onNext: () => void
  onBack: () => void
  className?: string
}

export function SheetSelection({
  sheets,
  onSheetsSelect,
  onNext,
  onBack,
  className
}: SheetSelectionProps) {
  const [selectedSheetNames, setSelectedSheetNames] = useState<Set<string>>(
    // Auto-select first sheet by default
    new Set(sheets.length > 0 ? [sheets[0].name] : [])
  )
  const [expandedSheets, setExpandedSheets] = useState<Set<string>>(new Set())

  const handleSheetToggle = (sheetName: string) => {
    const newSelected = new Set(selectedSheetNames)
    if (newSelected.has(sheetName)) {
      newSelected.delete(sheetName)
    } else {
      newSelected.add(sheetName)
    }
    setSelectedSheetNames(newSelected)
    
    // Update parent component
    const selectedSheets = sheets.filter(sheet => newSelected.has(sheet.name))
    onSheetsSelect(selectedSheets)
  }

  const handleSelectAll = () => {
    if (selectedSheetNames.size === sheets.length) {
      // Deselect all
      setSelectedSheetNames(new Set())
      onSheetsSelect([])
    } else {
      // Select all
      const allNames = new Set(sheets.map(sheet => sheet.name))
      setSelectedSheetNames(allNames)
      onSheetsSelect(sheets)
    }
  }

  const toggleSheetExpansion = (sheetName: string) => {
    const newExpanded = new Set(expandedSheets)
    if (newExpanded.has(sheetName)) {
      newExpanded.delete(sheetName)
    } else {
      newExpanded.add(sheetName)
    }
    setExpandedSheets(newExpanded)
  }

  const canProceed = selectedSheetNames.size > 0

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconTable className="h-5 w-5" />
            اختيار أوراق العمل
          </CardTitle>
          <CardDescription>
            تم العثور على {sheets.length} ورقة عمل في الملف. اختر الأوراق التي تريد استيرادها.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Select All Controls */}
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="select-all"
                  checked={selectedSheetNames.size === sheets.length}
                  onCheckedChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="text-sm font-medium">
                  تحديد الكل ({sheets.length} أوراق)
                </label>
              </div>
              <Badge variant="secondary">
                محدد: {selectedSheetNames.size} من {sheets.length}
              </Badge>
            </div>

            {/* Sheet List */}
            <div className="space-y-3">
              {sheets.map((sheet) => (
                <Card key={sheet.name} className="border-2 transition-colors">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          id={`sheet-${sheet.name}`}
                          checked={selectedSheetNames.has(sheet.name)}
                          onCheckedChange={() => handleSheetToggle(sheet.name)}
                        />
                        <div>
                          <CardTitle className="text-base">{sheet.name}</CardTitle>
                          <CardDescription>
                            {sheet.data.length} صف، {sheet.columns.length} عمود
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {selectedSheetNames.has(sheet.name) && (
                          <Badge variant="default" className="flex items-center gap-1">
                            <IconCheck className="h-3 w-3" />
                            محدد
                          </Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleSheetExpansion(sheet.name)}
                          className="flex items-center gap-1"
                        >
                          {expandedSheets.has(sheet.name) ? (
                            <IconChevronDown className="h-4 w-4" />
                          ) : (
                            <IconChevronRight className="h-4 w-4" />
                          )}
                          معاينة
                        </Button>
                      </div>
                    </div>
                  </CardHeader>

                  {expandedSheets.has(sheet.name) && (
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {/* Columns Preview */}
                        <div>
                          <h4 className="text-sm font-medium mb-2">الأعمدة المتاحة:</h4>
                          <div className="flex flex-wrap gap-1">
                            {sheet.columns.map((column, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {column}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Data Preview */}
                        {sheet.preview.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">
                              معاينة البيانات (أول {sheet.preview.length} صفوف):
                            </h4>
                            <div className="overflow-x-auto border rounded-md">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    {sheet.columns.slice(0, 6).map((column, index) => (
                                      <TableHead key={index} className="text-xs">
                                        {column}
                                      </TableHead>
                                    ))}
                                    {sheet.columns.length > 6 && (
                                      <TableHead className="text-xs">...</TableHead>
                                    )}
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {sheet.preview.map((row, rowIndex) => (
                                    <TableRow key={rowIndex}>
                                      {sheet.columns.slice(0, 6).map((column, colIndex) => (
                                        <TableCell key={colIndex} className="text-xs max-w-32 truncate">
                                          {row[column] || '-'}
                                        </TableCell>
                                      ))}
                                      {sheet.columns.length > 6 && (
                                        <TableCell className="text-xs">...</TableCell>
                                      )}
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>

            {/* Validation Messages */}
            {selectedSheetNames.size === 0 && (
              <Alert variant="destructive">
                <IconAlertCircle className="h-4 w-4" />
                <AlertDescription>
                  يرجى اختيار ورقة عمل واحدة على الأقل للمتابعة
                </AlertDescription>
              </Alert>
            )}

            {selectedSheetNames.size > 1 && (
              <Alert>
                <IconAlertCircle className="h-4 w-4" />
                <AlertDescription>
                  سيتم معالجة الأوراق المحددة بنفس إعدادات ربط الأعمدة. تأكد من أن جميع الأوراق لها نفس تنسيق الأعمدة.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          السابق
        </Button>
        <Button onClick={onNext} disabled={!canProceed}>
          التالي - ربط الأعمدة
        </Button>
      </div>
    </div>
  )
}
