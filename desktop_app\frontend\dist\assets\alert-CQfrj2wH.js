import{r as a,j as s,e as d,o as l}from"./index-DwPFwVGs.js";const o=l("relative w-full rounded-lg border p-4 [&>svg~*]:ps-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:start-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),n=a.forwardRef(({className:t,variant:e,...r},i)=>s.jsx("div",{ref:i,role:"alert",className:d(o({variant:e}),t),...r}));n.displayName="Alert";const v=a.forwardRef(({className:t,...e},r)=>s.jsx("h5",{ref:r,className:d("mb-1 font-medium leading-none tracking-tight",t),...e}));v.displayName="AlertTitle";const c=a.forwardRef(({className:t,...e},r)=>s.jsx("div",{ref:r,className:d("text-sm [&_p]:leading-relaxed",t),...e}));c.displayName="AlertDescription";export{n as A,c as a};
