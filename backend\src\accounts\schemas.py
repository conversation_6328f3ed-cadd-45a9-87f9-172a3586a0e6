from datetime import datetime
from ninja import Schema
from typing import List, Optional


class UserOfficeSchema(Schema):
    id: str
    merchant_id: str
    name: str
    slug: str
    address: str


class UserSchema(Schema):
    id: str
    first_name: str
    last_name: str
    username: str
    email: str
    phone_number: str
    role: str
    commission_fixed_rate: Optional[float]
    current_location_lat: Optional[float]
    current_location_lng: Optional[float]


class CreateUserSchema(Schema):
    first_name: str
    last_name: str
    username: str
    email: str
    phone_number: str
    office_id: str
    role: str
    commission_fixed_rate: float
    password: str


class UpdateUserSchema(Schema):
    first_name: str
    last_name: str
    username: str
    email: str
    phone_number: str
    role: str
    commission_fixed_rate: float
    current_location_lat: float
    current_location_lng: float


class UserWalletSchema(Schema):
    id: str
    balance: float
    last_update: datetime


class UserPointsSchema(Schema):
    id: str
    points: int
    last_update: datetime


# Authentication schemas
class LoginSchema(Schema):
    phone_number: str
    password: str
    office_id: str


class LoginResponseSchema(Schema):
    token: str
    user: UserSchema
    office: UserOfficeSchema


# Location schemas
class LocationUpdateSchema(Schema):
    latitude: float
    longitude: float


class LocationHistorySchema(Schema):
    id: str
    location_lat: float
    location_lng: float
    update_time: datetime


# List response schemas
class UserListResponseSchema(Schema):
    users: List[UserSchema]
    total: int
    page: int
    page_size: int


class LocationHistoryResponseSchema(Schema):
    locations: List[LocationHistorySchema]
    total: int
    page: int
    page_size: int
