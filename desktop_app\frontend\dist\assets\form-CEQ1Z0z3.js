import{a5 as J,u as K,a as Q,r as c,a6 as W,j as e,B as F,H as Y,a7 as Z,a8 as ee}from"./index-DM6GIfB4.js";import{S as se}from"./site-header-BZV9tMVa.js";import{I as x}from"./input-BhcMMszI.js";import{L as d}from"./label-D7aoP4f7.js";import{S as re,a as ae,b as te,c as le,d as I}from"./select-BCFM5Vl0.js";import{C as b,a as j,c as g,b as _,f as N}from"./card-D0yfVNGp.js";import{t as o,S as O}from"./index-B_SQ5DcE.js";import{B as v}from"./badge-frfbuLAi.js";const he=function(){const{id:p}=J.useSearch(),{selectedOffice:U}=K(),S=Q(),[u,A]=c.useState(1),[C,h]=c.useState(!1),[l,B]=c.useState(!1),[y,L]=c.useState(""),[r,E]=c.useState({first_name:"",last_name:"",email:"",phone_number:"",role:"",commission_fixed_rate:"",password:"",confirm_password:""}),[a,$]=c.useState({}),[k,R]=c.useState([]);c.useEffect(()=>{p&&(B(!0),P())},[p]);const m=s=>{const{name:t,value:n}=s.target;E(i=>({...i,[t]:n})),a[t]&&$(i=>({...i,[t]:""}))},H=(s,t)=>{E(n=>({...n,[s]:t})),a[s]&&$(n=>({...n,[s]:""}))},M=()=>{const s={};return r.first_name.trim()||(s.first_name="الاسم الأول مطلوب"),r.last_name.trim()||(s.last_name="الاسم الأخير مطلوب"),r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)||(s.email="البريد الإلكتروني غير صحيح"):s.email="البريد الإلكتروني مطلوب",r.phone_number.trim()||(s.phone_number="رقم الهاتف مطلوب"),r.role||(s.role="المنصب مطلوب"),r.commission_fixed_rate?(isNaN(Number(r.commission_fixed_rate))||Number(r.commission_fixed_rate)<0)&&(s.commission_fixed_rate="نسبة العمولة يجب أن تكون رقم موجب"):s.commission_fixed_rate="نسبة العمولة مطلوبة",l?(r.password&&r.password.length<8&&(s.password="كلمة المرور يجب أن تكون 8 أحرف على الأقل"),r.password&&!r.confirm_password?s.confirm_password="تأكيد كلمة المرور مطلوب":r.password&&r.password!==r.confirm_password&&(s.confirm_password="كلمة المرور غير متطابقة")):(r.password?r.password.length<8&&(s.password="كلمة المرور يجب أن تكون 8 أحرف على الأقل"):s.password="كلمة المرور مطلوبة",r.confirm_password?r.password!==r.confirm_password&&(s.confirm_password="كلمة المرور غير متطابقة"):s.confirm_password="تأكيد كلمة المرور مطلوب"),$(s),Object.keys(s).length===0},T=async s=>{var t;if(s.preventDefault(),!M()){o.error("يرجى تصحيح الأخطاء في النموذج");return}if(l){h(!0);try{await z()}catch(n){console.error("Error updating user:",n),o.error("حدث خطأ أثناء تحديث الموظف")}finally{h(!1)}return}h(!0);try{const i=((t=(await Y({query:{employee_phone_number:r.phone_number}})).data)==null?void 0:t.offices)??[];R(i),i.length>0?(A(2),o.info("تم العثور على مكاتب مرتبطة بهذا الرقم، يرجى اختيار المكتب")):await X(U.id)}catch(n){console.error("Error checking employee offices:",n),o.error("حدث خطأ أثناء التحقق من المكاتب")}finally{h(!1)}},V=async s=>{if(s.preventDefault(),!y){o.error("يرجى اختيار مكتب");return}h(!0);try{await X(y)}catch(t){console.error("Error creating employee:",t),o.error("حدث خطأ أثناء إنشاء الموظف")}finally{h(!1)}},X=async s=>{try{const t=await Z({body:{first_name:r.first_name,last_name:r.last_name,email:r.email,username:r.email.split("@")[0]+"_"+r.phone_number,phone_number:r.phone_number,office_id:s,role:r.role,commission_fixed_rate:Number(r.commission_fixed_rate),password:r.password}});if(!t.data)throw new Error("Failed to create user");if(t.data)o.success("تم إنشاء الموظف بنجاح"),S({to:"/employees"});else throw new Error("Failed to add employee to office")}catch(t){throw console.error("Error in createEmployee:",t),t}},q=()=>{S({to:"/employees"})},G=s=>s<u?"completed":s===u?"current":"pending",P=async()=>{if(p)try{const s=await W({path:{user_id:p}});s.data&&E({first_name:s.data.first_name,last_name:s.data.last_name,email:s.data.email,phone_number:s.data.phone_number,role:s.data.role,commission_fixed_rate:s.data.commission_fixed_rate.toString(),password:"",confirm_password:""})}catch(s){console.error("Error fetching user data:",s),o.error("حدث خطأ أثناء تحميل بيانات الموظف")}},z=async()=>{if(!p)return;const s={first_name:r.first_name,last_name:r.last_name,email:r.email,phone_number:r.phone_number,role:r.role,commission_fixed_rate:Number(r.commission_fixed_rate),username:r.email.split("@")[0]+"_"+r.phone_number,current_location_lat:0,current_location_lng:0};if(r.password&&(s.password=r.password),(await ee({path:{user_id:p},body:s})).data)o.success("تم تحديث المعلومات بنجاح"),S({to:"/employees"});else throw new Error("Failed to update user data")},D=(s,t,n)=>{const i=G(s),f=i==="completed",w=i==="current";return e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${f?"bg-green-100":w?"bg-blue-100":"bg-gray-100"}`,children:e.jsx("span",{className:`text-sm font-medium ${f?"text-green-600":w?"text-blue-600":"text-gray-400"}`,children:f?"✓":s})}),e.jsxs("div",{children:[e.jsx("p",{className:`text-sm font-medium ${f?"text-green-900":w?"text-blue-900":"text-gray-400"}`,children:t}),e.jsx("p",{className:`text-xs ${f?"text-green-700":w?"text-blue-700":"text-gray-400"}`,children:n})]})]})};return e.jsxs(e.Fragment,{children:[e.jsx(se,{title:"الموظفين"}),e.jsxs("div",{className:"flex min-h-screen pt-16",children:[e.jsx("div",{className:"hidden lg:flex lg:w-80",children:e.jsx("div",{className:"w-full p-8 pl-0",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:l?"تعديل موظف":"إضافة موظف جديد"}),e.jsx("p",{className:"text-gray-600 text-sm",children:l?"قم بتعديل معلومات الموظف أدناه":"املأ النموذج أدناه لإضافة موظف جديد للنظام"})]}),e.jsx(O,{}),!l&&e.jsxs("div",{className:"space-y-4",children:[D(1,"المعلومات الأساسية","الاسم والبريد الإلكتروني"),D(2,"معلومات الاتصال","رقم الهاتف والمنصب"),D(3,"إعدادات الحساب","كلمة المرور والصلاحيات")]}),e.jsx(O,{}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"💡 نصائح"}),e.jsxs("ul",{className:"text-xs text-blue-800 space-y-1",children:[e.jsx("li",{children:"• تأكد من صحة البريد الإلكتروني"}),e.jsx("li",{children:"• استخدم كلمة مرور قوية"}),e.jsx("li",{children:"• حدد الصلاحيات المناسبة"})]})]})]})})}),e.jsx("div",{className:"flex-1 p-4",children:e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs("div",{className:"lg:hidden mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:l?"تعديل موظف":"إضافة موظف جديد"}),e.jsx("p",{className:"text-gray-600",children:l?"قم بتعديل معلومات الموظف أدناه":"املأ النموذج أدناه لإضافة موظف جديد للنظام"})]}),e.jsxs("form",{className:"space-y-5",onSubmit:u===1?T:V,children:[u===1&&e.jsxs(e.Fragment,{children:[e.jsxs(b,{children:[e.jsxs(j,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(v,{variant:"secondary",className:"bg-blue-100 text-blue-700",children:"الخطوة 1"}),e.jsx(g,{className:"text-lg",children:"المعلومات الأساسية"})]}),e.jsx(_,{children:"أدخل المعلومات الأساسية للموظف"})]}),e.jsxs(N,{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"الاسم الأول"]}),e.jsx(x,{name:"first_name",value:r.first_name,onChange:m,placeholder:"أدخل الاسم الأول",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.first_name?"border-red-500":""}`}),a.first_name&&e.jsx("p",{className:"text-red-500 text-xs",children:a.first_name})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"الاسم الأخير"]}),e.jsx(x,{name:"last_name",value:r.last_name,onChange:m,placeholder:"أدخل الاسم الأخير",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.last_name?"border-red-500":""}`}),a.last_name&&e.jsx("p",{className:"text-red-500 text-xs",children:a.last_name})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"البريد الإلكتروني"]}),e.jsx(x,{name:"email",type:"email",value:r.email,onChange:m,placeholder:"<EMAIL>",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.email?"border-red-500":""}`}),a.email&&e.jsx("p",{className:"text-red-500 text-xs",children:a.email})]})]})]}),e.jsxs(b,{children:[e.jsxs(j,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(v,{variant:"secondary",className:"bg-green-100 text-green-700",children:"الخطوة 2"}),e.jsx(g,{className:"text-lg",children:"معلومات الاتصال"})]}),e.jsx(_,{children:"أدخل معلومات الاتصال والمنصب"})]}),e.jsxs(N,{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"رقم الهاتف"]}),e.jsx(x,{name:"phone_number",value:r.phone_number,onChange:m,placeholder:"+966 5X XXX XXXX",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.phone_number?"border-red-500":""}`}),a.phone_number&&e.jsx("p",{className:"text-red-500 text-xs",children:a.phone_number})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"المنصب"]}),e.jsxs(re,{value:r.role,onValueChange:s=>H("role",s),children:[e.jsx(ae,{className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.role?"border-red-500":""}`,children:e.jsx(te,{placeholder:"اختر المنصب"})}),e.jsxs(le,{children:[e.jsx(I,{value:"admin",children:"👨‍💼 ادمن"}),e.jsx(I,{value:"manager",children:"👨‍💻 مدير"}),e.jsx(I,{value:"employee",children:"👤 موظف"})]})]}),a.role&&e.jsx("p",{className:"text-red-500 text-xs",children:a.role})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-red-500 rounded-full mr-2"}),"نسبة العمولة الثابتة (%)"]}),e.jsx(x,{name:"commission_fixed_rate",type:"number",value:r.commission_fixed_rate,onChange:m,placeholder:"0.00",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 max-w-xs ${a.commission_fixed_rate?"border-red-500":""}`}),a.commission_fixed_rate&&e.jsx("p",{className:"text-red-500 text-xs",children:a.commission_fixed_rate})]})]})]}),e.jsxs(b,{children:[e.jsxs(j,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(v,{variant:"secondary",className:"bg-purple-100 text-purple-700",children:"الخطوة 3"}),e.jsx(g,{className:"text-lg",children:"إعدادات الحساب"})]}),e.jsx(_,{children:"قم بإنشاء كلمة مرور آمنة للحساب"})]}),e.jsxs(N,{className:"space-y-6",children:[l&&e.jsx("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:e.jsxs("p",{className:"text-sm text-blue-800",children:["💡 ",e.jsx("strong",{children:"ملاحظة:"})," اترك حقول كلمة المرور فارغة إذا كنت لا تريد تغيير كلمة المرور الحالية"]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:`w-2 h-2 rounded-full mr-2 ${l?"bg-gray-400":"bg-red-500"}`}),"كلمة المرور",l&&e.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"(اختياري)"})]}),e.jsx(x,{name:"password",type:"password",value:r.password,onChange:m,placeholder:l?"اترك فارغاً إذا كنت لا تريد التغيير":"أدخل كلمة مرور قوية",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.password?"border-red-500":""}`}),a.password&&e.jsx("p",{className:"text-red-500 text-xs",children:a.password})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(d,{className:"text-sm font-semibold text-gray-700 flex items-center",children:[e.jsx("span",{className:`w-2 h-2 rounded-full mr-2 ${l?"bg-gray-400":"bg-red-500"}`}),"تأكيد كلمة المرور",l&&e.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"(اختياري)"})]}),e.jsx(x,{name:"confirm_password",type:"password",value:r.confirm_password,onChange:m,placeholder:l?"أعد إدخال كلمة المرور الجديدة":"أعد إدخال كلمة المرور",className:`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${a.confirm_password?"border-red-500":""}`}),a.confirm_password&&e.jsx("p",{className:"text-red-500 text-xs",children:a.confirm_password})]})]})]})]})]}),u===2&&e.jsxs(b,{children:[e.jsxs(j,{children:[e.jsx(g,{children:"اختر المكتب"}),e.jsx(_,{children:"تم العثور على المكاتب التالية المرتبطة بهذا الرقم"})]}),e.jsx(N,{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:k.map(s=>e.jsxs(b,{className:`bg-card rounded-xl border py-6 shadow-sm cursor-pointer transition-all duration-200 ${y===s.id?"border-blue-500 bg-blue-50":"hover:border-gray-300"}`,onClick:()=>L(s.id),children:[e.jsxs(j,{children:[e.jsxs(g,{className:"flex items-center justify-between",children:[s.name,y===s.id&&e.jsx(v,{variant:"secondary",className:"bg-blue-100 text-blue-700",children:"مختار"})]}),e.jsx(_,{children:s.address})]}),e.jsx(N,{children:e.jsx("p",{className:"text-sm text-gray-600",children:s.phone_number})})]},s.id))})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6",children:[e.jsx(F,{type:"button",variant:"outline",onClick:q,disabled:C,children:"إلغاء"}),e.jsx(F,{type:"submit",disabled:C,children:C?"جاري المعالجة...":l?"تحديث الموظف":u===1?"التالي":"إضافة الموظف"})]})]})]})})]})]})};export{he as component};
