import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { IconFileText, IconUsers, IconBuilding, IconTruck } from '@tabler/icons-react'

export const Route = createFileRoute('/settings/')({
    component: RouteComponent,
})

function RouteComponent() {
    const settingsSections = [
        {
            title: 'قوالب الإلغاء',
            description: 'إدارة قوالب أسباب الإلغاء لمكتبك',
            icon: IconFileText,
            href: '/settings/cancellation_templates',
            color: 'text-red-600',
        },
        {
            title: 'إعدادات المكتب',
            description: 'تعديل إعدادات المكتب والمعلومات الأساسية',
            icon: IconBuilding,
            href: '/settings/office',
            color: 'text-blue-600',
        },
        {
            title: 'إدارة الموظفين',
            description: 'إدارة موظفي المكتب والصلاحيات',
            icon: IconUsers,
            href: '/employees',
            color: 'text-green-600',
        },
        {
            title: 'إعدادات التوصيل',
            description: 'إعدادات التوصيل والعمولات',
            icon: IconTruck,
            href: '/settings/delivery',
            color: 'text-purple-600',
        },
    ]

    return (
        <div className="container mx-auto p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold mb-2">الإعدادات</h1>
                <p className="text-gray-600">
                    إدارة إعدادات النظام والمكتب
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {settingsSections.map((section) => {
                    const IconComponent = section.icon
                    return (
                        <Card key={section.href} className="hover:shadow-lg transition-shadow">
                            <CardHeader>
                                <div className="flex items-center space-x-3">
                                    <IconComponent className={`h-6 w-6 ${section.color}`} />
                                    <CardTitle className="text-lg">{section.title}</CardTitle>
                                </div>
                                <CardDescription>{section.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button asChild className="w-full">
                                    <Link to={section.href}>
                                        إدارة {section.title}
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    )
                })}
            </div>
        </div>
    )
}
