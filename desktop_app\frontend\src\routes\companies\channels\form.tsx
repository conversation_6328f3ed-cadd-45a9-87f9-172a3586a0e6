import { SiteHeader } from '@/components/site-header'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { useAuth } from '@/contexts/AuthContext'
import { ordersApisCreateCompanyChannel, ordersApisGetCompanyChannel, ordersApisUpdateCompanyChannel } from '@/client'

export const Route = createFileRoute('/companies/channels/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
        company_id: search.company_id as string | undefined,
    }),
})

interface FormData {
    merchant_id: string
    office_id: string
    company_id: string | null
    name: string
    notes: string
    channel_whatsapp_number: string
}

interface FormErrors {
    [key: string]: string
}

function RouteComponent() {
    // get query params
    const search = Route.useSearch()
    const id = search.id
    const company_id = search.company_id
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [loading, setLoading] = useState(false)
    const [isEditMode, setIsEditMode] = useState(false)
    const [formData, setFormData] = useState<FormData>({
        merchant_id: '',
        office_id: '',
        company_id: company_id || null,
        name: '',
        notes: '',
        channel_whatsapp_number: '',
    })
    const [errors, setErrors] = useState<FormErrors>({})

    // Set default values if available
    useEffect(() => {
        if (selectedOffice?.id) {
            setFormData(prev => ({
                ...prev,
                office_id: selectedOffice.id,
                merchant_id: selectedOffice.merchant_id
            }))
        }
    }, [selectedOffice])

    // Load channel data if in edit mode
    useEffect(() => {
        if (id) {
            setIsEditMode(true)
            getChannelData()
        }
    }, [id])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }))
        }
    }

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {}

        if (!formData.name.trim()) {
            newErrors.name = 'اسم القناة مطلوب'
        }
        if (!formData.channel_whatsapp_number.trim()) {
            newErrors.channel_whatsapp_number = 'رقم الواتساب مطلوب'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            toast.error('يرجى تصحيح الأخطاء في النموذج')
            return
        }

        setLoading(true)
        try {
            if (isEditMode) {
                await updateChannelData()
            } else {
                await createChannel()
            }
        } catch (error) {
            console.error('Error saving channel:', error)
            toast.error('حدث خطأ أثناء حفظ القناة')
        } finally {
            setLoading(false)
        }
    }

    const createChannel = async () => {
        const response = await ordersApisCreateCompanyChannel({
            body: {
                ...formData,
            },
        })
        if (response.response.status === 200) {
            toast.success('تم إنشاء القناة بنجاح')
            navigate({ to: '/companies/channels', search: { company_id } })
        } else {
            toast.error('حدث خطأ أثناء إنشاء القناة')
        }
    }

    const updateChannelData = async () => {
        const response = await ordersApisUpdateCompanyChannel({
            path: {
                channel_id: id!,
            },
            body: {
                company_id: formData.company_id,
                name: formData.name,
                notes: formData.notes,
                channel_whatsapp_number: formData.channel_whatsapp_number,
            },
        })
        if (response.response.status === 200) {
            toast.success('تم تحديث القناة بنجاح')
            navigate({ to: '/companies/channels', search: { company_id } })
        } else {
            toast.error('حدث خطأ أثناء تحديث القناة')
        }
    }

    const handleCancel = () => {
        navigate({ to: '/companies/channels', search: { company_id } })
    }

    const getChannelData = async () => {
        if (!id) return
        try {
            const res = await ordersApisGetCompanyChannel({
                path: {
                    channel_id: id!,
                },
            })
            if (!res.data) return
            setFormData({
                merchant_id: res.data.merchant_id,
                office_id: res.data.office_id,
                company_id: res.data.company_id,
                name: res.data.name,
                notes: res.data.notes,
                channel_whatsapp_number: res.data.channel_whatsapp_number,
            })
        } catch (error) {
            console.error('Error fetching channel data:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات القناة')
        }
    }

    return (
        <>
            <SiteHeader title={"القنوات"} />
            <div className="flex min-h-screen pt-16">
                {/* Left Sidebar */}
                <div className="hidden lg:flex lg:w-80">
                    <div className="w-full p-8 pl-0">
                        <div className="space-y-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                                    {isEditMode ? "تعديل قناة" : "إضافة قناة جديدة"}
                                </h2>
                                <p className="text-gray-600 text-sm">
                                    {isEditMode
                                        ? "قم بتعديل معلومات القناة أدناه"
                                        : "املأ النموذج أدناه لإضافة قناة جديدة للشركة"
                                    }
                                </p>
                            </div>

                            <Separator />

                            <div className="bg-blue-50 rounded-lg p-4">
                                <h4 className="text-sm font-medium text-blue-900 mb-2">💡 نصائح</h4>
                                <ul className="text-xs text-blue-800 space-y-1">
                                    <li>• تأكد من صحة رقم الواتساب</li>
                                    <li>• أدخل اسم واضح للقناة</li>
                                    <li>• أضف ملاحظات مفيدة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 p-4">
                    <div className="max-w-3xl mx-auto">
                        {/* Mobile Header */}
                        <div className="lg:hidden mb-8">
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                {isEditMode ? "تعديل قناة" : "إضافة قناة جديدة"}
                            </h1>
                            <p className="text-gray-600">
                                {isEditMode
                                    ? "قم بتعديل معلومات القناة أدناه"
                                    : "املأ النموذج أدناه لإضافة قناة جديدة للشركة"
                                }
                            </p>
                        </div>

                        <form className="space-y-5" onSubmit={handleSubmit}>
                            {/* Basic Information */}
                            <Card>
                                <CardHeader className="pb-4">
                                    <div className="flex items-center space-x-3">
                                        <Badge variant="secondary" className="bg-blue-100 text-blue-700">المعلومات الأساسية</Badge>
                                        <CardTitle className="text-lg">معلومات القناة</CardTitle>
                                    </div>
                                    <CardDescription>أدخل المعلومات الأساسية للقناة</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            اسم القناة
                                        </Label>
                                        <Input
                                            name="name"
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            placeholder="مثال: قناة المبيعات"
                                            className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.name ? 'border-red-500' : ''
                                                }`}
                                        />
                                        {errors.name && (
                                            <p className="text-red-500 text-xs">{errors.name}</p>
                                        )}
                                    </div>
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                            <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            رقم الواتساب
                                        </Label>
                                        <Input
                                            name="channel_whatsapp_number"
                                            value={formData.channel_whatsapp_number}
                                            onChange={handleInputChange}
                                            placeholder="+966 5X XXX XXXX"
                                            className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.channel_whatsapp_number ? 'border-red-500' : ''
                                                }`}
                                        />
                                        {errors.channel_whatsapp_number && (
                                            <p className="text-red-500 text-xs">{errors.channel_whatsapp_number}</p>
                                        )}
                                    </div>
                                    <div className="space-y-3">
                                        <Label className="text-sm font-semibold text-gray-700">
                                            الملاحظات
                                        </Label>
                                        <Textarea
                                            name="notes"
                                            value={formData.notes}
                                            onChange={handleInputChange}
                                            placeholder="أدخل ملاحظات حول القناة (اختياري)"
                                            className="min-h-[100px] border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={loading}
                                >
                                    إلغاء
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading}
                                >
                                    {loading
                                        ? 'جاري المعالجة...'
                                        : isEditMode
                                            ? 'تحديث القناة'
                                            : 'إضافة القناة'
                                    }
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    )
} 