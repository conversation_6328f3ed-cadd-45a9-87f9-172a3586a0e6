{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../tiny-warning/dist/tiny-warning.esm.js", "../../@tanstack/react-router/src/awaited.tsx", "../../@tanstack/react-router/src/CatchBoundary.tsx", "../../@tanstack/react-router/src/ClientOnly.tsx", "../../@tanstack/react-store/src/index.ts", "../../@tanstack/react-router/src/routerContext.tsx", "../../@tanstack/react-router/src/useRouter.tsx", "../../@tanstack/react-router/src/useRouterState.tsx", "../../@tanstack/react-router/src/matchContext.tsx", "../../@tanstack/react-router/src/useMatch.tsx", "../../@tanstack/react-router/src/useLoaderData.tsx", "../../@tanstack/react-router/src/useLoaderDeps.tsx", "../../@tanstack/react-router/src/useParams.tsx", "../../@tanstack/react-router/src/useSearch.tsx", "../../@tanstack/react-router/src/useNavigate.tsx", "../../@tanstack/react-router/src/utils.ts", "../../@tanstack/react-router/src/link.tsx", "../../@tanstack/react-router/src/route.tsx", "../../@tanstack/react-router/src/fileRoute.ts", "../../@tanstack/react-router/src/lazyRouteComponent.tsx", "../../@tanstack/react-router/src/Transitioner.tsx", "../../@tanstack/react-router/src/not-found.tsx", "../../@tanstack/react-router/src/SafeFragment.tsx", "../../@tanstack/react-router/src/renderRouteNotFound.tsx", "../../@tanstack/react-router/src/ScriptOnce.tsx", "../../@tanstack/react-router/src/scroll-restoration.tsx", "../../@tanstack/react-router/src/Match.tsx", "../../@tanstack/react-router/src/Matches.tsx", "../../@tanstack/react-router/src/router.ts", "../../@tanstack/react-router/src/RouterProvider.tsx", "../../@tanstack/react-router/src/ScrollRestoration.tsx", "../../@tanstack/react-router/src/useBlocker.tsx", "../../@tanstack/react-router/src/useRouteContext.ts", "../../@tanstack/react-router/src/useLocation.tsx", "../../@tanstack/react-router/src/useCanGoBack.ts", "../../@tanstack/react-router/src/Asset.tsx", "../../@tanstack/react-router/src/HeadContent.tsx", "../../@tanstack/react-router/src/Scripts.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "var isProduction = process.env.NODE_ENV === 'production';\nfunction warning(condition, message) {\n  if (!isProduction) {\n    if (condition) {\n      return;\n    }\n\n    var text = \"Warning: \" + message;\n\n    if (typeof console !== 'undefined') {\n      console.warn(text);\n    }\n\n    try {\n      throw Error(text);\n    } catch (x) {}\n  }\n}\n\nexport default warning;\n", "import * as React from 'react'\n\nimport { TSR_DEFERRED_PROMISE, defer } from '@tanstack/router-core'\nimport type { DeferredPromise } from '@tanstack/router-core'\n\nexport type AwaitOptions<T> = {\n  promise: Promise<T>\n}\n\nexport function useAwaited<T>({\n  promise: _promise,\n}: AwaitOptions<T>): [T, DeferredPromise<T>] {\n  const promise = defer(_promise)\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'pending') {\n    throw promise\n  }\n\n  if (promise[TSR_DEFERRED_PROMISE].status === 'error') {\n    throw promise[TSR_DEFERRED_PROMISE].error\n  }\n\n  return [promise[TSR_DEFERRED_PROMISE].data, promise]\n}\n\nexport function Await<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n) {\n  const inner = <AwaitInner {...props} />\n  if (props.fallback) {\n    return <React.Suspense fallback={props.fallback}>{inner}</React.Suspense>\n  }\n  return inner\n}\n\nfunction AwaitInner<T>(\n  props: AwaitOptions<T> & {\n    fallback?: React.ReactNode\n    children: (result: T) => React.ReactNode\n  },\n): React.JSX.Element {\n  const [data] = useAwaited(props)\n\n  return props.children(data) as React.JSX.Element\n}\n", "import * as React from 'react'\nimport type { ErrorRouteComponent } from './route'\nimport type { ErrorInfo } from 'react'\n\nexport function CatchBoundary(props: {\n  getResetKey: () => number | string\n  children: React.ReactNode\n  errorComponent?: ErrorRouteComponent\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}) {\n  const errorComponent = props.errorComponent ?? ErrorComponent\n\n  return (\n    <CatchBoundaryImpl\n      getResetKey={props.getResetKey}\n      onCatch={props.onCatch}\n      children={({ error, reset }) => {\n        if (error) {\n          return React.createElement(errorComponent, {\n            error,\n            reset,\n          })\n        }\n\n        return props.children\n      }}\n    />\n  )\n}\n\nclass CatchBoundaryImpl extends React.Component<{\n  getResetKey: () => number | string\n  children: (props: {\n    error: Error | null\n    reset: () => void\n  }) => React.ReactNode\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n}> {\n  state = { error: null } as { error: Error | null; resetKey: string }\n  static getDerivedStateFromProps(props: any) {\n    return { resetKey: props.getResetKey() }\n  }\n  static getDerivedStateFromError(error: Error) {\n    return { error }\n  }\n  reset() {\n    this.setState({ error: null })\n  }\n  componentDidUpdate(\n    prevProps: Readonly<{\n      getResetKey: () => string\n      children: (props: { error: any; reset: () => void }) => any\n      onCatch?: ((error: any, info: any) => void) | undefined\n    }>,\n    prevState: any,\n  ): void {\n    if (prevState.error && prevState.resetKey !== this.state.resetKey) {\n      this.reset()\n    }\n  }\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    if (this.props.onCatch) {\n      this.props.onCatch(error, errorInfo)\n    }\n  }\n  render() {\n    // If the resetKey has changed, don't render the error\n    return this.props.children({\n      error:\n        this.state.resetKey !== this.props.getResetKey()\n          ? null\n          : this.state.error,\n      reset: () => {\n        this.reset()\n      },\n    })\n  }\n}\n\nexport function ErrorComponent({ error }: { error: any }) {\n  const [show, setShow] = React.useState(process.env.NODE_ENV !== 'production')\n\n  return (\n    <div style={{ padding: '.5rem', maxWidth: '100%' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>\n        <strong style={{ fontSize: '1rem' }}>Something went wrong!</strong>\n        <button\n          style={{\n            appearance: 'none',\n            fontSize: '.6em',\n            border: '1px solid currentColor',\n            padding: '.1rem .2rem',\n            fontWeight: 'bold',\n            borderRadius: '.25rem',\n          }}\n          onClick={() => setShow((d) => !d)}\n        >\n          {show ? 'Hide Error' : 'Show Error'}\n        </button>\n      </div>\n      <div style={{ height: '.25rem' }} />\n      {show ? (\n        <div>\n          <pre\n            style={{\n              fontSize: '.7em',\n              border: '1px solid red',\n              borderRadius: '.25rem',\n              padding: '.3rem',\n              color: 'red',\n              overflow: 'auto',\n            }}\n          >\n            {error.message ? <code>{error.message}</code> : null}\n          </pre>\n        </div>\n      ) : null}\n    </div>\n  )\n}\n", "import React from 'react'\n\nexport interface ClientOnlyProps {\n  /**\n   * The children to render when the JS is loaded.\n   */\n  children: React.ReactNode\n  /**\n   * The fallback component to render if the JS is not yet loaded.\n   */\n  fallback?: React.ReactNode\n}\n\n/**\n * Render the children only after the JS has loaded client-side. Use an optional\n * fallback component if the JS is not yet loaded.\n *\n * @example\n * Render a Chart component if JS loads, renders a simple FakeChart\n * component server-side or if there is no JS. The FakeChart can have only the\n * UI without the behavior or be a loading spinner or skeleton.\n *\n * ```tsx\n * return (\n *   <ClientOnly fallback={<FakeChart />}>\n *     <Chart />\n *   </ClientOnly>\n * )\n * ```\n */\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  return useHydrated() ? (\n    <React.Fragment>{children}</React.Fragment>\n  ) : (\n    <React.Fragment>{fallback}</React.Fragment>\n  )\n}\n\n/**\n * Return a boolean indicating if the JS has been hydrated already.\n * When doing Server-Side Rendering, the result will always be false.\n * When doing Client-Side Rendering, the result will always be false on the\n * first render and true from then on. Even if a new component renders it will\n * always start with true.\n *\n * @example\n * ```tsx\n * // Disable a button that needs JS to work.\n * let hydrated = useHydrated()\n * return (\n *   <button type=\"button\" disabled={!hydrated} onClick={doSomethingCustom}>\n *     Click me\n *   </button>\n * )\n * ```\n * @returns True if the JS has been hydrated already, false otherwise.\n */\nfunction useHydrated(): boolean {\n  return React.useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false,\n  )\n}\n\nfunction subscribe() {\n  return () => {}\n}\n", "import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\nimport type { Derived, Store } from '@tanstack/store'\n\nexport * from '@tanstack/store'\n\n/**\n * @private\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Derived<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any> | Derived<TState, any>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n): TSelected {\n  const slice = useSyncExternalStoreWithSelector(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow,\n  )\n\n  return slice\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false\n    for (const v of objA) {\n      if (!objB.has(v)) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Date && objB instanceof Date) {\n    if (objA.getTime() !== objB.getTime()) return false\n    return true\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n", "import * as React from 'react'\nimport type { AnyRouter } from '@tanstack/router-core'\n\ndeclare global {\n  interface Window {\n    __TSR_ROUTER_CONTEXT__?: React.Context<AnyRouter>\n  }\n}\n\nconst routerContext = React.createContext<AnyRouter>(null!)\n\nexport function getRouterContext() {\n  if (typeof document === 'undefined') {\n    return routerContext\n  }\n\n  if (window.__TSR_ROUTER_CONTEXT__) {\n    return window.__TSR_ROUTER_CONTEXT__\n  }\n\n  window.__TSR_ROUTER_CONTEXT__ = routerContext as any\n\n  return routerContext\n}\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { getRouterContext } from './routerContext'\nimport type { AnyRouter, RegisteredRouter } from '@tanstack/router-core'\n\nexport function useRouter<TRouter extends AnyRouter = RegisteredRouter>(opts?: {\n  warn?: boolean\n}): TRouter {\n  const value = React.useContext(getRouterContext())\n  warning(\n    !((opts?.warn ?? true) && !value),\n    'useRouter must be used inside a <RouterProvider> component!',\n  )\n  return value as any\n}\n", "import { useStore } from '@tanstack/react-store'\nimport { useRef } from 'react'\nimport { replaceEqualDeep } from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterState,\n} from '@tanstack/router-core'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\n\nexport type UseRouterStateOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> = {\n  router?: TRouter\n  select?: (\n    state: RouterState<TRouter['routeTree']>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n} & StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseRouterStateResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? RouterState<TRouter['routeTree']> : TSelected\n\nexport function useRouterState<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseRouterStateOptions<TRouter, TSelected, TStructuralSharing>,\n): UseRouterStateResult<TRouter, TSelected> {\n  const contextRouter = useRouter<TRouter>({\n    warn: opts?.router === undefined,\n  })\n  const router = opts?.router || contextRouter\n  const previousResult =\n    useRef<ValidateSelected<TRouter, TSelected, TStructuralSharing>>(undefined)\n\n  return useStore(router.__store, (state) => {\n    if (opts?.select) {\n      if (opts.structuralSharing ?? router.options.defaultStructuralSharing) {\n        const newSlice = replaceEqualDeep(\n          previousResult.current,\n          opts.select(state),\n        )\n        previousResult.current = newSlice\n        return newSlice\n      }\n      return opts.select(state)\n    }\n    return state\n  }) as UseRouterStateResult<TRouter, TSelected>\n}\n", "import * as React from 'react'\n\nexport const matchContext = React.createContext<string | undefined>(undefined)\n\n// N.B. this only exists so we can conditionally call useContext on it when we are not interested in the nearest match\nexport const dummyMatchContext = React.createContext<string | undefined>(\n  undefined,\n)\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport { useRouterState } from './useRouterState'\nimport { dummyMatchContext, matchContext } from './matchContext'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyR<PERSON>er,\n  MakeRouteMatch,\n  MakeRouteMatchUnion,\n  RegisteredRouter,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n} from '@tanstack/router-core'\n\nexport interface UseMatchBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> {\n  select?: (\n    match: MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseMatchRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    true,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseMatchResult<TRouter, TFrom, true, TSelected>\n\nexport type UseMatchOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing extends boolean,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseMatchBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseMatchResult<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n> = unknown extends TSelected\n  ? TStrict extends true\n    ? MakeRouteMatch<TRouter['routeTree'], TFrom, TStrict>\n    : MakeRouteMatchUnion<TRouter>\n  : TSelected\n\nexport function useMatch<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseMatchOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<UseMatchResult<TRouter, TFrom, TStrict, TSelected>, TThrow> {\n  const nearestMatchId = React.useContext(\n    opts.from ? dummyMatchContext : matchContext,\n  )\n\n  const matchSelection = useRouterState({\n    select: (state: any) => {\n      const match = state.matches.find((d: any) =>\n        opts.from ? opts.from === d.routeId : d.id === nearestMatchId,\n      )\n      invariant(\n        !((opts.shouldThrow ?? true) && !match),\n        `Could not find ${opts.from ? `an active match from \"${opts.from}\"` : 'a nearest match!'}`,\n      )\n\n      if (match === undefined) {\n        return undefined\n      }\n\n      return opts.select ? opts.select(match) : match\n    },\n    structuralSharing: opts.structuralSharing,\n  } as any)\n\n  return matchSelection as any\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseLoaderData,\n  StrictOrFrom,\n  UseLoaderDataResult,\n} from '@tanstack/router-core'\n\nexport interface UseLoaderDataBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    match: ResolveUseLoaderData<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLoaderDataOptions<\n  TRouter extends AnyRouter,\n  T<PERSON>rom extends string | undefined,\n  TStrict extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseLoaderDataBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseLoaderDataRoute<out TId> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLoaderDataBaseOptions<\n    TRouter,\n    TId,\n    true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseLoaderDataResult<TRouter, TId, true, TSelected>\n\nexport function useLoaderData<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseLoaderDataOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TSelected,\n    TStructuralSharing\n  >,\n): UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    structuralSharing: opts.structuralSharing,\n    select: (s: any) => {\n      return opts.select ? opts.select(s.loaderData) : s.loaderData\n    },\n  } as any) as UseLoaderDataResult<TRouter, TFrom, TStrict, TSelected>\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseLoaderDeps,\n  StrictOrFrom,\n  UseLoaderDepsResult,\n} from '@tanstack/router-core'\n\nexport interface UseLoaderDepsBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    deps: ResolveUseLoaderDeps<TRouter, TFrom>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLoaderDepsOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom> &\n  UseLoaderDepsBaseOptions<TRouter, TFrom, TSelected, TStructuralSharing> &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseLoaderDepsRoute<out TId> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLoaderDepsBaseOptions<TRouter, TId, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, false>,\n) => UseLoaderDepsResult<TRouter, TId, TSelected>\n\nexport function useLoaderDeps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseLoaderDepsOptions<TRouter, TFrom, TSelected, TStructuralSharing>,\n): UseLoaderDepsResult<TRouter, TFrom, TSelected> {\n  const { select, ...rest } = opts\n  return useMatch({\n    ...rest,\n    select: (s) => {\n      return select ? select(s.loaderDeps) : s.loaderDeps\n    },\n  }) as UseLoaderDepsResult<TRouter, TFrom, TSelected>\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseParams,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n  UseParamsResult,\n} from '@tanstack/router-core'\n\nexport interface UseParamsBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    params: ResolveUseParams<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseParamsOptions<\n  TRouter extends AnyRouter,\n  TFrom extends string | undefined,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseParamsBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseParamsRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseParamsBaseOptions<\n    TRouter,\n    TFrom,\n    /* TStrict */ true,\n    /* TThrow */ true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseParamsResult<TRouter, TFrom, true, TSelected>\n\nexport function useParams<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseParamsOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<\n  UseParamsResult<TRouter, TFrom, TStrict, TSelected>,\n  TThrow\n> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    shouldThrow: opts.shouldThrow,\n    structuralSharing: opts.structuralSharing,\n    select: (match: any) => {\n      return opts.select ? opts.select(match.params) : match.params\n    },\n  }) as any\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ResolveUseSearch,\n  StrictOrFrom,\n  ThrowConstraint,\n  ThrowOrOptional,\n  UseSearchResult,\n} from '@tanstack/router-core'\n\nexport interface UseSearchBaseOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    state: ResolveUseSearch<TRouter, TFrom, TStrict>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n  shouldThrow?: TThrow\n}\n\nexport type UseSearchOptions<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean,\n  TThrow extends boolean,\n  TSelected,\n  TStructuralSharing,\n> = StrictOrFrom<TRouter, TFrom, TStrict> &\n  UseSearchBaseOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    TThrow,\n    TSelected,\n    TStructuralSharing\n  > &\n  StructuralSharingOption<TRouter, TSelected, TStructuralSharing>\n\nexport type UseSearchRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseSearchBaseOptions<\n    TRouter,\n    TFrom,\n    /* TStrict */ true,\n    /* TThrow */ true,\n    TSelected,\n    TStructuralSharing\n  > &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n) => UseSearchResult<TRouter, TFrom, true, TSelected>\n\nexport function useSearch<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TThrow extends boolean = true,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts: UseSearchOptions<\n    TRouter,\n    TFrom,\n    TStrict,\n    ThrowConstraint<TStrict, TThrow>,\n    TSelected,\n    TStructuralSharing\n  >,\n): ThrowOrOptional<\n  UseSearchResult<TRouter, TFrom, TStrict, TSelected>,\n  TThrow\n> {\n  return useMatch({\n    from: opts.from!,\n    strict: opts.strict,\n    shouldThrow: opts.shouldThrow,\n    structuralSharing: opts.structuralSharing,\n    select: (match: any) => {\n      return opts.select ? opts.select(match.search) : match.search\n    },\n  }) as any\n}\n", "import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport { useMatch } from './useMatch'\nimport type {\n  AnyRouter,\n  FromPathOption,\n  NavigateOptions,\n  RegisteredRouter,\n  UseNavigateResult,\n} from '@tanstack/router-core'\n\nexport function useNavigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDefaultFrom extends string = string,\n>(_defaultOpts?: {\n  from?: FromPathOption<TRouter, TDefaultFrom>\n}): UseNavigateResult<TDefaultFrom> {\n  const { navigate, state } = useRouter()\n\n  // Just get the index of the current match to avoid rerenders\n  // as much as possible\n  const matchIndex = useMatch({\n    strict: false,\n    select: (match) => match.index,\n  })\n\n  return React.useCallback(\n    (options: NavigateOptions) => {\n      const from =\n        options.from ??\n        _defaultOpts?.from ??\n        state.matches[matchIndex]!.fullPath\n\n      return navigate({\n        ...options,\n        from,\n      })\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [_defaultOpts?.from, navigate],\n  ) as UseNavigateResult<TDefaultFrom>\n}\n\nexport function Navigate<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): null {\n  const router = useRouter()\n  const navigate = useNavigate()\n\n  const previousPropsRef = React.useRef<NavigateOptions<\n    TRouter,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  > | null>(null)\n  React.useEffect(() => {\n    if (previousPropsRef.current !== props) {\n      navigate(props)\n      previousPropsRef.current = props\n    }\n  }, [router, props, navigate])\n  return null\n}\n", "import * as React from 'react'\n\nexport function useStableCallback<T extends (...args: Array<any>) => any>(\n  fn: T,\n): T {\n  const fnRef = React.useRef(fn)\n  fnRef.current = fn\n\n  const ref = React.useRef((...args: Array<any>) => fnRef.current(...args))\n  return ref.current as T\n}\n\nexport const useLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\n/**\n * Taken from https://www.developerway.com/posts/implementing-advanced-use-previous-hook#part3\n */\nexport function usePrevious<T>(value: T): T | null {\n  // initialise the ref with previous and current values\n  const ref = React.useRef<{ value: T; prev: T | null }>({\n    value: value,\n    prev: null,\n  })\n\n  const current = ref.current.value\n\n  // if the value passed into hook doesn't match what we store as \"current\"\n  // move the \"current\" to the \"previous\"\n  // and store the passed value as \"current\"\n  if (value !== current) {\n    ref.current = {\n      value: value,\n      prev: current,\n    }\n  }\n\n  // return the previous value only\n  return ref.current.prev\n}\n\n/**\n * React hook to wrap `IntersectionObserver`.\n *\n * This hook will create an `IntersectionObserver` and observe the ref passed to it.\n *\n * When the intersection changes, the callback will be called with the `IntersectionObserverEntry`.\n *\n * @param ref - The ref to observe\n * @param intersectionObserverOptions - The options to pass to the IntersectionObserver\n * @param options - The options to pass to the hook\n * @param callback - The callback to call when the intersection changes\n * @returns The IntersectionObserver instance\n * @example\n * ```tsx\n * const MyComponent = () => {\n * const ref = React.useRef<HTMLDivElement>(null)\n * useIntersectionObserver(\n *  ref,\n *  (entry) => { doSomething(entry) },\n *  { rootMargin: '10px' },\n *  { disabled: false }\n * )\n * return <div ref={ref} />\n * ```\n */\nexport function useIntersectionObserver<T extends Element>(\n  ref: React.RefObject<T | null>,\n  callback: (entry: IntersectionObserverEntry | undefined) => void,\n  intersectionObserverOptions: IntersectionObserverInit = {},\n  options: { disabled?: boolean } = {},\n) {\n  React.useEffect(() => {\n    if (\n      !ref.current ||\n      options.disabled ||\n      typeof IntersectionObserver !== 'function'\n    ) {\n      return\n    }\n\n    const observer = new IntersectionObserver(([entry]) => {\n      callback(entry)\n    }, intersectionObserverOptions)\n\n    observer.observe(ref.current)\n\n    return () => {\n      observer.disconnect()\n    }\n  }, [callback, intersectionObserverOptions, options.disabled, ref])\n}\n\n/**\n * React hook to take a `React.ForwardedRef` and returns a `ref` that can be used on a DOM element.\n *\n * @param ref - The forwarded ref\n * @returns The inner ref returned by `useRef`\n * @example\n * ```tsx\n * const MyComponent = React.forwardRef((props, ref) => {\n *  const innerRef = useForwardedRef(ref)\n *  return <div ref={innerRef} />\n * })\n * ```\n */\nexport function useForwardedRef<T>(ref?: React.ForwardedRef<T>) {\n  const innerRef = React.useRef<T>(null)\n  React.useImperativeHandle(ref, () => innerRef.current!, [])\n  return innerRef\n}\n", "import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  deepEqual,\n  exactPathTest,\n  functionalUpdate,\n  preloadWarning,\n  removeTrailingSlash,\n} from '@tanstack/router-core'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\n\nimport { useForwardedRef, useIntersectionObserver } from './utils'\n\nimport { useMatch } from './useMatch'\nimport type {\n  AnyRouter,\n  Constrain,\n  LinkOptions,\n  RegisteredRouter,\n  RoutePaths,\n} from '@tanstack/router-core'\nimport type { ReactNode } from 'react'\nimport type {\n  ValidateLinkOptions,\n  ValidateLinkOptionsArray,\n} from './typePrimitives'\n\nexport function useLinkProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  options: UseLinkPropsOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n  forwardedRef?: React.ForwardedRef<Element>,\n): React.ComponentPropsWithRef<'a'> {\n  const router = useRouter()\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  const hasRenderFetched = React.useRef(false)\n  const innerRef = useForwardedRef(forwardedRef)\n\n  const {\n    // custom props\n    activeProps,\n    inactiveProps,\n    activeOptions,\n    to,\n    preload: userPreload,\n    preloadDelay: userPreloadDelay,\n    hashScrollIntoView,\n    replace,\n    startTransition,\n    resetScroll,\n    viewTransition,\n    // element props\n    children,\n    target,\n    disabled,\n    style,\n    className,\n    onClick,\n    onFocus,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart,\n    ignoreBlocker,\n    // prevent these from being returned\n    params: _params,\n    search: _search,\n    hash: _hash,\n    state: _state,\n    mask: _mask,\n    reloadDocument: _reloadDocument,\n    unsafeRelative: _unsafeRelative,\n    from: _from,\n    _fromLocation,\n    ...propsSafeToSpread\n  } = options\n\n  // If this link simply reloads the current route,\n  // make sure it has a new key so it will trigger a data refresh\n\n  // If this `to` is a valid external URL, return\n  // null for LinkUtils\n\n  const type: 'internal' | 'external' = React.useMemo(() => {\n    try {\n      new URL(to as any)\n      return 'external'\n    } catch {}\n    return 'internal'\n  }, [to])\n\n  // subscribe to search params to re-build location if it changes\n  const currentSearch = useRouterState({\n    select: (s) => s.location.search,\n    structuralSharing: true as any,\n  })\n\n  const from = useMatch({\n    strict: false,\n    select: (match) => options.from ?? match.fullPath,\n  })\n\n  const next = React.useMemo(\n    () => router.buildLocation({ ...options, from } as any),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      router,\n      currentSearch,\n      options._fromLocation,\n      from,\n      options.hash,\n      options.to,\n      options.search,\n      options.params,\n      options.state,\n      options.mask,\n      options.unsafeRelative,\n    ],\n  )\n\n  const isExternal = type === 'external'\n\n  const preload =\n    options.reloadDocument || isExternal\n      ? false\n      : (userPreload ?? router.options.defaultPreload)\n  const preloadDelay =\n    userPreloadDelay ?? router.options.defaultPreloadDelay ?? 0\n\n  const isActive = useRouterState({\n    select: (s) => {\n      if (isExternal) return false\n      if (activeOptions?.exact) {\n        const testExact = exactPathTest(\n          s.location.pathname,\n          next.pathname,\n          router.basepath,\n        )\n        if (!testExact) {\n          return false\n        }\n      } else {\n        const currentPathSplit = removeTrailingSlash(\n          s.location.pathname,\n          router.basepath,\n        )\n        const nextPathSplit = removeTrailingSlash(\n          next.pathname,\n          router.basepath,\n        )\n\n        const pathIsFuzzyEqual =\n          currentPathSplit.startsWith(nextPathSplit) &&\n          (currentPathSplit.length === nextPathSplit.length ||\n            currentPathSplit[nextPathSplit.length] === '/')\n\n        if (!pathIsFuzzyEqual) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeSearch ?? true) {\n        const searchTest = deepEqual(s.location.search, next.search, {\n          partial: !activeOptions?.exact,\n          ignoreUndefined: !activeOptions?.explicitUndefined,\n        })\n        if (!searchTest) {\n          return false\n        }\n      }\n\n      if (activeOptions?.includeHash) {\n        return s.location.hash === next.hash\n      }\n      return true\n    },\n  })\n\n  const doPreload = React.useCallback(\n    () => {\n      router.preloadRoute({ ...options, from } as any).catch((err) => {\n        console.warn(err)\n        console.warn(preloadWarning)\n      })\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      router,\n      options.to,\n      options._fromLocation,\n      from,\n      options.search,\n      options.hash,\n      options.params,\n      options.state,\n      options.mask,\n      options.unsafeRelative,\n      options.hashScrollIntoView,\n      options.href,\n      options.ignoreBlocker,\n      options.reloadDocument,\n      options.replace,\n      options.resetScroll,\n      options.viewTransition,\n    ],\n  )\n\n  const preloadViewportIoCallback = React.useCallback(\n    (entry: IntersectionObserverEntry | undefined) => {\n      if (entry?.isIntersecting) {\n        doPreload()\n      }\n    },\n    [doPreload],\n  )\n\n  useIntersectionObserver(\n    innerRef,\n    preloadViewportIoCallback,\n    intersectionObserverOptions,\n    { disabled: !!disabled || !(preload === 'viewport') },\n  )\n\n  React.useEffect(() => {\n    if (hasRenderFetched.current) {\n      return\n    }\n    if (!disabled && preload === 'render') {\n      doPreload()\n      hasRenderFetched.current = true\n    }\n  }, [disabled, doPreload, preload])\n\n  if (isExternal) {\n    return {\n      ...propsSafeToSpread,\n      ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n      type,\n      href: to,\n      ...(children && { children }),\n      ...(target && { target }),\n      ...(disabled && { disabled }),\n      ...(style && { style }),\n      ...(className && { className }),\n      ...(onClick && { onClick }),\n      ...(onFocus && { onFocus }),\n      ...(onMouseEnter && { onMouseEnter }),\n      ...(onMouseLeave && { onMouseLeave }),\n      ...(onTouchStart && { onTouchStart }),\n    }\n  }\n\n  // The click handler\n  const handleClick = (e: React.MouseEvent) => {\n    if (\n      !disabled &&\n      !isCtrlEvent(e) &&\n      !e.defaultPrevented &&\n      (!target || target === '_self') &&\n      e.button === 0\n    ) {\n      e.preventDefault()\n\n      flushSync(() => {\n        setIsTransitioning(true)\n      })\n\n      const unsub = router.subscribe('onResolved', () => {\n        unsub()\n        setIsTransitioning(false)\n      })\n\n      // All is well? Navigate!\n      // N.B. we don't call `router.commitLocation(next) here because we want to run `validateSearch` before committing\n      return router.navigate({\n        ...options,\n        from,\n        replace,\n        resetScroll,\n        hashScrollIntoView,\n        startTransition,\n        viewTransition,\n        ignoreBlocker,\n      })\n    }\n  }\n\n  // The click handler\n  const handleFocus = (_: React.MouseEvent) => {\n    if (disabled) return\n    if (preload) {\n      doPreload()\n    }\n  }\n\n  const handleTouchStart = handleFocus\n\n  const handleEnter = (e: React.MouseEvent) => {\n    if (disabled || !preload) return\n\n    if (!preloadDelay) {\n      doPreload()\n    } else {\n      const eventTarget = e.target\n      if (timeoutMap.has(eventTarget)) {\n        return\n      }\n      const id = setTimeout(() => {\n        timeoutMap.delete(eventTarget)\n        doPreload()\n      }, preloadDelay)\n      timeoutMap.set(eventTarget, id)\n    }\n  }\n\n  const handleLeave = (e: React.MouseEvent) => {\n    if (disabled || !preload || !preloadDelay) return\n    const eventTarget = e.target\n    const id = timeoutMap.get(eventTarget)\n    if (id) {\n      clearTimeout(id)\n      timeoutMap.delete(eventTarget)\n    }\n  }\n\n  // Get the active props\n  const resolvedActiveProps: React.HTMLAttributes<HTMLAnchorElement> = isActive\n    ? (functionalUpdate(activeProps as any, {}) ?? STATIC_ACTIVE_OBJECT)\n    : STATIC_EMPTY_OBJECT\n\n  // Get the inactive props\n  const resolvedInactiveProps: React.HTMLAttributes<HTMLAnchorElement> =\n    isActive\n      ? STATIC_EMPTY_OBJECT\n      : (functionalUpdate(inactiveProps, {}) ?? STATIC_EMPTY_OBJECT)\n\n  const resolvedClassName = [\n    className,\n    resolvedActiveProps.className,\n    resolvedInactiveProps.className,\n  ]\n    .filter(Boolean)\n    .join(' ')\n\n  const resolvedStyle = (style ||\n    resolvedActiveProps.style ||\n    resolvedInactiveProps.style) && {\n    ...style,\n    ...resolvedActiveProps.style,\n    ...resolvedInactiveProps.style,\n  }\n\n  return {\n    ...propsSafeToSpread,\n    ...resolvedActiveProps,\n    ...resolvedInactiveProps,\n    href: disabled\n      ? undefined\n      : next.maskedLocation\n        ? router.history.createHref(next.maskedLocation.href)\n        : router.history.createHref(next.href),\n    ref: innerRef as React.ComponentPropsWithRef<'a'>['ref'],\n    onClick: composeHandlers([onClick, handleClick]),\n    onFocus: composeHandlers([onFocus, handleFocus]),\n    onMouseEnter: composeHandlers([onMouseEnter, handleEnter]),\n    onMouseLeave: composeHandlers([onMouseLeave, handleLeave]),\n    onTouchStart: composeHandlers([onTouchStart, handleTouchStart]),\n    disabled: !!disabled,\n    target,\n    ...(resolvedStyle && { style: resolvedStyle }),\n    ...(resolvedClassName && { className: resolvedClassName }),\n    ...(disabled && STATIC_DISABLED_PROPS),\n    ...(isActive && STATIC_ACTIVE_PROPS),\n    ...(isTransitioning && STATIC_TRANSITIONING_PROPS),\n  }\n}\n\nconst STATIC_EMPTY_OBJECT = {}\nconst STATIC_ACTIVE_OBJECT = { className: 'active' }\nconst STATIC_DISABLED_PROPS = { role: 'link', 'aria-disabled': true }\nconst STATIC_ACTIVE_PROPS = { 'data-status': 'active', 'aria-current': 'page' }\nconst STATIC_TRANSITIONING_PROPS = { 'data-transitioning': 'transitioning' }\n\nconst timeoutMap = new WeakMap<EventTarget, ReturnType<typeof setTimeout>>()\n\nconst intersectionObserverOptions: IntersectionObserverInit = {\n  rootMargin: '100px',\n}\n\nconst composeHandlers =\n  (handlers: Array<undefined | React.EventHandler<any>>) =>\n  (e: React.SyntheticEvent) => {\n    handlers.filter(Boolean).forEach((handler) => {\n      if (e.defaultPrevented) return\n      handler!(e)\n    })\n  }\n\ntype UseLinkReactProps<TComp> = TComp extends keyof React.JSX.IntrinsicElements\n  ? React.JSX.IntrinsicElements[TComp]\n  : TComp extends React.ComponentType<any>\n    ? React.ComponentPropsWithoutRef<TComp> &\n        React.RefAttributes<React.ComponentRef<TComp>>\n    : never\n\nexport type UseLinkPropsOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends RoutePaths<TRouter['routeTree']> | string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends RoutePaths<TRouter['routeTree']> | string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<'a', TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  UseLinkReactProps<'a'>\n\nexport type ActiveLinkOptions<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  ActiveLinkOptionProps<TComp>\n\ntype ActiveLinkProps<TComp> = Partial<\n  LinkComponentReactProps<TComp> & {\n    [key: `data-${string}`]: unknown\n  }\n>\n\nexport interface ActiveLinkOptionProps<TComp = 'a'> {\n  /**\n   * A function that returns additional props for the `active` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  activeProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n  /**\n   * A function that returns additional props for the `inactive` state of this link.\n   * These props override other props passed to the link (`style`'s are merged, `className`'s are concatenated)\n   */\n  inactiveProps?: ActiveLinkProps<TComp> | (() => ActiveLinkProps<TComp>)\n}\n\nexport type LinkProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ActiveLinkOptions<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo> &\n  LinkPropsChildren\n\nexport interface LinkPropsChildren {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | React.ReactNode\n    | ((state: {\n        isActive: boolean\n        isTransitioning: boolean\n      }) => React.ReactNode)\n}\n\ntype LinkComponentReactProps<TComp> = Omit<\n  UseLinkReactProps<TComp>,\n  keyof CreateLinkProps\n>\n\nexport type LinkComponentProps<\n  TComp = 'a',\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = LinkComponentReactProps<TComp> &\n  LinkProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport type CreateLinkProps = LinkProps<\n  any,\n  any,\n  string,\n  string,\n  string,\n  string\n>\n\nexport type LinkComponent<\n  in out TComp,\n  in out TDefaultFrom extends string = string,\n> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = TDefaultFrom,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  props: LinkComponentProps<TComp, TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n) => React.ReactElement\n\nexport interface LinkComponentRoute<\n  in out TDefaultFrom extends string = string,\n> {\n  defaultFrom: TDefaultFrom\n  <\n    TRouter extends AnyRouter = RegisteredRouter,\n    const TTo extends string | undefined = undefined,\n    const TMaskTo extends string = '',\n  >(\n    props: LinkComponentProps<\n      'a',\n      TRouter,\n      this['defaultFrom'],\n      TTo,\n      this['defaultFrom'],\n      TMaskTo\n    >,\n  ): React.ReactElement\n}\n\nexport function createLink<const TComp>(\n  Comp: Constrain<TComp, any, (props: CreateLinkProps) => ReactNode>,\n): LinkComponent<TComp> {\n  return React.forwardRef(function CreatedLink(props, ref) {\n    return <Link {...(props as any)} _asChild={Comp} ref={ref} />\n  }) as any\n}\n\nexport const Link: LinkComponent<'a'> = React.forwardRef<Element, any>(\n  (props, ref) => {\n    const { _asChild, ...rest } = props\n    const {\n      type: _type,\n      ref: innerRef,\n      ...linkProps\n    } = useLinkProps(rest as any, ref)\n\n    const children =\n      typeof rest.children === 'function'\n        ? rest.children({\n            isActive: (linkProps as any)['data-status'] === 'active',\n          })\n        : rest.children\n\n    if (_asChild === undefined) {\n      // the ReturnType of useLinkProps returns the correct type for a <a> element, not a general component that has a disabled prop\n      // @ts-expect-error\n      delete linkProps.disabled\n    }\n\n    return React.createElement(\n      _asChild ? _asChild : 'a',\n      {\n        ...linkProps,\n        ref: innerRef,\n      },\n      children,\n    )\n  },\n) as any\n\nfunction isCtrlEvent(e: React.MouseEvent) {\n  return !!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n}\n\nexport type LinkOptionsFnOptions<\n  TOptions,\n  TComp,\n  TRouter extends AnyRouter = RegisteredRouter,\n> =\n  TOptions extends ReadonlyArray<any>\n    ? ValidateLinkOptionsArray<TRouter, TOptions, string, TComp>\n    : ValidateLinkOptions<TRouter, TOptions, string, TComp>\n\nexport type LinkOptionsFn<TComp> = <\n  const TOptions,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(\n  options: LinkOptionsFnOptions<TOptions, TComp, TRouter>,\n) => TOptions\n\nexport const linkOptions: LinkOptionsFn<'a'> = (options) => {\n  return options as any\n}\n", "import {\n  BaseRootRoute,\n  <PERSON>Route,\n  BaseRouteApi,\n  notFound,\n} from '@tanstack/router-core'\nimport React from 'react'\nimport { useLoaderData } from './useLoaderData'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useParams } from './useParams'\nimport { useSearch } from './useSearch'\nimport { useNavigate } from './useNavigate'\nimport { useMatch } from './useMatch'\nimport { useRouter } from './useRouter'\nimport { Link } from './link'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  ConstrainLiteral,\n  ErrorComponentProps,\n  NotFoundError,\n  NotFoundRouteProps,\n  RegisteredRouter,\n  ResolveFullPath,\n  ResolveId,\n  ResolveParams,\n  RootRoute as RootRouteCore,\n  RootRouteId,\n  RootRouteOptions,\n  RouteConstraints,\n  Route as RouteCore,\n  RouteIds,\n  RouteMask,\n  RouteOptions,\n  RouteTypesById,\n  RouterCore,\n  ToMaskOptions,\n  UseNavigateResult,\n} from '@tanstack/router-core'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseSearchRoute } from './useSearch'\nimport type { UseRouteContextRoute } from './useRouteContext'\nimport type { LinkComponentRoute } from './link'\n\ndeclare module '@tanstack/router-core' {\n  export interface UpdatableRouteOptionsExtensions {\n    component?: RouteComponent\n    errorComponent?: false | null | ErrorRouteComponent\n    notFoundComponent?: NotFoundRouteComponent\n    pendingComponent?: RouteComponent\n  }\n\n  export interface RootRouteOptionsExtensions {\n    shellComponent?: ({\n      children,\n    }: {\n      children: React.ReactNode\n    }) => React.ReactNode\n  }\n\n  export interface RouteExtensions<\n    in out TId extends string,\n    in out TFullPath extends string,\n  > {\n    useMatch: UseMatchRoute<TId>\n    useRouteContext: UseRouteContextRoute<TId>\n    useSearch: UseSearchRoute<TId>\n    useParams: UseParamsRoute<TId>\n    useLoaderDeps: UseLoaderDepsRoute<TId>\n    useLoaderData: UseLoaderDataRoute<TId>\n    useNavigate: () => UseNavigateResult<TFullPath>\n    Link: LinkComponentRoute<TFullPath>\n  }\n}\n\nexport function getRouteApi<\n  const TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return new RouteApi<TId, TRouter>({ id })\n}\n\nexport class RouteApi<\n  TId,\n  TRouter extends AnyRouter = RegisteredRouter,\n> extends BaseRouteApi<TId, TRouter> {\n  /**\n   * @deprecated Use the `getRouteApi` function instead.\n   */\n  constructor({ id }: { id: TId }) {\n    super({ id })\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts) => {\n    return useMatch({\n      from: this.id as any,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id, strict: false } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<\n    RouteTypesById<TRouter, TId>['fullPath']\n  > => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.id as string].fullPath })\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n\n  Link: LinkComponentRoute<RouteTypesById<TRouter, TId>['fullPath']> =\n    React.forwardRef((props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      const router = useRouter()\n      const fullPath = router.routesById[this.id as string].fullPath\n      return <Link ref={ref} from={fullPath as never} {...props} />\n    }) as unknown as LinkComponentRoute<\n      RouteTypesById<TRouter, TId>['fullPath']\n    >\n}\n\nexport class Route<\n    in out TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n    in out TPath extends RouteConstraints['TPath'] = '/',\n    in out TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n      TParentRoute,\n      TPath\n    >,\n    in out TCustomId extends RouteConstraints['TCustomId'] = string,\n    in out TId extends RouteConstraints['TId'] = ResolveId<\n      TParentRoute,\n      TCustomId,\n      TPath\n    >,\n    in out TSearchValidator = undefined,\n    in out TParams = ResolveParams<TPath>,\n    in out TRouterContext = AnyContext,\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRoute<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RouteCore<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated Use the `createRoute` function instead.\n   */\n  constructor(\n    options?: RouteOptions<\n      TParentRoute,\n      TId,\n      TCustomId,\n      TFullPath,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TId> = (opts?) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<TFullPath> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<TFullPath> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath as never} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<TFullPath>\n}\n\nexport function createRoute<\n  TParentRoute extends RouteConstraints['TParentRoute'] = AnyRoute,\n  TPath extends RouteConstraints['TPath'] = '/',\n  TFullPath extends RouteConstraints['TFullPath'] = ResolveFullPath<\n    TParentRoute,\n    TPath\n  >,\n  TCustomId extends RouteConstraints['TCustomId'] = string,\n  TId extends RouteConstraints['TId'] = ResolveId<\n    TParentRoute,\n    TCustomId,\n    TPath\n  >,\n  TSearchValidator = undefined,\n  TParams = ResolveParams<TPath>,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n>(\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n): Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  AnyContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  return new Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  >(options)\n}\n\nexport type AnyRootRoute = RootRoute<any, any, any, any, any, any, any, any>\n\nexport function createRootRouteWithContext<TRouterContext extends {}>() {\n  return <\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TSearchValidator = undefined,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n  >(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) => {\n    return createRootRoute<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >(options as any)\n  }\n}\n\n/**\n * @deprecated Use the `createRootRouteWithContext` function instead.\n */\nexport const rootRouteWithContext = createRootRouteWithContext\n\nexport class RootRoute<\n    in out TSearchValidator = undefined,\n    in out TRouterContext = {},\n    in out TRouteContextFn = AnyContext,\n    in out TBeforeLoadFn = AnyContext,\n    in out TLoaderDeps extends Record<string, any> = {},\n    in out TLoaderFn = undefined,\n    in out TChildren = unknown,\n    in out TFileRouteTypes = unknown,\n  >\n  extends BaseRootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  implements\n    RootRouteCore<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n{\n  /**\n   * @deprecated `RootRoute` is now an internal implementation detail. Use `createRootRoute()` instead.\n   */\n  constructor(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) {\n    super(options)\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<RootRouteId> = (opts) => {\n    return useMatch({\n      ...opts,\n      from: this.id,\n      select: (d) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<RootRouteId> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<RootRouteId> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<RootRouteId> = (opts) => {\n    return useLoaderData({ ...opts, from: this.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<'/'> => {\n    return useNavigate({ from: this.fullPath })\n  }\n\n  Link: LinkComponentRoute<'/'> = React.forwardRef(\n    (props, ref: React.ForwardedRef<HTMLAnchorElement>) => {\n      return <Link ref={ref} from={this.fullPath} {...props} />\n    },\n  ) as unknown as LinkComponentRoute<'/'>\n}\n\nexport function createRootRoute<\n  TSearchValidator = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n>(\n  options?: RootRouteOptions<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >,\n): RootRoute<\n  TSearchValidator,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  unknown,\n  unknown\n> {\n  return new RootRoute<\n    TSearchValidator,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn\n  >(options)\n}\n\nexport function createRouteMask<\n  TRouteTree extends AnyRoute,\n  TFrom extends string,\n  TTo extends string,\n>(\n  opts: {\n    routeTree: TRouteTree\n  } & ToMaskOptions<RouterCore<TRouteTree, 'never', boolean>, TFrom, TTo>,\n): RouteMask<TRouteTree> {\n  return opts as any\n}\n\nexport interface DefaultRouteTypes<TProps> {\n  component:\n    | ((props: TProps) => any)\n    | React.LazyExoticComponent<(props: TProps) => any>\n}\nexport interface RouteTypes<TProps> extends DefaultRouteTypes<TProps> {}\n\nexport type AsyncRouteComponent<TProps> = RouteTypes<TProps>['component'] & {\n  preload?: () => Promise<void>\n}\n\nexport type RouteComponent = AsyncRouteComponent<{}>\n\nexport type ErrorRouteComponent = AsyncRouteComponent<ErrorComponentProps>\n\nexport type NotFoundRouteComponent = RouteTypes<NotFoundRouteProps>['component']\n\nexport class NotFoundRoute<\n  TParentRoute extends AnyRootRoute,\n  TRouterContext = AnyContext,\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TSearchValidator = undefined,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TChildren = unknown,\n> extends Route<\n  TParentRoute,\n  '/404',\n  '/404',\n  '404',\n  '404',\n  TSearchValidator,\n  {},\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren\n> {\n  constructor(\n    options: Omit<\n      RouteOptions<\n        TParentRoute,\n        string,\n        string,\n        string,\n        string,\n        TSearchValidator,\n        {},\n        TLoaderDeps,\n        TLoaderFn,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      | 'caseSensitive'\n      | 'parseParams'\n      | 'stringifyParams'\n      | 'path'\n      | 'id'\n      | 'params'\n    >,\n  ) {\n    super({\n      ...(options as any),\n      id: '404',\n    })\n  }\n}\n", "import warning from 'tiny-warning'\nimport { createRoute } from './route'\n\nimport { useMatch } from './useMatch'\nimport { useLoaderDeps } from './useLoaderDeps'\nimport { useLoaderData } from './useLoaderData'\nimport { useSearch } from './useSearch'\nimport { useParams } from './useParams'\nimport { useNavigate } from './useNavigate'\nimport { useRouter } from './useRouter'\nimport type { UseParamsRoute } from './useParams'\nimport type { UseMatchRoute } from './useMatch'\nimport type { UseSearchRoute } from './useSearch'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouter,\n  Constrain,\n  ConstrainLiteral,\n  FileBaseRouteOptions,\n  FileRoutesByPath,\n  LazyRouteOptions,\n  RegisteredRouter,\n  ResolveParams,\n  Route,\n  RouteById,\n  RouteConstraints,\n  RouteIds,\n  RouteLoaderFn,\n  UpdatableRouteOptions,\n  UseNavigateResult,\n} from '@tanstack/router-core'\nimport type { UseLoaderDepsRoute } from './useLoaderDeps'\nimport type { UseLoaderDataRoute } from './useLoaderData'\nimport type { UseRouteContextRoute } from './useRouteContext'\n\nexport function createFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n>(\n  path?: TFilePath,\n): FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>['createRoute'] {\n  if (typeof path === 'object') {\n    return new FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>(path, {\n      silent: true,\n    }).createRoute(path) as any\n  }\n  return new FileRoute<TFilePath, TParentRoute, TId, TPath, TFullPath>(path, {\n    silent: true,\n  }).createRoute\n}\n\n/** \n  @deprecated It's no longer recommended to use the `FileRoute` class directly.\n  Instead, use `createFileRoute('/path/to/file')(options)` to create a file route.\n*/\nexport class FileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TParentRoute extends AnyRoute = FileRoutesByPath[TFilePath]['parentRoute'],\n  TId extends RouteConstraints['TId'] = FileRoutesByPath[TFilePath]['id'],\n  TPath extends RouteConstraints['TPath'] = FileRoutesByPath[TFilePath]['path'],\n  TFullPath extends\n    RouteConstraints['TFullPath'] = FileRoutesByPath[TFilePath]['fullPath'],\n> {\n  silent?: boolean\n\n  constructor(\n    public path?: TFilePath,\n    _opts?: { silent: boolean },\n  ) {\n    this.silent = _opts?.silent\n  }\n\n  createRoute = <\n    TSearchValidator = undefined,\n    TParams = ResolveParams<TPath>,\n    TRouteContextFn = AnyContext,\n    TBeforeLoadFn = AnyContext,\n    TLoaderDeps extends Record<string, any> = {},\n    TLoaderFn = undefined,\n    TChildren = unknown,\n  >(\n    options?: FileBaseRouteOptions<\n      TParentRoute,\n      TId,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      AnyContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    > &\n      UpdatableRouteOptions<\n        TParentRoute,\n        TId,\n        TFullPath,\n        TParams,\n        TSearchValidator,\n        TLoaderFn,\n        TLoaderDeps,\n        AnyContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n  ): Route<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TFilePath,\n    TId,\n    TSearchValidator,\n    TParams,\n    AnyContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    unknown\n  > => {\n    warning(\n      this.silent,\n      'FileRoute is deprecated and will be removed in the next major version. Use the createFileRoute(path)(options) function instead.',\n    )\n    const route = createRoute(options as any)\n    ;(route as any).isRoot = false\n    return route as any\n  }\n}\n\n/** \n  @deprecated It's recommended not to split loaders into separate files.\n  Instead, place the loader function in the the main route file, inside the\n  `createFileRoute('/path/to/file)(options)` options.\n*/\nexport function FileRouteLoader<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(\n  _path: TFilePath,\n): <TLoaderFn>(\n  loaderFn: Constrain<\n    TLoaderFn,\n    RouteLoaderFn<\n      TRoute['parentRoute'],\n      TRoute['types']['id'],\n      TRoute['types']['params'],\n      TRoute['types']['loaderDeps'],\n      TRoute['types']['routerContext'],\n      TRoute['types']['routeContextFn'],\n      TRoute['types']['beforeLoadFn']\n    >\n  >,\n) => TLoaderFn {\n  warning(\n    false,\n    `FileRouteLoader is deprecated and will be removed in the next major version. Please place the loader function in the the main route file, inside the \\`createFileRoute('/path/to/file')(options)\\` options`,\n  )\n  return (loaderFn) => loaderFn as any\n}\n\ndeclare module '@tanstack/router-core' {\n  export interface LazyRoute<in out TRoute extends AnyRoute> {\n    useMatch: UseMatchRoute<TRoute['id']>\n    useRouteContext: UseRouteContextRoute<TRoute['id']>\n    useSearch: UseSearchRoute<TRoute['id']>\n    useParams: UseParamsRoute<TRoute['id']>\n    useLoaderDeps: UseLoaderDepsRoute<TRoute['id']>\n    useLoaderData: UseLoaderDataRoute<TRoute['id']>\n    useNavigate: () => UseNavigateResult<TRoute['fullPath']>\n  }\n}\n\nexport class LazyRoute<TRoute extends AnyRoute> {\n  options: {\n    id: string\n  } & LazyRouteOptions\n\n  constructor(\n    opts: {\n      id: string\n    } & LazyRouteOptions,\n  ) {\n    this.options = opts\n    ;(this as any).$$typeof = Symbol.for('react.memo')\n  }\n\n  useMatch: UseMatchRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      select: opts?.select,\n      from: this.options.id,\n      structuralSharing: opts?.structuralSharing,\n    } as any) as any\n  }\n\n  useRouteContext: UseRouteContextRoute<TRoute['id']> = (opts) => {\n    return useMatch({\n      from: this.options.id,\n      select: (d: any) => (opts?.select ? opts.select(d.context) : d.context),\n    }) as any\n  }\n\n  useSearch: UseSearchRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useSearch({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useParams: UseParamsRoute<TRoute['id']> = (opts) => {\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    return useParams({\n      select: opts?.select,\n      structuralSharing: opts?.structuralSharing,\n      from: this.options.id,\n    } as any) as any\n  }\n\n  useLoaderDeps: UseLoaderDepsRoute<TRoute['id']> = (opts) => {\n    return useLoaderDeps({ ...opts, from: this.options.id } as any)\n  }\n\n  useLoaderData: UseLoaderDataRoute<TRoute['id']> = (opts) => {\n    return useLoaderData({ ...opts, from: this.options.id } as any)\n  }\n\n  useNavigate = (): UseNavigateResult<TRoute['fullPath']> => {\n    const router = useRouter()\n    return useNavigate({ from: router.routesById[this.options.id].fullPath })\n  }\n}\n\nexport function createLazyRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TId extends string = string,\n  TRoute extends AnyRoute = RouteById<TRouter['routeTree'], TId>,\n>(id: ConstrainLiteral<TId, RouteIds<TRouter['routeTree']>>) {\n  return (opts: LazyRouteOptions) => {\n    return new LazyRoute<TRoute>({\n      id: id,\n      ...opts,\n    })\n  }\n}\n\nexport function createLazyFileRoute<\n  TFilePath extends keyof FileRoutesByPath,\n  TRoute extends FileRoutesByPath[TFilePath]['preLoaderRoute'],\n>(id: TFilePath): (opts: LazyRouteOptions) => LazyRoute<TRoute> {\n  if (typeof id === 'object') {\n    return new LazyRoute<TRoute>(id) as any\n  }\n\n  return (opts: LazyRouteOptions) => new LazyRoute<TRoute>({ id, ...opts })\n}\n", "import * as React from 'react'\nimport { isModuleNotFoundError } from '@tanstack/router-core'\nimport type { AsyncRouteComponent } from './route'\n\nexport function lazyRouteComponent<\n  T extends Record<string, any>,\n  <PERSON><PERSON><PERSON> extends keyof T = 'default',\n>(\n  importer: () => Promise<T>,\n  exportName?: TKey,\n): T[T<PERSON><PERSON>] extends (props: infer TProps) => any\n  ? AsyncRouteComponent<TProps>\n  : never {\n  let loadPromise: Promise<any> | undefined\n  let comp: T[TKey] | T['default']\n  let error: any\n  let reload: boolean\n\n  const load = () => {\n    if (!loadPromise) {\n      loadPromise = importer()\n        .then((res) => {\n          loadPromise = undefined\n          comp = res[exportName ?? 'default']\n        })\n        .catch((err) => {\n          // We don't want an error thrown from preload in this case, because\n          // there's nothing we want to do about module not found during preload.\n          // Record the error, the rest is handled during the render path.\n          error = err\n          // If the load fails due to module not found, it may mean a new version of\n          // the build was deployed and the user's browser is still using an old version.\n          // If this happens, the old version in the user's browser would have an outdated\n          // URL to the lazy module.\n          // In that case, we want to attempt one window refresh to get the latest.\n          if (isModuleNotFoundError(error)) {\n            if (\n              error instanceof Error &&\n              typeof window !== 'undefined' &&\n              typeof sessionStorage !== 'undefined'\n            ) {\n              // Again, we want to reload one time on module not found error and not enter\n              // a reload loop if there is some other issue besides an old deploy.\n              // That's why we store our reload attempt in sessionStorage.\n              // Use error.message as key because it contains the module path that failed.\n              const storageKey = `tanstack_router_reload:${error.message}`\n              if (!sessionStorage.getItem(storageKey)) {\n                sessionStorage.setItem(storageKey, '1')\n                reload = true\n              }\n            }\n          }\n        })\n    }\n\n    return loadPromise\n  }\n\n  const lazyComp = function Lazy(props: any) {\n    // Now that we're out of preload and into actual render path,\n    if (reload) {\n      // If it was a module loading error,\n      // throw eternal suspense while we wait for window to reload\n      window.location.reload()\n      throw new Promise(() => {})\n    }\n    if (error) {\n      // Otherwise, just throw the error\n      throw error\n    }\n\n    if (!comp) {\n      throw load()\n    }\n\n    return React.createElement(comp, props)\n  }\n\n  ;(lazyComp as any).preload = load\n\n  return lazyComp as any\n}\n", "import * as React from 'react'\nimport {\n  getLocationChangeInfo,\n  handleHashScroll,\n  trimPathRight,\n} from '@tanstack/router-core'\nimport { useLayoutEffect, usePrevious } from './utils'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\n\nexport function Transitioner() {\n  const router = useRouter()\n  const mountLoadForRouter = React.useRef({ router, mounted: false })\n\n  const [isTransitioning, setIsTransitioning] = React.useState(false)\n  // Track pending state changes\n  const { hasPendingMatches, isLoading } = useRouterState({\n    select: (s) => ({\n      isLoading: s.isLoading,\n      hasPendingMatches: s.matches.some((d) => d.status === 'pending'),\n    }),\n    structuralSharing: true,\n  })\n\n  const previousIsLoading = usePrevious(isLoading)\n\n  const isAnyPending = isLoading || isTransitioning || hasPendingMatches\n  const previousIsAnyPending = usePrevious(isAnyPending)\n\n  const isPagePending = isLoading || hasPendingMatches\n  const previousIsPagePending = usePrevious(isPagePending)\n\n  router.startTransition = (fn: () => void) => {\n    setIsTransitioning(true)\n    React.startTransition(() => {\n      fn()\n      setIsTransitioning(false)\n    })\n  }\n\n  // Subscribe to location changes\n  // and try to load the new location\n  React.useEffect(() => {\n    const unsub = router.history.subscribe(router.load)\n\n    const nextLocation = router.buildLocation({\n      to: router.latestLocation.pathname,\n      search: true,\n      params: true,\n      hash: true,\n      state: true,\n      _includeValidateSearch: true,\n    })\n\n    if (\n      trimPathRight(router.latestLocation.href) !==\n      trimPathRight(nextLocation.href)\n    ) {\n      router.commitLocation({ ...nextLocation, replace: true })\n    }\n\n    return () => {\n      unsub()\n    }\n  }, [router, router.history])\n\n  // Try to load the initial location\n  useLayoutEffect(() => {\n    if (\n      // if we are hydrating from SSR, loading is triggered in ssr-client\n      (typeof window !== 'undefined' && router.ssr) ||\n      (mountLoadForRouter.current.router === router &&\n        mountLoadForRouter.current.mounted)\n    ) {\n      return\n    }\n    mountLoadForRouter.current = { router, mounted: true }\n\n    const tryLoad = async () => {\n      try {\n        await router.load()\n      } catch (err) {\n        console.error(err)\n      }\n    }\n\n    tryLoad()\n  }, [router])\n\n  useLayoutEffect(() => {\n    // The router was loading and now it's not\n    if (previousIsLoading && !isLoading) {\n      router.emit({\n        type: 'onLoad', // When the new URL has committed, when the new matches have been loaded into state.matches\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [previousIsLoading, router, isLoading])\n\n  useLayoutEffect(() => {\n    // emit onBeforeRouteMount\n    if (previousIsPagePending && !isPagePending) {\n      router.emit({\n        type: 'onBeforeRouteMount',\n        ...getLocationChangeInfo(router.state),\n      })\n    }\n  }, [isPagePending, previousIsPagePending, router])\n\n  useLayoutEffect(() => {\n    // The router was pending and now it's not\n    if (previousIsAnyPending && !isAnyPending) {\n      router.emit({\n        type: 'onResolved',\n        ...getLocationChangeInfo(router.state),\n      })\n\n      router.__store.setState((s) => ({\n        ...s,\n        status: 'idle',\n        resolvedLocation: s.location,\n      }))\n\n      handleHashScroll(router)\n    }\n  }, [isAnyPending, previousIsAnyPending, router])\n\n  return null\n}\n", "import * as React from 'react'\nimport { isNotFound } from '@tanstack/router-core'\nimport { CatchBoundary } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport type { ErrorInfo } from 'react'\nimport type { NotFoundError } from '@tanstack/router-core'\n\nexport function CatchNotFound(props: {\n  fallback?: (error: NotFoundError) => React.ReactElement\n  onCatch?: (error: Error, errorInfo: ErrorInfo) => void\n  children: React.ReactNode\n}) {\n  // TODO: Some way for the user to programmatically reset the not-found boundary?\n  const resetKey = useRouterState({\n    select: (s) => `not-found-${s.location.pathname}-${s.status}`,\n  })\n\n  return (\n    <CatchBoundary\n      getResetKey={() => resetKey}\n      onCatch={(error, errorInfo) => {\n        if (isNotFound(error)) {\n          props.onCatch?.(error, errorInfo)\n        } else {\n          throw error\n        }\n      }}\n      errorComponent={({ error }) => {\n        if (isNotFound(error)) {\n          return props.fallback?.(error)\n        } else {\n          throw error\n        }\n      }}\n    >\n      {props.children}\n    </CatchBoundary>\n  )\n}\n\nexport function DefaultGlobalNotFound() {\n  return <p>Not Found</p>\n}\n", "import * as React from 'react'\n\nexport function SafeFragment(props: any) {\n  return <>{props.children}</>\n}\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { DefaultGlobalNotFound } from './not-found'\nimport type { AnyRoute, AnyRouter } from '@tanstack/router-core'\n\nexport function renderRouteNotFound(\n  router: AnyRouter,\n  route: AnyRoute,\n  data: any,\n) {\n  if (!route.options.notFoundComponent) {\n    if (router.options.defaultNotFoundComponent) {\n      return <router.options.defaultNotFoundComponent data={data} />\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      warning(\n        route.options.notFoundComponent,\n        `A notFoundError was encountered on the route with ID \"${route.id}\", but a notFoundComponent option was not configured, nor was a router level defaultNotFoundComponent configured. Consider configuring at least one of these to avoid TanStack Router's overly generic defaultNotFoundComponent (<div>Not Found<div>)`,\n      )\n    }\n\n    return <DefaultGlobalNotFound />\n  }\n\n  return <route.options.notFoundComponent data={data} />\n}\n", "export function ScriptOnce({\n  children,\n}: {\n  children: string\n  log?: boolean\n  sync?: boolean\n}) {\n  if (typeof document !== 'undefined') {\n    return null\n  }\n\n  return (\n    <script\n      className=\"$tsr\"\n      dangerouslySetInnerHTML={{\n        __html: [children].filter(Boolean).join('\\n'),\n      }}\n    />\n  )\n}\n", "import {\n  defaultGetScrollRestorationKey,\n  restoreScroll,\n  storageKey,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport { ScriptOnce } from './ScriptOnce'\n\nexport function ScrollRestoration() {\n  const router = useRouter()\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n  const userKey = getKey(router.latestLocation)\n  const resolvedKey =\n    userKey !== defaultGetScrollRestorationKey(router.latestLocation)\n      ? userKey\n      : null\n\n  if (!router.isScrollRestoring || !router.isServer) {\n    return null\n  }\n\n  return (\n    <ScriptOnce\n      children={`(${restoreScroll.toString()})(${JSON.stringify(storageKey)},${JSON.stringify(resolvedKey)}, undefined, true)`}\n    />\n  )\n}\n", "import * as React from 'react'\nimport invariant from 'tiny-invariant'\nimport warning from 'tiny-warning'\nimport {\n  createControlledPromise,\n  getLocationChangeInfo,\n  isNotFound,\n  isRedirect,\n  pick,\n  rootRouteId,\n} from '@tanstack/router-core'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { CatchNotFound } from './not-found'\nimport { matchContext } from './matchContext'\nimport { SafeFragment } from './SafeFragment'\nimport { renderRouteNotFound } from './renderRouteNotFound'\nimport { ScrollRestoration } from './scroll-restoration'\nimport { ClientOnly } from './ClientOnly'\nimport type {\n  AnyRoute,\n  ParsedLocation,\n  RootRouteOptions,\n} from '@tanstack/router-core'\n\nexport const Match = React.memo(function MatchImpl({\n  matchId,\n}: {\n  matchId: string\n}) {\n  const router = useRouter()\n  const matchState = useRouterState({\n    select: (s) => {\n      const match = s.matches.find((d) => d.id === matchId)\n      invariant(\n        match,\n        `Could not find match for matchId \"${matchId}\". Please file an issue!`,\n      )\n      return pick(match, ['routeId', 'ssr', '_displayPending'])\n    },\n    structuralSharing: true as any,\n  })\n\n  const route: AnyRoute = router.routesById[matchState.routeId]\n\n  const PendingComponent =\n    route.options.pendingComponent ?? router.options.defaultPendingComponent\n\n  const pendingElement = PendingComponent ? <PendingComponent /> : null\n\n  const routeErrorComponent =\n    route.options.errorComponent ?? router.options.defaultErrorComponent\n\n  const routeOnCatch = route.options.onCatch ?? router.options.defaultOnCatch\n\n  const routeNotFoundComponent = route.isRoot\n    ? // If it's the root route, use the globalNotFound option, with fallback to the notFoundRoute's component\n      (route.options.notFoundComponent ??\n      router.options.notFoundRoute?.options.component)\n    : route.options.notFoundComponent\n\n  const resolvedNoSsr =\n    matchState.ssr === false || matchState.ssr === 'data-only'\n  const ResolvedSuspenseBoundary =\n    // If we're on the root route, allow forcefully wrapping in suspense\n    (!route.isRoot || route.options.wrapInSuspense || resolvedNoSsr) &&\n    (route.options.wrapInSuspense ??\n      PendingComponent ??\n      ((route.options.errorComponent as any)?.preload || resolvedNoSsr))\n      ? React.Suspense\n      : SafeFragment\n\n  const ResolvedCatchBoundary = routeErrorComponent\n    ? CatchBoundary\n    : SafeFragment\n\n  const ResolvedNotFoundBoundary = routeNotFoundComponent\n    ? CatchNotFound\n    : SafeFragment\n\n  const resetKey = useRouterState({\n    select: (s) => s.loadedAt,\n  })\n\n  const parentRouteId = useRouterState({\n    select: (s) => {\n      const index = s.matches.findIndex((d) => d.id === matchId)\n      return s.matches[index - 1]?.routeId as string\n    },\n  })\n\n  const ShellComponent = route.isRoot\n    ? ((route.options as RootRouteOptions).shellComponent ?? SafeFragment)\n    : SafeFragment\n  return (\n    <ShellComponent>\n      <matchContext.Provider value={matchId}>\n        <ResolvedSuspenseBoundary fallback={pendingElement}>\n          <ResolvedCatchBoundary\n            getResetKey={() => resetKey}\n            errorComponent={routeErrorComponent || ErrorComponent}\n            onCatch={(error, errorInfo) => {\n              // Forward not found errors (we don't want to show the error component for these)\n              if (isNotFound(error)) throw error\n              warning(false, `Error in route match: ${matchId}`)\n              routeOnCatch?.(error, errorInfo)\n            }}\n          >\n            <ResolvedNotFoundBoundary\n              fallback={(error) => {\n                // If the current not found handler doesn't exist or it has a\n                // route ID which doesn't match the current route, rethrow the error\n                if (\n                  !routeNotFoundComponent ||\n                  (error.routeId && error.routeId !== matchState.routeId) ||\n                  (!error.routeId && !route.isRoot)\n                )\n                  throw error\n\n                return React.createElement(routeNotFoundComponent, error as any)\n              }}\n            >\n              {resolvedNoSsr || matchState._displayPending ? (\n                <ClientOnly fallback={pendingElement}>\n                  <MatchInner matchId={matchId} />\n                </ClientOnly>\n              ) : (\n                <MatchInner matchId={matchId} />\n              )}\n            </ResolvedNotFoundBoundary>\n          </ResolvedCatchBoundary>\n        </ResolvedSuspenseBoundary>\n      </matchContext.Provider>\n      {parentRouteId === rootRouteId && router.options.scrollRestoration ? (\n        <>\n          <OnRendered />\n          <ScrollRestoration />\n        </>\n      ) : null}\n    </ShellComponent>\n  )\n})\n\n// On Rendered can't happen above the root layout because it actually\n// renders a dummy dom element to track the rendered state of the app.\n// We render a script tag with a key that changes based on the current\n// location state.__TSR_key. Also, because it's below the root layout, it\n// allows us to fire onRendered events even after a hydration mismatch\n// error that occurred above the root layout (like bad head/link tags,\n// which is common).\nfunction OnRendered() {\n  const router = useRouter()\n\n  const prevLocationRef = React.useRef<undefined | ParsedLocation<{}>>(\n    undefined,\n  )\n\n  return (\n    <script\n      key={router.latestLocation.state.__TSR_key}\n      suppressHydrationWarning\n      ref={(el) => {\n        if (\n          el &&\n          (prevLocationRef.current === undefined ||\n            prevLocationRef.current.href !== router.latestLocation.href)\n        ) {\n          router.emit({\n            type: 'onRendered',\n            ...getLocationChangeInfo(router.state),\n          })\n          prevLocationRef.current = router.latestLocation\n        }\n      }}\n    />\n  )\n}\n\nexport const MatchInner = React.memo(function MatchInnerImpl({\n  matchId,\n}: {\n  matchId: string\n}): any {\n  const router = useRouter()\n\n  const { match, key, routeId } = useRouterState({\n    select: (s) => {\n      const matchIndex = s.matches.findIndex((d) => d.id === matchId)\n      const match = s.matches[matchIndex]!\n      const routeId = match.routeId as string\n\n      const remountFn =\n        (router.routesById[routeId] as AnyRoute).options.remountDeps ??\n        router.options.defaultRemountDeps\n      const remountDeps = remountFn?.({\n        routeId,\n        loaderDeps: match.loaderDeps,\n        params: match._strictParams,\n        search: match._strictSearch,\n      })\n      const key = remountDeps ? JSON.stringify(remountDeps) : undefined\n\n      return {\n        key,\n        routeId,\n        match: pick(match, [\n          'id',\n          'status',\n          'error',\n          '_forcePending',\n          '_displayPending',\n        ]),\n      }\n    },\n    structuralSharing: true as any,\n  })\n\n  const route = router.routesById[routeId] as AnyRoute\n\n  const out = React.useMemo(() => {\n    const Comp = route.options.component ?? router.options.defaultComponent\n    if (Comp) {\n      return <Comp key={key} />\n    }\n    return <Outlet />\n  }, [key, route.options.component, router.options.defaultComponent])\n\n  if (match._displayPending) {\n    throw router.getMatch(match.id)?.displayPendingPromise\n  }\n\n  if (match._forcePending) {\n    throw router.getMatch(match.id)?.minPendingPromise\n  }\n\n  // see also hydrate() in packages/router-core/src/ssr/ssr-client.ts\n  if (match.status === 'pending') {\n    // We're pending, and if we have a minPendingMs, we need to wait for it\n    const pendingMinMs =\n      route.options.pendingMinMs ?? router.options.defaultPendingMinMs\n\n    if (pendingMinMs && !router.getMatch(match.id)?.minPendingPromise) {\n      // Create a promise that will resolve after the minPendingMs\n      if (!router.isServer) {\n        const minPendingPromise = createControlledPromise<void>()\n\n        Promise.resolve().then(() => {\n          router.updateMatch(match.id, (prev) => ({\n            ...prev,\n            minPendingPromise,\n          }))\n        })\n\n        setTimeout(() => {\n          minPendingPromise.resolve()\n\n          // We've handled the minPendingPromise, so we can delete it\n          router.updateMatch(match.id, (prev) => ({\n            ...prev,\n            minPendingPromise: undefined,\n          }))\n        }, pendingMinMs)\n      }\n    }\n    throw router.getMatch(match.id)?.loadPromise\n  }\n\n  if (match.status === 'notFound') {\n    invariant(isNotFound(match.error), 'Expected a notFound error')\n    return renderRouteNotFound(router, route, match.error)\n  }\n\n  if (match.status === 'redirected') {\n    // Redirects should be handled by the router transition. If we happen to\n    // encounter a redirect here, it's a bug. Let's warn, but render nothing.\n    invariant(isRedirect(match.error), 'Expected a redirect error')\n\n    // warning(\n    //   false,\n    //   'Tried to render a redirected route match! This is a weird circumstance, please file an issue!',\n    // )\n    throw router.getMatch(match.id)?.loadPromise\n  }\n\n  if (match.status === 'error') {\n    // If we're on the server, we need to use React's new and super\n    // wonky api for throwing errors from a server side render inside\n    // of a suspense boundary. This is the only way to get\n    // renderToPipeableStream to not hang indefinitely.\n    // We'll serialize the error and rethrow it on the client.\n    if (router.isServer) {\n      const RouteErrorComponent =\n        (route.options.errorComponent ??\n          router.options.defaultErrorComponent) ||\n        ErrorComponent\n      return (\n        <RouteErrorComponent\n          error={match.error as any}\n          reset={undefined as any}\n          info={{\n            componentStack: '',\n          }}\n        />\n      )\n    }\n\n    throw match.error\n  }\n\n  return out\n})\n\nexport const Outlet = React.memo(function OutletImpl() {\n  const router = useRouter()\n  const matchId = React.useContext(matchContext)\n  const routeId = useRouterState({\n    select: (s) => s.matches.find((d) => d.id === matchId)?.routeId as string,\n  })\n\n  const route = router.routesById[routeId]!\n\n  const parentGlobalNotFound = useRouterState({\n    select: (s) => {\n      const matches = s.matches\n      const parentMatch = matches.find((d) => d.id === matchId)\n      invariant(\n        parentMatch,\n        `Could not find parent match for matchId \"${matchId}\"`,\n      )\n      return parentMatch.globalNotFound\n    },\n  })\n\n  const childMatchId = useRouterState({\n    select: (s) => {\n      const matches = s.matches\n      const index = matches.findIndex((d) => d.id === matchId)\n      return matches[index + 1]?.id\n    },\n  })\n\n  const pendingElement = router.options.defaultPendingComponent ? (\n    <router.options.defaultPendingComponent />\n  ) : null\n\n  if (parentGlobalNotFound) {\n    return renderRouteNotFound(router, route, undefined)\n  }\n\n  if (!childMatchId) {\n    return null\n  }\n\n  const nextMatch = <Match matchId={childMatchId} />\n\n  if (matchId === rootRouteId) {\n    return (\n      <React.Suspense fallback={pendingElement}>{nextMatch}</React.Suspense>\n    )\n  }\n\n  return nextMatch\n})\n", "import * as React from 'react'\nimport warning from 'tiny-warning'\nimport { CatchBoundary, ErrorComponent } from './CatchBoundary'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport { Transitioner } from './Transitioner'\nimport { matchContext } from './matchContext'\nimport { Match } from './Match'\nimport { SafeFragment } from './SafeFragment'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  DeepPartial,\n  Expand,\n  MakeOptionalPathParams,\n  MakeOptionalSearchParams,\n  MakeRouteMatchUnion,\n  MaskOptions,\n  MatchRouteOptions,\n  NoInfer,\n  RegisteredRouter,\n  ResolveRelativePath,\n  ResolveRoute,\n  RouteByPath,\n  RouterState,\n  ToSubOptionsProps,\n} from '@tanstack/router-core'\n\ndeclare module '@tanstack/router-core' {\n  export interface RouteMatchExtensions {\n    meta?: Array<React.JSX.IntrinsicElements['meta'] | undefined>\n    links?: Array<React.JSX.IntrinsicElements['link'] | undefined>\n    scripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n    styles?: Array<React.JSX.IntrinsicElements['style'] | undefined>\n    headScripts?: Array<React.JSX.IntrinsicElements['script'] | undefined>\n  }\n}\n\nexport function Matches() {\n  const router = useRouter()\n\n  const pendingElement = router.options.defaultPendingComponent ? (\n    <router.options.defaultPendingComponent />\n  ) : null\n\n  // Do not render a root Suspense during SSR or hydrating from SSR\n  const ResolvedSuspense =\n    router.isServer || (typeof document !== 'undefined' && router.ssr)\n      ? SafeFragment\n      : React.Suspense\n\n  const inner = (\n    <ResolvedSuspense fallback={pendingElement}>\n      {!router.isServer && <Transitioner />}\n      <MatchesInner />\n    </ResolvedSuspense>\n  )\n\n  return router.options.InnerWrap ? (\n    <router.options.InnerWrap>{inner}</router.options.InnerWrap>\n  ) : (\n    inner\n  )\n}\n\nfunction MatchesInner() {\n  const matchId = useRouterState({\n    select: (s) => {\n      return s.matches[0]?.id\n    },\n  })\n\n  const resetKey = useRouterState({\n    select: (s) => s.loadedAt,\n  })\n\n  return (\n    <matchContext.Provider value={matchId}>\n      <CatchBoundary\n        getResetKey={() => resetKey}\n        errorComponent={ErrorComponent}\n        onCatch={(error) => {\n          warning(\n            false,\n            `The following error wasn't caught by any route! At the very least, consider setting an 'errorComponent' in your RootRoute!`,\n          )\n          warning(false, error.message || error.toString())\n        }}\n      >\n        {matchId ? <Match matchId={matchId} /> : null}\n      </CatchBoundary>\n    </matchContext.Provider>\n  )\n}\n\nexport type UseMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = ToSubOptionsProps<TRouter, TFrom, TTo> &\n  DeepPartial<MakeOptionalSearchParams<TRouter, TFrom, TTo>> &\n  DeepPartial<MakeOptionalPathParams<TRouter, TFrom, TTo>> &\n  MaskOptions<TRouter, TMaskFrom, TMaskTo> &\n  MatchRouteOptions\n\nexport function useMatchRoute<TRouter extends AnyRouter = RegisteredRouter>() {\n  const router = useRouter()\n\n  useRouterState({\n    select: (s) => [s.location.href, s.resolvedLocation?.href, s.status],\n    structuralSharing: true as any,\n  })\n\n  return React.useCallback(\n    <\n      const TFrom extends string = string,\n      const TTo extends string | undefined = undefined,\n      const TMaskFrom extends string = TFrom,\n      const TMaskTo extends string = '',\n    >(\n      opts: UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n    ):\n      | false\n      | Expand<ResolveRoute<TRouter, TFrom, TTo>['types']['allParams']> => {\n      const { pending, caseSensitive, fuzzy, includeSearch, ...rest } = opts\n\n      return router.matchRoute(rest as any, {\n        pending,\n        caseSensitive,\n        fuzzy,\n        includeSearch,\n      })\n    },\n    [router],\n  )\n}\n\nexport type MakeMatchRouteOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = UseMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & {\n  // If a function is passed as a child, it will be given the `isActive` boolean to aid in further styling on the element it returns\n  children?:\n    | ((\n        params?: RouteByPath<\n          TRouter['routeTree'],\n          ResolveRelativePath<TFrom, NoInfer<TTo>>\n        >['types']['allParams'],\n      ) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport function MatchRoute<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string = string,\n  const TTo extends string | undefined = undefined,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(props: MakeMatchRouteOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>): any {\n  const matchRoute = useMatchRoute()\n  const params = matchRoute(props as any) as boolean\n\n  if (typeof props.children === 'function') {\n    return (props.children as any)(params)\n  }\n\n  return params ? props.children : null\n}\n\nexport interface UseMatchesBaseOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing,\n> {\n  select?: (\n    matches: Array<MakeRouteMatchUnion<TRouter>>,\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseMatchesResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected ? Array<MakeRouteMatchUnion<TRouter>> : TSelected\n\nexport function useMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  return useRouterState({\n    select: (state: RouterState<TRouter['routeTree']>) => {\n      const matches = state.matches\n      return opts?.select\n        ? opts.select(matches as Array<MakeRouteMatchUnion<TRouter>>)\n        : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any) as UseMatchesResult<TRouter, TSelected>\n}\n\nexport function useParentMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        0,\n        matches.findIndex((d) => d.id === contextMatchId),\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n\nexport function useChildMatches<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseMatchesBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseMatchesResult<TRouter, TSelected> {\n  const contextMatchId = React.useContext(matchContext)\n\n  return useMatches({\n    select: (matches: Array<MakeRouteMatchUnion<TRouter>>) => {\n      matches = matches.slice(\n        matches.findIndex((d) => d.id === contextMatchId) + 1,\n      )\n      return opts?.select ? opts.select(matches) : matches\n    },\n    structuralSharing: opts?.structuralSharing,\n  } as any)\n}\n", "import { RouterCore } from '@tanstack/router-core'\nimport { createFileRoute, createLazyFileRoute } from './fileRoute'\nimport type { RouterHistory } from '@tanstack/history'\nimport type {\n  AnyRoute,\n  CreateRouterFn,\n  RouterConstructorOptions,\n  TrailingSlashOption,\n} from '@tanstack/router-core'\n\nimport type {\n  ErrorRouteComponent,\n  NotFoundRouteComponent,\n  RouteComponent,\n} from './route'\n\ndeclare module '@tanstack/router-core' {\n  export interface RouterOptionsExtensions {\n    /**\n     * The default `component` a route should use if no component is provided.\n     *\n     * @default Outlet\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultcomponent-property)\n     */\n    defaultComponent?: RouteComponent\n    /**\n     * The default `errorComponent` a route should use if no error component is provided.\n     *\n     * @default ErrorComponent\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaulterrorcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#handling-errors-with-routeoptionserrorcomponent)\n     */\n    defaultErrorComponent?: ErrorRouteComponent\n    /**\n     * The default `pendingComponent` a route should use if no pending component is provided.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#showing-a-pending-component)\n     */\n    defaultPendingComponent?: RouteComponent\n    /**\n     * The default `notFoundComponent` a route should use if no notFound component is provided.\n     *\n     * @default NotFound\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultnotfoundcomponent-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/not-found-errors#default-router-wide-not-found-handling)\n     */\n    defaultNotFoundComponent?: NotFoundRouteComponent\n    /**\n     * A component that will be used to wrap the entire router.\n     *\n     * This is useful for providing a context to the entire router.\n     *\n     * Only non-DOM-rendering components like providers should be used, anything else will cause a hydration error.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#wrap-property)\n     */\n    Wrap?: (props: { children: any }) => React.JSX.Element\n    /**\n     * A component that will be used to wrap the inner contents of the router.\n     *\n     * This is useful for providing a context to the inner contents of the router where you also need access to the router context and hooks.\n     *\n     * Only non-DOM-rendering components like providers should be used, anything else will cause a hydration error.\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#innerwrap-property)\n     */\n    InnerWrap?: (props: { children: any }) => React.JSX.Element\n\n    /**\n     * The default `onCatch` handler for errors caught by the Router ErrorBoundary\n     *\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultoncatch-property)\n     * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#handling-errors-with-routeoptionsoncatch)\n     */\n    defaultOnCatch?: (error: Error, errorInfo: React.ErrorInfo) => void\n  }\n}\n\nexport const createRouter: CreateRouterFn = (options) => {\n  return new Router(options)\n}\n\nexport class Router<\n  in out TRouteTree extends AnyRoute,\n  in out TTrailingSlashOption extends TrailingSlashOption = 'never',\n  in out TDefaultStructuralSharingOption extends boolean = false,\n  in out TRouterHistory extends RouterHistory = RouterHistory,\n  in out TDehydrated extends Record<string, any> = Record<string, any>,\n> extends RouterCore<\n  TRouteTree,\n  TTrailingSlashOption,\n  TDefaultStructuralSharingOption,\n  TRouterHistory,\n  TDehydrated\n> {\n  constructor(\n    options: RouterConstructorOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n  ) {\n    super(options)\n  }\n}\n\nif (typeof globalThis !== 'undefined') {\n  ;(globalThis as any).createFileRoute = createFileRoute\n  ;(globalThis as any).createLazyFileRoute = createLazyFileRoute\n} else if (typeof window !== 'undefined') {\n  ;(window as any).createFileRoute = createFileRoute\n  ;(window as any).createFileRoute = createLazyFileRoute\n}\n", "import * as React from 'react'\nimport { Matches } from './Matches'\nimport { getRouterContext } from './routerContext'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterOptions,\n} from '@tanstack/router-core'\n\nexport function RouterContextProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({\n  router,\n  children,\n  ...rest\n}: RouterProps<TRouter, TDehydrated> & {\n  children: React.ReactNode\n}) {\n  if (Object.keys(rest).length > 0) {\n    // Allow the router to update options on the router instance\n    router.update({\n      ...router.options,\n      ...rest,\n      context: {\n        ...router.options.context,\n        ...rest.context,\n      },\n    } as any)\n  }\n\n  const routerContext = getRouterContext()\n\n  const provider = (\n    <routerContext.Provider value={router as AnyRouter}>\n      {children}\n    </routerContext.Provider>\n  )\n\n  if (router.options.Wrap) {\n    return <router.options.Wrap>{provider}</router.options.Wrap>\n  }\n\n  return provider\n}\n\nexport function RouterProvider<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>({ router, ...rest }: RouterProps<TRouter, TDehydrated>) {\n  return (\n    <RouterContextProvider router={router} {...rest}>\n      <Matches />\n    </RouterContextProvider>\n  )\n}\n\nexport type RouterProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> = Omit<\n  RouterOptions<\n    TRouter['routeTree'],\n    NonNullable<TRouter['options']['trailingSlash']>,\n    NonNullable<TRouter['options']['defaultStructuralSharing']>,\n    TRouter['history'],\n    TDehydrated\n  >,\n  'context'\n> & {\n  router: TRouter\n  context?: Partial<\n    RouterOptions<\n      TRouter['routeTree'],\n      NonNullable<TRouter['options']['trailingSlash']>,\n      NonNullable<TRouter['options']['defaultStructuralSharing']>,\n      TRouter['history'],\n      TDehydrated\n    >['context']\n  >\n}\n", "import {\n  defaultGetScrollRestorationKey,\n  getCssSelector,\n  scrollRestorationCache,\n  setupScrollRestoration,\n} from '@tanstack/router-core'\nimport { useRouter } from './useRouter'\nimport type {\n  ParsedLocation,\n  ScrollRestorationEntry,\n  ScrollRestorationOptions,\n} from '@tanstack/router-core'\n\nfunction useScrollRestoration() {\n  const router = useRouter()\n  setupScrollRestoration(router, true)\n}\n\n/**\n * @deprecated use createRouter's `scrollRestoration` option instead\n */\nexport function ScrollRestoration(_props: ScrollRestorationOptions) {\n  useScrollRestoration()\n\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      \"The ScrollRestoration component is deprecated. Use createRouter's `scrollRestoration` option instead.\",\n    )\n  }\n\n  return null\n}\n\nexport function useElementScrollRestoration(\n  options: (\n    | {\n        id: string\n        getElement?: () => Window | Element | undefined | null\n      }\n    | {\n        id?: string\n        getElement: () => Window | Element | undefined | null\n      }\n  ) & {\n    getKey?: (location: ParsedLocation) => string\n  },\n): ScrollRestorationEntry | undefined {\n  useScrollRestoration()\n\n  const router = useRouter()\n  const getKey = options.getKey || defaultGetScrollRestorationKey\n\n  let elementSelector = ''\n\n  if (options.id) {\n    elementSelector = `[data-scroll-restoration-id=\"${options.id}\"]`\n  } else {\n    const element = options.getElement?.()\n    if (!element) {\n      return\n    }\n    elementSelector =\n      element instanceof Window ? 'window' : getCssSelector(element)\n  }\n\n  const restoreKey = getKey(router.latestLocation)\n  const byKey = scrollRestorationCache?.state[restoreKey]\n  return byKey?.[elementSelector]\n}\n", "import * as React from 'react'\nimport { useRouter } from './useRouter'\nimport type {\n  BlockerFnArgs,\n  HistoryAction,\n  HistoryLocation,\n} from '@tanstack/history'\nimport type {\n  AnyRoute,\n  AnyRouter,\n  ParseRoute,\n  RegisteredRouter,\n} from '@tanstack/router-core'\n\ninterface ShouldBlockFnLocation<\n  out TRouteId,\n  out TFullPath,\n  out TAllParams,\n  out TFullSearchSchema,\n> {\n  routeId: TRouteId\n  fullPath: TFullPath\n  pathname: string\n  params: TAllParams\n  search: TFullSearchSchema\n}\n\ntype AnyShouldBlockFnLocation = ShouldBlockFnLocation<any, any, any, any>\ntype MakeShouldBlockFnLocationUnion<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TRoute extends AnyRoute = ParseRoute<TRouter['routeTree']>,\n> = TRoute extends any\n  ? ShouldBlockFnLocation<\n      TRoute['id'],\n      TRoute['fullPath'],\n      TRoute['types']['allParams'],\n      TRoute['types']['fullSearchSchema']\n    >\n  : never\n\ntype BlockerResolver<TRouter extends AnyRouter = RegisteredRouter> =\n  | {\n      status: 'blocked'\n      current: MakeShouldBlockFnLocationUnion<TRouter>\n      next: MakeShouldBlockFnLocationUnion<TRouter>\n      action: HistoryAction\n      proceed: () => void\n      reset: () => void\n    }\n  | {\n      status: 'idle'\n      current: undefined\n      next: undefined\n      action: undefined\n      proceed: undefined\n      reset: undefined\n    }\n\ntype ShouldBlockFnArgs<TRouter extends AnyRouter = RegisteredRouter> = {\n  current: MakeShouldBlockFnLocationUnion<TRouter>\n  next: MakeShouldBlockFnLocationUnion<TRouter>\n  action: HistoryAction\n}\n\nexport type ShouldBlockFn<TRouter extends AnyRouter = RegisteredRouter> = (\n  args: ShouldBlockFnArgs<TRouter>,\n) => boolean | Promise<boolean>\nexport type UseBlockerOpts<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n> = {\n  shouldBlockFn: ShouldBlockFn<TRouter>\n  enableBeforeUnload?: boolean | (() => boolean)\n  disabled?: boolean\n  withResolver?: TWithResolver\n}\n\ntype LegacyBlockerFn = () => Promise<any> | any\ntype LegacyBlockerOpts = {\n  blockerFn?: LegacyBlockerFn\n  condition?: boolean | any\n}\n\nfunction _resolveBlockerOpts(\n  opts?: UseBlockerOpts | LegacyBlockerOpts | LegacyBlockerFn,\n  condition?: boolean | any,\n): UseBlockerOpts {\n  if (opts === undefined) {\n    return {\n      shouldBlockFn: () => true,\n      withResolver: false,\n    }\n  }\n\n  if ('shouldBlockFn' in opts) {\n    return opts\n  }\n\n  if (typeof opts === 'function') {\n    const shouldBlock = Boolean(condition ?? true)\n\n    const _customBlockerFn = async () => {\n      if (shouldBlock) return await opts()\n      return false\n    }\n\n    return {\n      shouldBlockFn: _customBlockerFn,\n      enableBeforeUnload: shouldBlock,\n      withResolver: false,\n    }\n  }\n\n  const shouldBlock = Boolean(opts.condition ?? true)\n  const fn = opts.blockerFn\n\n  const _customBlockerFn = async () => {\n    if (shouldBlock && fn !== undefined) {\n      return await fn()\n    }\n    return shouldBlock\n  }\n\n  return {\n    shouldBlockFn: _customBlockerFn,\n    enableBeforeUnload: shouldBlock,\n    withResolver: fn === undefined,\n  }\n}\n\nexport function useBlocker<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = false,\n>(\n  opts: UseBlockerOpts<TRouter, TWithResolver>,\n): TWithResolver extends true ? BlockerResolver<TRouter> : void\n\n/**\n * @deprecated Use the shouldBlockFn property instead\n */\nexport function useBlocker(blockerFnOrOpts?: LegacyBlockerOpts): BlockerResolver\n\n/**\n * @deprecated Use the UseBlockerOpts object syntax instead\n */\nexport function useBlocker(\n  blockerFn?: LegacyBlockerFn,\n  condition?: boolean | any,\n): BlockerResolver\n\nexport function useBlocker(\n  opts?: UseBlockerOpts | LegacyBlockerOpts | LegacyBlockerFn,\n  condition?: boolean | any,\n): BlockerResolver | void {\n  const {\n    shouldBlockFn,\n    enableBeforeUnload = true,\n    disabled = false,\n    withResolver = false,\n  } = _resolveBlockerOpts(opts, condition)\n\n  const router = useRouter()\n  const { history } = router\n\n  const [resolver, setResolver] = React.useState<BlockerResolver>({\n    status: 'idle',\n    current: undefined,\n    next: undefined,\n    action: undefined,\n    proceed: undefined,\n    reset: undefined,\n  })\n\n  React.useEffect(() => {\n    const blockerFnComposed = async (blockerFnArgs: BlockerFnArgs) => {\n      function getLocation(\n        location: HistoryLocation,\n      ): AnyShouldBlockFnLocation {\n        const parsedLocation = router.parseLocation(undefined, location)\n        const matchedRoutes = router.getMatchedRoutes(\n          parsedLocation.pathname,\n          undefined,\n        )\n        if (matchedRoutes.foundRoute === undefined) {\n          throw new Error(`No route found for location ${location.href}`)\n        }\n        return {\n          routeId: matchedRoutes.foundRoute.id,\n          fullPath: matchedRoutes.foundRoute.fullPath,\n          pathname: parsedLocation.pathname,\n          params: matchedRoutes.routeParams,\n          search: parsedLocation.search,\n        }\n      }\n\n      const current = getLocation(blockerFnArgs.currentLocation)\n      const next = getLocation(blockerFnArgs.nextLocation)\n\n      const shouldBlock = await shouldBlockFn({\n        action: blockerFnArgs.action,\n        current,\n        next,\n      })\n      if (!withResolver) {\n        return shouldBlock\n      }\n\n      if (!shouldBlock) {\n        return false\n      }\n\n      const promise = new Promise<boolean>((resolve) => {\n        setResolver({\n          status: 'blocked',\n          current,\n          next,\n          action: blockerFnArgs.action,\n          proceed: () => resolve(false),\n          reset: () => resolve(true),\n        })\n      })\n\n      const canNavigateAsync = await promise\n      setResolver({\n        status: 'idle',\n        current: undefined,\n        next: undefined,\n        action: undefined,\n        proceed: undefined,\n        reset: undefined,\n      })\n\n      return canNavigateAsync\n    }\n\n    return disabled\n      ? undefined\n      : history.block({ blockerFn: blockerFnComposed, enableBeforeUnload })\n  }, [\n    shouldBlockFn,\n    enableBeforeUnload,\n    disabled,\n    withResolver,\n    history,\n    router,\n  ])\n\n  return resolver\n}\n\nconst _resolvePromptBlockerArgs = (\n  props: PromptProps | LegacyPromptProps,\n): UseBlockerOpts => {\n  if ('shouldBlockFn' in props) {\n    return { ...props }\n  }\n\n  const shouldBlock = Boolean(props.condition ?? true)\n  const fn = props.blockerFn\n\n  const _customBlockerFn = async () => {\n    if (shouldBlock && fn !== undefined) {\n      return await fn()\n    }\n    return shouldBlock\n  }\n\n  return {\n    shouldBlockFn: _customBlockerFn,\n    enableBeforeUnload: shouldBlock,\n    withResolver: fn === undefined,\n  }\n}\n\nexport function Block<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n>(opts: PromptProps<TRouter, TWithResolver>): React.ReactNode\n\n/**\n *  @deprecated Use the UseBlockerOpts property instead\n */\nexport function Block(opts: LegacyPromptProps): React.ReactNode\n\nexport function Block(opts: PromptProps | LegacyPromptProps): React.ReactNode {\n  const { children, ...rest } = opts\n  const args = _resolvePromptBlockerArgs(rest)\n\n  const resolver = useBlocker(args)\n  return children\n    ? typeof children === 'function'\n      ? children(resolver as any)\n      : children\n    : null\n}\n\ntype LegacyPromptProps = {\n  blockerFn?: LegacyBlockerFn\n  condition?: boolean | any\n  children?: React.ReactNode | ((params: BlockerResolver) => React.ReactNode)\n}\n\ntype PromptProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TWithResolver extends boolean = boolean,\n  TParams = TWithResolver extends true ? BlockerResolver<TRouter> : void,\n> = UseBlockerOpts<TRouter, TWithResolver> & {\n  children?: React.ReactNode | ((params: TParams) => React.ReactNode)\n}\n", "import { useMatch } from './useMatch'\nimport type {\n  AnyRout<PERSON>,\n  RegisteredRouter,\n  UseRouteContextBaseOptions,\n  UseRouteContextOptions,\n  UseRouteContextResult,\n} from '@tanstack/router-core'\n\nexport type UseRouteContextRoute<out TFrom> = <\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n>(\n  opts?: UseRouteContextBaseOptions<TRouter, TFrom, true, TSelected>,\n) => UseRouteContextResult<TRouter, TFrom, true, TSelected>\n\nexport function useRouteContext<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TFrom extends string | undefined = undefined,\n  TStrict extends boolean = true,\n  TSelected = unknown,\n>(\n  opts: UseRouteContextOptions<TRouter, TFrom, TStrict, TSelected>,\n): UseRouteContextResult<TRouter, TFrom, TStrict, TSelected> {\n  return useMatch({\n    ...(opts as any),\n    select: (match) =>\n      opts.select ? opts.select(match.context) : match.context,\n  }) as UseRouteContextResult<TRouter, TFrom, TStrict, TSelected>\n}\n", "import { useRouterState } from './useRouterState'\nimport type {\n  StructuralSharingOption,\n  ValidateSelected,\n} from './structuralSharing'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  RouterState,\n} from '@tanstack/router-core'\n\nexport interface UseLocationBaseOptions<\n  TRouter extends AnyRouter,\n  TSelected,\n  TStructuralSharing extends boolean = boolean,\n> {\n  select?: (\n    state: RouterState<TRouter['routeTree']>['location'],\n  ) => ValidateSelected<TRouter, TSelected, TStructuralSharing>\n}\n\nexport type UseLocationResult<\n  TRouter extends AnyRouter,\n  TSelected,\n> = unknown extends TSelected\n  ? RouterState<TRouter['routeTree']>['location']\n  : TSelected\n\nexport function useLocation<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TSelected = unknown,\n  TStructuralSharing extends boolean = boolean,\n>(\n  opts?: UseLocationBaseOptions<TRouter, TSelected, TStructuralSharing> &\n    StructuralSharingOption<TRouter, TSelected, TStructuralSharing>,\n): UseLocationResult<TRouter, TSelected> {\n  return useRouterState({\n    select: (state: any) =>\n      opts?.select ? opts.select(state.location) : state.location,\n  } as any) as UseLocationResult<TRouter, TSelected>\n}\n", "import { useRouterState } from './useRouterState'\n\nexport function useCanGoBack() {\n  return useRouterState({ select: (s) => s.location.state.__TSR_index !== 0 })\n}\n", "import * as React from 'react'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\ninterface ScriptAttrs {\n  [key: string]: string | boolean | undefined\n  src?: string\n  suppressHydrationWarning?: boolean\n}\n\nexport function Asset({\n  tag,\n  attrs,\n  children,\n}: RouterManagedTag): React.ReactElement | null {\n  switch (tag) {\n    case 'title':\n      return (\n        <title {...attrs} suppressHydrationWarning>\n          {children}\n        </title>\n      )\n    case 'meta':\n      return <meta {...attrs} suppressHydrationWarning />\n    case 'link':\n      return <link {...attrs} suppressHydrationWarning />\n    case 'style':\n      return (\n        <style\n          {...attrs}\n          dangerouslySetInnerHTML={{ __html: children as string }}\n        />\n      )\n    case 'script':\n      return <Script attrs={attrs}>{children}</Script>\n    default:\n      return null\n  }\n}\n\nfunction Script({\n  attrs,\n  children,\n}: {\n  attrs?: ScriptAttrs\n  children?: string\n}) {\n  React.useEffect(() => {\n    if (attrs?.src) {\n      const script = document.createElement('script')\n\n      for (const [key, value] of Object.entries(attrs)) {\n        if (\n          key !== 'suppressHydrationWarning' &&\n          value !== undefined &&\n          value !== false\n        ) {\n          script.setAttribute(\n            key,\n            typeof value === 'boolean' ? '' : String(value),\n          )\n        }\n      }\n\n      document.head.appendChild(script)\n\n      return () => {\n        if (script.parentNode) {\n          script.parentNode.removeChild(script)\n        }\n      }\n    }\n\n    if (typeof children === 'string') {\n      const script = document.createElement('script')\n      script.textContent = children\n\n      if (attrs) {\n        for (const [key, value] of Object.entries(attrs)) {\n          if (\n            key !== 'suppressHydrationWarning' &&\n            value !== undefined &&\n            value !== false\n          ) {\n            script.setAttribute(\n              key,\n              typeof value === 'boolean' ? '' : String(value),\n            )\n          }\n        }\n      }\n\n      document.head.appendChild(script)\n\n      return () => {\n        if (script.parentNode) {\n          script.parentNode.removeChild(script)\n        }\n      }\n    }\n\n    return undefined\n  }, [attrs, children])\n\n  if (attrs?.src && typeof attrs.src === 'string') {\n    return <script {...attrs} suppressHydrationWarning />\n  }\n\n  if (typeof children === 'string') {\n    return (\n      <script\n        {...attrs}\n        dangerouslySetInnerHTML={{ __html: children }}\n        suppressHydrationWarning\n      />\n    )\n  }\n\n  return null\n}\n", "import * as React from 'react'\nimport { Asset } from './Asset'\nimport { useRouter } from './useRouter'\nimport { useRouterState } from './useRouterState'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\nexport const useTags = () => {\n  const router = useRouter()\n\n  const routeMeta = useRouterState({\n    select: (state) => {\n      return state.matches.map((match) => match.meta!).filter(Boolean)\n    },\n  })\n\n  const meta: Array<RouterManagedTag> = React.useMemo(() => {\n    const resultMeta: Array<RouterManagedTag> = []\n    const metaByAttribute: Record<string, true> = {}\n    let title: RouterManagedTag | undefined\n    ;[...routeMeta].reverse().forEach((metas) => {\n      ;[...metas].reverse().forEach((m) => {\n        if (!m) return\n\n        if (m.title) {\n          if (!title) {\n            title = {\n              tag: 'title',\n              children: m.title,\n            }\n          }\n        } else {\n          const attribute = m.name ?? m.property\n          if (attribute) {\n            if (metaByAttribute[attribute]) {\n              return\n            } else {\n              metaByAttribute[attribute] = true\n            }\n          }\n\n          resultMeta.push({\n            tag: 'meta',\n            attrs: {\n              ...m,\n            },\n          })\n        }\n      })\n    })\n\n    if (title) {\n      resultMeta.push(title)\n    }\n\n    resultMeta.reverse()\n\n    return resultMeta\n  }, [routeMeta])\n\n  const links = useRouterState({\n    select: (state) => {\n      const constructed = state.matches\n        .map((match) => match.links!)\n        .filter(Boolean)\n        .flat(1)\n        .map((link) => ({\n          tag: 'link',\n          attrs: {\n            ...link,\n          },\n        })) satisfies Array<RouterManagedTag>\n\n      const manifest = router.ssr?.manifest\n\n      // These are the assets extracted from the ViteManifest\n      // using the `startManifestPlugin`\n      const assets = state.matches\n        .map((match) => manifest?.routes[match.routeId]?.assets ?? [])\n        .filter(Boolean)\n        .flat(1)\n        .filter((asset) => asset.tag === 'link')\n        .map(\n          (asset) =>\n            ({\n              tag: 'link',\n              attrs: {\n                ...asset.attrs,\n                suppressHydrationWarning: true,\n              },\n            }) satisfies RouterManagedTag,\n        )\n\n      return [...constructed, ...assets]\n    },\n    structuralSharing: true as any,\n  })\n\n  const preloadMeta = useRouterState({\n    select: (state) => {\n      const preloadMeta: Array<RouterManagedTag> = []\n\n      state.matches\n        .map((match) => router.looseRoutesById[match.routeId]!)\n        .forEach((route) =>\n          router.ssr?.manifest?.routes[route.id]?.preloads\n            ?.filter(Boolean)\n            .forEach((preload) => {\n              preloadMeta.push({\n                tag: 'link',\n                attrs: {\n                  rel: 'modulepreload',\n                  href: preload,\n                },\n              })\n            }),\n        )\n\n      return preloadMeta\n    },\n    structuralSharing: true as any,\n  })\n\n  const styles = useRouterState({\n    select: (state) =>\n      (\n        state.matches\n          .map((match) => match.styles!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...attrs }) => ({\n        tag: 'style',\n        attrs,\n        children,\n      })),\n    structuralSharing: true as any,\n  })\n\n  const headScripts = useRouterState({\n    select: (state) =>\n      (\n        state.matches\n          .map((match) => match.headScripts!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...script }) => ({\n        tag: 'script',\n        attrs: {\n          ...script,\n        },\n        children,\n      })),\n    structuralSharing: true as any,\n  })\n\n  return uniqBy(\n    [\n      ...meta,\n      ...preloadMeta,\n      ...links,\n      ...styles,\n      ...headScripts,\n    ] as Array<RouterManagedTag>,\n    (d) => {\n      return JSON.stringify(d)\n    },\n  )\n}\n\n/**\n * @description The `HeadContent` component is used to render meta tags, links, and scripts for the current route.\n * It should be rendered in the `<head>` of your document.\n */\nexport function HeadContent() {\n  const tags = useTags()\n  return tags.map((tag) => (\n    <Asset {...tag} key={`tsr-meta-${JSON.stringify(tag)}`} />\n  ))\n}\n\nfunction uniqBy<T>(arr: Array<T>, fn: (item: T) => string) {\n  const seen = new Set<string>()\n  return arr.filter((item) => {\n    const key = fn(item)\n    if (seen.has(key)) {\n      return false\n    }\n    seen.add(key)\n    return true\n  })\n}\n", "import { Asset } from './Asset'\nimport { useRouterState } from './useRouterState'\nimport { useRouter } from './useRouter'\nimport type { RouterManagedTag } from '@tanstack/router-core'\n\nexport const Scripts = () => {\n  const router = useRouter()\n\n  const assetScripts = useRouterState({\n    select: (state) => {\n      const assetScripts: Array<RouterManagedTag> = []\n      const manifest = router.ssr?.manifest\n\n      if (!manifest) {\n        return []\n      }\n\n      state.matches\n        .map((match) => router.looseRoutesById[match.routeId]!)\n        .forEach((route) =>\n          manifest.routes[route.id]?.assets\n            ?.filter((d) => d.tag === 'script')\n            .forEach((asset) => {\n              assetScripts.push({\n                tag: 'script',\n                attrs: asset.attrs,\n                children: asset.children,\n              } as any)\n            }),\n        )\n\n      return assetScripts\n    },\n    structuralSharing: true as any,\n  })\n\n  const { scripts } = useRouterState({\n    select: (state) => ({\n      scripts: (\n        state.matches\n          .map((match) => match.scripts!)\n          .flat(1)\n          .filter(Boolean) as Array<RouterManagedTag>\n      ).map(({ children, ...script }) => ({\n        tag: 'script',\n        attrs: {\n          ...script,\n          suppressHydrationWarning: true,\n        },\n        children,\n      })),\n    }),\n    structuralSharing: true as any,\n  })\n\n  const allScripts = [...scripts, ...assetScripts] as Array<RouterManagedTag>\n\n  return (\n    <>\n      {allScripts.map((asset, i) => (\n        <Asset {...asset} key={`tsr-scripts-${asset.tag}-${i}`} />\n      ))}\n    </>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIA,UAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,uBAAuB,KAAK,sBAC5BC,UAASD,QAAM,QACfE,aAAYF,QAAM,WAClBG,WAAUH,QAAM,SAChB,gBAAgBA,QAAM;AACxB,cAAQ,mCAAmC,SACzCI,YACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAUH,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQ,qBAAqBC,YAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAF;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA,IAAI,eAAe;AACnB,SAAS,QAAQ,WAAW,SAAS;AACnC,MAAI,CAAC,cAAc;AACjB,QAAI,WAAW;AACb;AAAA,IACF;AAEA,QAAI,OAAO,cAAc;AAEzB,QAAI,OAAO,YAAY,aAAa;AAClC,cAAQ,KAAK,IAAI;AAAA,IACnB;AAEA,QAAI;AACF,YAAM,MAAM,IAAI;AAAA,IAClB,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAO,2BAAQ;A;;;;ACVR,SAAS,WAAc;EAC5B,SAAS;AACX,GAA6C;AACrC,QAAA,UAAU,MAAM,QAAQ;AAE9B,MAAI,QAAQ,oBAAoB,EAAE,WAAW,WAAW;AAChD,UAAA;EAAA;AAGR,MAAI,QAAQ,oBAAoB,EAAE,WAAW,SAAS;AAC9C,UAAA,QAAQ,oBAAoB,EAAE;EAAA;AAGtC,SAAO,CAAC,QAAQ,oBAAoB,EAAE,MAAM,OAAO;AACrD;AAEO,SAAS,MACd,OAIA;AACA,QAAM,YAAQ,wBAAC,YAAY,EAAA,GAAG,MAAO,CAAA;AACrC,MAAI,MAAM,UAAU;AAClB,eAAA,wBAAc,gBAAN,EAAe,UAAU,MAAM,UAAW,UAAM,MAAA,CAAA;EAAA;AAEnD,SAAA;AACT;AAEA,SAAS,WACP,OAImB;AACnB,QAAM,CAAC,IAAI,IAAI,WAAW,KAAK;AAExB,SAAA,MAAM,SAAS,IAAI;AAC5B;;;;;AC3CO,SAAS,cAAc,OAK3B;AACK,QAAA,iBAAiB,MAAM,kBAAkB;AAG7C,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,MAAM;MACf,UAAU,CAAC,EAAE,OAAO,MAAA,MAAY;AAC9B,YAAI,OAAO;AACF,iBAAM,qBAAc,gBAAgB;YACzC;YACA;UAAA,CACD;QAAA;AAGH,eAAO,MAAM;MAAA;IACf;EACF;AAEJ;AAEA,IAAM,oBAAN,cAAsC,iBAOnC;EAPH,cAAA;AAAA,UAAA,GAAA,SAAA;AAQU,SAAA,QAAA,EAAE,OAAO,KAAK;EAAA;EACtB,OAAO,yBAAyB,OAAY;AAC1C,WAAO,EAAE,UAAU,MAAM,YAAA,EAAc;EAAA;EAEzC,OAAO,yBAAyB,OAAc;AAC5C,WAAO,EAAE,MAAM;EAAA;EAEjB,QAAQ;AACN,SAAK,SAAS,EAAE,OAAO,KAAA,CAAM;EAAA;EAE/B,mBACE,WAKA,WACM;AACN,QAAI,UAAU,SAAS,UAAU,aAAa,KAAK,MAAM,UAAU;AACjE,WAAK,MAAM;IAAA;EACb;EAEF,kBAAkB,OAAc,WAAsB;AAChD,QAAA,KAAK,MAAM,SAAS;AACjB,WAAA,MAAM,QAAQ,OAAO,SAAS;IAAA;EACrC;EAEF,SAAS;AAEA,WAAA,KAAK,MAAM,SAAS;MACzB,OACE,KAAK,MAAM,aAAa,KAAK,MAAM,YAAA,IAC/B,OACA,KAAK,MAAM;MACjB,OAAO,MAAM;AACX,aAAK,MAAM;MAAA;IACb,CACD;EAAA;AAEL;AAEgB,SAAA,eAAe,EAAE,MAAA,GAAyB;AAClD,QAAA,CAAC,MAAM,OAAO,IAAU,gBAAS,IAAqC;AAG1E,aAAA,0BAAC,OAAA,EAAI,OAAO,EAAE,SAAS,SAAS,UAAU,OACxC,GAAA,UAAA;QAAC,0BAAA,OAAA,EAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,QAAA,GACxD,UAAA;UAAA,yBAAC,UAAA,EAAO,OAAO,EAAE,UAAU,OAAA,GAAU,UAAqB,wBAAA,CAAA;UAC1D;QAAC;QAAA;UACC,OAAO;YACL,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,cAAc;UAChB;UACA,SAAS,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC;UAE/B,UAAA,OAAO,eAAe;QAAA;MAAA;IACzB,EAAA,CACF;QAAA,yBACC,OAAI,EAAA,OAAO,EAAE,QAAQ,SAAA,EAAA,CAAY;IACjC,WAAA,yBACE,OACC,EAAA,cAAA;MAAC;MAAA;QACC,OAAO;UACL,UAAU;UACV,QAAQ;UACR,cAAc;UACd,SAAS;UACT,OAAO;UACP,UAAU;QACZ;QAEC,UAAA,MAAM,cAAU,yBAAC,QAAM,EAAA,UAAA,MAAM,QAAA,CAAQ,IAAU;MAAA;IAAA,EAAA,CAEpD,IACE;EAAA,EAAA,CACN;AAEJ;;;;;ACzFO,SAAS,WAAW,EAAE,UAAU,WAAW,KAAA,GAAyB;AACzE,SAAO,YAAY,QAChB,yBAAAG,aAAAA,QAAM,UAAN,EAAgB,SAAS,CAAA,QAEzB,yBAAAA,aAAAA,QAAM,UAAN,EAAgB,UAAS,SAAA,CAAA;AAE9B;AAqBA,SAAS,cAAuB;AAC9B,SAAOA,aAAAA,QAAM;IACX;IACA,MAAM;IACN,MAAM;EACR;AACF;AAEA,SAAS,YAAY;AACnB,SAAO,MAAM;EAAC;AAChB;A;;;;;;;;;;ACjDO,SAAS,SACd,OACA,WAAkD,CAAC,MAAM,GAC9C;AACX,QAAM,YAAQ;IACZ,MAAM;IACN,MAAM,MAAM;IACZ,MAAM,MAAM;IACZ;IACA;EAAA;AAGF,SAAO;AACT;AAEO,SAAS,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;EAAA;AAGT,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;EAAA;AAGT,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,GAAG,CAAC,KAAK,MAAM;AACzB,UAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAG,QAAO;IAAA;AAEzD,WAAO;EAAA;AAGT,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,KAAK,MAAM;AACpB,UAAI,CAAC,KAAK,IAAI,CAAC,EAAG,QAAO;IAAA;AAE3B,WAAO;EAAA;AAGT,MAAI,gBAAgB,QAAQ,gBAAgB,MAAM;AAChD,QAAI,KAAK,QAAA,MAAc,KAAK,QAAA,EAAW,QAAO;AAC9C,WAAO;EAAA;AAGT,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACA,aAAO;IAAA;EACT;AAEF,SAAO;AACT;A;;;;;;;;;ACzEA,IAAM,gBAAsB,qBAAyB,IAAK;AAEnD,SAAS,mBAAmB;AAC7B,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EAAA;AAGT,MAAI,OAAO,wBAAwB;AACjC,WAAO,OAAO;EAAA;AAGhB,SAAO,yBAAyB;AAEzB,SAAA;AACT;;;AClBO,SAAS,UAAwD,MAE5D;AACV,QAAM,QAAc,kBAAW,iBAAA,CAAkB;AACjD;IACE,IAAG,QAAA,OAAA,SAAA,KAAM,SAAQ,SAAS,CAAC;IAC3B;EACF;AACO,SAAA;AACT;;;ACgBO,SAAS,eAKd,MAC0C;AAC1C,QAAM,gBAAgB,UAAmB;IACvC,OAAM,QAAA,OAAA,SAAA,KAAM,YAAW;EAAA,CACxB;AACK,QAAA,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU;AACzB,QAAA,qBACJ,sBAAiE,MAAS;AAE5E,SAAO,SAAS,OAAO,SAAS,CAAC,UAAU;AACzC,QAAI,QAAA,OAAA,SAAA,KAAM,QAAQ;AAChB,UAAI,KAAK,qBAAqB,OAAO,QAAQ,0BAA0B;AACrE,cAAM,WAAW;UACf,eAAe;UACf,KAAK,OAAO,KAAK;QACnB;AACA,uBAAe,UAAU;AAClB,eAAA;MAAA;AAEF,aAAA,KAAK,OAAO,KAAK;IAAA;AAEnB,WAAA;EAAA,CACR;AACH;;;;ACxDa,IAAA,eAAqB,qBAAkC,MAAS;AAGtE,IAAM,oBAA0B;EACrC;AACF;;;ACsEO,SAAS,SAQd,MAQ6E;AAC7E,QAAM,iBAAuB;IAC3B,KAAK,OAAO,oBAAoB;EAClC;AAEA,QAAM,iBAAiB,eAAe;IACpC,QAAQ,CAAC,UAAe;AAChB,YAAA,QAAQ,MAAM,QAAQ;QAAK,CAAC,MAChC,KAAK,OAAO,KAAK,SAAS,EAAE,UAAU,EAAE,OAAO;MACjD;AACA;QACE,GAAG,KAAK,eAAe,SAAS,CAAC;QACjC,kBAAkB,KAAK,OAAO,yBAAyB,KAAK,IAAI,MAAM,kBAAkB;MAC1F;AAEA,UAAI,UAAU,QAAW;AAChB,eAAA;MAAA;AAGT,aAAO,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI;IAC5C;IACA,mBAAmB,KAAK;EAAA,CAClB;AAED,SAAA;AACT;;;AC9DO,SAAS,cAOd,MAOyD;AACzD,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,mBAAmB,KAAK;IACxB,QAAQ,CAAC,MAAW;AAClB,aAAO,KAAK,SAAS,KAAK,OAAO,EAAE,UAAU,IAAI,EAAE;IAAA;EACrD,CACM;AACV;;;ACrCO,SAAS,cAMd,MACgD;AAChD,QAAM,EAAE,QAAQ,GAAG,KAAA,IAAS;AAC5B,SAAO,SAAS;IACd,GAAG;IACH,QAAQ,CAAC,MAAM;AACb,aAAO,SAAS,OAAO,EAAE,UAAU,IAAI,EAAE;IAAA;EAC3C,CACD;AACH;;;ACMO,SAAS,UAQd,MAWA;AACA,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,aAAa,KAAK;IAClB,mBAAmB,KAAK;IACxB,QAAQ,CAAC,UAAe;AACtB,aAAO,KAAK,SAAS,KAAK,OAAO,MAAM,MAAM,IAAI,MAAM;IAAA;EACzD,CACD;AACH;;;AC7BO,SAAS,UAQd,MAWA;AACA,SAAO,SAAS;IACd,MAAM,KAAK;IACX,QAAQ,KAAK;IACb,aAAa,KAAK;IAClB,mBAAmB,KAAK;IACxB,QAAQ,CAAC,UAAe;AACtB,aAAO,KAAK,SAAS,KAAK,OAAO,MAAM,MAAM,IAAI,MAAM;IAAA;EACzD,CACD;AACH;A;;;ACjFO,SAAS,YAGd,cAEkC;AAClC,QAAM,EAAE,UAAU,MAAM,IAAI,UAAU;AAItC,QAAM,aAAa,SAAS;IAC1B,QAAQ;IACR,QAAQ,CAAC,UAAU,MAAM;EAAA,CAC1B;AAED,SAAa;IACX,CAAC,YAA6B;AACtB,YAAA,OACJ,QAAQ,SACR,gBAAA,OAAA,SAAA,aAAc,SACd,MAAM,QAAQ,UAAU,EAAG;AAE7B,aAAO,SAAS;QACd,GAAG;QACH;MAAA,CACD;IACH;;IAEA,CAAC,gBAAA,OAAA,SAAA,aAAc,MAAM,QAAQ;EAC/B;AACF;AAEO,SAAS,SAMd,OAAuE;AACvE,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAEvB,QAAA,mBAAyB,cAMrB,IAAI;AACd,EAAM,iBAAU,MAAM;AAChB,QAAA,iBAAiB,YAAY,OAAO;AACtC,eAAS,KAAK;AACd,uBAAiB,UAAU;IAAA;EAE5B,GAAA,CAAC,QAAQ,OAAO,QAAQ,CAAC;AACrB,SAAA;AACT;A;;;;;;;;ACjEO,SAAS,kBACd,IACG;AACG,QAAA,QAAc,cAAO,EAAE;AAC7B,QAAM,UAAU;AAEV,QAAA,MAAY,cAAO,IAAI,SAAqB,MAAM,QAAQ,GAAG,IAAI,CAAC;AACxE,SAAO,IAAI;AACb;AAEO,IAAMC,mBACX,OAAO,WAAW,cAAoB,yBAAwB;AAKzD,SAAS,YAAe,OAAoB;AAE3C,QAAA,MAAY,cAAqC;IACrD;IACA,MAAM;EAAA,CACP;AAEK,QAAA,UAAU,IAAI,QAAQ;AAK5B,MAAI,UAAU,SAAS;AACrB,QAAI,UAAU;MACZ;MACA,MAAM;IACR;EAAA;AAIF,SAAO,IAAI,QAAQ;AACrB;AA2BgB,SAAA,wBACd,KACA,UACAC,+BAAwD,CACxD,GAAA,UAAkC,CAAA,GAClC;AACA,EAAM,iBAAU,MAAM;AACpB,QACE,CAAC,IAAI,WACL,QAAQ,YACR,OAAO,yBAAyB,YAChC;AACA;IAAA;AAGF,UAAM,WAAW,IAAI,qBAAqB,CAAC,CAAC,KAAK,MAAM;AACrD,eAAS,KAAK;IAAA,GACbA,4BAA2B;AAErB,aAAA,QAAQ,IAAI,OAAO;AAE5B,WAAO,MAAM;AACX,eAAS,WAAW;IACtB;EAAA,GACC,CAAC,UAAUA,8BAA6B,QAAQ,UAAU,GAAG,CAAC;AACnE;AAeO,SAAS,gBAAmB,KAA6B;AACxD,QAAA,WAAiB,cAAU,IAAI;AACrC,EAAM,2BAAoB,KAAK,MAAM,SAAS,SAAU,CAAA,CAAE;AACnD,SAAA;AACT;;;AClFgB,SAAA,aAOd,SACA,cACkC;AAClC,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,gBAAS,KAAK;AAC5D,QAAA,mBAAyB,cAAO,KAAK;AACrC,QAAA,WAAW,gBAAgB,YAAY;AAEvC,QAAA;;IAEJ;IACA;IACA;IACA;IACA,SAAS;IACT,cAAc;IACd;IACA;IACA,iBAAAC;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,gBAAgB;IAChB,gBAAgB;IAChB,MAAM;IACN;IACA,GAAG;EAAA,IACD;AAQE,QAAA,OAAsC,eAAQ,MAAM;AACpD,QAAA;AACF,UAAI,IAAI,EAAS;AACV,aAAA;IAAA,QACD;IAAA;AACD,WAAA;EAAA,GACN,CAAC,EAAE,CAAC;AAGP,QAAM,gBAAgB,eAAe;IACnC,QAAQ,CAAC,MAAM,EAAE,SAAS;IAC1B,mBAAmB;EAAA,CACpB;AAED,QAAM,OAAO,SAAS;IACpB,QAAQ;IACR,QAAQ,CAAC,UAAU,QAAQ,QAAQ,MAAM;EAAA,CAC1C;AAED,QAAM,OAAa;IACjB,MAAM,OAAO,cAAc,EAAE,GAAG,SAAS,KAAA,CAAa;;IAEtD;MACE;MACA;MACA,QAAQ;MACR;MACA,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;IAAA;EAEZ;AAEA,QAAM,aAAa,SAAS;AAE5B,QAAM,UACJ,QAAQ,kBAAkB,aACtB,QACC,eAAe,OAAO,QAAQ;AACrC,QAAM,eACJ,oBAAoB,OAAO,QAAQ,uBAAuB;AAE5D,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM;AACb,UAAI,WAAmB,QAAA;AACvB,UAAI,iBAAA,OAAA,SAAA,cAAe,OAAO;AACxB,cAAM,YAAY;UAChB,EAAE,SAAS;UACX,KAAK;UACL,OAAO;QACT;AACA,YAAI,CAAC,WAAW;AACP,iBAAA;QAAA;MACT,OACK;AACL,cAAM,mBAAmB;UACvB,EAAE,SAAS;UACX,OAAO;QACT;AACA,cAAM,gBAAgB;UACpB,KAAK;UACL,OAAO;QACT;AAEA,cAAM,mBACJ,iBAAiB,WAAW,aAAa,MACxC,iBAAiB,WAAW,cAAc,UACzC,iBAAiB,cAAc,MAAM,MAAM;AAE/C,YAAI,CAAC,kBAAkB;AACd,iBAAA;QAAA;MACT;AAGE,WAAA,iBAAA,OAAA,SAAA,cAAe,kBAAiB,MAAM;AACxC,cAAM,aAAa,UAAU,EAAE,SAAS,QAAQ,KAAK,QAAQ;UAC3D,SAAS,EAAC,iBAAA,OAAA,SAAA,cAAe;UACzB,iBAAiB,EAAC,iBAAA,OAAA,SAAA,cAAe;QAAA,CAClC;AACD,YAAI,CAAC,YAAY;AACR,iBAAA;QAAA;MACT;AAGF,UAAI,iBAAA,OAAA,SAAA,cAAe,aAAa;AACvB,eAAA,EAAE,SAAS,SAAS,KAAK;MAAA;AAE3B,aAAA;IAAA;EACT,CACD;AAED,QAAM,YAAkB;IACtB,MAAM;AACG,aAAA,aAAa,EAAE,GAAG,SAAS,KAAA,CAAa,EAAE,MAAM,CAAC,QAAQ;AAC9D,gBAAQ,KAAK,GAAG;AAChB,gBAAQ,KAAK,cAAc;MAAA,CAC5B;IACH;;IAEA;MACE;MACA,QAAQ;MACR,QAAQ;MACR;MACA,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;IAAA;EAEZ;AAEA,QAAM,4BAAkC;IACtC,CAAC,UAAiD;AAChD,UAAI,SAAA,OAAA,SAAA,MAAO,gBAAgB;AACf,kBAAA;MAAA;IAEd;IACA,CAAC,SAAS;EACZ;AAEA;IACE;IACA;IACA;IACA,EAAE,UAAU,CAAC,CAAC,YAAY,EAAE,YAAY,YAAY;EACtD;AAEA,EAAM,iBAAU,MAAM;AACpB,QAAI,iBAAiB,SAAS;AAC5B;IAAA;AAEE,QAAA,CAAC,YAAY,YAAY,UAAU;AAC3B,gBAAA;AACV,uBAAiB,UAAU;IAAA;EAE5B,GAAA,CAAC,UAAU,WAAW,OAAO,CAAC;AAEjC,MAAI,YAAY;AACP,WAAA;MACL,GAAG;MACH,KAAK;MACL;MACA,MAAM;MACN,GAAI,YAAY,EAAE,SAAS;MAC3B,GAAI,UAAU,EAAE,OAAO;MACvB,GAAI,YAAY,EAAE,SAAS;MAC3B,GAAI,SAAS,EAAE,MAAM;MACrB,GAAI,aAAa,EAAE,UAAU;MAC7B,GAAI,WAAW,EAAE,QAAQ;MACzB,GAAI,WAAW,EAAE,QAAQ;MACzB,GAAI,gBAAgB,EAAE,aAAa;MACnC,GAAI,gBAAgB,EAAE,aAAa;MACnC,GAAI,gBAAgB,EAAE,aAAa;IACrC;EAAA;AAII,QAAA,cAAc,CAAC,MAAwB;AAC3C,QACE,CAAC,YACD,CAAC,YAAY,CAAC,KACd,CAAC,EAAE,qBACF,CAAC,UAAU,WAAW,YACvB,EAAE,WAAW,GACb;AACA,QAAE,eAAe;AAEjB,sCAAU,MAAM;AACd,2BAAmB,IAAI;MAAA,CACxB;AAED,YAAM,QAAQ,OAAO,UAAU,cAAc,MAAM;AAC3C,cAAA;AACN,2BAAmB,KAAK;MAAA,CACzB;AAID,aAAO,OAAO,SAAS;QACrB,GAAG;QACH;QACA;QACA;QACA;QACA,iBAAAA;QACA;QACA;MAAA,CACD;IAAA;EAEL;AAGM,QAAA,cAAc,CAAC,MAAwB;AAC3C,QAAI,SAAU;AACd,QAAI,SAAS;AACD,gBAAA;IAAA;EAEd;AAEA,QAAM,mBAAmB;AAEnB,QAAA,cAAc,CAAC,MAAwB;AACvC,QAAA,YAAY,CAAC,QAAS;AAE1B,QAAI,CAAC,cAAc;AACP,gBAAA;IAAA,OACL;AACL,YAAM,cAAc,EAAE;AAClB,UAAA,WAAW,IAAI,WAAW,GAAG;AAC/B;MAAA;AAEI,YAAA,KAAK,WAAW,MAAM;AAC1B,mBAAW,OAAO,WAAW;AACnB,kBAAA;MAAA,GACT,YAAY;AACJ,iBAAA,IAAI,aAAa,EAAE;IAAA;EAElC;AAEM,QAAA,cAAc,CAAC,MAAwB;AAC3C,QAAI,YAAY,CAAC,WAAW,CAAC,aAAc;AAC3C,UAAM,cAAc,EAAE;AAChB,UAAA,KAAK,WAAW,IAAI,WAAW;AACrC,QAAI,IAAI;AACN,mBAAa,EAAE;AACf,iBAAW,OAAO,WAAW;IAAA;EAEjC;AAGA,QAAM,sBAA+D,WAChE,iBAAiB,aAAoB,CAAE,CAAA,KAAK,uBAC7C;AAGJ,QAAM,wBACJ,WACI,sBACC,iBAAiB,eAAe,CAAE,CAAA,KAAK;AAE9C,QAAM,oBAAoB;IACxB;IACA,oBAAoB;IACpB,sBAAsB;EAErB,EAAA,OAAO,OAAO,EACd,KAAK,GAAG;AAEX,QAAM,iBAAiB,SACrB,oBAAoB,SACpB,sBAAsB,UAAU;IAChC,GAAG;IACH,GAAG,oBAAoB;IACvB,GAAG,sBAAsB;EAC3B;AAEO,SAAA;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM,WACF,SACA,KAAK,iBACH,OAAO,QAAQ,WAAW,KAAK,eAAe,IAAI,IAClD,OAAO,QAAQ,WAAW,KAAK,IAAI;IACzC,KAAK;IACL,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,SAAS,gBAAgB,CAAC,SAAS,WAAW,CAAC;IAC/C,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,WAAW,CAAC;IACzD,cAAc,gBAAgB,CAAC,cAAc,gBAAgB,CAAC;IAC9D,UAAU,CAAC,CAAC;IACZ;IACA,GAAI,iBAAiB,EAAE,OAAO,cAAc;IAC5C,GAAI,qBAAqB,EAAE,WAAW,kBAAkB;IACxD,GAAI,YAAY;IAChB,GAAI,YAAY;IAChB,GAAI,mBAAmB;EACzB;AACF;AAEA,IAAM,sBAAsB,CAAC;AAC7B,IAAM,uBAAuB,EAAE,WAAW,SAAS;AACnD,IAAM,wBAAwB,EAAE,MAAM,QAAQ,iBAAiB,KAAK;AACpE,IAAM,sBAAsB,EAAE,eAAe,UAAU,gBAAgB,OAAO;AAC9E,IAAM,6BAA6B,EAAE,sBAAsB,gBAAgB;AAE3E,IAAM,aAAA,oBAAiB,QAAoD;AAE3E,IAAM,8BAAwD;EAC5D,YAAY;AACd;AAEA,IAAM,kBACJ,CAAC,aACD,CAAC,MAA4B;AAC3B,WAAS,OAAO,OAAO,EAAE,QAAQ,CAAC,YAAY;AAC5C,QAAI,EAAE,iBAAkB;AACxB,YAAS,CAAC;EAAA,CACX;AACH;AA4HK,SAAS,WACd,MACsB;AACtB,SAAa,kBAAW,SAAS,YAAY,OAAO,KAAK;AACvD,eAAA,yBAAQ,MAAM,EAAA,GAAI,OAAe,UAAU,MAAM,IAAA,CAAU;EAAA,CAC5D;AACH;AAEO,IAAM,OAAiC;EAC5C,CAAC,OAAO,QAAQ;AACd,UAAM,EAAE,UAAU,GAAG,KAAA,IAAS;AACxB,UAAA;MACJ,MAAM;MACN,KAAK;MACL,GAAG;IAAA,IACD,aAAa,MAAa,GAAG;AAEjC,UAAM,WACJ,OAAO,KAAK,aAAa,aACrB,KAAK,SAAS;MACZ,UAAW,UAAkB,aAAa,MAAM;IAAA,CACjD,IACD,KAAK;AAEX,QAAI,aAAa,QAAW;AAG1B,aAAO,UAAU;IAAA;AAGnB,WAAa;MACX,WAAW,WAAW;MACtB;QACE,GAAG;QACH,KAAK;MACP;MACA;IACF;EAAA;AAEJ;AAEA,SAAS,YAAY,GAAqB;AACjC,SAAA,CAAC,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACpD;AAkBa,IAAA,cAAkC,CAAC,YAAY;AACnD,SAAA;AACT;;;AC5fO,SAAS,YAGd,IAA2D;AAC3D,SAAO,IAAI,SAAuB,EAAE,GAAA,CAAI;AAC1C;AAEO,IAAM,WAAN,cAGG,aAA2B;;;;EAInC,YAAY,EAAE,GAAA,GAAmB;AACzB,UAAA,EAAE,GAAA,CAAI;AAGd,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAA6C,CAAC,SAAS;AACrD,aAAO,SAAS;QACd,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,MAAA,CAAc;IACvE;AAEA,SAAA,gBAAyC,CAAC,SAAS;AAC1C,aAAA,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,QAAQ,MAAA,CAAc;IACvE;AAEA,SAAA,cAAc,MAET;AACH,YAAM,SAAS,UAAU;AAClB,aAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,EAAY,EAAE,SAAA,CAAU;IAC5E;AAEA,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAO,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,KAAA,CAAM;IACzD;AAEA,SAAA,OACEC,cAAAA,QAAM,WAAW,CAAC,OAAO,QAA+C;AACtE,YAAM,SAAS,UAAU;AACzB,YAAM,WAAW,OAAO,WAAW,KAAK,EAAY,EAAE;AACtD,iBAAA,yBAAQ,MAAK,EAAA,KAAU,MAAM,UAAoB,GAAG,MAAA,CAAO;IAAA,CAC5D;EAAA;AAGL;AAEO,IAAM,QAAN,cAuBG,UAiCV;;;;EAIE,YACE,SAcA;AACA,UAAM,OAAO;AAIf,SAAA,WAA+B,CAAC,SAAS;AACvC,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAA6C,CAAC,SAAU;AACtD,aAAO,SAAS;QACd,GAAG;QACH,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAiC,CAAC,SAAS;AAEzC,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,gBAAyC,CAAC,SAAS;AACjD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,cAAc,MAAoC;AAChD,aAAO,YAAY,EAAE,MAAM,KAAK,SAAA,CAAU;IAC5C;AAEA,SAAA,OAAsCA,cAAAA,QAAM;MAC1C,CAAC,OAAO,QAA+C;AACrD,mBAAA,yBAAQ,MAAK,EAAA,KAAU,MAAM,KAAK,UAAoB,GAAG,MAAA,CAAO;MAAA;IAEpE;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAsDrD;AAEO,SAAS,YAqBd,SA4BA;AACO,SAAA,IAAI,MAcT,OAAO;AACX;AAIO,SAAS,6BAAwD;AACtE,SAAO,CAOL,YAQG;AACH,WAAO,gBAOL,OAAc;EAClB;AACF;AAKO,IAAM,uBAAuB;AAE7B,IAAM,YAAN,cAUG,cAqBV;;;;EAIE,YACE,SAQA;AACA,UAAM,OAAO;AAIf,SAAA,WAAuC,CAAC,SAAS;AAC/C,aAAO,SAAS;QACd,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,MAAM,KAAK;QACX,mBAAmB,QAAA,OAAA,SAAA,KAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAAqD,CAAC,SAAS;AAC7D,aAAO,SAAS;QACd,GAAG;QACH,MAAM,KAAK;QACX,QAAQ,CAAC,OAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAC3D;IACH;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,YAAyC,CAAC,SAAS;AAEjD,aAAO,UAAU;QACf,QAAQ,QAAA,OAAA,SAAA,KAAM;QACd,mBAAmB,QAAA,OAAA,SAAA,KAAM;QACzB,MAAM,KAAK;MAAA,CACL;IACV;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,gBAAiD,CAAC,SAAS;AACzD,aAAO,cAAc,EAAE,GAAG,MAAM,MAAM,KAAK,GAAA,CAAW;IACxD;AAEA,SAAA,cAAc,MAA8B;AAC1C,aAAO,YAAY,EAAE,MAAM,KAAK,SAAA,CAAU;IAC5C;AAEA,SAAA,OAAgCA,cAAAA,QAAM;MACpC,CAAC,OAAO,QAA+C;AACrD,mBAAA,yBAAQ,MAAK,EAAA,KAAU,MAAM,KAAK,UAAW,GAAG,MAAA,CAAO;MAAA;IAE3D;AArDI,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAsDrD;AAEO,SAAS,gBAQd,SAiBA;AACO,SAAA,IAAI,UAOT,OAAO;AACX;AAEO,SAAS,gBAKd,MAGuB;AAChB,SAAA;AACT;AAmBO,IAAM,gBAAN,cASG,MAcR;EACA,YACE,SAsBA;AACM,UAAA;MACJ,GAAI;MACJ,IAAI;IAAA,CACL;EAAA;AAEL;;;AClkBO,SAAS,gBAQd,MAC0E;AACtE,MAAA,OAAO,SAAS,UAAU;AACrB,WAAA,IAAI,UAA0D,MAAM;MACzE,QAAQ;IAAA,CACT,EAAE,YAAY,IAAI;EAAA;AAEd,SAAA,IAAI,UAA0D,MAAM;IACzE,QAAQ;EACT,CAAA,EAAE;AACL;AAMO,IAAM,YAAN,MAOL;EAGA,YACS,MACP,OACA;AAFO,SAAA,OAAA;AAMT,SAAA,cAAc,CASZ,YAuCG;AACH;QACE,KAAK;QACL;MACF;AACM,YAAA,QAAQ,YAAY,OAAc;AACtC,YAAc,SAAS;AAClB,aAAA;IACT;AA3DE,SAAK,SAAS,SAAA,OAAA,SAAA,MAAO;EAAA;AA4DzB;AAOO,SAAS,gBAId,OAca;AACb;IACE;IACA;EACF;AACA,SAAO,CAAC,aAAa;AACvB;AAcO,IAAM,YAAN,MAAyC;EAK9C,YACE,MAGA;AAKF,SAAA,WAAwC,CAACC,UAAS;AAChD,aAAO,SAAS;QACd,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,MAAM,KAAK,QAAQ;QACnB,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;MAAA,CACnB;IACV;AAEA,SAAA,kBAAsD,CAACA,UAAS;AAC9D,aAAO,SAAS;QACd,MAAM,KAAK,QAAQ;QACnB,QAAQ,CAAC,OAAYA,SAAA,OAAA,SAAAA,MAAM,UAASA,MAAK,OAAO,EAAE,OAAO,IAAI,EAAE;MAAA,CAChE;IACH;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAO,UAAU;QACf,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;QACzB,MAAM,KAAK,QAAQ;MAAA,CACb;IACV;AAEA,SAAA,YAA0C,CAACA,UAAS;AAElD,aAAO,UAAU;QACf,QAAQA,SAAA,OAAA,SAAAA,MAAM;QACd,mBAAmBA,SAAA,OAAA,SAAAA,MAAM;QACzB,MAAM,KAAK,QAAQ;MAAA,CACb;IACV;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAChE;AAEA,SAAA,gBAAkD,CAACA,UAAS;AACnD,aAAA,cAAc,EAAE,GAAGA,OAAM,MAAM,KAAK,QAAQ,GAAA,CAAW;IAChE;AAEA,SAAA,cAAc,MAA6C;AACzD,YAAM,SAAS,UAAU;AAClB,aAAA,YAAY,EAAE,MAAM,OAAO,WAAW,KAAK,QAAQ,EAAE,EAAE,SAAA,CAAU;IAC1E;AAhDE,SAAK,UAAU;AACb,SAAa,WAAW,OAAO,IAAI,YAAY;EAAA;AAgDrD;AAEO,SAAS,gBAId,IAA2D;AAC3D,SAAO,CAAC,SAA2B;AACjC,WAAO,IAAI,UAAkB;MAC3B;MACA,GAAG;IAAA,CACJ;EACH;AACF;AAEO,SAAS,oBAGd,IAA8D;AAC1D,MAAA,OAAO,OAAO,UAAU;AACnB,WAAA,IAAI,UAAkB,EAAE;EAAA;AAG1B,SAAA,CAAC,SAA2B,IAAI,UAAkB,EAAE,IAAI,GAAG,KAAA,CAAM;AAC1E;A;;;AClQgB,SAAA,mBAId,UACA,YAGQ;AACJ,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AAEJ,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,aAAa;AAChB,oBAAc,SAAS,EACpB,KAAK,CAAC,QAAQ;AACC,sBAAA;AACP,eAAA,IAAI,cAAc,SAAS;MAAA,CACnC,EACA,MAAM,CAAC,QAAQ;AAIN,gBAAA;AAMJ,YAAA,sBAAsB,KAAK,GAAG;AAChC,cACE,iBAAiB,SACjB,OAAO,WAAW,eAClB,OAAO,mBAAmB,aAC1B;AAKM,kBAAAC,cAAa,0BAA0B,MAAM,OAAO;AAC1D,gBAAI,CAAC,eAAe,QAAQA,WAAU,GAAG;AACxB,6BAAA,QAAQA,aAAY,GAAG;AAC7B,uBAAA;YAAA;UACX;QACF;MACF,CACD;IAAA;AAGE,WAAA;EACT;AAEM,QAAA,WAAW,SAAS,KAAK,OAAY;AAEzC,QAAI,QAAQ;AAGV,aAAO,SAAS,OAAO;AACjB,YAAA,IAAI,QAAQ,MAAM;MAAA,CAAE;IAAA;AAE5B,QAAI,OAAO;AAEH,YAAA;IAAA;AAGR,QAAI,CAAC,MAAM;AACT,YAAM,KAAK;IAAA;AAGN,WAAM,sBAAc,MAAM,KAAK;EACxC;AAEE,WAAiB,UAAU;AAEtB,SAAA;AACT;A;;;;;;;ACvEO,SAAS,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,qBAA2B,eAAO,EAAE,QAAQ,SAAS,MAAA,CAAO;AAElE,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,iBAAS,KAAK;AAElE,QAAM,EAAE,mBAAmB,UAAU,IAAI,eAAe;IACtD,QAAQ,CAAC,OAAO;MACd,WAAW,EAAE;MACb,mBAAmB,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,SAAS;IAAA;IAEjE,mBAAmB;EAAA,CACpB;AAEK,QAAA,oBAAoB,YAAY,SAAS;AAEzC,QAAA,eAAe,aAAa,mBAAmB;AAC/C,QAAA,uBAAuB,YAAY,YAAY;AAErD,QAAM,gBAAgB,aAAa;AAC7B,QAAA,wBAAwB,YAAY,aAAa;AAEhD,SAAA,kBAAkB,CAAC,OAAmB;AAC3C,uBAAmB,IAAI;AACvB,IAAM,wBAAgB,MAAM;AACvB,SAAA;AACH,yBAAmB,KAAK;IAAA,CACzB;EACH;AAIA,EAAM,kBAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI;AAE5C,UAAA,eAAe,OAAO,cAAc;MACxC,IAAI,OAAO,eAAe;MAC1B,QAAQ;MACR,QAAQ;MACR,MAAM;MACN,OAAO;MACP,wBAAwB;IAAA,CACzB;AAGC,QAAA,cAAc,OAAO,eAAe,IAAI,MACxC,cAAc,aAAa,IAAI,GAC/B;AACA,aAAO,eAAe,EAAE,GAAG,cAAc,SAAS,KAAA,CAAM;IAAA;AAG1D,WAAO,MAAM;AACL,YAAA;IACR;EACC,GAAA,CAAC,QAAQ,OAAO,OAAO,CAAC;AAG3B,EAAAC,iBAAgB,MAAM;AACpB;;MAEG,OAAO,WAAW,eAAe,OAAO,OACxC,mBAAmB,QAAQ,WAAW,UACrC,mBAAmB,QAAQ;MAC7B;AACA;IAAA;AAEF,uBAAmB,UAAU,EAAE,QAAQ,SAAS,KAAK;AAErD,UAAM,UAAU,YAAY;AACtB,UAAA;AACF,cAAM,OAAO,KAAK;MAAA,SACX,KAAK;AACZ,gBAAQ,MAAM,GAAG;MAAA;IAErB;AAEQ,YAAA;EAAA,GACP,CAAC,MAAM,CAAC;AAEX,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,qBAAqB,CAAC,WAAW;AACnC,aAAO,KAAK;QACV,MAAM;;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;IAAA;EAEF,GAAA,CAAC,mBAAmB,QAAQ,SAAS,CAAC;AAEzC,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,yBAAyB,CAAC,eAAe;AAC3C,aAAO,KAAK;QACV,MAAM;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;IAAA;EAEF,GAAA,CAAC,eAAe,uBAAuB,MAAM,CAAC;AAEjD,EAAAA,iBAAgB,MAAM;AAEhB,QAAA,wBAAwB,CAAC,cAAc;AACzC,aAAO,KAAK;QACV,MAAM;QACN,GAAG,sBAAsB,OAAO,KAAK;MAAA,CACtC;AAEM,aAAA,QAAQ,SAAS,CAAC,OAAO;QAC9B,GAAG;QACH,QAAQ;QACR,kBAAkB,EAAE;MAAA,EACpB;AAEF,uBAAiB,MAAM;IAAA;EAExB,GAAA,CAAC,cAAc,sBAAsB,MAAM,CAAC;AAExC,SAAA;AACT;A;;;;;;;ACzHO,SAAS,cAAc,OAI3B;AAED,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,aAAa,EAAE,SAAS,QAAQ,IAAI,EAAE,MAAM;EAAA,CAC5D;AAGC,aAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,SAAS,CAAC,OAAO,cAAc;;AACzB,YAAA,WAAW,KAAK,GAAG;AACf,WAAA,KAAA,MAAA,YAAA,OAAA,SAAA,GAAA,KAAA,OAAU,OAAO,SAAA;QAAS,OAC3B;AACC,gBAAA;QAAA;MAEV;MACA,gBAAgB,CAAC,EAAE,MAAA,MAAY;;AACzB,YAAA,WAAW,KAAK,GAAG;AACd,kBAAA,KAAA,MAAM,aAAN,OAAA,SAAA,GAAA,KAAA,OAAiB,KAAA;QAAK,OACxB;AACC,gBAAA;QAAA;MAEV;MAEC,UAAM,MAAA;IAAA;EACT;AAEJ;AAEO,SAAS,wBAAwB;AAC/B,aAAA,yBAAC,KAAA,EAAE,UAAS,YAAA,CAAA;AACrB;;;;ACxCO,SAAS,aAAa,OAAY;AAChC,aAAA,yBAAA,8BAAA,EAAG,UAAA,MAAM,SAAS,CAAA;AAC3B;A;;;ACCgB,SAAA,oBACd,QACA,OACA,MACA;AACI,MAAA,CAAC,MAAM,QAAQ,mBAAmB;AAChC,QAAA,OAAO,QAAQ,0BAA0B;AAC3C,iBAAQ,yBAAA,OAAO,QAAQ,0BAAf,EAAwC,KAAY,CAAA;IAAA;AAG1D,QAAA,MAAwC;AAC1C;QACE,MAAM,QAAQ;QACd,yDAAyD,MAAM,EAAE;MACnE;IAAA;AAGF,eAAA,yBAAQ,uBAAsB,CAAA,CAAA;EAAA;AAGhC,aAAQ,yBAAA,MAAM,QAAQ,mBAAd,EAAgC,KAAY,CAAA;AACtD;A;;;;;;AC1BO,SAAS,WAAW;EACzB;AACF,GAIG;AACG,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EAAA;AAIP,aAAA;IAAC;IAAA;MACC,WAAU;MACV,yBAAyB;QACvB,QAAQ,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;MAAA;IAC9C;EACF;AAEJ;;;ACXO,SAAS,oBAAoB;AAClC,QAAM,SAAS,UAAU;AACnB,QAAA,SACJ,OAAO,QAAQ,2BAA2B;AACtC,QAAA,UAAU,OAAO,OAAO,cAAc;AAC5C,QAAM,cACJ,YAAY,+BAA+B,OAAO,cAAc,IAC5D,UACA;AAEN,MAAI,CAAC,OAAO,qBAAqB,CAAC,OAAO,UAAU;AAC1C,WAAA;EAAA;AAIP,aAAA;IAAC;IAAA;MACC,UAAU,IAAI,cAAc,SAAU,CAAA,KAAK,KAAK,UAAU,UAAU,CAAC,IAAI,KAAK,UAAU,WAAW,CAAC;IAAA;EACtG;AAEJ;;;ACDO,IAAM,QAAc,aAAK,SAAS,UAAU;EACjD;AACF,GAEG;;AACD,QAAM,SAAS,UAAU;AACzB,QAAM,aAAa,eAAe;IAChC,QAAQ,CAAC,MAAM;AACP,YAAA,QAAQ,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACpD;QACE;QACA,qCAAqC,OAAO;MAC9C;AACA,aAAO,KAAK,OAAO,CAAC,WAAW,OAAO,iBAAiB,CAAC;IAC1D;IACA,mBAAmB;EAAA,CACpB;AAED,QAAM,QAAkB,OAAO,WAAW,WAAW,OAAO;AAE5D,QAAM,mBACJ,MAAM,QAAQ,oBAAoB,OAAO,QAAQ;AAEnD,QAAM,iBAAiB,uBAAoB,0BAAA,kBAAA,CAAA,CAAiB,IAAK;AAEjE,QAAM,sBACJ,MAAM,QAAQ,kBAAkB,OAAO,QAAQ;AAEjD,QAAM,eAAe,MAAM,QAAQ,WAAW,OAAO,QAAQ;AAE7D,QAAM,yBAAyB,MAAM;;IAEhC,MAAM,QAAQ,uBACf,KAAA,OAAO,QAAQ,kBAAf,OAAA,SAAA,GAA8B,QAAQ;MACtC,MAAM,QAAQ;AAElB,QAAM,gBACJ,WAAW,QAAQ,SAAS,WAAW,QAAQ;AAC3C,QAAA;;KAEH,CAAC,MAAM,UAAU,MAAM,QAAQ,kBAAkB,mBACjD,MAAM,QAAQ,kBACb,uBACE,KAAA,MAAM,QAAQ,mBAAd,OAAA,SAAA,GAAsC,YAAW,kBAC3C,mBACN;;AAEA,QAAA,wBAAwB,sBAC1B,gBACA;AAEE,QAAA,2BAA2B,yBAC7B,gBACA;AAEJ,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAED,QAAM,gBAAgB,eAAe;IACnC,QAAQ,CAAC,MAAM;;AACP,YAAA,QAAQ,EAAE,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AACzD,cAAOC,MAAA,EAAE,QAAQ,QAAQ,CAAC,MAAnB,OAAA,SAAAA,IAAsB;IAAA;EAC/B,CACD;AAED,QAAM,iBAAiB,MAAM,SACvB,MAAM,QAA6B,kBAAkB,eACvD;AACJ,aAAA,2BACG,gBACC,EAAA,UAAA;QAAC,0BAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAC,0BAAA,0BAAA,EAAyB,UAAU,gBAClC,cAAA;MAAC;MAAA;QACC,aAAa,MAAM;QACnB,gBAAgB,uBAAuB;QACvC,SAAS,CAAC,OAAO,cAAc;AAEzB,cAAA,WAAW,KAAK,EAAS,OAAA;AACrB,mCAAA,OAAO,yBAAyB,OAAO,EAAE;AACjD,0BAAA,OAAA,SAAA,aAAe,OAAO,SAAA;QACxB;QAEA,cAAA;UAAC;UAAA;YACC,UAAU,CAAC,UAAU;AAGnB,kBACE,CAAC,0BACA,MAAM,WAAW,MAAM,YAAY,WAAW,WAC9C,CAAC,MAAM,WAAW,CAAC,MAAM;AAEpB,sBAAA;AAED,qBAAM,sBAAc,wBAAwB,KAAY;YACjE;YAEC,UAAiB,iBAAA,WAAW,sBAC3B,0BAAC,YAAA,EAAW,UAAU,gBACpB,cAAC,0BAAA,YAAA,EAAW,QAAA,CAAkB,EAAA,CAChC,QAEA,0BAAC,YAAA,EAAW,QAAkB,CAAA;UAAA;QAAA;MAElC;IAAA,EAAA,CAEJ,EACF,CAAA;IACC,kBAAkB,eAAe,OAAO,QAAQ,wBAE7C,2BAAA,+BAAA,EAAA,UAAA;UAAA,0BAAC,YAAW,CAAA,CAAA;UAAA,0BACX,mBAAkB,CAAA,CAAA;IAAA,EAAA,CACrB,IACE;EAAA,EAAA,CACN;AAEJ,CAAC;AASD,SAAS,aAAa;AACpB,QAAM,SAAS,UAAU;AAEzB,QAAM,kBAAwB;IAC5B;EACF;AAGE,aAAA;IAAC;IAAA;MAEC,0BAAwB;MACxB,KAAK,CAAC,OAAO;AAET,YAAA,OACC,gBAAgB,YAAY,UAC3B,gBAAgB,QAAQ,SAAS,OAAO,eAAe,OACzD;AACA,iBAAO,KAAK;YACV,MAAM;YACN,GAAG,sBAAsB,OAAO,KAAK;UAAA,CACtC;AACD,0BAAgB,UAAU,OAAO;QAAA;MACnC;IACF;IAdK,OAAO,eAAe,MAAM;EAenC;AAEJ;AAEO,IAAM,aAAmB,aAAK,SAAS,eAAe;EAC3D;AACF,GAEQ;;AACN,QAAM,SAAS,UAAU;AAEzB,QAAM,EAAE,OAAO,KAAK,QAAA,IAAY,eAAe;IAC7C,QAAQ,CAAC,MAAM;AACP,YAAA,aAAa,EAAE,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AACxDC,YAAAA,SAAQ,EAAE,QAAQ,UAAU;AAClC,YAAMC,WAAUD,OAAM;AAEhB,YAAA,YACH,OAAO,WAAWC,QAAO,EAAe,QAAQ,eACjD,OAAO,QAAQ;AACjB,YAAM,cAAc,aAAA,OAAA,SAAA,UAAY;QAC9B,SAAAA;QACA,YAAYD,OAAM;QAClB,QAAQA,OAAM;QACd,QAAQA,OAAM;MAAA,CAAA;AAEhB,YAAME,OAAM,cAAc,KAAK,UAAU,WAAW,IAAI;AAEjD,aAAA;QACL,KAAAA;QACA,SAAAD;QACA,OAAO,KAAKD,QAAO;UACjB;UACA;UACA;UACA;UACA;QACD,CAAA;MACH;IACF;IACA,mBAAmB;EAAA,CACpB;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEjC,QAAA,MAAY,gBAAQ,MAAM;AAC9B,UAAM,OAAO,MAAM,QAAQ,aAAa,OAAO,QAAQ;AACvD,QAAI,MAAM;AACD,iBAAA,0BAAC,MAAA,CAAA,GAAU,GAAK;IAAA;AAEzB,eAAA,0BAAQ,QAAO,CAAA,CAAA;EAAA,GACd,CAAC,KAAK,MAAM,QAAQ,WAAW,OAAO,QAAQ,gBAAgB,CAAC;AAElE,MAAI,MAAM,iBAAiB;AACzB,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAGnC,MAAI,MAAM,eAAe;AACvB,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAI/B,MAAA,MAAM,WAAW,WAAW;AAE9B,UAAM,eACJ,MAAM,QAAQ,gBAAgB,OAAO,QAAQ;AAE/C,QAAI,gBAAgB,GAAC,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B,oBAAmB;AAE7D,UAAA,CAAC,OAAO,UAAU;AACpB,cAAM,oBAAoB,wBAA8B;AAEhD,gBAAA,QAAA,EAAU,KAAK,MAAM;AAC3B,iBAAO,YAAY,MAAM,IAAI,CAAC,UAAU;YACtC,GAAG;YACH;UAAA,EACA;QAAA,CACH;AAED,mBAAW,MAAM;AACf,4BAAkB,QAAQ;AAG1B,iBAAO,YAAY,MAAM,IAAI,CAAC,UAAU;YACtC,GAAG;YACH,mBAAmB;UAAA,EACnB;QAAA,GACD,YAAY;MAAA;IACjB;AAEF,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAG/B,MAAA,MAAM,WAAW,YAAY;AAC/B,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAC9D,WAAO,oBAAoB,QAAQ,OAAO,MAAM,KAAK;EAAA;AAGnD,MAAA,MAAM,WAAW,cAAc;AAGjC,cAAU,WAAW,MAAM,KAAK,GAAG,2BAA2B;AAM9D,WAAM,KAAA,OAAO,SAAS,MAAM,EAAE,MAAxB,OAAA,SAAA,GAA2B;EAAA;AAG/B,MAAA,MAAM,WAAW,SAAS;AAM5B,QAAI,OAAO,UAAU;AACnB,YAAM,uBACH,MAAM,QAAQ,kBACb,OAAO,QAAQ,0BACjB;AAEA,iBAAA;QAAC;QAAA;UACC,OAAO,MAAM;UACb,OAAO;UACP,MAAM;YACJ,gBAAgB;UAAA;QAClB;MACF;IAAA;AAIJ,UAAM,MAAM;EAAA;AAGP,SAAA;AACT,CAAC;AAEM,IAAM,SAAe,aAAK,SAAS,aAAa;AACrD,QAAM,SAAS,UAAU;AACnB,QAAA,UAAgB,mBAAW,YAAY;AAC7C,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAA;;AAAM,cAAA,KAAA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAtC,OAAA,SAAA,GAAyC;IAAA;EAAA,CACzD;AAEK,QAAA,QAAQ,OAAO,WAAW,OAAO;AAEvC,QAAM,uBAAuB,eAAe;IAC1C,QAAQ,CAAC,MAAM;AACb,YAAM,UAAU,EAAE;AAClB,YAAM,cAAc,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD;QACE;QACA,4CAA4C,OAAO;MACrD;AACA,aAAO,YAAY;IAAA;EACrB,CACD;AAED,QAAM,eAAe,eAAe;IAClC,QAAQ,CAAC,MAAM;;AACb,YAAM,UAAU,EAAE;AAClB,YAAM,QAAQ,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,OAAO;AAChD,cAAA,KAAA,QAAQ,QAAQ,CAAC,MAAjB,OAAA,SAAA,GAAoB;IAAA;EAC7B,CACD;AAEK,QAAA,iBAAiB,OAAO,QAAQ,8BAAA,0BACnC,OAAO,QAAQ,yBAAf,CAAuC,CAAA,IACtC;AAEJ,MAAI,sBAAsB;AACjB,WAAA,oBAAoB,QAAQ,OAAO,MAAS;EAAA;AAGrD,MAAI,CAAC,cAAc;AACV,WAAA;EAAA;AAGT,QAAM,gBAAY,0BAAC,OAAM,EAAA,SAAS,aAAc,CAAA;AAEhD,MAAI,YAAY,aAAa;AAC3B,eAAA,0BACS,kBAAN,EAAe,UAAU,gBAAiB,UAAU,UAAA,CAAA;EAAA;AAIlD,SAAA;AACT,CAAC;;;AClUM,SAAS,UAAU;AACxB,QAAM,SAAS,UAAU;AAEnB,QAAA,iBAAiB,OAAO,QAAQ,8BAAA,0BACnC,OAAO,QAAQ,yBAAf,CAAuC,CAAA,IACtC;AAGE,QAAA,mBACJ,OAAO,YAAa,OAAO,aAAa,eAAe,OAAO,MAC1D,eACM;AAEZ,QAAM,YACJ,2BAAC,kBAAiB,EAAA,UAAU,gBACzB,UAAA;IAAC,CAAA,OAAO,gBAAY,0BAAC,cAAa,CAAA,CAAA;QAAA,0BAClC,cAAa,CAAA,CAAA;EAAA,EAAA,CAChB;AAGK,SAAA,OAAO,QAAQ,gBACpB,0BAAC,OAAO,QAAQ,WAAf,EAA0B,UAAA,MAAA,CAAM,IAEjC;AAEJ;AAEA,SAAS,eAAe;AACtB,QAAM,UAAU,eAAe;IAC7B,QAAQ,CAAC,MAAM;;AACN,cAAA,KAAA,EAAE,QAAQ,CAAC,MAAX,OAAA,SAAA,GAAc;IAAA;EACvB,CACD;AAED,QAAM,WAAW,eAAe;IAC9B,QAAQ,CAAC,MAAM,EAAE;EAAA,CAClB;AAED,aACG,0BAAA,aAAa,UAAb,EAAsB,OAAO,SAC5B,cAAA;IAAC;IAAA;MACC,aAAa,MAAM;MACnB,gBAAgB;MAChB,SAAS,CAAC,UAAU;AAClB;UACE;UACA;QACF;AACA,iCAAQ,OAAO,MAAM,WAAW,MAAM,SAAA,CAAU;MAClD;MAEC,UAAU,cAAA,0BAAC,OAAM,EAAA,QAAkB,CAAA,IAAK;IAAA;EAAA,EAAA,CAE7C;AAEJ;AAcO,SAAS,gBAA8D;AAC5E,QAAM,SAAS,UAAU;AAEV,iBAAA;IACb,QAAQ,CAAC,MAAA;;AAAM,aAAA,CAAC,EAAE,SAAS,OAAM,KAAA,EAAE,qBAAF,OAAA,SAAA,GAAoB,MAAM,EAAE,MAAM;IAAA;IACnE,mBAAmB;EAAA,CACpB;AAED,SAAa;IACX,CAME,SAGqE;AACrE,YAAM,EAAE,SAAS,eAAe,OAAO,eAAe,GAAG,KAAA,IAAS;AAE3D,aAAA,OAAO,WAAW,MAAa;QACpC;QACA;QACA;QACA;MAAA,CACD;IACH;IACA,CAAC,MAAM;EACT;AACF;AAoBO,SAAS,WAMd,OAA4E;AAC5E,QAAM,aAAa,cAAc;AAC3B,QAAA,SAAS,WAAW,KAAY;AAElC,MAAA,OAAO,MAAM,aAAa,YAAY;AAChC,WAAA,MAAM,SAAiB,MAAM;EAAA;AAGhC,SAAA,SAAS,MAAM,WAAW;AACnC;AAiBO,SAAS,WAKd,MAEsC;AACtC,SAAO,eAAe;IACpB,QAAQ,CAAC,UAA6C;AACpD,YAAM,UAAU,MAAM;AACtB,cAAO,QAAA,OAAA,SAAA,KAAM,UACT,KAAK,OAAO,OAA8C,IAC1D;IACN;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;AAEO,SAAS,iBAKd,MAEsC;AAChC,QAAA,iBAAuB,mBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;QAChB;QACA,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc;MAClD;AACA,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,OAAO,IAAI;IAC/C;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;AAEO,SAAS,gBAKd,MAEsC;AAChC,QAAA,iBAAuB,mBAAW,YAAY;AAEpD,SAAO,WAAW;IAChB,QAAQ,CAAC,YAAiD;AACxD,gBAAU,QAAQ;QAChB,QAAQ,UAAU,CAAC,MAAM,EAAE,OAAO,cAAc,IAAI;MACtD;AACA,cAAO,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,OAAO,IAAI;IAC/C;IACA,mBAAmB,QAAA,OAAA,SAAA,KAAM;EAAA,CACnB;AACV;;;AC7Ka,IAAA,eAA+B,CAAC,YAAY;AAChD,SAAA,IAAI,OAAO,OAAO;AAC3B;AAEO,IAAM,SAAN,cAMG,WAMR;EACA,YACE,SAOA;AACA,UAAM,OAAO;EAAA;AAEjB;AAEA,IAAI,OAAO,eAAe,aAAa;AACnC,aAAmB,kBAAkB;AACrC,aAAmB,sBAAsB;AAC7C,WAAW,OAAO,WAAW,aAAa;AACtC,SAAe,kBAAkB;AACjC,SAAe,kBAAkB;AACrC;A;;;AC1GO,SAAS,sBAGd;EACA;EACA;EACA,GAAG;AACL,GAEG;AACD,MAAI,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG;AAEhC,WAAO,OAAO;MACZ,GAAG,OAAO;MACV,GAAG;MACH,SAAS;QACP,GAAG,OAAO,QAAQ;QAClB,GAAG,KAAK;MAAA;IACV,CACM;EAAA;AAGV,QAAMG,iBAAgB,iBAAiB;AAEvC,QAAM,eACH,0BAAAA,eAAc,UAAd,EAAuB,OAAO,QAC5B,SAAA,CACH;AAGE,MAAA,OAAO,QAAQ,MAAM;AACvB,eAAQ,0BAAA,OAAO,QAAQ,MAAf,EAAqB,UAAS,SAAA,CAAA;EAAA;AAGjC,SAAA;AACT;AAEO,SAAS,eAGd,EAAE,QAAQ,GAAG,KAAA,GAA2C;AACxD,aAAA,0BACG,uBAAsB,EAAA,QAAiB,GAAG,MACzC,cAAA,0BAAC,SAAA,CAAQ,CAAA,EAAA,CACX;AAEJ;;;AC1CA,SAAS,uBAAuB;AAC9B,QAAM,SAAS,UAAU;AACzB,yBAAuB,QAAQ,IAAI;AACrC;AAKO,SAASC,mBAAkB,QAAkC;AAC7C,uBAAA;AAEjB,MAAA,MAAwC;AAClC,YAAA;MACN;IACF;EAAA;AAGK,SAAA;AACT;AAEO,SAAS,4BACd,SAYoC;;AACf,uBAAA;AAErB,QAAM,SAAS,UAAU;AACnB,QAAA,SAAS,QAAQ,UAAU;AAEjC,MAAI,kBAAkB;AAEtB,MAAI,QAAQ,IAAI;AACI,sBAAA,gCAAgC,QAAQ,EAAE;EAAA,OACvD;AACC,UAAA,WAAU,KAAA,QAAQ,eAAR,OAAA,SAAA,GAAA,KAAA,OAAA;AAChB,QAAI,CAAC,SAAS;AACZ;IAAA;AAEF,sBACE,mBAAmB,SAAS,WAAW,eAAe,OAAO;EAAA;AAG3D,QAAA,aAAa,OAAO,OAAO,cAAc;AACzC,QAAA,SAAQ,KAAA,2BAAA,OAAA,SAAA,GAAwB,MAAM,UAAA;AAC5C,SAAO,SAAA,OAAA,SAAA,MAAQ,eAAA;AACjB;A;;;ACeA,SAAS,oBACP,MACA,WACgB;AAChB,MAAI,SAAS,QAAW;AACf,WAAA;MACL,eAAe,MAAM;MACrB,cAAc;IAChB;EAAA;AAGF,MAAI,mBAAmB,MAAM;AACpB,WAAA;EAAA;AAGL,MAAA,OAAO,SAAS,YAAY;AACxBC,UAAAA,eAAc,QAAQ,aAAa,IAAI;AAE7C,UAAMC,oBAAmB,YAAY;AAC/BD,UAAAA,aAAoB,QAAA,MAAM,KAAK;AAC5B,aAAA;IACT;AAEO,WAAA;MACL,eAAeC;MACf,oBAAoBD;MACpB,cAAc;IAChB;EAAA;AAGF,QAAM,cAAc,QAAQ,KAAK,aAAa,IAAI;AAClD,QAAM,KAAK,KAAK;AAEhB,QAAM,mBAAmB,YAAY;AAC/B,QAAA,eAAe,OAAO,QAAW;AACnC,aAAO,MAAM,GAAG;IAAA;AAEX,WAAA;EACT;AAEO,SAAA;IACL,eAAe;IACf,oBAAoB;IACpB,cAAc,OAAO;EACvB;AACF;AAsBgB,SAAA,WACd,MACA,WACwB;AAClB,QAAA;IACJ;IACA,qBAAqB;IACrB,WAAW;IACX,eAAe;EAAA,IACb,oBAAoB,MAAM,SAAS;AAEvC,QAAM,SAAS,UAAU;AACnB,QAAA,EAAE,QAAA,IAAY;AAEpB,QAAM,CAAC,UAAU,WAAW,IAAU,iBAA0B;IAC9D,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,SAAS;IACT,OAAO;EAAA,CACR;AAED,EAAM,kBAAU,MAAM;AACd,UAAA,oBAAoB,OAAO,kBAAiC;AAChE,eAAS,YACP,UAC0B;AAC1B,cAAM,iBAAiB,OAAO,cAAc,QAAW,QAAQ;AAC/D,cAAM,gBAAgB,OAAO;UAC3B,eAAe;UACf;QACF;AACI,YAAA,cAAc,eAAe,QAAW;AAC1C,gBAAM,IAAI,MAAM,+BAA+B,SAAS,IAAI,EAAE;QAAA;AAEzD,eAAA;UACL,SAAS,cAAc,WAAW;UAClC,UAAU,cAAc,WAAW;UACnC,UAAU,eAAe;UACzB,QAAQ,cAAc;UACtB,QAAQ,eAAe;QACzB;MAAA;AAGI,YAAA,UAAU,YAAY,cAAc,eAAe;AACnD,YAAA,OAAO,YAAY,cAAc,YAAY;AAE7C,YAAA,cAAc,MAAM,cAAc;QACtC,QAAQ,cAAc;QACtB;QACA;MAAA,CACD;AACD,UAAI,CAAC,cAAc;AACV,eAAA;MAAA;AAGT,UAAI,CAAC,aAAa;AACT,eAAA;MAAA;AAGT,YAAM,UAAU,IAAI,QAAiB,CAAC,YAAY;AACpC,oBAAA;UACV,QAAQ;UACR;UACA;UACA,QAAQ,cAAc;UACtB,SAAS,MAAM,QAAQ,KAAK;UAC5B,OAAO,MAAM,QAAQ,IAAI;QAAA,CAC1B;MAAA,CACF;AAED,YAAM,mBAAmB,MAAM;AACnB,kBAAA;QACV,QAAQ;QACR,SAAS;QACT,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO;MAAA,CACR;AAEM,aAAA;IACT;AAEO,WAAA,WACH,SACA,QAAQ,MAAM,EAAE,WAAW,mBAAmB,mBAAA,CAAoB;EAAA,GACrE;IACD;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEM,SAAA;AACT;AAEA,IAAM,4BAA4B,CAChC,UACmB;AACnB,MAAI,mBAAmB,OAAO;AACrB,WAAA,EAAE,GAAG,MAAM;EAAA;AAGpB,QAAM,cAAc,QAAQ,MAAM,aAAa,IAAI;AACnD,QAAM,KAAK,MAAM;AAEjB,QAAM,mBAAmB,YAAY;AAC/B,QAAA,eAAe,OAAO,QAAW;AACnC,aAAO,MAAM,GAAG;IAAA;AAEX,WAAA;EACT;AAEO,SAAA;IACL,eAAe;IACf,oBAAoB;IACpB,cAAc,OAAO;EACvB;AACF;AAYO,SAAS,MAAM,MAAwD;AAC5E,QAAM,EAAE,UAAU,GAAG,KAAA,IAAS;AACxB,QAAA,OAAO,0BAA0B,IAAI;AAErC,QAAA,WAAW,WAAW,IAAI;AAChC,SAAO,WACH,OAAO,aAAa,aAClB,SAAS,QAAe,IACxB,WACF;AACN;;;ACtRO,SAAS,gBAMd,MAC2D;AAC3D,SAAO,SAAS;IACd,GAAI;IACJ,QAAQ,CAAC,UACP,KAAK,SAAS,KAAK,OAAO,MAAM,OAAO,IAAI,MAAM;EAAA,CACpD;AACH;;;ACDO,SAAS,YAKd,MAEuC;AACvC,SAAO,eAAe;IACpB,QAAQ,CAAC,WACP,QAAA,OAAA,SAAA,KAAM,UAAS,KAAK,OAAO,MAAM,QAAQ,IAAI,MAAM;EAAA,CAC/C;AACV;;;ACtCO,SAAS,eAAe;AACtB,SAAA,eAAe,EAAE,QAAQ,CAAC,MAAM,EAAE,SAAS,MAAM,gBAAgB,EAAA,CAAG;AAC7E;;;;;ACKO,SAAS,MAAM;EACpB;EACA;EACA;AACF,GAAgD;AAC9C,UAAQ,KAAK;IACX,KAAK;AACH,iBAAA,0BACG,SAAO,EAAA,GAAG,OAAO,0BAAwB,MACvC,SAAA,CACH;IAEJ,KAAK;AACH,iBAAQ,0BAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,KAAA,CAAC;IACnD,KAAK;AACH,iBAAQ,0BAAA,QAAA,EAAM,GAAG,OAAO,0BAAwB,KAAA,CAAC;IACnD,KAAK;AAED,iBAAA;QAAC;QAAA;UACE,GAAG;UACJ,yBAAyB,EAAE,QAAQ,SAAmB;QAAA;MACxD;IAEJ,KAAK;AACI,iBAAA,0BAAC,QAAO,EAAA,OAAe,SAAS,CAAA;IACzC;AACS,aAAA;EAAA;AAEb;AAEA,SAAS,OAAO;EACd;EACA;AACF,GAGG;AACD,EAAM,kBAAU,MAAM;AACpB,QAAI,SAAA,OAAA,SAAA,MAAO,KAAK;AACR,YAAA,SAAS,SAAS,cAAc,QAAQ;AAE9C,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,YACE,QAAQ,8BACR,UAAU,UACV,UAAU,OACV;AACO,iBAAA;YACL;YACA,OAAO,UAAU,YAAY,KAAK,OAAO,KAAK;UAChD;QAAA;MACF;AAGO,eAAA,KAAK,YAAY,MAAM;AAEhC,aAAO,MAAM;AACX,YAAI,OAAO,YAAY;AACd,iBAAA,WAAW,YAAY,MAAM;QAAA;MAExC;IAAA;AAGE,QAAA,OAAO,aAAa,UAAU;AAC1B,YAAA,SAAS,SAAS,cAAc,QAAQ;AAC9C,aAAO,cAAc;AAErB,UAAI,OAAO;AACT,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,cACE,QAAQ,8BACR,UAAU,UACV,UAAU,OACV;AACO,mBAAA;cACL;cACA,OAAO,UAAU,YAAY,KAAK,OAAO,KAAK;YAChD;UAAA;QACF;MACF;AAGO,eAAA,KAAK,YAAY,MAAM;AAEhC,aAAO,MAAM;AACX,YAAI,OAAO,YAAY;AACd,iBAAA,WAAW,YAAY,MAAM;QAAA;MAExC;IAAA;AAGK,WAAA;EAAA,GACN,CAAC,OAAO,QAAQ,CAAC;AAEpB,OAAI,SAAA,OAAA,SAAA,MAAO,QAAO,OAAO,MAAM,QAAQ,UAAU;AAC/C,eAAQ,0BAAA,UAAA,EAAQ,GAAG,OAAO,0BAAwB,KAAA,CAAC;EAAA;AAGjD,MAAA,OAAO,aAAa,UAAU;AAE9B,eAAA;MAAC;MAAA;QACE,GAAG;QACJ,yBAAyB,EAAE,QAAQ,SAAS;QAC5C,0BAAwB;MAAA;IAC1B;EAAA;AAIG,SAAA;AACT;A;;;;AChHO,IAAM,UAAU,MAAM;AAC3B,QAAM,SAAS,UAAU;AAEzB,QAAM,YAAY,eAAe;IAC/B,QAAQ,CAAC,UAAU;AACV,aAAA,MAAM,QAAQ,IAAI,CAAC,UAAU,MAAM,IAAK,EAAE,OAAO,OAAO;IAAA;EACjE,CACD;AAEK,QAAA,OAAsC,gBAAQ,MAAM;AACxD,UAAM,aAAsC,CAAC;AAC7C,UAAM,kBAAwC,CAAC;AAC3C,QAAA;AACH,KAAC,GAAG,SAAS,EAAE,QAAU,EAAA,QAAQ,CAAC,UAAU;AAC1C,OAAC,GAAG,KAAK,EAAE,QAAU,EAAA,QAAQ,CAAC,MAAM;AACnC,YAAI,CAAC,EAAG;AAER,YAAI,EAAE,OAAO;AACX,cAAI,CAAC,OAAO;AACF,oBAAA;cACN,KAAK;cACL,UAAU,EAAE;YACd;UAAA;QACF,OACK;AACC,gBAAA,YAAY,EAAE,QAAQ,EAAE;AAC9B,cAAI,WAAW;AACT,gBAAA,gBAAgB,SAAS,GAAG;AAC9B;YAAA,OACK;AACL,8BAAgB,SAAS,IAAI;YAAA;UAC/B;AAGF,qBAAW,KAAK;YACd,KAAK;YACL,OAAO;cACL,GAAG;YAAA;UACL,CACD;QAAA;MACH,CACD;IAAA,CACF;AAED,QAAI,OAAO;AACT,iBAAW,KAAK,KAAK;IAAA;AAGvB,eAAW,QAAQ;AAEZ,WAAA;EAAA,GACN,CAAC,SAAS,CAAC;AAEd,QAAM,QAAQ,eAAe;IAC3B,QAAQ,CAAC,UAAU;;AACjB,YAAM,cAAc,MAAM,QACvB,IAAI,CAAC,UAAU,MAAM,KAAM,EAC3B,OAAO,OAAO,EACd,KAAK,CAAC,EACN,IAAI,CAAC,UAAU;QACd,KAAK;QACL,OAAO;UACL,GAAG;QAAA;MACL,EACA;AAEE,YAAA,YAAW,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY;AAIvB,YAAA,SAAS,MAAM,QAClB,IAAI,CAAC,UAAA;;AAAU,iBAAAE,MAAA,YAAA,OAAA,SAAA,SAAU,OAAO,MAAM,OAAA,MAAvB,OAAA,SAAAA,IAAiC,WAAU,CAAA;MAAA,CAAE,EAC5D,OAAO,OAAO,EACd,KAAK,CAAC,EACN,OAAO,CAAC,UAAU,MAAM,QAAQ,MAAM,EACtC;QACC,CAAC,WACE;UACC,KAAK;UACL,OAAO;YACL,GAAG,MAAM;YACT,0BAA0B;UAAA;QAE9B;MACJ;AAEF,aAAO,CAAC,GAAG,aAAa,GAAG,MAAM;IACnC;IACA,mBAAmB;EAAA,CACpB;AAED,QAAM,cAAc,eAAe;IACjC,QAAQ,CAAC,UAAU;AACjB,YAAMC,eAAuC,CAAC;AAExC,YAAA,QACH,IAAI,CAAC,UAAU,OAAO,gBAAgB,MAAM,OAAO,CAAE,EACrD;QAAQ,CAAC,UAAA;;AACR,kBAAA,MAAA,MAAA,MAAA,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY,aAAZ,OAAA,SAAA,GAAsB,OAAO,MAAM,EAAA,MAAnC,OAAA,SAAA,GAAwC,aAAxC,OAAA,SAAA,GACI,OAAO,OAAA,EACR,QAAQ,CAAC,YAAY;AACpBA,yBAAY,KAAK;cACf,KAAK;cACL,OAAO;gBACL,KAAK;gBACL,MAAM;cAAA;YACR,CACD;UACF,CAAA;QAAA;MACL;AAEKA,aAAAA;IACT;IACA,mBAAmB;EAAA,CACpB;AAED,QAAM,SAAS,eAAe;IAC5B,QAAQ,CAAC,UAEL,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,MAAO,EAC5B,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,MAAA,OAAa;MACjC,KAAK;MACL;MACA;IAAA,EACA;IACJ,mBAAmB;EAAA,CACpB;AAED,QAAM,cAAc,eAAe;IACjC,QAAQ,CAAC,UAEL,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,WAAY,EACjC,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,OAAA,OAAc;MAClC,KAAK;MACL,OAAO;QACL,GAAG;MACL;MACA;IAAA,EACA;IACJ,mBAAmB;EAAA,CACpB;AAEM,SAAA;IACL;MACE,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;IACL;IACA,CAAC,MAAM;AACE,aAAA,KAAK,UAAU,CAAC;IAAA;EAE3B;AACF;AAMO,SAAS,cAAc;AAC5B,QAAM,OAAO,QAAQ;AACrB,SAAO,KAAK,IAAI,CAAC,YAAA,6BACd,OAAO,EAAA,GAAG,KAAK,KAAK,YAAY,KAAK,UAAU,GAAG,CAAC,GAAA,CAAI,CACzD;AACH;AAEA,SAAS,OAAU,KAAe,IAAyB;AACnD,QAAA,OAAA,oBAAW,IAAY;AACtB,SAAA,IAAI,OAAO,CAAC,SAAS;AACpB,UAAA,MAAM,GAAG,IAAI;AACf,QAAA,KAAK,IAAI,GAAG,GAAG;AACV,aAAA;IAAA;AAET,SAAK,IAAI,GAAG;AACL,WAAA;EAAA,CACR;AACH;A;;;;ACxLO,IAAM,UAAU,MAAM;AAC3B,QAAM,SAAS,UAAU;AAEzB,QAAM,eAAe,eAAe;IAClC,QAAQ,CAAC,UAAU;;AACjB,YAAMC,gBAAwC,CAAC;AACzC,YAAA,YAAW,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY;AAE7B,UAAI,CAAC,UAAU;AACb,eAAO,CAAC;MAAA;AAGJ,YAAA,QACH,IAAI,CAAC,UAAU,OAAO,gBAAgB,MAAM,OAAO,CAAE,EACrD;QAAQ,CAAC,UACR;;AAAA,kBAAA,MAAAC,MAAA,SAAS,OAAO,MAAM,EAAE,MAAxB,OAAA,SAAAA,IAA2B,WAA3B,OAAA,SAAA,GACI,OAAO,CAAC,MAAM,EAAE,QAAQ,QAAA,EACzB,QAAQ,CAAC,UAAU;AAClBD,0BAAa,KAAK;cAChB,KAAK;cACL,OAAO,MAAM;cACb,UAAU,MAAM;YAAA,CACV;UACT,CAAA;QAAA;MACL;AAEKA,aAAAA;IACT;IACA,mBAAmB;EAAA,CACpB;AAEK,QAAA,EAAE,QAAQ,IAAI,eAAe;IACjC,QAAQ,CAAC,WAAW;MAClB,SACE,MAAM,QACH,IAAI,CAAC,UAAU,MAAM,OAAQ,EAC7B,KAAK,CAAC,EACN,OAAO,OAAO,EACjB,IAAI,CAAC,EAAE,UAAU,GAAG,OAAA,OAAc;QAClC,KAAK;QACL,OAAO;UACL,GAAG;UACH,0BAA0B;QAC5B;QACA;MAAA,EACA;IAAA;IAEJ,mBAAmB;EAAA,CACpB;AAED,QAAM,aAAa,CAAC,GAAG,SAAS,GAAG,YAAY;AAE/C,aAAA,0BAAA,+BAAA,EAEK,UAAW,WAAA,IAAI,CAAC,OAAO,UAAA,6BACrB,OAAO,EAAA,GAAG,OAAO,KAAK,eAAe,MAAM,GAAG,IAAI,CAAC,GAAA,CAAI,CACzD,EAAA,CACH;AAEJ;", "names": ["React", "useRef", "useEffect", "useMemo", "subscribe", "React", "useLayoutEffect", "intersectionObserverOptions", "startTransition", "React", "opts", "storageKey", "useLayoutEffect", "_a", "match", "routeId", "key", "routerContext", "ScrollRestoration", "shouldBlock", "_customBlockerFn", "_a", "preloadMeta", "assetScripts", "_a"]}