{"name": "frontend", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@popperjs/core": "^2.11.8", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/react-table": "^8.21.3", "@tanstack/router-plugin": "^1.121.2", "@types/bootstrap": "^5.2.10", "@types/papaparse": "^5.3.16", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^2.15.4", "shadcn-rtl": "^0.0.5", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^4.0.5"}, "devDependencies": {"@hey-api/openapi-ts": "0.79.1", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^24.0.15", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}