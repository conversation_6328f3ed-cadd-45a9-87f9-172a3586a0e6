from django.db import models
import ulid


def generate_ulid():
    """Generate a ULID string that Django can serialize in migrations."""
    return str(ulid.new())


class MyModel(models.Model):
    id = models.CharField(
        primary_key=True, max_length=26, default=generate_ulid, editable=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects: models.Manager = models.Manager()

    class Meta:
        abstract = True

    def __str__(self) -> str:
        return str(self.id)


class HttpException(Exception):
    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message
