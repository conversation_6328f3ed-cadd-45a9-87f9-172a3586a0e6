import{j as e,aI as r,B as s}from"./index-DwPFwVGs.js";import{S as t}from"./index-DVysEDS8.js";function l({title:a}){return e.jsx("header",{className:"flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)",children:e.jsxs("div",{className:"flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6",children:[e.jsx(r,{className:"-ms-1"}),e.jsx(t,{orientation:"vertical",className:"mx-2 data-[orientation=vertical]:h-4"}),e.jsx("h1",{className:"text-base font-medium",children:a}),e.jsx("div",{className:"ms-auto flex items-center gap-2",children:e.jsx(s,{variant:"ghost",asChild:!0,size:"sm",className:"hidden sm:flex",children:e.jsx("a",{href:"https://github.com/shadcn-ui/ui/tree/main/apps/v4/app/(examples)/dashboard",rel:"noopener noreferrer",target:"_blank",className:"dark:text-foreground",children:"GitHub"})})})]})})}export{l as S};
