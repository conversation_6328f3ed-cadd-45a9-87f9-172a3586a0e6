import{r as i,j as o,k as L,a4 as O,P as _,f as H,ak as N,h as j,al as G,e as K,am as U}from"./index-DwPFwVGs.js";import{u as X}from"./select-S-eFdQZZ.js";var y="Checkbox",[$,Z]=L(y),[J,P]=$(y);function Q(t){const{__scopeCheckbox:n,checked:c,children:l,defaultChecked:a,disabled:e,form:f,name:h,onCheckedChange:d,required:k,value:x="on",internal_do_not_use_render:u}=t,[p,v]=H({prop:c,defaultProp:a??!1,onChange:d,caller:y}),[C,m]=i.useState(null),[g,r]=i.useState(null),s=i.useRef(!1),E=C?!!f||!!C.closest("form"):!0,R={checked:p,disabled:e,setChecked:v,control:C,setControl:m,name:h,form:f,value:x,hasConsumerStoppedPropagationRef:s,required:k,defaultChecked:b(a)?!1:a,isFormControl:E,bubbleInput:g,setBubbleInput:r};return o.jsx(J,{scope:n,...R,children:V(u)?u(R):l})}var S="CheckboxTrigger",w=i.forwardRef(({__scopeCheckbox:t,onKeyDown:n,onClick:c,...l},a)=>{const{control:e,value:f,disabled:h,checked:d,required:k,setControl:x,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:C}=P(S,t),m=N(a,x),g=i.useRef(d);return i.useEffect(()=>{const r=e==null?void 0:e.form;if(r){const s=()=>u(g.current);return r.addEventListener("reset",s),()=>r.removeEventListener("reset",s)}},[e,u]),o.jsx(_.button,{type:"button",role:"checkbox","aria-checked":b(d)?"mixed":d,"aria-required":k,"data-state":z(d),"data-disabled":h?"":void 0,disabled:h,value:f,...l,ref:m,onKeyDown:j(n,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:j(c,r=>{u(s=>b(s)?!0:!s),C&&v&&(p.current=r.isPropagationStopped(),p.current||r.stopPropagation())})})});w.displayName=S;var B=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,name:l,checked:a,defaultChecked:e,required:f,disabled:h,value:d,onCheckedChange:k,form:x,...u}=t;return o.jsx(Q,{__scopeCheckbox:c,checked:a,defaultChecked:e,disabled:h,required:f,onCheckedChange:k,name:l,form:x,value:d,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(w,{...u,ref:n,__scopeCheckbox:c}),p&&o.jsx(A,{__scopeCheckbox:c})]})})});B.displayName=y;var M="CheckboxIndicator",T=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,forceMount:l,...a}=t,e=P(M,c);return o.jsx(O,{present:l||b(e.checked)||e.checked===!0,children:o.jsx(_.span,{"data-state":z(e.checked),"data-disabled":e.disabled?"":void 0,...a,ref:n,style:{pointerEvents:"none",...t.style}})})});T.displayName=M;var q="CheckboxBubbleInput",A=i.forwardRef(({__scopeCheckbox:t,...n},c)=>{const{control:l,hasConsumerStoppedPropagationRef:a,checked:e,defaultChecked:f,required:h,disabled:d,name:k,value:x,form:u,bubbleInput:p,setBubbleInput:v}=P(q,t),C=N(c,v),m=X(e),g=G(l);i.useEffect(()=>{const s=p;if(!s)return;const E=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(E,"checked").set,D=!a.current;if(m!==e&&I){const F=new Event("click",{bubbles:D});s.indeterminate=b(e),I.call(s,b(e)?!1:e),s.dispatchEvent(F)}},[p,m,e,a]);const r=i.useRef(b(e)?!1:e);return o.jsx(_.input,{type:"checkbox","aria-hidden":!0,defaultChecked:f??r.current,required:h,disabled:d,name:k,value:x,form:u,...n,tabIndex:-1,ref:C,style:{...n.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=q;function V(t){return typeof t=="function"}function b(t){return t==="indeterminate"}function z(t){return b(t)?"indeterminate":t?"checked":"unchecked"}function ee({className:t,...n}){return o.jsx(B,{"data-slot":"checkbox",className:K("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:o.jsx(T,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(U,{className:"size-3.5"})})})}export{ee as C};
